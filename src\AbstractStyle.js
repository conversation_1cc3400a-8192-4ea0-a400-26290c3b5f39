import { refreshModel, bindModel } from "./utils/ModelUtil";
/**
 * 抽象 样式
 * 公共的(common)
 * 自定义(custom)
 */
export default class AbstractStyle {
  constructor(context) {
    if (context) {
      this.context = context;
      this.designer = context.getDesigner();
    }
  }
  getContainer () {
    return $(`<div class="model-wrap"></div>`);
  }
  getClazzColor () {
    return "chart-color";
  }
  initPanel (parentContainer, data, callback) {
    if (parentContainer) {
      this.container = this.getContainer();
      parentContainer.append(this.container);
      //初始化
      if (data) {
        if (Array.isArray(data)) {
          this.list = data;
        } else {
          this.item = data;
        }
      }
      this.callback = callback;
      this.initModel(this.container);
    }
  }
  /**
   * 抽象-方法-初始化DOM模块
   */
  initModel (container) {
    if (container) {
      container.empty();
      const bodyContainer = $(`<div class="model-body"></div>`);
      container.append(bodyContainer);
      this.refreshBody(bodyContainer);
    }
  }
  refreshBody (bodyContainer) {
    if (bodyContainer) {
      bodyContainer.empty();
      const modelName = this.getModelName();
      this.refreshPanel(bodyContainer, modelName, this.item, this.callback);
      //刷新颜色Class绑定调色板
      const clazzColor = this.getClazzColor();
      this.refreshClazzColor(bodyContainer, clazzColor);
      // this.refreshModel(item);
      // this.bindModel(item, callback);
    }
  }
  refreshPanel (chartBody, modelName, item, callback) {

  }
  /**
   * 描述:刷新颜色
   */
  refreshClazzColor (bodyContainer, clazzName) {
    const container = bodyContainer || this.container;
    const clazz = clazzName || "chart-color";
    if (container) {
      const colorContainer = $(container).find("." + clazz);//.find(".com-color");
      colorContainer.each(function (index, element) {
        const color = ($(element).val() || "transparent");
        $(element).spectrum({
          color: color,
          appendTo: "parent",
          preferredFormat: "rgb",
          showPalette: true,
          maxPaletteSize: 6,
          showAlpha: true,
          showInput: true
        });
      });
    }
  }
  setOptions (options) {
    this.options = options;
  }
  refreshOption (selectContainer, list, key = "key", text = "text") {//keyCol, textCol
    if (selectContainer) {
      $(selectContainer).empty();
      selectContainer.prepend("<option value=''>--请选择--</option>");//添加第一个option值
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          if (item[key] && item[text]) {
            selectContainer.append("<option value='" + item[key] + "'>" + item[text] + "</option>");
          } else {
            selectContainer.append("<option value='" + item + "'>" + item + "</option>");
          }
          // let key = item.key;
          // let text = item.text;
          // if (keyCol) {
          //   key = item[keyCol];
          // }
          // if (textCol) {
          //   text = item[textCol];
          // }
          // selectContainer.append("<option value='" + key + "'>" + text + "</option>");
        }
      }
    }
  }
  /**
   * 
   * @returns 
   */
  getTitle () {
    return "标题";
  }
  /**
   * 
   * @returns 
   */
  getModelName () {
    return "v-model";
  }
  /**
   * 
   * @param {*} key 
   * @param {*} value 
   */
  refreshEvent (key, value) { }
  refreshEventByBind(key, value){}
  /**
   * 描述:绑定属性
   */
  bindModel (property, callback, model) {
    const modelName = model || this.getModelName();
    if (this.container && property) {
      // bindModel(this.container, property, callback, modelName);
      const self = this;
      bindModel(this.container, property, function (key, value) {
        self.refreshEvent(key, value);
        self.refreshEventByBind(key,value);
        if (callback) {
          callback();
        }
      }, modelName);
    }
  }
  /**
   * 描述:刷新属性
   * @param property
   * @param callback
   * @param modelName
   */
  refreshModel (property, callback, model) {
    const modelName = model || this.getModelName();
    if (this.container && property) {
      // refreshModel(this.container, property, callback, modelName);
      const self = this;
      refreshModel(this.container, property, function (key, value) {
        self.refreshEvent(key, value);
        if (callback) {
          callback();
        }
      }, modelName);
    }
  }
}