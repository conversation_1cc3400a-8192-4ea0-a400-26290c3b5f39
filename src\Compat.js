import MaterialService from "./service/MaterialService";
import MaterialModel from "./model/MaterialModel";
/**
 * 兼容
 */
export default class Compat {
  constructor(context) {
    this.context = context;
  }
  //兼容开始
  start(json) {
    if (json) {
      if (json["background"]) {
        this.parseBackground(json["background"]);
      }
      if (json["layers"]) {
        // this.parseLayers(json["layers"]);
      }
      if (json["busModels"]) {
        json["models"] = json["busModels"];
        delete json["busModels"];
        //解析
        this.parseModels(json["models"]);
      }
      if (json["dicts"]) {
        this.parseDicts(json["dicts"]);
      }
      if (json["params"]) {
        this.parseParams(json["params"]);
      }
      if (json["option"]) {
        delete json["option"];
      }
    }
  }
  parseBackground(bg) {
    if (bg) {
      if (bg["type"]) {
        bg["bgType"] = bg["type"];
        delete bg["type"];
      }
      if (bg["id"]) {
        if (!bg["image"]) {
          bg["image"] = {};
        }
        bg["image"]["id"] = bg["id"];
        delete bg["id"];
        //查询
        const self = this;
        const params = { id: bg["id"] };
        const materialService = new MaterialService(this.context);
        materialService.queryList(params, function (result) {
          if (result) {
            const model = new MaterialModel(self.context);
            model.refreshProperty(result);
            self.context.addMaterial(model);
          }
        });
      }
      if (bg["url"]) {
        if (!bg["image"]) {
          bg["image"] = {};
        }
        bg["image"]["imageUrl"] = bg["url"];
        delete bg["url"];
      }
      if (bg["opactity"]) {
        bg["opacity"] = bg["opactity"];
        delete bg["opactity"];
      }
    }
  }
  parseLayers(list) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        this.parseLayer(item);
        //处理 layers
        const layers = item.layers;
        if (layers && layers.length) {
          item["childs"] = layers;
          //删除
          delete item.layers;
        }
        //递归
        const childs = item.childs;
        if (childs && childs.length) {
          this.parseLayers(childs);
        }
      }
    }
  }
  parseModels(list) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        if (item["bind"]) {
          const bind = item["bind"];
          if (bind["id"]) {
            item["linkId"] = bind["id"];
          }
          if (bind["datasetType"]) {
            item["datasetType"] = bind["datasetType"];
          }
          if (bind["params"]) {
            item["params"] = bind["params"];
          }
          if (bind["columns"]) {
            item["columns"] = bind["columns"];
          }
          delete item["bind"];
        }
      }
    }
  }
  parseDicts(list) {
    // if (list && list.length) {
    //   for (let i = 0; i < list.length; i++) {
    //     const item = list[i];
    //     // this.parseDict(item);
    //   }
    // }
  }
  parseParams(list) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        if (item["linkId"]) {
          item["modelId"] = item["linkId"];
          delete item["linkId"];
        }
      }
    }
  }
  /**############################### */
  refreshChart(property) {
    if (property) {
      if (property.name) {
        property.label = property.name;
      }
      const chart = {};
      //处理chart
      if (property.chart) {
        const chartMap = property.chart;
        for (let key in chartMap) {
          chart[key] = chartMap[key];
        }
      }
      property["chart"] = chart;
      //处理property
      if (property.width) {
        chart.width = property.width;
      }
      if (property.height) {
        chart.height = property.height;
      }
      if (property.isOverFlow) {
        chart.isOverFlow = property.isOverFlow;
        delete property.isOverFlow;
      }
      if (property.isMarquee) {
        chart.isMarquee = property.isMarquee;
        delete property.isMarquee;
      }
      if (property.text) {
        chart.text = property.text;
        delete property.text;
      }
      if (property.font) {
        chart.font = this.refreshFont(property.font);
        delete property.font;
      }
      if (property.border) {
        chart.border = property.border;
        delete property.border;
      }
      if (property.bg) {
        chart.bg = this.refreshBg(property.bg);
        delete property.bg;
      }
      if (property.marquee) {
        chart.marquee = property.marquee;
        delete property.marquee;
      }
      if (property.shape) {
        chart.shape = property.shape;
        delete property.shape;
      }
      if (property.controlStyle) {
        const controlStyle = property.controlStyle;
        for (let key in controlStyle) {
          // $.extend(true, chart, controlStyle.key);
          chart[key] = controlStyle[key];
        }
        delete property.controlStyle;
      }
      if (property.filter) {
        const filter = property.filter;
        const cell = {};
        for (let key in filter) {
          if (key === "select") {
            cell["selected"] = filter[key];
          } else {
            cell[key] = filter[key];
          }
        }
        if (cell) {
          chart["cell"] = cell;
          delete property.filter;
        }
      }
      if (property.table) {
        const table = property.table;
        if (table.columns) {
          chart.columns = table.columns;
        }
        if (table.columnStyles) {
          chart.columnStyles = table.columnStyles;
          if (chart.columnStyles.length) {
            for (let cloItem of chart.columnStyles) {
              if (cloItem.cellStyle.bg) {
                cloItem.cellStyle.bg = this.refreshBg(cloItem.cellStyle.bg);
              }
              if (cloItem.cellStyle.font) {
                cloItem.cellStyle.font = this.refreshFont(
                  cloItem.cellStyle.font
                );
              }
              if (cloItem.textStyle.bg) {
                cloItem.textStyle.bg = this.refreshBg(cloItem.textStyle.bg);
              }
              if (cloItem.textStyle.font) {
                cloItem.textStyle.font = this.refreshFont(
                  cloItem.textStyle.font
                );
              }
            }
          }
        }
        if (table.noData) {
          chart.noData = table.noData;
        }
        if (table.overflow) {
          chart.overflow = table.overflow;
        }
        if (table.rowStyles) {
          chart.rowStyles = table.rowStyles;
          if (chart.rowStyles.font) {
            chart.rowStyles.font = this.refreshFont(chart.rowStyles.font);
          }
          if (chart.rowStyles.series.length) {
            for (let rowItem of chart.rowStyles.series) {
              if (rowItem.bg) {
                rowItem.bg = this.refreshBg(rowItem.bg);
              }
              if (rowItem.font) {
                rowItem.font = this.refreshFont(rowItem.font);
              }
            }
          }
        }
        if (table.theadStyles) {
          chart.theadStyles = table.theadStyles;
          if (chart.theadStyles.bg) {
            chart.theadStyles.bg = this.refreshBg(chart.theadStyles.bg);
          }
          if (chart.theadStyles.font) {
            chart.theadStyles.font = this.refreshFont(chart.theadStyles.font);
          }
        }
        if (table.marquee) {
          chart.marquee = table.marquee;
        }

        delete property.table;
      }
      //数字
      if (property.stepTotal) {
        chart.stepTotal = property.stepTotal;
        delete property.stepTotal;
      }
      if (property.stepSpiltTime) {
        chart.stepSpiltTime = property.stepSpiltTime;
        delete property.stepSpiltTime;
      }
      if (property.num) {
        chart.num = property.num;
        if (chart.num.font) {
          chart.num.font = this.refreshFont(chart.num.font);
        }
        delete property.num;
      }
      if (property.flex) {
        chart.flex = property.flex;
        delete property.flex;
      }
      if (property.thousand) {
        chart.thousand = property.thousand;
        if (chart.thousand.num.font) {
          chart.thousand.num.font = this.refreshFont(chart.thousand.num.font);
        }
        delete property.thousand;
      }
      if (property.unit) {
        chart.unit = property.unit;
        if (chart.unit.font) {
          chart.unit.font = this.refreshFont(chart.unit.font);
        }
        delete property.unit;
      }
      //超链接
      if (property.iframe) {
        const iframe = property.iframe;
        for (let key in iframe) {
          chart[key] = iframe[key];
        }
        delete property.iframe;
      }
      //图标
      if (property.icon) {
        chart.icon = property.icon;
        delete property.icon;
      }
      //文本
      if (property.isLengthHidden) {
        chart.isLengthHidden = property.isLengthHidden;
        delete property.isLengthHidden;
      }
      if (property.isScroll) {
        chart.isScroll = property.isScroll;
        delete property.isScroll;
      }
      if (property.text) {
        chart.text = property.text;
        delete property.text;
      }
      //树形
      if (property.iconStyle) {
        chart.iconStyle = property.iconStyle;
        delete property.iconStyle;
      }
      if (property.textStyle) {
        chart.textStyle = property.textStyle;
        delete property.textStyle;
      }
      if (property.switchStyle) {
        chart.switchStyle = property.switchStyle;
        delete property.switchStyle;
      }
      //视频
      if (property.video) {
        const video = property.video;
        for (let key in video) {
          chart[key] = video[key];
        }
        delete property.video;
      }
    }
  }

  refreshBg(bg) {
    let bgStyle = {};
    const color = {};
    const image = {};
    if (bg.backdropfilter) {
      bgStyle.backdropfilter = bg.backdropfilter;
    }
    if (bg.opacity) {
      bgStyle.opacity = bg.opacity;
    }
    if (bg.rotate) {
      bgStyle.rotate = bg.rotate;
    }
    if (bg.blur) {
      bgStyle.blur = bg.blur;
    }
    if (bg.type === "COLOR" || bg.backgroundColor) {
      bgStyle.bgType = "color";
      color["type"] = "color";
      color["color"] = bg.backgroundColor;
      bgStyle["color"] = color;
    } else if (bg.type === "IMAGE" || bg.imageUrl) {
      bgStyle.bgType = "image";
      image.imageUrl = bg.imageUrl;
      bgStyle["image"] = image;
    } else if (bg.colors && bg.colors.length) {
      bgStyle.bgType = "color";
      color["type"] = "gradient";
      const gradient = {};
      gradient["gradientFn"] = bg.gradientFn;
      gradient["gradientAngle"] = bg.gradientAngle;
      gradient["gradientAtPosition"] = bg.gradientAtPosition;
      gradient["gradientRadialShape"] = bg.gradientRadialShape;
      gradient["gradientRadialSize"] = bg.gradientRadialSize;
      gradient["colors"] = bg.colors;
      color["gradient"] = gradient;
      bgStyle["color"] = color;
    }
    return bgStyle;
  }
  refreshFont(font) {
    let fontStyle = {};
    if (font) {
      let textShadow = {};
      for (let key in font) {
        if (key === "shadowColor") {
          textShadow[key] = font[key];
        } else if (key === "shadowCount") {
          textShadow[key] = font[key];
        } else if (key === "hShadow") {
          textShadow[key] = font[key];
        } else if (key === "vShadow") {
          textShadow[key] = font[key];
        } else if (key === "blur") {
          textShadow[key] = font[key];
        } else {
          fontStyle[key] = font[key];
        }
      }
      if (textShadow) {
        fontStyle["textShadow"] = textShadow;
      }
    }
    return fontStyle;
  }
  refreshBind(property) {
    if (property && property.bind) {
      const bind = property.bind;
      for (let key in bind) {
        if (key === "bindType") {
          //模板数据
          if (bind[key] === "datatm") {
            bind[key] = "mock";
            if (property.bindData && property.bindData.length) {
              bind.mock = property.bindData;
              delete property.bindData;
            }
          }
        }
        if (key === "mappings") {
          if (bind[key] && bind[key].length) {
            const mappings = bind[key];
            for (let item of mappings) {
              if (
                item.key === "name" &&
                property.type === "PieDoughnutChartLayer"
              ) {
                item.key = "dimension";
              } else if (
                property.type.indexOf("Pie") != -1 &&
                item.key === "group"
              ) {
                item.key = "dimension";
              }
              if (property.type.indexOf("Pie") === -1) {
                item.value = `\$\{${item.key}\}`;
              }
            }
          }
        }
        if (key === "bindLayerId") {
          bind["layerId"] = bind[key];
          delete bind[key];
        }
        //bind.dict.id  bind.dict.level
        if (key === "dict") {
          if (bind[key].id) {
            bind["dictId"] = bind[key].id;
          }
          if (bind[key].level) {
            bind["dictLevel"] = bind[key].id;
          }
          delete bind[key];
        }
      }
    }
  }
  refreshOption(property) {
    if (property && property.chart) {
      if (property.name) {
        property.label = property.name;
      }
      const chart = property.chart;
      //解析颜色
      // if(chart.colorSet){
      //   chart.color = this.buildColor(chart.colorSet);
      //   delete chart.colorSet;
      // }
      const global = chart.global;
      // for(let key in global){
      //   const globalItem = global[key];
      //   if(globalItem.itemStyle){
      //     this.refreshColor(globalItem.itemStyle);
      //   }
      //   if(globalItem.areaStyle){
      //     this.refreshColor(globalItem.areaStyle);
      //   }
      //   if(globalItem.lineStyle){
      //    this.refreshColor(globalItem.lineStyle);
      //   }
      // }
      // if(chart.bar){
      //   this.parseSeriesStyle(chart.bar);
      // }
      // if(chart.line){
      //   this.parseSeriesStyle(chart.line);
      // }
      // if(chart.pie){
      //   this.parseSeriesStyle(chart.pie);
      // }
      // if(chart.xAxis){
      //   this.parseSeriesStyle(chart.xAxis);
      // }
      // if(chart.yAxis){
      //   this.parseSeriesStyle(chart.yAxis);
      // }
      const bars = chart.bar;
      const lineList = chart.line;
      const pies = chart.pie;
      let list;
      if (bars && lineList) {
        if (bars && bars.length) {
          list = [];
          for (let i = 0; i < bars.length; i++) {
            const series = bars[i];
            const item = { type: "bar" };
            $.extend(true, item, series);
            if (global) {
              for (let key in global) {
                const globalStyle = global[key];
                for (let style in globalStyle) {
                  item[style] = globalStyle[style];
                }
              }
            }
            list.push(item);
          }
          chart["barSeries"] = list;
          delete chart.bar;
          delete chart.global;
        }
        if (lineList && lineList.length) {
          list = [];
          for (let i = 0; i < lineList.length; i++) {
            const series = lineList[i];
            const item = { type: "line" };
            $.extend(true, item, series);
            if (global) {
              for (let key in global) {
                const globalStyle = global[key];
                for (let style in globalStyle) {
                  item[style] = globalStyle[style];
                }
              }
            }
            list.push(item);
          }
          chart["lineSeries"] = lineList;
          delete chart.line;
          delete chart.global;
        }
      } else {
        if (bars && bars.length) {
          list = [];
          for (let i = 0; i < bars.length; i++) {
            const series = bars[i];
            if (series.yAxisIndex) {
              delete series.yAxisIndex;
            }
            const barItem = { type: "bar" };
            $.extend(true, barItem, series);
            if (global) {
              for (let key in global) {
                const globalStyle = global[key];
                for (let style in globalStyle) {
                  barItem[style] = globalStyle[style];
                }
              }
            }
            list.push(barItem);
          }
          chart["series"] = list;
          delete chart.bar;
          delete chart.global;
        }
        if (lineList && lineList.length) {
          list = [];
          for (let i = 0; i < lineList.length; i++) {
            const series = lineList[i];
            const item = { type: "line" };
            $.extend(true, item, series);
            if (global) {
              for (let key in global) {
                const globalStyle = global[key];
                for (let style in globalStyle) {
                  item[style] = globalStyle[style];
                }
              }
            }
            list.push(item);
          }
          chart["series"] = lineList;
          delete chart.line;
          delete chart.global;
        }
      }
      //饼图
      if (pies && pies.length) {
        list = [];
        const item = { type: "pie", data: [] };
        list.push(item);
        if (global) {
          for (let key in global) {
            const globalStyle = global[key];
            for (let style in globalStyle) {
              item[style] = globalStyle[style];
            }
          }
        }
        for (let i = 0; i < pies.length; i++) {
          const series = pies[i];
          item["data"].push(series);
        }
        chart["series"] = list;
        delete chart.global;
        delete chart.pie;
      }
      //仪表盘
      if (chart.gauge) {
        const gauge = chart.gauge;
        list = [];
        const seriesItem = { type: "gauge" };
        for (let key in gauge) {
          seriesItem[key] = gauge[key];
        }
        if (seriesItem) {
          list.push(seriesItem);
        }
        chart["series"] = list;
        delete chart.gauge;
      }
    }
  }
  parseLayer(property) {
    if (property) {
      if (property["locked"]) {
        property["isLock"] = property["locked"];
        delete property["locked"];
      }
      if (property["hidden"]) {
        property["isHide"] = property["hidden"];
        delete property["hidden"];
      }
    }
    if (property && property.type) {
      const type = property.type;
      // let json = JSON.parse(JSON.stringify(property));
      // "导向力图",type:"GraphChartSxLayer"
      // "轮播图",type:"WheelLayer",
      // "自定义表格",type:"DivTableLayer",
      switch (type) {
        //基础
        case "GroupLayer": // "组合",
          property.type = "GroupLayer";
          property.marquee = {
            loop: -1, //指定内容将滚动多少次。 “ -1”用于无限运动播放
            scrolldelay: 0, //两次移动之间的延迟量（以毫秒为单位）
            scrollamount: 50, //内容移动速度（像素/秒）
            circular: false, //如果为“ true”-字符串是连续的
            drag: false, //如果为“ true”-启用行拖动
            runshort: true, //如果“ true”-短字符串也“运行”，则“ false”-静止不动
            hoverstop: false, //true-该行在鼠标悬停时停止，false-该行不停止
            direction: "up", //up,down,left,right 上下左右
          };
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "GroupMarqueeLayer": // "组合跑马灯",//组合,最近(完成)
          property.type = "GroupLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "IFrameLayer": // "框架",
          property.type = "IFrameLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "NumQuotaLayer": // "数字翻牌",
          property.type = "NumLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "PercentageQuotaLayer": // "百分比",//数字翻牌
          property.type = "NumLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "TextLayer": // "文本",
          property.type = "TextLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "MarqueeLayer": // "跑马灯",
          property.type = "MarqueeLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "ImageLayer": // "图片",
          property.type = "ImageLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "SvgLayer": // "矢量图",
          property.type = "SvgLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "VideoLayer": // "视频",
          property.type = "VideoLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        // case 'XgPlayerLayer':// "西瓜视频",
        //   property.type = 'XgPlayerLayer';
        //   this.refreshChart(property);
        //   this.refreshBind(property);
        //   break;
        case "FullScreenLayer": // "全屏",
          property.type = "FullScreenLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "SimpleTableLayer": // "表格",
          property.type = "TableLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "D3ModelLayer": // "3D模型",
          property.type = "Iframe3DLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "BaseTreeLayer": // "树形",//最近(完成)
          property.type = "TreeLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "GridBordLayer": // "栅格",//最近(完成)
          property.type = "GridLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "DateLayer": // "栅格",//最近(完成)
          property.type = "DateLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "BorderLayer": // "边框",
          property.type = "BorderLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "IconLayer": // "图标",
          property.type = "IconLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        // 形状
        case "CircularLayer": // "圆形",
          property.type = "CircularShapeLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "OvalLayer": // "椭圆",//圆形
          property.type = "CircularShapeLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "RectangleLayer": // "矩形",
          property.type = "RectangleShapeLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        //过滤
        case "SingleTableFilterLayer": // "单选列表",
          property.type = "RadioFilterLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "SingleSelectFilterLayer": // "单选下拉",
          property.type = "SelectFilterLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;
        case "DateFilterLayer": // "日期组件",
          property.type = "DateFilterLayer";
          this.refreshChart(property);
          this.refreshBind(property);
          break;

        //图表
        case "LineChartLayer": // "面积图",//折线
          property.type = "LineChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "LineChartDjLayer": // "堆积面积图",//折线(堆积)
          property.type = "LineChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "LinesChartLayer": // "动态折线图",//折线(堆积)
          property.type = "LineChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "BarChartLayer": // "柱状图",//柱状
          property.type = "BarChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "BarChartQdLayer": // "曲顶柱状图",//柱状图
          property.type = "BarChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "BarChartDjLayer": // "堆积柱状图",//柱状(堆积)
          property.type = "BarChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "BarChartTxLayer": // "条形图",//条形图
          property.type = "BarYChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "BarChartTxdjLayer": // "条形堆积图",//条形图(堆积)
          property.type = "BarYChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "BarChartSyLayer": // "双Y条形图",//[水平柱]
          property.type = "BarPictorYChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "BarChartHxLayer": // "进度环柱状图",//环形柱状图
          property.type = "BarDoughnutChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "BarChartSzLayer": // "双轴图",//折柱混合
          property.type = "LineBarChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "PieChartLayer": // "饼状图",
          property.type = "PieChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "PieChartMgLayer": // "玫瑰图",
          property.type = "PieRoseChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "PieChartEwLayer": // "二维饼图",//[嵌套饼图](label)
          property.type = "PieNestChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "PieChartHxLayer": // "环形图",
          property.type = "PieDoughnutChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "PieChartHxDkLayer": // "环形断开图",
          property.type = "PieDoughnutGapChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "PieChartHxJdLayer": // "进度环图",//环图图？
          property.type = "PieDoughnutChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "GaugeChartLayer": // "仪表盘",
          property.type = "GaugeChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "RadarChartLayer": // "雷达图",
          property.type = "RadarChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "ScatterChartLayer": // "散点图",
          property.type = "ScatterChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "ScatterQpChartLayer": // "气泡图",
          property.type = "ScatterChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "WordChartLayer": // "词云",
          property.type = "WordChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "GraphChartLayer": // "图谱",
          property.type = "GraphChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "GraphChartWlLayer": // "关系网络图",
          property.type = "GraphChartLayer";

          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "LiquidChartLayer": // "水晶球",
          property.type = "LiquidChartLayer";

          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "SankeyChartLayer": // "桑基图",
          property.type = "SankeyChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "KLineChartLayer": // "K线图",
          property.type = "CandlestickChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "FunnelChartLayer": // "漏斗图",
          property.type = "FunnelChartLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "AngleChartLayer": // "极坐标图",(后面补)
          break;
        case "AngleChartDjLayer": // "极坐标堆积图",(后面补)
          break;
        case "TreemapChartLayer": // "矩形树图",(后面补)
          break;
        //地图
        case "MapChartZgscLayer": // "中国色彩",
          property.type = "ZgScMapLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "MapChartZgqpLayer": // "中国气泡",
          property.type = "ZgQpMapLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "MapChartZgrlLayer": // "中国热力",
          property.type = "ZgRlMapLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "MapChartZgfxLayer": // "中国飞线",
          property.type = "ZgFxMapLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        case "MapChartZgmpLayer": // "中国标点",//中国气泡
          property.type = "ZgQpMapLayer";
          this.refreshOption(property);
          this.refreshBind(property);
          break;
        default:
        // console.error("创建图层失败，不支持该类型的图层：" + type);
      }
    }
  }
}
