import { remove } from "./utils/Util";
import { encodeBase64 } from "./utils/DecodeUtil";
export default class Context {
  constructor() {
    this.instance = null;
    this.isDesign = false;
    this.config = this.initConfig();
    this.echarts = require("echarts");
    require("echarts-gl");
    require("echarts-wordcloud");
    require("echarts-liquidfill");
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new Context();
    }
    return this.instance;
  }
  /**##############################存储[designer]############################ */
  /**
   * designer
   */
  getIsDesign() {
    return this.isDesign || false;
  }
  setIsDesign(flag) {
    this.isDesign = flag;
  }
  getDesigner() {
    if (this.designer) {
      return this.designer;
    }
  }
  setDesigner(designer) {
    if (designer) {
      this.designer = designer;
    }
  }
  /**##############################存储[config]############################ */
  /**
   * config
   */
  initConfig() {
    return {
      width: 1920,
      height: 1080,
      resizeType: "2", //scale: 0.7
      speed: 1, //速度 图层上下左右移动的速度 使用键盘移动
      background: {
        bgType: "color", //bgType:image,color 加载方式: 1.url 2.base64 3.color
        color: "rgb(43, 51, 64)",
        image: {},
        blur: 0, //模糊度
        opacity: 100, //透明度 //opactity: 100,//透明度
      },
      // 全局Loading配置
      globalLoading: {
        enabled: true, // 是否启用全局loading
        showDelay: 100, // 显示延迟(ms)
        hideDelay: 200, // 隐藏延迟(ms)
        timeout: 30000, // 超时时间(ms)
        minShowTime: 500, // 最小显示时间(ms)
        defaultMessages: {
          get: "加载中...",
          post: "提交中...",
          postEncrypt: "加密提交中...",
          getDecrypt: "解密加载中...",
        },
      },
      previewTool: {
        isShow: false,
        top: 20,
        backgroundColor: "rgba(255,255,255,0.16)",
        dataMap: {
          share: { isShow: false, name: "分享", icon: "&#xe6a4;" },
          warn: { isShow: false, name: "预警", icon: "&#xe6a7;" },
        },
      },
      /**
       * {
       *    id: 1,
       *    name: '消息1',
       *    messageName: '',
       *    condition: "",
       *    isOpen: true,
       *    links: [
       *      {
       *        id: 1,
       *        isOpen: true,
       *        name: "联动1配置",
       *        param: "",
       *        constValue: "",
       *      }
       *    ]
       *  }
       */
      messageCenters: [],
      host: {},
    };
  }
  getConfig() {
    if (this.config) {
      return this.config;
    }
  }
  setConfig(key, value) {
    if (this.config && key) {
      this.config[key] = value;
    }
  }

  /**
   * 获取全局Loading配置
   * @returns {object} 全局Loading配置对象
   */
  getGlobalLoadingConfig() {
    if (this.config && this.config.globalLoading) {
      return this.config.globalLoading;
    }
    // 返回默认配置
    return {
      enabled: true,
      showDelay: 100,
      hideDelay: 200,
      timeout: 30000,
      minShowTime: 500,
      defaultMessages: {
        get: "加载中...",
        post: "提交中...",
        postEncrypt: "加密提交中...",
        getDecrypt: "解密加载中...",
      },
    };
  }

  /**
   * 设置全局Loading配置
   * @param {object} loadingConfig - Loading配置对象
   */
  setGlobalLoadingConfig(loadingConfig) {
    if (this.config && loadingConfig) {
      if (!this.config.globalLoading) {
        this.config.globalLoading = {};
      }
      // 合并配置
      this.config.globalLoading = {
        ...this.config.globalLoading,
        ...loadingConfig,
      };
    }
  }

  /**
   * 检查是否启用全局Loading
   * @returns {boolean} 是否启用
   */
  isGlobalLoadingEnabled() {
    const config = this.getGlobalLoadingConfig();
    return config.enabled === true;
  }

  /**
   * 启用/禁用全局Loading
   * @param {boolean} enabled - 是否启用
   */
  setGlobalLoadingEnabled(enabled) {
    this.setGlobalLoadingConfig({ enabled: !!enabled });
  }
  getHost() {
    if (this.config) {
      return this.config["host"] || {};
    }
  }
  setHost(map) {
    if (this.config && map) {
      if (!this.config["host"]) {
        this.config["host"] = {};
      }
      for (let key in map) {
        this.config["host"][key] = map[key];
      }
    }
  }
  getTool() {
    if (this.config) {
      return this.config["previewTool"] || {};
    }
  }
  getBackground() {
    if (this.config) {
      return this.config["background"] || {};
    }
  }
  getMessageCenters() {
    if (this.config) {
      return this.config["messageCenters"] || [];
    }
    return [];
  }
  getWidth() {
    if (this.config) {
      return this.config["width"] || 1920;
    }
  }
  getHeight() {
    if (this.config) {
      return this.config["height"] || 1080;
    }
  }
  getScaleType() {
    if (this.config) {
      return this.config["resizeType"] || 1;
    }
  }
  /**
   * 比例
   * @returns
   */
  getScale() {
    if (this.config) {
      return this.config["scale"] || 1;
    }
  }
  setScale(value) {
    if (this.config && (value || value === 0)) {
      this.config["scale"] = value;
    }
  }
  buildScale(container) {
    if (container) {
      const width = this.getWidth();
      const tempWidth = $(container).width();
      const scale = Math.round((tempWidth / width) * 100) / 100; //取整
      if (this.config) {
        this.config["scale"] = scale;
      }
    }
  }
  getResize(container) {
    let scale;
    if (container) {
      const width = this.getWidth();
      const height = this.getHeight();
      const tempWidth = $(container).width();
      const tempHeight = $(container).height();
      const type = this.getScaleType();
      let scaleX;
      let scaleY;
      if (type === "2") {
        scale = Math.round((tempWidth / width) * 1000) / 1000; //取整
      } else if (type === "3") {
        scale = Math.round((tempHeight / height) * 1000) / 1000; //取整
      } else {
        scaleX = Math.round((tempWidth / width) * 1000) / 1000; //取整
        scaleY = Math.round((tempHeight / height) * 1000) / 1000; //取整
        scale = scaleX + "," + scaleY;
      }
      // resize = { type: type, scale: scale, scaleX: scaleX, scaleY: scaleY };
    }
    return scale;
  }
  /**##############################存储[菜单拖拽(item)]############################ */
  /**
   * 菜单组件
   * @param {*} comp
   */
  addItem(comp) {
    if (comp) {
      this.comp = comp;
    }
  }
  getItem() {
    if (this.comp) {
      return this.comp;
    }
  }
  delItem() {
    if (this.comp) {
      delete this.comp;
    }
  }
  /**##############################存储[自定义地图]############################ */
  /**
   * 自定义地图
   * @param {*} model
   */
  addCustomMap(model) {
    if (model && Object.keys(model).length) {
      this.customMap = model;
    }
  }
  getCustomMap() {
    if (this.customMap) {
      return this.customMap;
    }
  }

  /**
   * 获取使用了自定义地图图层的自定义地图ID
   * @returns {*[]}
   */
  getUseCustomMapIds() {
    const idMap = {};
    const layerMap = this.getLayerMap();
    if (layerMap) {
      for (const key in layerMap) {
        const layer = layerMap[key];
        if (layer.property && layer.property.map) {
          const map = layer.property.map;
          if (map.isCustom) {
            idMap[map.id] = map.id;
          }
        }
      }
    }
    return Object.keys(idMap);
  }
  /**##############################存储[素材]############################ */
  /**
   * 素材
   * @param {*} model
   */
  addMaterial(model) {
    if (model && model.property && model.property.id) {
      if (!this.materialMap) {
        this.materialMap = {};
      }
      if (!this.materialMap[model.property.id]) {
        this.materialMap[model.property.id] = model;
      }
    }
  }
  getMaterialMap() {
    if (this.materialMap) {
      return this.materialMap;
    }
  }
  getMaterialById(id) {
    const modelMap = this.getMaterialMap();
    if (id && modelMap && modelMap[id]) {
      return modelMap[id];
    }
  }
  delMaterialById(id) {
    const modelMap = this.getMaterialMap();
    if (id && modelMap && modelMap[id]) {
      delete modelMap[id];
    }
  }
  getMaterialIds() {
    const modelMap = this.getMaterialMap();
    if (modelMap) {
      return Object.keys(modelMap);
    }
    return [];
  }
  getMaterialPropertyList() {
    let list;
    const modelMap = this.getMaterialMap();
    if (modelMap) {
      for (let key in modelMap) {
        const model = modelMap[key];
        const property = model["property"];
        if (!list) {
          list = [];
        }
        list.push(property);
      }
      return list;
    }
  }
  getMaterialPropertyById(id) {
    const modelMap = this.getMaterialMap();
    if (id && modelMap && modelMap[id]) {
      const model = modelMap[id];
      return model["property"];
    }
  }
  /**##############################存储[参数模型]############################ */
  /**
   * 参数
   * @param {*} model
   */
  addParam(model) {
    if (model && model.property && model.property.id) {
      if (!this.paramMap) {
        this.paramMap = {};
      }
      this.paramMap[model.property.id] = model;
    }
  }
  getParamMap() {
    if (this.paramMap) {
      return this.paramMap;
    }
  }
  getParamById(id) {
    const modelMap = this.getParamMap();
    if (id && modelMap && modelMap[id]) {
      return modelMap[id];
    }
  }
  delParamById(id) {
    const modelMap = this.getParamMap();
    if (id && modelMap && modelMap[id]) {
      delete modelMap[id];
    }
  }
  getParamPropertyList() {
    let list;
    const modelMap = this.getParamMap();
    if (modelMap) {
      for (let key in modelMap) {
        const model = modelMap[key];
        const property = model["property"];
        if (!list) {
          list = [];
        }
        list.push(property);
      }
      return list;
    }
  }
  getParamPropertyMap() {
    let itemMap;
    if (this.paramMap) {
      for (let key in this.paramMap) {
        const property = this.paramMap[key].property;
        const paramCode = property.code;
        const paramValue = property.value;
        if (!itemMap) {
          itemMap = {};
        }
        if (paramCode) {
          itemMap[paramCode] = paramValue;
        }
      }
    }
    return itemMap;
  }
  getParamValue(code) {
    let value;
    const paramMap = this.getParamMap();
    if (paramMap) {
      for (let key in paramMap) {
        const param = paramMap[key];
        const property = param.property;
        if (property && property.code && code === property.code) {
          value = property.value;
        }
      }
    }
    return value;
  }
  /**
   * 设置参数值
   * @param {*} code
   * @param {*} value
   */
  setParamValue(code, value) {
    const paramMap = this.getParamMap();
    if (paramMap) {
      for (let key in paramMap) {
        const param = paramMap[key];
        const property = param.property;
        if (property && property.code && code === property.code) {
          if (!value) {
            if (value === "0" || value === 0) {
              property.value = "0";
            } else {
              property.value = "";
            }
          } else {
            property.value = value;
          }
        }
      }
    }
  }
  /**##############################存储[数据集]############################ */
  /**
   * dataset
   * @param {*} model
   */
  addDataSet(model) {
    if (model && model.property && model.property.id) {
      if (!this.dataSetMap) {
        this.dataSetMap = {};
      }
      this.dataSetMap[model.property.id] = model;
    }
  }
  getDataSetMap() {
    if (this.dataSetMap) {
      return this.dataSetMap;
    }
  }
  getDataSetById(id) {
    const modelMap = this.getDataSetMap();
    if (id && modelMap && modelMap[id]) {
      return modelMap[id];
    }
  }
  delDataSetById(id) {
    const modelMap = this.getDataSetMap();
    if (id && modelMap && modelMap[id]) {
      delete modelMap[id];
    }
  }
  getDataSetPropertyList() {
    let list;
    const modelMap = this.getDataSetMap();
    if (modelMap) {
      for (let key in modelMap) {
        const model = modelMap[key];
        const property = model["property"];
        if (!list) {
          list = [];
        }
        list.push(property);
      }
      return list;
    }
  }
  getDatasetFileDatas() {
    const list = [];
    const modelMap = this.getDataSetMap();
    if (modelMap) {
      for (let key in modelMap) {
        const model = modelMap[key];
        const property = model["property"];
        const type = property.type;
        if (type && type === "file") {
          const id = property["id"];
          const datas = model["datas"];
          list.push({
            id: id,
            datas: encodeBase64(JSON.stringify(datas)),
          });
        }
      }
    }
    return list;
  }
  getDatasetModelIds() {
    const ids = [];
    const modelMap = this.getDataSetMap();
    if (modelMap) {
      for (let key in modelMap) {
        const model = modelMap[key];
        if (model.type === "model") {
          ids.push(model.modelId);
        }
      }
    }
    return ids;
  }
  /**##############################存储[数据模型]############################ */
  /**
   * dataset
   * @param {*} model
   */
  addData(model) {
    if (model && model.property && model.property.id) {
      if (!this.dataMap) {
        this.dataMap = {};
      }
      this.dataMap[model.property.id] = model;
    }
  }
  getDataMap() {
    if (this.dataMap) {
      return this.dataMap;
    }
  }
  getDataById(id) {
    const modelMap = this.getDataMap();
    if (id && modelMap && modelMap[id]) {
      return modelMap[id];
    }
  }
  delDataById(id) {
    const modelMap = this.getDataMap();
    if (id && modelMap && modelMap[id]) {
      delete modelMap[id];
    }
  }
  getDataPropertyList() {
    let list;
    const modelMap = this.getDataMap();
    if (modelMap) {
      for (let key in modelMap) {
        const model = modelMap[key];
        const property = model["property"];
        if (!list) {
          list = [];
        }
        list.push(property);
      }
      return list;
    }
  }
  /**##############################存储[字典模型]############################ */
  /**
   * dictSet
   * @param {*} model
   */
  addDict(model) {
    if (model && model.property && model.property.id) {
      if (!this.dictMap) {
        this.dictMap = {};
      }
      this.dictMap[model.property.id] = model;
    }
  }
  getDictMap() {
    if (this.dictMap) {
      return this.dictMap;
    }
  }
  getDictById(id) {
    const modelMap = this.getDictMap();
    if (id && modelMap && modelMap[id]) {
      return modelMap[id];
    }
  }
  delDictById(id) {
    const modelMap = this.getDictMap();
    if (id && modelMap && modelMap[id]) {
      delete modelMap[id];
    }
  }
  getDictPropertyList() {
    let list;
    const modelMap = this.getDictMap();
    if (modelMap) {
      for (let key in modelMap) {
        const model = modelMap[key];
        const property = model["property"];
        if (!list) {
          list = [];
        }
        list.push(property);
      }
      return list;
    }
  }

  /**##############################存储[动画模型]############################ */
  /**
   * AnimationSet
   * @param {*} model
   */
  addAnimation(model) {
    if (model && model.property && model.property.id) {
      if (!this.AnimationMap) {
        this.AnimationMap = {};
      }
      this.AnimationMap[model.property.id] = model;
    }
  }
  getAnimationMap() {
    if (this.AnimationMap) {
      return this.AnimationMap;
    }
  }
  getAnimationById(id) {
    const modelMap = this.getAnimationMap();
    if (id && modelMap && modelMap[id]) {
      return modelMap[id];
    }
  }
  delAnimationById(id) {
    const modelMap = this.getAnimationMap();
    if (id && modelMap && modelMap[id]) {
      modelMap[id].isPublish = false;
      delete modelMap[id];
    }
  }
  getAnimationPropertyList() {
    let list;
    const modelMap = this.getAnimationMap();
    if (modelMap) {
      for (let key in modelMap) {
        const model = modelMap[key];
        const property = model["property"];
        if (!list) {
          list = [];
        }
        list.push(property);
      }
      return list;
    }
  }
  /**
   * 获取已经发布的动画列表
   */
  getPublishedAnimations() {
    const modelMap = this.getAnimationMap();
    const map = {};
    if (modelMap) {
      for (let key in modelMap) {
        const model = modelMap[key];
        const property = model["property"];
        if (property.isPublish) {
          map[property.id] = property.name;
        }
      }
    }
    return map;
  }

  /**##############################存储[撤销重做]############################ */
  /**
   * 添加撤销动作
   * @param model 撤销数据 layer图层 prevAction旧数据 action新数据
   * @param isClean 是否清除重做
   */
  addUndo(model, isClean = false) {
    if (model && model.layer && model.prevAction && model.action) {
      // 本次操作新值和旧值是否有差异
      const isNewOldDiff =
        JSON.stringify(model.prevAction) !== JSON.stringify(model.action);

      if (this.undos && Array.isArray(this.undos) && this.undos.length) {
        const last = this.undos[this.undos.length - 1];
        // 上次操作的旧值和现在操作的旧值是否有差异
        const isLastDiff =
          JSON.stringify(last.prevAction) !== JSON.stringify(model.prevAction);
        if (isNewOldDiff && isLastDiff) {
          // 最多存储10次
          if (this.undos.length >= 10) {
            this.undos.shift();
          }
          this.undos.push(model);
        }
      } else {
        if (isNewOldDiff) {
          this.undos = [model];
        }
      }
      if (isClean) {
        this.redos = [];
      }
    }
  }
  getUndos() {
    if (this.undos && Array.isArray(this.undos)) {
      return this.undos;
    }
    return [];
  }
  addRedo(model) {
    if (model && model.layer && model.action) {
      if (this.redos && Array.isArray(this.redos)) {
        this.redos.push(model);
      } else {
        this.redos = [model];
      }
    }
  }
  getRedos() {
    if (this.redos && Array.isArray(this.redos)) {
      return this.redos;
    }
    return [];
  }

  /**
   * 删除图层的所有操作
   * @param layerId 图层id
   */
  delUndosById(layerId) {
    const undos = this.getUndos();
    if (undos && undos.length) {
      // 删除该图层的所有操作
      this.undos = undos.filter(
        (undo) =>
          undo.layer &&
          undo.layer.property &&
          undo.layer.property.id !== layerId
      );
    }
    // 清空重做
    this.redos = [];
  }
  /**##############################存储[图层(组件)模型]############################ */
  /**
   * Layer
   */
  addLayer(layer, list) {
    if (layer && layer.property && layer.property.id) {
      if (!this.layerTree) {
        this.layerTree = [];
      }
      this.parseLayerAdd(this.layerTree, layer);
    }
  }
  getLayerTree() {
    if (this.layerTree) {
      return this.layerTree;
    }
  }
  getLayerMap() {
    const layerMap = {};
    const layerTree = this.getLayerTree();
    if (layerTree) {
      this.parseLayerMap(layerTree, layerMap);
    }
    return layerMap;
  }
  getLayerById(id) {
    const layerMap = this.getLayerMap();
    if (id && layerMap && layerMap[id]) {
      return layerMap[id];
    }
  }
  delLayerById(id) {
    const layerTree = this.getLayerTree();
    if (layerTree && id) {
      this.parseLayerDel(layerTree, id);
    }
  }
  parseLayerDel(layers, id) {
    if (layers && layers.length) {
      for (let i = 0; i < layers.length; i++) {
        const layer = layers[i];
        if (id) {
          if (id === layer.property.id) {
            //删除Tree
            if (layers && layers.length) {
              remove(layers, layer);
            }
            //孩子
            const childs = layer["childs"];
            this.parseLayerDel(childs);
          } else {
            //孩子
            const childs = layer["childs"];
            this.parseLayerDel(childs, id);
          }
        } else {
          //删除Tree
          if (layers && layers.length) {
            remove(layers, layer);
          }
          //孩子
          const childs = layer["childs"];
          this.parseLayerDel(childs);
        }
      }
    }
  }
  parseLayerAdd(list, layer) {
    if (layer && layer.property && layer.property.id) {
      //判断parentId
      if (layer.property.parentId) {
        if (list && list.length) {
          for (let i = 0; i < list.length; i++) {
            //图层
            const item = list[i];
            let childs = item["childs"];
            if (layer.property.parentId === item.property.id) {
              //添加
              if (!childs) {
                childs = [layer];
              } else {
                childs.push(layer);
              }
            } else {
              //递归
              this.parseLayerAdd(childs, layer);
            }
          }
        }
      } else {
        if (!list) {
          list = [];
        }
        list.push(layer);
      }
    }
  }
  parseLayerMap(layers, layerMap) {
    if (layers && layers.length) {
      for (let i = 0; i < layers.length; i++) {
        const layer = layers[i];
        if (layer && layer.property && layer.property.id) {
          if (!layerMap) {
            layerMap = {};
          }
          layerMap[layer.property.id] = layer;
        }
        const childs = layer["childs"];
        this.parseLayerMap(childs, layerMap);
      }
    }
  }
  /**
   * 设置图层zIndex
   * @param {*} layers
   * @param {*} size
   * @param {*} multiple
   */
  refreshZIndex(list, size, coef) {
    const layers = list || this.getLayerTree();
    const maxLevel = size || 100;
    const multiple = coef || 10;
    if (layers && layers.length) {
      let index = layers.length;
      for (let i = 0; i < layers.length; i++) {
        const layer = layers[i];
        const zIndex = maxLevel + index * multiple;
        //计算zIndex
        index--;
        //刷新zIndex
        layer.refreshZIndex(zIndex);
        //孩子节点
        const childs = layer["childs"];
        if (childs && childs.length) {
          this.refreshZIndex(childs, zIndex, multiple);
        }
      }
    }
  }
  //当前图层
  getLayer() {
    if (this.currentLayer) {
      return this.currentLayer;
    }
  }
  /**##############################存储[选中的数据]############################ */
  /**
   * 添加-选中项
   * @param {*} property
   */
  addSelected(layer, event) {
    //清除选中-未按ctrl键
    if (!event || !event.ctrlKey) {
      this.clearSelected();
    }
    if (layer && layer.property) {
      //当前图层
      this.currentLayer = layer;
      //缓存
      const property = layer.property;
      const item = { id: property.id, top: property.top, left: property.left };
      //缓存选中
      if (!this.selectMap) {
        this.selectMap = {};
      }
      this.selectMap[item.id] = item;
      //缓存固定
      if (!this.selectFixed) {
        this.selectFixed = {};
      }
      this.selectFixed[item.id] = JSON.parse(JSON.stringify(item));
    }
  }

  /**
   * 选中所有第一级图层
   */
  selectAllLayers() {
    const layerTree = this.getLayerTree();
    if (layerTree && layerTree.length) {
      for (let i = 0; i < layerTree.length; i++) {
        const layer = layerTree[i];
        if (layer && layer.property) {
          const property = layer.property;
          const item = {
            id: property.id,
            top: property.top,
            left: property.left,
          };
          //缓存选中
          if (!this.selectMap) {
            this.selectMap = {};
          }
          this.selectMap[item.id] = item;
          //缓存固定
          if (!this.selectFixed) {
            this.selectFixed = {};
          }
          this.selectFixed[item.id] = JSON.parse(JSON.stringify(item));
        }
      }
    }
  }
  clearSelected() {
    if (this.currentLayer) {
      this.currentLayer = {};
    }
    if (this.selectMap) {
      this.selectMap = {};
    }
    if (this.selectFixed) {
      this.selectFixed = {};
    }
  }
  getSelectMap() {
    if (this.selectMap) {
      return this.selectMap;
    }
  }
  getSelectFixed() {
    if (this.selectFixed) {
      return this.selectFixed;
    }
  }
  isSelected(id) {
    let flag = false;
    if (id && this.selectMap && this.selectMap[id]) {
      flag = true;
    }
    return flag;
  }
  //覆盖
  coverSelectFixed() {
    if (this.selectFixed && this.selectMap) {
      this.selectFixed = JSON.parse(JSON.stringify(this.selectMap));
    }
  }
  /**
   * 刷新-所有图层选中项
   */
  refreshSelected() {
    const layerMap = this.getLayerMap();
    if (layerMap) {
      for (let key in layerMap) {
        const layer = layerMap[key];
        const property = layer.property;
        if (this.isSelected(property.id)) {
          layer.setChecked(true);
        } else {
          layer.setChecked(false);
        }
        if (layer.refreshChecked) {
          layer.refreshChecked();
        }
      }
    }
  }
  /**##############################鼠标############################ */
  /**
   * 是否鼠标点下,用于隔离键盘Ctrl
   */
  setIsMousedown(isMousedown) {
    this.isMousedown = isMousedown || false;
  }
  getIsMousedown() {
    return this.isMousedown;
  }
}
