import ToolPanel from "./tool/ToolPanel";
import MenuPanel from "./menu/MenuPanel";
import DrawPanel from "./draw/DrawPanel";
import PropertyPanel from "./property/PropertyPanel";
import Starter from "./Starter";
import Submit from "./Submit";
import { getLocalCopy, setLocalCopy } from "./utils/StorageUtil";
import { buildLayer, parseLayer } from "./layer/LayerUtil";
import { moveUp, moveDown, moveTop, moveBottom } from "./utils/MoveUtil";
import MaterialModel from "./model/MaterialModel";
import { undoLayer, redoLayer, alignLayers } from "./utils/TopToolUtil";
import globalLoadingManager from "./utils/GlobalLoadingManager";

export default class Designer {
  constructor(context, config) {
    this.context = context;
    this.context.setIsDesign(true);
    this.context.setDesigner(this);
    this.context.setHost(config);

    // 设置GlobalLoadingManager的Context
    globalLoadingManager.setContext(context);

    //刷新键盘事件
    // this.refreshKeydownEvent();
  }
  initPanel(parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="design-container">
        <div class="design-head"></div>
        <div class="design-body">
          <div class="design-menu"></div>
          <div class="design-draw"></div>
          <div class="design-property"></div>
        </div>
      </div>`);
      parentContainer.append(this.container);
      //工具
      const toolContainer = $(this.container).find(".design-head");
      this.toolPanel = new ToolPanel(this.context);
      this.toolPanel.initPanel(toolContainer);
      this.toolPanel.refreshPanel();

      //菜单
      const menuContainer = $(this.container).find(".design-menu");
      this.menuPanel = new MenuPanel(this.context);
      this.menuPanel.initPanel(menuContainer);
      this.menuPanel.refreshPanel();

      //绘画
      const drawContainer = $(this.container).find(".design-draw");
      this.drawPanel = new DrawPanel(this.context);
      this.drawPanel.initPanel(drawContainer);
      this.drawPanel.refreshPanel();

      //属性
      const propertyContainer = $(this.container).find(".design-property");
      this.propertyPanel = new PropertyPanel(this.context);
      this.propertyPanel.initPanel(propertyContainer);
      this.propertyPanel.refreshPanel();
    }
  }
  refreshPanel() {
    const self = this;
    const starter = new Starter(this.context);
    starter.start(function () {
      self.refreshRender();
    });
  }
  /**
   * 渲染
   */
  refreshRender() {
    const canvasContainer = this.getCanvasContainer();
    if (this.context && canvasContainer) {
      //缩放
      this.refreshScale();
      //背景
      this.refreshBackground();
      //图层
      const layers = this.context.getLayerTree();
      if (layers && layers.length) {
        for (let i = 0; i < layers.length; i++) {
          const layer = layers[i];
          const layerContainer = layer.getContainer();
          canvasContainer.append(layerContainer);
        }
      }
    }
  }
  /**
   * 绘画容器
   * @returns
   */
  getCanvasContainer() {
    if (this.drawPanel) {
      return this.drawPanel.getCanvasContainer();
    }
  }
  /**
   * 刷新缩放比例
   * @param {*} context
   * @param {*} container
   */
  refreshScale() {
    if (this.drawPanel) {
      this.drawPanel.refreshScale();
    }
  }
  /**
   * 刷新背景
   */
  refreshBackground() {
    if (this.drawPanel) {
      this.drawPanel.refreshBackground();
    }
  }
  /**
   * 联动面板-来源工具栏
   * @param {*} layer
   */
  linkByTool(layer) {
    if (layer) {
      //菜单面板
      if (this.menuPanel) {
        this.menuPanel.refreshLinkPanel();
      }
    }
  }
  /**
   * 联动面板-来源图层
   */
  linkByLayer(layer, event) {
    if (layer) {
      //添加选中缓存
      this.context.addSelected(layer, event);
      this.context.refreshSelected();
      //属性工具栏
      if (this.toolPanel) {
        this.toolPanel.refreshLinkPanel(layer);
      }
      //菜单面板
      if (this.menuPanel) {
        this.menuPanel.refreshLinkPanel();
      }
      //属性面板
      if (this.propertyPanel) {
        this.propertyPanel.refreshLinkPanel(layer);
      }
    }
  }
  //联动面板-来源菜单
  linkByMenu(layer) {
    //图层选中
    if (layer) {
      this.context.addSelected(layer);
      this.context.refreshSelected();
      // 聚焦画布
      this.focusLayer(layer);
    }
    //属性工具栏
    if (this.toolPanel) {
      this.toolPanel.refreshLinkPanel(layer);
    }
    //属性面板
    if (this.propertyPanel) {
      this.propertyPanel.refreshLinkPanel(layer);
    }
  }
  //联动面板-来源绘画
  linkByDraw() {
    //清除选中
    this.context.clearSelected();
    this.context.refreshSelected();
    //属性工具栏
    if (this.toolPanel) {
      this.toolPanel.refreshLinkPanel();
    }
    //菜单面板
    if (this.menuPanel) {
      this.menuPanel.refreshLinkPanel();
    }
    //属性面板
    if (this.propertyPanel) {
      this.propertyPanel.refreshLinkPanel();
    }
  }
  /**
   * 聚焦图层
   * @param layer 菜单选中的图层
   */
  focusLayer(layer) {
    // 当前选中的图层是否在视口可见，如果可见，则不做操作
    const isVisible = this.isElementVisible(layer.getContainer());
    if (isVisible) {
      return;
    }
    // 需要移动的距离 = 选中容器的偏移 - 画布的偏移
    const x =
      layer.getContainer().offset().left -
      this.getCanvasContainer().offset().left;
    const y =
      layer.getContainer().offset().top -
      this.getCanvasContainer().offset().top;
    this.scrollTo(x, y);
  }

  /**
   * 判断元素于当前视口是否可见
   * @param el jquery对象
   * @returns {boolean} 是否可见
   */
  isElementVisible(el) {
    if (el && el.get(0)) {
      const rect = el.get(0).getBoundingClientRect();
      // 屏幕宽度 - 左侧悬浮属性面板宽度
      const vWidth =
        Number(window.innerWidth || document.documentElement.clientWidth) - 320;
      // 屏幕高度 - 顶部工具栏高度
      const vHeight =
        Number(window.innerHeight || document.documentElement.clientHeight) -
        62;
      // return !(rect.right < 0 ||
      //   rect.bottom  < 0 ||
      //   rect.left > vWidth ||
      //   rect.top < 0 ||
      //   rect.top > vHeight);
      return (
        rect.left >= 0 &&
        rect.left <= vWidth &&
        rect.top >= 0 &&
        rect.top <= vHeight
      );
    }
    return false;
  }
  scrollTo(x = 0, y = 0) {
    if (this.drawPanel) {
      this.drawPanel.scrollTo(x, y);
    }
  }
  /**
   * 添加-图层
   * @param {*} canvasContainer
   * @param {*} property
   */
  addLayer(canvasContainer, property) {
    if (this.context && canvasContainer && property) {
      const layer = buildLayer(this.context, property, true);
      if (layer) {
        this.context.refreshZIndex();
        //刷新选中
        // this.context.addSelected(layer);
        // this.context.refreshSelected();
        this.linkByLayer(layer);
        //绑定图层容器
        const layerContainer = layer.getContainer();
        if (layerContainer) {
          canvasContainer.append(layerContainer);
        }
      }
    }
  }
  /**
   * 删除-图层
   * @param {*} layer
   */
  delLayer(layer) {
    //获取画布
    const canvasContainer = this.getCanvasContainer();
    if (this.context && canvasContainer && layer) {
      //获取图层Id
      if (layer.property && layer.property.id) {
        const layerId = layer.property.id;
        const layerContainer = $(canvasContainer).find("#" + layerId);
        if (layerContainer) {
          //删除渲染
          $(layerContainer).remove();
          //删除数据-当前
          this.context.delLayerById(layerId);
          // 删除图层所有撤销动作
          this.context.delUndosById(layerId);
          //删除数据-孩子
          // const self = this;
          // $(layerContainer).find('[id]').each(function (index, element) {
          //   const childId = this.id;
          //   self.context.delLayerById(childId);
          // });
        }
      }
    }
  }
  moveLayer(type, layer) {
    if (type && layer && layer.property) {
      let list;
      if (layer.property.parentId) {
        const groupLayer = this.context.getLayerById(layer.property.parentId);
        if (groupLayer && groupLayer.childs) {
          list = groupLayer.childs;
        }
      } else {
        list = this.context.getLayerTree();
      }
      if (list && list.length) {
        let index = list.indexOf(layer) || 0;
        switch (type) {
          case "top":
            moveTop(list, index);
            break;
          case "bottom":
            moveBottom(list, index);
            break;
          case "up":
            moveUp(list, index);
            break;
          case "down":
            moveDown(list, index);
            break;
          default:
            console.error("移动失败![不存在" + type + "类型]");
        }
        /**
         * 刷新ZIndex;
         */
        this.context.refreshZIndex();
      }
    }
  }
  /**
   * 拷贝-图层
   * @param {*} layer
   */
  copyLayer(layer) {
    const property = parseLayer(true, true, false, false, layer);
    if (property) {
      //添加图层
      const canvasContainer = this.getCanvasContainer();
      if (canvasContainer) {
        this.addLayer(canvasContainer, property);
      }
    }
  }
  /**
   * localCopy
   * @param {*} layer
   */
  localCopyLayer(layer) {
    let jsons = {};
    if (layer) {
      const property = parseLayer(true, false, false, false, layer);
      if (property) {
        jsons["property"] = property;
        const materialMap = {};
        const submit = new Submit(this.context);
        submit.parseMaterial(layer, materialMap);
        jsons["materialMap"] = materialMap;
        setLocalCopy(jsons);
      }
    }
  }
  /**
   * localPaste
   */
  localPasteLayer(layer) {
    const localCopy = getLocalCopy();
    const property = localCopy.property;
    const materialMap = localCopy.materialMap;
    //处理素材
    if (materialMap) {
      for (const key in materialMap) {
        const material = materialMap[key];
        const model = new MaterialModel(this.context);
        model.initProperty(material);
        this.context.addMaterial(model);
      }
    }
    //处理图层
    if (property) {
      let canvasContainer;
      if (
        layer &&
        layer.property &&
        layer.property.type &&
        layer.property.type === "GroupLayer"
      ) {
        property["parentId"] = layer.property.id;
        // property["left"] = 0;
        // property["top"] = 0;
        canvasContainer = layer.getContainer();
      } else {
        canvasContainer = this.getCanvasContainer();
      }
      if (canvasContainer) {
        property.left += 20;
        property.top += 20;
        //添加图层
        this.addLayer(canvasContainer, property);
      }
    }
  }
  /**
   * 撤销
   */
  undoLayer() {
    undoLayer(this.context);
  }

  /**
   * 重做
   */
  redoLayer() {
    redoLayer(this.context);
  }

  /*#############方向对齐相关###############*/
  /**
   * 方向对齐
   * @param position top middle bottom left center right
   */
  alignLayers(position) {
    alignLayers(this.context, position);
  }
  /**
   * 刷新键盘事件
   */
  // refreshKeydownEvent() {
  //   const self = this;
  //   window.addEventListener("keydown", (event) => {
  //     let key = event.key
  //     if (!self.context.getIsMousedown()) {
  //       const selectMap = self.context.getSelectMap();
  //       const selectFixed = self.context.getSelectFixed();
  //       const layerMap = self.context.getLayerMap();
  //       if (selectMap && selectFixed && layerMap) {
  //         for (let id in selectMap) {
  //           const select = selectMap[id];
  //           //刷新位置
  //           self.refreshPosition(select, key);
  //           //覆值
  //           const fixed = selectFixed[id];
  //           const layer = layerMap[id];
  //           if (fixed && layer && layer.property) {
  //             layer.property.top = select.top;
  //             layer.property.left = select.left;
  //             fixed.top = select.top;
  //             fixed.left = select.left;
  //             layer.refreshLayerCss();
  //           }
  //         }
  //       }
  //     }
  //   });
  // }
  // /**
  //  * 刷新位置
  //  * @param {*} select
  //  * @param {*} key
  //  */
  // refreshPosition(select, key) {
  //   if (select && key) {
  //     switch (key) {//方向键移动当前组件
  //       case 'ArrowDown':
  //         select.top += 1;
  //         break;
  //       case 'ArrowUp':
  //         select.top -= 1;
  //         break;
  //       case 'ArrowLeft':
  //         select.left -= 1
  //         break;
  //       case 'ArrowRight':
  //         select.left += 1
  //         break;
  //     }
  //   }
  // }
}
