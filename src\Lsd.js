import "./css/base.css";
import "./css/lsd.css";
import "./css/custom.css";
import "./css/animate.css";
import "./css/animate-extend.css";
import "./css/com.css";

import "./assets/font/fd-font.css";
import "./assets/font/icon-font.css";
import "./assets/font/lsd-font.css";
import "./assets/font/ft-font.css";

import Context from "./Context.js";
import Designer from "./Designer.js";
import Preview from "./Preview.js";

export function createDesigner(container,config){
  const context = Context.getInstance();
  const designer=new Designer(context,config);
  designer.initPanel(container);
  designer.refreshPanel();
  return designer;
}
window.createDesigner=createDesigner;

export function createPreview(container,config){
  const context = Context.getInstance();
  const preview=new Preview(context,config);
  preview.initPanel(container);
  preview.refreshPanel();
  preview.addListener();
  return preview;
}
window.createPreview=createPreview;