import Context from "./Context.js";
import Starter from "./Starter";
import { refreshBackground } from "./utils/StyleUtil";
import { receiverMessage } from "./utils/ListenerUtil";
import ToolPreview from "./tool/ToolPreview";
import globalLoadingManager from "./utils/GlobalLoadingManager";

export default class Preview {
  constructor(context, config) {
    this.context = context;
    this.context.setHost(config);

    // 设置GlobalLoadingManager的Context
    globalLoadingManager.setContext(context);
  }
  initPanel(parentContainer) {
    //初始化容器
    if (parentContainer) {
      this.container = $(`<div class="preview-container">
        <div class="preview-canvas"></div>
        <div class="preview-tool"></div>
      </div>`);
      parentContainer.append(this.container);
      this.canvasContainer = $(this.container).find(".preview-canvas");
      this.toolContainer = $(this.container).find(".preview-tool");
    }
  }
  refreshPanel(paramContext, paramData) {
    // if (this.container) {
    //   $(this.container).empty();
    // }
    const self = this;
    //加载数据
    const context = paramContext || this.context;
    const starter = new Starter(context);
    if (paramData) {
      starter.refreshQuery(paramData, function () {
        //加载图层渲染
        self.refreshCompOrTemp(context, paramData);
        self.refreshRender(context);
      });
    } else {
      starter.start(function () {
        self.refreshTool();
        //加载图层渲染
        self.refreshRender();
        //开启定时
        self.refreshInterval();
      });
    }
  }
  refreshTool(paramContext) {
    const context = paramContext || this.context;
    const toolContainer = this.toolContainer;
    if (toolContainer) {
      //清空
      $(toolContainer).empty();
      //添加工具栏
      const toolPreview = new ToolPreview(context);
      toolPreview.initProperty(context.getTool());
      toolPreview.initPanel(toolContainer);
      toolPreview.refreshPanel();
    }
  }
  /**
   * 渲染
   */
  refreshRender(paramContext) {
    const context = paramContext || this.context;
    const container = this.container;
    const canvasContainer = this.canvasContainer;
    if (context && container && canvasContainer) {
      //清空
      $(canvasContainer).empty();
      //缩放
      this.refreshScale(context, container, canvasContainer);
      //背景
      this.refreshBackground(context, canvasContainer);
      //图层
      const layers = context.getLayerTree();
      if (layers && layers.length) {
        for (let i = 0; i < layers.length; i++) {
          const layer = layers[i];
          const layerContainer = layer.getContainer();
          canvasContainer.append(layerContainer);
        }
      }
    }
  }
  refreshInterval(paramContext) {
    const context = paramContext || this.context;
    if (context) {
      const layerMap = context.getLayerMap();
      if (layerMap) {
        for (let key in layerMap) {
          const layer = layerMap[key];
          layer.refreshInterval();
        }
      }
    }
  }
  /**
   * 刷新缩放
   * @param {*} context
   * @param {*} container
   */
  refreshScale(paramContext, container, canvasContainer) {
    const context = paramContext || this.context;
    if (context && container && canvasContainer) {
      //比例
      const scale = context.getResize(container);
      //宽高
      const width = context.getWidth();
      const height = context.getHeight();
      $(canvasContainer).css("width", width);
      $(canvasContainer).css("height", height);
      $(canvasContainer).css("transform", "scale(" + scale + ")");
    }
  }
  refreshBackground(paramContext, canvasContainer) {
    const context = paramContext || this.context;
    if (context && canvasContainer) {
      refreshBackground(context, canvasContainer);
    }
  }

  /**
   * 添加监听事件
   */
  addListener() {
    receiverMessage((params) => {
      if (params && params.type && params.type === "tempLSD") {
        const data = params.data;
        const context = new Context();
        context.setHost(this.context.getHost());
        this.refreshPanel(context, data);
      }
    });
  }
  refreshCompOrTemp(paramContext, paramData) {
    const toolContainer = this.toolContainer;
    if (toolContainer) {
      //清空
      $(toolContainer).empty();
    }
    const context = paramContext || this.context;
    if (context && paramData) {
      context.setConfig("resizeType", "1");
      if (paramData["type"] === "1") {
        //组件
        //设置宽高
        const layers = context.getLayerTree();
        if (layers && layers.length) {
          const layer = layers[0];
          const width = layer.property.width;
          const height = layer.property.height;
          context.setConfig("width", width);
          context.setConfig("height", height);
        }
        //设置背景
        const bg = { bgType: "color", color: "rgb(43, 51, 64)" };
        context.setConfig("background", bg);
      }
    }
  }
}
