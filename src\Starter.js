import { start, end, setMsg } from "./assets/element/jquery/load/LoadUtil";
import ScreenService from "./service/ScreenService";
import { getUrlParams, getUrlParameter } from "./utils/Util";
import { isBase64, decodeBase64 } from "./utils/DecodeUtil";
import { setToken, setShareModel, getLocalCache } from "./utils/StorageUtil";
import { buildLayer } from "./layer/LayerUtil";

import MaterialModel from "./model/MaterialModel";
import DataModel from "./model/DataModel";
import DictModel from "./model/DictModel";
import ParamModel from "./model/ParamModel";
import Compat from "./Compat";
import AnimationModel from "./model/AnimationModel";
import DataSetModel from "./model/DataSetModel";
import {registryMessageCenter} from "./utils/ListenerUtil";

export default class Starter {
  constructor(context) {
    this.context = context;
    // this.designer = context.getDesigner();
  }
  start (callback) {
    const id = getUrlParams("id");
    //获取请求地址token信息
    const token = getUrlParams("token");
    if (token) {
      setToken(token);
    }
    //获取请求地址分享token信息
    const shareToken = getUrlParameter("shareToken");
    if (shareToken) {
      setShareModel(shareToken);
    }
    if (id) {
      this.queryData(id, callback);
    } else {
      this.queryLocal(callback);
    }
  }
  /**
   * 查询-数据
   * @param {*} id 
   * @param {*} callback 
   */
  queryData (id, callback) {
    const notLoad = getUrlParams("notLoad");
    start(notLoad);
    setMsg("加载开始!");
    const self = this;
    const screenService = new ScreenService(this.context);
    screenService.queryScreen({ id: id }, function (result) {
      self.refreshQuery(result, callback);
    }, function (error) {
      setMsg("加载结束![查询失败]");
      end();
      if (callback) callback();
    });
  }
  /**
   * 查询-本地
   * @param {*} callback 
   */
  queryLocal (callback) {
    const notLoad = getUrlParams("notLoad");
    start(notLoad);
    setMsg("加载开始!");
    const result = getLocalCache();
    this.refreshQuery(result, callback);
  }
  /**
   * 
   */
  refreshQuery (result, callback) {
    if (result) {
      // 1组件 2模板 3大屏
      const type = result.type;
      //title
      if (result["name"]) {
        const titleContainer = $("title");
        if (titleContainer) {
          $(titleContainer).text(result["name"]);
        }
      }
      //materials素材
      if (result["materials"]) {
        this.parseMaterial(result["materials"]);
      }
      // 大屏
      if (result["mapList"]) {
        this.parseMapList(result["mapList"]);
      }// 模板和组件
      else if (result["customMap"]) {
        // 模型和组件使用
        this.parseCustomMap(result["customMap"]);
      }
      //jsons
      if (result["jsons"]) {
        const jsons = this.parseJsons(result["jsons"]);
        this.refreshJson(jsons, callback);
        // 消息中心监听
        this.parseMessageCenter()
      } else {
        setMsg("加载结束![不存在Jsons]");
        end();
        if (callback) callback();
      }
    } else {
      setMsg("加载结束![不存在Jsons]");
      end();
      if (callback) callback();
    }
  }
  /**####################[数据解析]#################### */
  parseMapList (mapList) {
    if (mapList && mapList.length) {
      const idMap = {}; // 辅助映射，用于快速查找父节点
      const roots = [];  // 最外层节点（parentId === "0"）
      // 格式化数据源
      mapList = mapList.map(map => {
        return {
          id: map.id,
          name: map.name,
          level: map.levels,
          lng: Number(map.lng),
          lat: Number(map.lat),
          parentId: map.parentId,
          path: map.fileUrl,
          childs: {}
        }
      })
      // 初始化节点映射
      for (const item of mapList) {
        // 使用 id 作为键映射到节点
        idMap[item.id] = item;
        if (!item.parentId) {
          roots.push(item); // 找到根节点
        }
      }
      // 构建树结构
      for (const item of mapList) {
        if (item.parentId && idMap[item.parentId]) {
          // 将当前节点添加到其父节点的 childs 对象中
          idMap[item.parentId].childs[item.id] = item;
        }
      }
      const customMap = {};
      for (const root of roots) {
        customMap[root.id] = root;
      }
      this.context.addCustomMap(customMap)
    }
  }
  parseCustomMap (customMap) {
    if (customMap) {
      this.context.addCustomMap(customMap)
    }
  }
  parseMaterial (list) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const model = new MaterialModel(this.context);
        model.initProperty(item);
        this.context.addMaterial(model);
      }
    }
  }
  parseDataModel (list) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const model = new DataModel(this.context);
        model.initProperty(item);
        this.context.addData(model);
      }
    }
  }
  parseDataSetModel (list) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const model = new DataSetModel(this.context);
        model.initProperty(item);
        this.context.addDataSet(model);
      }
    }
  }
  parseMessageCenter () {
    registryMessageCenter(this.context)
  }
  parseDataDict (list) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const model = new DictModel(this.context);
        model.initProperty(item);
        this.context.addDict(model);
      }
    }
  }
  parseParam (list) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const model = new ParamModel(this.context);
        model.initProperty(item);
        this.context.addParam(model);
      }
    }
  }
  parseAnimation (animations) {
    if (animations && animations.length) {
      for(let i = 0; i < animations.length; i++) {
        const animation = animations[i];
        const model = new AnimationModel(this.context);
        model.initProperty(animation);
        // 发布自定义动画到style标签
        model.publishAnimation();
        this.context.addAnimation(model)
      }
    }
  }
  parseLayer (list) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const layer = buildLayer(this.context, item, false);
      }
    }
  }
  parseJsons (jsonStr) {
    if (jsonStr) {
      if (isBase64(jsonStr)) {
        jsonStr = decodeBase64(jsonStr);
      }
      try {
        jsonStr = JSON.parse(jsonStr)
      } catch (e) {
        console.error(e)
      }
      return jsonStr;
    }
  }
  /**#################[刷新]################## */
  refreshJson (json, callback) {
    //判断Json是否存在 不存在(加载:结束)
    if (!json) {
      setMsg("加载结束![Json不存在]");
      end();
      if (callback) callback();
    } else {
      //兼容
      const compat = new Compat(this.context);
      compat.start(json);
      //1.刷新上下文
      this.refreshContext(json);
      //2.刷新数据
      this.refreshData(callback);
    }
  }
  /**##################[刷新上下文]################# */
  /**
   * 刷新上下文配置(填充 fillContext)
   * @param {*} json 
   */
  refreshContext (json) {
    if (json) {
      console.log('json', json)
      for (let key in json) {
        if (key === "models") {
          this.parseDataModel(json[key]);
          delete json[key];
          continue;
        }
        if (key === "dicts") {
          this.parseDataDict(json[key]);
          delete json[key];
          continue;
        }
        //兼容结束
        if (key === "layers") {
          this.parseLayer(json[key]);
          continue;
        }
        if (key === "params") {
          this.parseParam(json[key]);
          continue;
        }
        if (key === "animations") {
          this.parseAnimation(json[key]);
        }
        if (key === "datasets") {
          this.parseDataSetModel(json[key])
        }
        this.context.setConfig(key, json[key]);
      }
    }
  }
  /**##################[刷新数据]################# */
  /**
   * 
   * @param {*} callback 
   */
  refreshData (callback) {
    const self = this;
    //刷新数据-参数
    this.refreshDataByParam();
    //刷新数据-开始(初始化)
    this.refreshDatasetByStart(function () {
      if (self.isLoadedDatasetByStart()) {
        const layerTree = self.context.getLayerTree();

        const layerMap = self.getFilterLayerMap(layerTree);
        self.refreshDataByLayers(layerMap.bindLayers, function () {
          if (self.isLoadedByLayer(layerMap.bindLayerMap)) {
            //非绑定请求图层
            self.refreshDataByLayers(layerMap.notBindLayers, function () {
              if (self.isLoadedByLayer(layerMap.notBindLayerMap)) {
                //加载结束
                setMsg("加载结束!");
                end();
                if (callback) {
                  callback();
                }
              }
            });
          }
        });
      }
    });
    // setMsg("加载结束!");
    // end();
    // if (callback) {
    //   callback();
    // }
  }
  refreshDataByParam () {
    const paramMap = this.context.getParamMap();
    if (paramMap) {
      for (let key in paramMap) {
        const param = paramMap[key];
        param.refreshData();
      }
    }
  }
  refreshDatasetByStart (callback) {
    const datasetMap = this.context.getDataSetMap();
    if (datasetMap) {
      for (let key in datasetMap) {
        const dataset = datasetMap[key];
        dataset.refreshData(function () {
          callback && callback();
        });
      }
    } else {
      callback && callback();
    }
  }
  isLoadedDatasetByStart () {
    let flag = true;
    const datasetMap = this.context.getDataSetMap();
    if (!this.isLoaded(datasetMap)) {
      flag = false;
    }
    return flag;
  }
  refreshDataByStart (callback) {
    const self = this;
    const dataMap = this.context.getDataMap();
    const dictMap = this.context.getDictMap();
    if (dataMap || dictMap) {
      if (dataMap) {
        for (let key1 in dataMap) {
          const model = dataMap[key1];
          model.refreshData(function () {
            if (callback) {
              callback();
            }
          });
        }
      }
      if (dictMap) {
        for (let key2 in dictMap) {
          const model = dictMap[key2];
          model.refreshData(function () {
            if (callback) {
              callback();
            }
          });
        }
      }
    } else {
      if (callback) {
        callback();
      }
    }
  }
  refreshDataByLayers (layers, callback) {
    //执行数据请求
    if (layers && layers.length) {
      for (let i = 0; i < layers.length; i++) {
        const layer = layers[i];
        layer.refreshData(callback);
      }
    } else {
      if (callback) {
        callback();
      }
    }
  }
  // refreshDataByLayers (callback) {
  //   const layers = this.context.getLayerTree();
  //   if (layers && layers.length) {
  //     for (let i = 0; i < layers.length; i++) {
  //       const layer = layers[i];
  //       this.refreshDataByLayer(layer, callback);
  //     }
  //   } else {
  //     if (callback) {
  //       callback();
  //     }
  //   }
  // }
  // refreshDataByLayer (layer, callback) {
  //   if (layer) {
  //     const childs = layer["childs"];
  //     if (childs && childs.length) {
  //       for (let i = 0; i < childs.length; i++) {
  //         const child = childs[i];
  //         this.refreshDataByLayer(child, callback);
  //       }
  //     }
  //     layer.refreshData(function () {
  //       if (callback) {
  //         callback();
  //       }
  //     });
  //   }
  // }
  isLoadedByStart () {
    let flag = true;
    const dataMap = this.context.getDataMap();
    const dictMap = this.context.getDictMap();
    if (!this.isLoaded(dataMap) || !this.isLoaded(dictMap)) {
      flag = false;
    }
    return flag;
  }
  isLoadedByLayer (layerMap) {
    let flag = true;
    if (layerMap) {
      flag = this.isLoaded(layerMap);
    }
    return flag;
  }
  // isLoadedByLayer () {
  //   let flag = true;
  //   const layerMap = this.context.getLayerMap();
  //   flag = this.isLoaded(layerMap);
  //   return flag;
  // }
  /**
   * 是否全部加载结束
   * @param {*} loadMap 
   * @returns 
   */
  isLoaded (loadMap) {
    let flag = true;
    if (loadMap) {
      for (let key in loadMap) {
        const obj = loadMap[key];
        if (obj.$$isLoading) {
          flag = false;
        }
      }
    }
    return flag;
  }

  getFilterLayerMap(layers, map) {
    layers = layers || [];
    map = map || { bindLayers: [], notBindLayers: [], bindLayerMap: {}, notBindLayerMap: {} };

    for (let i = 0; i < layers.length; i++) {
      const layer = layers[i];
      const property = layer.property;
      const bind = property.bind;

      // 先处理有子节点的情况
      if (layer.childs && layer.childs.length) {
        this.getFilterLayerMap(layer.childs, map);
      }

      const isDataBind = bind && bind.bindType === "data";
      // 将绑定类型为 data 的放入绑定层，不是则放入非绑定层
      if (isDataBind) {
        map.bindLayers.push(layer);
        map.bindLayerMap[property.id] = layer;
      } else {
        map.notBindLayers.push(layer);
        map.notBindLayerMap[property.id] = layer;
      }
    }
    return map;
  }
}