import { alert, warn, error } from './assets/element/jquery/msg/MsgUtil';
import { getUrlParams, getHttpHost, getAppCode, toList } from "./utils/Util";
import { encodeBase64 } from "./utils/DecodeUtil";
import { setLocalCache } from "./utils/StorageUtil";
import { buildLayer, parseLayer } from "./layer/LayerUtil";
import ResourcesService from './service/ResourcesService';
import ScreenService from "./service/ScreenService";
export default class Submit {
  constructor(context) {
    this.context = context;
    this.designer = context.getDesigner();
  }
  static getInstance (context) {
    if (!this.instance) {
      this.instance = new Submit(context);
    }
    return this.instance;
  }
  /**
   * 预览
   */
  preview () {
    //http://************:8082/designer/lsd/preview.html?id=22dbf2742ddd701d84f7e3277eddcb7d
    // let url = getHttpHost() + "/" + getAppCode() + "/lsd-designer/preview.html";
    let url = "./preview.html";
    const id = getUrlParams("id");
    if (id) {
      url += "?";
      url += "id=" + id;
    }
    window.open(url, "_blank");
  }
  /**
   * 保存
   */
  save () {
    const id = getUrlParams("id");
    const json = this.getJsonScreen();
    if (id) {
      this.saveData(id, json);
    } else {
      this.saveLocal(json);
    }
  }
  saveData (id, params) {
    if (this.context && this.designer && id && params) {
      const self = this;
      //需要截取Div
      const canvasContainer = this.designer.getCanvasContainer();
      if (canvasContainer) {
        const canvasElement = canvasContainer[0];
        this.convertBase64Img(canvasElement, function (base64) {
          params["id"] = id;
          params["image"] = base64;
          params["type"] = '3';
          const screenService = new ScreenService(self.context);
          screenService.saveScreen(params, function (result) {
            alert("保存成功!")
          });
        });
      }
    }
  }
  saveLocal (json) {
    if (json) {
      setLocalCache(json);
    }
  }
  /**
   * 保存-模板
   * @param {*} item 
   */
  saveTemp (item, callback) {

    if (item) {
      const params = item.screen;
      for (let key in item) {
        if (key === "dirId") {
          params[key] = item[key];
        }
        if (key === "dirName") {
          params[key] = item[key];
        }
        if (key === "tagIds") {
          params["labelId"] = item[key];
        }
      }
      //截取图片
      const self = this;
      const iframeContainer = item.iframeContainer;
      if (iframeContainer) {
        const iframeBody = iframeContainer[0].contentDocument.body;
        const previewContainer = $(iframeBody).find(".preview-container");
        const canvasElement = previewContainer[0];
        this.convertBase64Img(canvasElement, function (base64) {
          params["image"] = base64;
          if (callback) {
            callback();
          }
          const screenService = new ScreenService(self.context);
          screenService.saveScreen(params, function (result) {
            alert("保存成功!");
          });
        });
      } else {
        alert("保存失败!");
        if (callback) {
          callback();
        }
      }
    }
  }

  /**
   * 保存分享
   * @param {*} item 
   * @returns 
   */
  saveShare (item) {
    const self = this;
    if (item) {
      if (!item.datefilter.realDate.initDate) {
        error('请选择有效期');
        return
      }
      if (item.isEnable) {
        item.isEnable = "1";
      } else {
        item.isEnable = "0";
      }
      if (item.isAccessLimit) {
        item.isAccessLimit = "1";
      } else {
        item.isAccessLimit = "0";
      }
      if (item.isVerifyDate) {
        item.isVerifyDate = "1";
      } else {
        item.isVerifyDate = "0";
      }
      let params = {
        businessId: item.id,
        endTime: item.datefilter.realDate.initDate,
        code: "DEV_LSD",
        uid: item.uuid,
        startTime: "1998-02-21",
        isEnable: item.isEnable,
        isAccessLimit: item.isAccessLimit,
        isVerifyDate: item.isVerifyDate,
        accessType: item.accessType,
        accessPassword: item.accessPassword,
        accessVerifyToken: item.accessVerifyToken,
      };
      const resourcesService = new ResourcesService(self.context);
      resourcesService.save(params, function (result) {
        alert("保存成功!");
      });
    }
  }
  /**
   * 获取大屏对象
   * @param {*} layer 
   * @returns 
   */
  getJsonScreen (isRepl, type, layer) {
    //jsons字符串
    let jsonExpr;
    //素材（平台数据）
    let materialIds;
    // 数据集文件类型数据
    let fileDatas;
    // 数据集其他数据
    let datasetDatas;
    // 模型id数组
    let dataModelIds;
    // 自定义地图
    let customMapIds;
    let customMap;
    if (type) {
      if (type === "1") {
        //组件(脱敏)静态数据
        jsonExpr = this.getLayerExpr(isRepl, layer);
        const materialMap = {}
        this.parseMaterial(layer, materialMap);
        materialIds = Object.keys(materialMap);
        customMap = this.context.getCustomMap();
      } else {
        //模板(脱敏)静态数据
        jsonExpr = this.getJsonExpr(isRepl);
        materialIds = this.context.getMaterialIds();
        customMap = this.context.getCustomMap();
      }
    } else {
      //大屏
      jsonExpr = this.getJsonExpr(isRepl);
      // materials = this.context.getMaterialPropertyList() || [];
      // dataModels = this.context.getDataPropertyList() || [];
      // dataDicts = this.context.getDictPropertyList() || [];
      materialIds = this.context.getMaterialIds()
      fileDatas = this.context.getDatasetFileDatas();
      dataModelIds = this.context.getDatasetModelIds();
      customMapIds = this.context.getUseCustomMapIds();
      console.log('fileDatas', fileDatas)
      console.log('materialIds', materialIds)
      console.log('dataModelIds', dataModelIds)
      console.log('customMapIds', customMapIds)
    }
    //屏对象
    const screen = {
      status: 1,
      jsons: jsonExpr,
      materialIds: materialIds,
      customMapIds: customMapIds,
      customMap: customMap,
      fileDatas: fileDatas,
      dataModelIds: dataModelIds
    };
    return screen;
  }
  /**
   * Json表达式
   * @param {*} isReplace 是否替换数据
   * @returns 
   */
  getJsonExpr (isReplace) {
    const report = {};
    //配置
    const config = this.context.getConfig();
    if (config) {
      // const coupyJson = JSON.parse(JSON.stringify(config));
      $.extend(report, config);
      if (report["host"]) {
        delete report["host"];
      }
    }
    //图层
    const layers = this.context.getLayerTree();
    if (layers && layers.length) {
      for (let i = 0; i < layers.length; i++) {
        const layer = layers[i];
        const property = parseLayer(false, false, false, isReplace, layer);
        if (!report["layers"]) {
          report["layers"] = [];
        }
        report["layers"].push(property);
      }
    }
    //参数
    const params = this.context.getParamPropertyList();
    if (params) {
      report["params"] = params;
    }
    // 自定义动画
    const animations = this.context.getAnimationPropertyList();
    if (animations) {
      report['animations'] = animations;
    }
    // //数据集（平台数据）
    const datasets = this.context.getDataSetPropertyList() || [];
    if (datasets) {
      report["datasets"] = datasets;
    }
    // //字典集（平台数据）
    // const dataDicts = this.context.getDictPropertyList() || [];
    // if (dataDicts) {
    //   report["dicts"] = dataDicts;
    // }
    //加密Base64
    const exprStr = encodeBase64(JSON.stringify(report));

    return exprStr;
  }

  /**
   * 获取图层Json表达式
   * @param {*} isReplace 是否替换数据
   * @returns 
   */
  getLayerExpr (isReplace, layer) {
    const report = {};
    const property = parseLayer(true, false, false, isReplace, layer);
    if (property) {
      property["left"] = 0;
      property["top"] = 0;
      report["layers"] = [property];
    }
    const exprStr = encodeBase64(JSON.stringify(report));
    return exprStr;
  }

  /**
   * 转换base64图片
   * @param {*} element 
   * @param {*} callback 
   */
  convertBase64Img (element, callback) {
    if (element) {
      const rect = element.getBoundingClientRect();
      // 生成图片并上传到数据库保存
      html2canvas(element, {
        scale: 2, // 数值越大生成的图片越清晰
        useCORS: true,
        allowTaint: true,
        y: 0, // 滚动条高度修复
        x: 0,
        scrollX: 0,
        scrollY: rect.top, // 关键代码
        height: rect.height, // 加高度，避免截取不全
      }).then((canvas) => {
        let base64 = canvas.toDataURL("image/jpeg"); // 拿到数据流
        if (callback) {
          callback(base64);
        }
      })
    }
  }
  /**
   * 处理素材（过滤掉相同的素材）
   * @param {*} layer 
   * @param {*} map 
   */
  parseMaterial (layer, map) {
    if (layer && map) {
      const list = layer.getMaterials();
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          if (map && item && item.id) {
            map[item.id] = item;
          }
        }
      }
      const childs = layer.childs;
      if (childs && childs.length) {
        for (let i = 0; i < childs.length; i++) {
          const child = childs[i];
          this.parseMaterial(child, map);
        }
      }
    }
  }
}