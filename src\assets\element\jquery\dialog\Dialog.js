import "./css/dialog.css"
export default class Dialog {
  constructor() {
    this.initPanel();
    this.index = 0;
  }
  static getInstance () {
    if (!this.instance) {
      this.instance = new Dialog();
    }
    return this.instance;
  }
  static closeAllModel () {
    $(".dialog-container").empty();
  }
  /**
   * 初始化属性
   * @param {*} property 
   */
  initProperty (property) {
    this.property = {
      id: "dialog-model-default",
      title: "标题",
      maxHeight:($(window).height() - 100),
      width: "100%",
      closeText: "&times;",
      button: {
        close: { text: "取消", click: function () { } },
        submit: { text: "确定", click: function () { } },
      }
    };
    if (property) {
      for (let key in property) {
        this.property[key] = property[key];
      }
    }
  }
  initPanel () {
    const container = $("body").find(".dialog-container");
    if (container && container.length) {
      this.container = container;
    } else {
      this.container = $(`<div class="dialog-container"></div>`);
      $("body").append(this.container);
    }
  }
  // refreshPanel (property) {
  //   if (this.container) {
  //     $(this.container).empty();
  //     this.addModel(property);
  //   }
  // }
  addModel (contentContainer, property) {
    const self = this;
    //初始化属性
    this.initProperty(property);
    //
    this.index++;
    const model = this.property;
    if (this.container && model) {
      if (model["id"]) {
        model["id"] = model["id"] + "-" + this.index;
      } else {
        model["id"] = "dialog-model-default-" + this.index;
      }
      // this.container.fadeIn();
      // this.container.show();
      const modelContainer = $(`<div class="dialog-mask">
        <div class="dialog-model">
          <div class="dialog-wrap">
            <div class="dialog-head">
              <div class="head-title" style="display: inline-block;width: 80%;">标题</div>
              <div class="head-close" style="display: inline-block;float: right;cursor: pointer;">&times</div>
            </div>
            <div class="dialog-body"></div>
            <div class="dialog-foot">
              <div class="foot-btn">确定</div>
              <div class="foot-btn">取消</div>
            </div>
          </div>
        </div>
      </div>`);
      this.container.append(modelContainer);

      //添加Id
      if (model["id"]) {
        $(modelContainer).attr("id", model["id"]);
      }
      //设置宽高
      const wrapContainer = $(modelContainer).find(".dialog-wrap");
      if (wrapContainer && model["width"]) {
        $(wrapContainer).css("width", model["width"]);
      }
      if (wrapContainer && model["height"]) {
        $(wrapContainer).css("height", model["height"]);
      }
      if (wrapContainer && model["maxHeight"]) {
        $(wrapContainer).css("max-height", model["maxHeight"]);
      }
      //head-标题
      const titleContainer = $(modelContainer).find(".head-title");
      if (titleContainer && model["title"]) {
        $(titleContainer).text(model["title"]);
      }
      //head-关闭
      const closeContainer = $(modelContainer).find(".head-close");
      if (closeContainer) {
        //文本
        if (model["closeText"]) {
          $(closeContainer).html(model["closeText"]);
        }
        //点击
        $(closeContainer).on("click", function (event) {
          self.delModel(model["id"]);
        });
      }
      //body
      const bodyContainer = $(modelContainer).find(".dialog-body");
      if (bodyContainer && contentContainer) {
        bodyContainer.append(contentContainer);
      }
      //foot
      const footContainer = $(modelContainer).find(".dialog-foot");
      if (footContainer) {
        $(footContainer).empty();
        if (model["button"]) {
          for (let key in model["button"]) {
            const btn = model["button"][key];
            console.log(model["button"][key]);
            const btnContainer = $(`<div class="foot-btn ${btn.className}">` + btn.text + `</div>`);
            footContainer.append(btnContainer);
            if (key === "submit" || key === "close") {
              $(btnContainer).on("click", function (event) {
                self.delModel(model["id"]);
                if (btn.click) {
                  btn.click();
                }
              });
            } else {
              $(btnContainer).on("click", function (event) {
                if (btn.click) {
                  btn.click(model["id"]);
                }
              })
            }
          }
        }
      }
    }
  }
  delModel (id) {
    if (this.container && id) {
      const modelContainer = $(this.container).find("#" + id);
      if (modelContainer && modelContainer.length) {
        modelContainer.remove();
      }
    }
    // if (this.container) {
    //   $(this.container).fadeOut();
    //   // $(this.container).hide();
    //   $(this.container).empty();

    //   // const model = this.property;
    //   // if (model && model["id"]) {
    //   //   const modelContainer = $(this.container).find("#" + model["id"]);
    //   //   modelContainer.remove();
    //   //   $(this.container).fadeOut();
    //   // } else {
    //   //   $(this.container).empty();
    //   //   $(this.container).fadeOut();
    //   // }
    // }
  }

  /**
   * 添加信息提示dom
   * @param containerId 当前窗口的主容器ID
   * @param alertCallback 回调，返回一个容器
   */
  addAlert (containerId, alertCallback) {
    if (!containerId) {
      return;
    }
    const alertModel = $(`<div class="dialog-alert">
            <span class="dialog-alert-icon ft-font icon-_alert_icon"></span>
            <span class="dialog-alert-tip">已选：</span>
            <span class="dialog-alert-tags"></span>
       </div>`)
    const container = $(`#${containerId}`);
    container.find('.dialog-model .dialog-head').after(alertModel);
    const alertContainer = container.find(".dialog-alert");
    const tagsContainer = $(alertContainer).find('.dialog-alert-tags');
    alertCallback && alertCallback(tagsContainer);
  }
}