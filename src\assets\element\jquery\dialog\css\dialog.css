/* .dialog-container {} */
.dialog-mask {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 999;
  background: rgba(0, 0, 0, 0.8);
}

.dialog-model {
  position: absolute;
  top: 50%;
  left: 50%;
  background-color: #141414;
  border-radius: 8px 8px 8px 8px;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
}

.dialog-model .dialog-wrap {
  position: relative;
  /* width: 400px;
  height: 500px; */
  /* min-height: 580px; */
  display: flex;
  flex-direction: column;
}

.dialog-wrap .dialog-head {
  padding: 20px 20px 10px 20px;
}

.dialog-wrap .dialog-head .head-title {
  font-size: 18px;
  color: #E5EAF3;
}

.dialog-wrap .dialog-head .head-close {
  font-size: 20px;
  color: #CFD3DC;
}

.dialog-wrap .dialog-alert {
  padding: 10px 16px;
  margin: 20px 20px 0 20px;
  display: flex;
  align-items: center;
  background: #18222C;
  border-radius: 4px;
  word-break: break-all;
}
.dialog-wrap .dialog-alert span {
  font-weight: 400;
  font-size: 13px;
  color: #CFD3DC;
}
.dialog-wrap .dialog-alert .dialog-alert-tip {
  word-break: keep-all;
}
.dialog-wrap .dialog-alert .dialog-alert-icon {
  font-size: 14px;
  color: #409eff;
  margin-right: 9px;
}

.dialog-wrap .dialog-body {
  padding: 10px 8px;
  /* height: calc(100% - 120px); */
  height: 100%;
  overflow: auto;
}

.dialog-wrap .dialog-foot {
  padding: 10px 20px 20px 20px;
}

.dialog-foot .foot-btn {
  height: 32px;
  padding: 6px 16px;
  background: #000000;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #4C4D4F;
  font-weight: 600;
  font-size: 14px;
  color: #CFD3DC;
  float: right;
  margin: 1px 2px;
  text-align: center;
  cursor: pointer;
}

.dialog-foot .foot-save {
  margin-left: 12px;
  background: #409EFF;
}

/* 配置-图片列表 */
.com-config-image {
  height: 132px;
  margin-left: 157px;
  margin-bottom: 22px;
  display: flex;
  flex-wrap: wrap;
  gap: 26px;
  border: 1px solid #4C4D4F;
  border-radius: 8px 8px 8px 8px;
  padding: 16px;
  overflow: auto;
  box-sizing: border-box;
}

.com-config-image .image-item {
  box-sizing: border-box;
  position: relative;
}

.com-config-image .image-item .item-block {
  max-width: 100px;
  min-width: 100px;
  height: 100px;
  text-align: center;
  background-color: #3F4B5F;
}

.com-config-image .image-item .item-img {
  height: 100%;
  width: 100%;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #4C4D4F;
}

.com-config-image .item-triangle {
  width: 0;
  height: 0;
  border-top: 16px solid transparent;
  border-bottom: 16px solid transparent;
  border-left: 16px solid #67C23A;
  transform: rotateZ(-45deg);
  position: absolute;
  top: -9px;
  right: -2px;
  display: none;
}

.com-config-image .item-icon {
  color: #fff;
  transform: rotateZ(45deg);
  position: absolute;
  top: -5px;
  right: 4px;
  font-size: 12px;
  font-weight: bold;
}

.com-config-image .image-item .item-title {
  line-height: 30px;
  font-size: 13px;
  color: #ccc;
  /* text-align: center; */
}

.com-config-image .image-item:hover {
  border: 1px solid #409EFF;

}


/* 多选选中列表 */
.selected-list {
  height: 50px;
  background-color: #2195f389;
}