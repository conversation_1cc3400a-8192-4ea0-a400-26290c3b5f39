import Loading from "./Loading";
/**
 * 打开加载
 * @returns {jQuery|HTMLElement}
 */
export function start (notLoad) {
  // 只要notLoad有值，就不出现loading
  if (notLoad) {
    return null;
  }
  const loading=Loading.getInstance();
  return loading.addModel();
}
/**
* 描述:关闭加载
* @param container
*/
export function end (container) {
  const loading=Loading.getInstance();
  loading.delModel(container);
}
/**
* 描述:刷新加载的数据信息
* @param container
*/
export function setMsg (msg, container) {
  const loading=Loading.getInstance();
  loading.addText(container,msg);
}