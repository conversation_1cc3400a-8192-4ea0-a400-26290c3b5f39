import "./css/load.css";

export default class Loading {
  constructor() {
    this.initPanel();
  }
  static getInstance () {
    if (!this.instance) {
      this.instance = new Loading();
    }
    return this.instance;
  }
  /**
   * 初始化属性
   * @param {*} property 
   */
  initProperty (property) {
    this.property = {};
    if (property) {
      for (let key in property) {
        this.property[key] = property[key];
      }
    }
  }
  refreshProperty (modelContainer) {
    if (modelContainer && this.property) {
      if (this.property.id) {
        $(modelContainer).attr("id", this.property.id);
      }
      $(modelContainer).css(this.property);
    }
  }
  initPanel () {
    const container = $("body").find(".loading-container");
    if (container && container.length) {
      this.container = container;
    } else {
      this.container = $(`<div class="loading-container"></div>`);
      $("body").append(this.container);
    }
    this.refreshShow(false);
  }
  // refreshPanel (property) {
  //   if (property) {
  //     this.initProperty(property);
  //   }
  //   if (this.container) {
  //     $(this.container).empty();
  //     this.addModel(this.container);
  //   }
  // }
  addModel () {
    const container = this.container;
    if (container) {
      const modelContainer = $(`<div class='loading-wrap'>
          <div class='loading-body'>
              <div class="loading-image">
                <img src="`+ require('../../../loading.png') + `"/>
              </div>
              <div class="loading-text" >加载中...</div>
          </div>
      </div>`);
      container.append(modelContainer);
      //刷新属性
      this.refreshProperty(modelContainer);
      //刷新显示
      this.refreshShow(true);
      return modelContainer;
    }
  }
  delModel (modelContainer) {
    if (this.container) {
      if (modelContainer) {
        $(modelContainer).remove();
      } else {
        $(this.container).empty();
      }
      this.refreshShow(false);
    }
  }
  addText (modelContainer, text) {
    const container = modelContainer || this.container;
    if (container && text) {
      const textContainer = $(container).find(".loading-text");
      $(textContainer).text(text);
    }
  }

  refreshShow (isShow) {
    if (this.container) {
      if (isShow) {
        $(this.container).show();
      } else {
        $(this.container).hide();
      }
    }
  }
}