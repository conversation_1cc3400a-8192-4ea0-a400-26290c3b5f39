/*==加载======*/
.loading-container {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background-color: rgba(44, 62, 80, 0.5);
}

.loading-wrap {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
}

.loading-wrap .loading-body {
  position: relative;
  padding: 10px;
  /* background-color: #3a3f51; */
  border-radius: 5px;
  color: #fff;
}

.loading-body .loading-image {
  display: flex;
  width: 100%;
  height: 40px;
  align-items: center;
  justify-content: center;
}

.loading-body .loading-image img {
  width: 35px;
  height: 35px;
  margin: 0 auto;
  background-color: transparent;
}

.loading-body .loading-text {
  display: flex;
  line-height: 30px;
  width: 100%;
  align-items: center;
  justify-content: center;
}

.loading-model {
  width: 100%;
  height: 100%;
  background-color: rgba(44, 62, 80, 0.2);
}