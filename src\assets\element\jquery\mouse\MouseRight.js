import "./css/mouse.css"
export default class MouseRight {
  constructor() {

  }
  static getInstance () {
    if (!this.instance) {
      this.instance = new MouseRight();
    }
    return this.instance;
  }
  /**
   * 初始化属性
   * @param {*} property 
   */
  initProperty (property) {
    this.property = {
      id: "mouse-model-default",
      left: 0, top: 0, width: 150, //height: "300",
      color: "#ccc",
      background: "rgba(43, 51, 64, 0.8)",
      boxShadow: "5px 5px 10px #1c2129cc",
      cursor: "pointer",
      //display: 'block',//block none
      // item:{}
    };
    if (property) {
      for (let key in property) {
        this.property[key] = property[key];
      }
    }
  }
  initPanel (property) {
    const container = $("body").find(".mouse-container");
    if (container && container.length) {
      this.container = container;
    } else {
      this.container = $(`<div class="mouse-container"></div>`);
      $("body").append(this.container);
    }
    //初始化属性
    this.initProperty(property);
    //显示/隐藏
    this.refreshShow(false);

    // 点击任意位置 (不是鼠标容器就隐藏)
    const self = this;
    $(document).mousedown((event) => {
      var target = $(event.target);
      if (!target.closest('.mouse-container').length) {
        self.refreshShow(false);
      }
    })
  }
  refreshPanel (datas) {
    if (this.container) {
      $(this.container).empty();
      //刷新渲染
      if (datas && datas.length) {
        for (let i = 0; i < datas.length; i++) {
          const data = datas[i];
          this.refreshItem(this.container, data);
        }
      }
      //刷新属性样式
      this.refreshProperty();
    }
  }
  refreshItem (container, item) {
    if (container && item) {
      const itemContainer = $(`<li class="mouse-item"><i class="mouse-icon"></i> ` + item.name + `</li>`);
      container.append(itemContainer);
      const iconContainer = $(itemContainer).find(".mouse-icon");
      if (item.iconFamily && item.iconClazz) {
        $(iconContainer).addClass(item.iconFamily);
        $(iconContainer).addClass(item.iconClazz);
      }
      const self = this;
      $(itemContainer).on("click", function (event) {
        self.refreshShow(false);
        if (self.events && self.events.click) {
          self.events.click(item);
        }
      });
    }
  }
  refreshShow (isShow) {
    if (this.container) {
      if (isShow) {
        $(this.container).show();
      } else {
        $(this.container).hide();
      }
    }
  }
  refreshProperty () {
    if (this.container && this.property) {
      $(this.container).css(this.property);
      if (this.property.id) {
        $(this.container).attr("id", this.property.id);
      }
    }
  }
  refreshPosition (offsetX, offsetY) {
    if (this.container) {
      const outerHeight = $(this.container).outerHeight();
      const windowHeight = $(window).height();
      const pageX = offsetX || 0;
      const pageY = offsetY || 0;
      const width=pageX;
      let height;
      const temp=outerHeight+pageY;
      if(temp>=windowHeight){
        height=pageY-outerHeight;
      }else{
        height=pageY;
      }
      
      const style = { top: height, left: width };
      $(this.container).css(style);
    }
  }
  refreshEvent (event, events) {
    //触发
    this.refreshPosition(event.pageX, event.pageY);
    this.refreshShow(true);
    //回调
    if (events) {
      this.events = events;
    }
  }
  // refreshEvent (modelContainer, events) {
  //   const self = this;
  //   $(modelContainer).on('contextmenu', (event) => {
  //     event.preventDefault(); // 阻止
  //     //触发
  //     self.refreshPosition(event.pageX, event.pageY);
  //     self.refreshShow(true);
  //     //回调
  //     if(events){
  //       self.events=events;
  //     }
  //   })
  // }
}