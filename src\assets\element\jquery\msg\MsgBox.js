import './css/msg.css'
export default class MsgBox{
    constructor() {
        this.init();
    }
    /**
     * 描述:获取单例
     * @returns {Message}
     */
    static getInstance() {
        if(!MsgBox.instance) {
            MsgBox.instance = new MsgBox();
        }
        return MsgBox.instance;
    }
    /**
     * 描述:初始化
     */
    init(){
        this.container  = $(`<div class="lsd-msg-list"></div>`);
        $("body").append(this.container);
    }
    /**
     * 描述:消息提示
     * @param msg
     */
    alert(msg){
        const msgItem =  $(`<div class="lsd-msg-alert">
            <div class="lsd-msg-icon lsd-icon lsd-icon-atm"></div>
            <div class="lsd-msg-text">`+msg+`</div>
        </div>`);
        this.container.append(msgItem);
        msgItem.fadeIn(1000) ;
        setTimeout(function(){
            msgItem.fadeOut(1000);
        }, this.getTimeOut());
    }

    /**
     * 描述:消息提示
     * @param msg
     */
    warn(msg){
        const msgItem =  $(`<div class="lsd-msg-warn">
            <div class="lsd-msg-icon lsd-icon lsd-icon-prompt-fill"></div>
            <div class="lsd-msg-text">`+msg+`</div>
        </div>`);
        this.container.append(msgItem);
        msgItem.fadeIn(1000) ;
        setTimeout(function(){
            msgItem.fadeOut(1000);
        }, this.getTimeOut());
    }
    getTimeOut(){
        return 3000;
    }
    /**
     * 描述:错误提示
     * @param msg
     */
    error(msg){
        const msgItem =  $(`<div class="lsd-msg-error">
            <div class="lsd-msg-icon lsd-icon lsd-icon-help"></div>
            <div class="lsd-msg-text">`+msg+`</div>
        </div>`);
        this.container.append(msgItem);
        msgItem.fadeIn(1000) ;
        setTimeout(function(){
            msgItem.fadeOut(1000);
        }, this.getTimeOut());
    }
}