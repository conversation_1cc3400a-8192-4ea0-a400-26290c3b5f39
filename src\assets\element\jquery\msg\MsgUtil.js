import MsgBox from "./MsgBox";
/**
 * 描述:信息框对象用于输出提示信息
 *  import * as MsgBox from './MsgBox.js';
 *  MsgBox.alert("添加节点没有type属性，无法添加.");
 * @param msg
 */
export function alert(msg){
    const msgBox = MsgBox.getInstance();
    msgBox.alert(msg);
}
/**
 * 错误消息
 * @param msg
 */
export function warn(msg,obj){
    const msgBox = MsgBox.getInstance();
    console.warn(msg,obj);
    msgBox.warn(msg);
}
/**
 * 错误消息
 * @param msg
 */
export function error(msg,obj){
    const msgBox = MsgBox.getInstance();
    console.error(msg,obj);
    msgBox.error(msg);
}