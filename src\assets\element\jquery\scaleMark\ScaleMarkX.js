import "./css/scale.css"
export default class ScaleMarkX {
  constructor(context) {
    if(context){
      this.context = context;
      this.designer = context.getDesigner();
    }
  }
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div style="width: 100%;height: 100%;"></div>`);
      parentContainer.append(this.container);
    }
  }
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      //table
      const tableContainer = $(`<table cellpadding="0" cellspacing="0" style="width: 100%;height: 100%"></table>`);
      this.container.append(tableContainer);
      const tbodyContainer=$(`<tbody></tbody>`);
      tableContainer.append(tbodyContainer);
      //tr
      const trContainer=$(`<tr></tr>`);
      tbodyContainer.append(trContainer);
      //td
      this.refreshTd(trContainer,24);
    }
  }
  refreshTd(parentContainer,size){
    if(parentContainer && size){
      for(let i=0;i<size;i++){
        const tdContainer=$(`<td key="scaleX`+i+`"></td>`);
        parentContainer.append(tdContainer);
        if(i===0){
          $(tdContainer).addClass('bothBorder-x');
        }else{
          $(tdContainer).addClass('rightBorder-x');
        }
        this.refreshItem(tdContainer);
      }
    }
  }
  refreshItem(parentContainer){
    if(parentContainer){
      const itemContainer=$(`
      <div style="height: 50%;"></div>
      <div style="width: 100%;height: 50%;">
        <table class="short-table" cellpadding="0" cellspacing="0" style="width: 100%;height: 100%;border-collapse:initial;">
          <tr>
            <td class="rightBorder-x"/>
            <td class="rightBorder-x"/>
            <td class="rightBorder-x"/>
            <td class="rightBorder-x"/>
            <td style="border-right: 1px solid transparent"/>
          </tr>
        </table>
      </div>`);
      parentContainer.append(itemContainer);
    }
  }
}