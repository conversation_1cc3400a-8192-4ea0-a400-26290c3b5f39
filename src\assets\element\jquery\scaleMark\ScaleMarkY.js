import "./css/scale.css"
export default class ScaleMarkY {
  constructor(context) {
    if(context){
      this.context = context;
      this.designer = context.getDesigner();
    }
  }
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div style="width: 100%;height: 100%;"></div>`);
      parentContainer.append(this.container);
    }
  }
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      const tableContainer = $(`<table cellpadding="0" cellspacing="0" style="width: 100%;height: 100%"></table>`);
      this.container.append(tableContainer);
      const tbodyContainer=$(`<tbody></tbody>`);
      tableContainer.append(tbodyContainer);
      this.refreshTr(tbodyContainer,12);
    }
  }
  refreshTr(parentContainer,size){
    if(parentContainer && size){
      for(let i=0;i<size;i++){
        const trContainer=this.createTr(i);
        parentContainer.append(trContainer);
      }
    }
  }
  createTr(i){
    const trContainer=$(`
    <tr key="scaleY`+i+`">
      <td class="scale-td">
        <div style="width: 50%;height: 100%;float: left"></div>
        <div style="width: 50%;height: 100%;float: right;">
          <table cellpadding="0" cellspacing="0" style="width: 100%;height: 100%;border-collapse:initial;">
            <tr>
              <td class="bottomBorder-y"></td>
            </tr>
            <tr>
              <td class="bottomBorder-y"></td>
            </tr>
            <tr>
              <td class="bottomBorder-y"></td>
            </tr>
            <tr>
              <td class="bottomBorder-y"></td>
            </tr>
            <tr>
              <td style="border-bottom: 1px solid transparent"></td>
            </tr>
          </table>
        </div>
      </td>
    </tr>`);
    const tdContainer=$(trContainer).find(".scale-td");
    if(i===0){
      $(tdContainer).addClass('bothBorder-y');
    }else{
      $(tdContainer).addClass('bottomBorder-y');
    }
    return trContainer;
  }
}