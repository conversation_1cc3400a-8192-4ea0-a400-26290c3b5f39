export default class Slider {
  constructor(context) {
    if(context){
      this.context = context;
      this.designer = context.getDesigner();
    }
  }
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="slide-container"></div>`);
      parentContainer.append(this.container);
    }
  }
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      const tempContainer = $(`<div id="template" style="display: flex; align-items: center">
        <div id="slider-box" style="width: 200px;">
            <input class="slider-range" type="range" min="10"  max="200" step="1" style="margin-right:5px;display:inline;width: 100%;">
        </div>
        <div id="slider-text" style="border:0; color:#f6931f; line-height: 24px;font-size: 14px;font-weight:bold;margin-left: 10px">缩放:</div>
      </div>`);
      this.container.append(tempContainer);
      this.textContainer = $(tempContainer).find("#slider-text");//文本
      this.rangeContainer = $(tempContainer).find(".slider-range");//范围
      this.refreshChange(this.rangeContainer);
    }
  }
  refreshScale(scale){
    if ((scale||scale==0)) {
      // const size=scale*100;
      const size = Math.round(scale*100);//取整
      this.refreshRange(size);
      this.refreshText(size);
    }
  }
  refreshEvent(event){
    if(event){
      this.event=event
    }
  }

  refreshChange(container){
    const self=this;
    if(container){
      $(container).on("mousedown",function(event){//mousedown change
        const change=this;
        document.onmousemove = function (me) { 
          const value=change.value;     
          self.refreshText(value);
          if(self.event && self.event.slide){
            const val=value/100;
            self.event.slide(val);
          }
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
          const value=change.value;     
          self.refreshText(value);
          if(self.event && self.event.slide){
            const val=value/100;
            self.event.slide(val);
          }
        };
      });
    }
  }
  refreshRange(size){
    if(this.rangeContainer){
      $(this.rangeContainer).val(size);
    }
  }
  refreshText(size){
    if(this.textContainer){
      const text="缩放: "+size+" %";
      $(this.textContainer).text(text);
    }
  }
}