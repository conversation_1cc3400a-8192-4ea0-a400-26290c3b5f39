import { refreshModel, bindModel } from "../../../../utils/ModelUtil";
import { refreshCss } from "../../../../utils/StyleUtil";
import Pager from "./Pager";
import "./css/table.css";

export default class PageTable {
  constructor(isBind) {
    // 如果是绑定数据，则隐藏单选框
    this.isBind = isBind || false;
    this.table = {
      // tools: ["add", "edit", "del"],
      //text input select
      columns: [
        { key: "value", name: "值", type: "input", style: { width: "10%" } },
        {
          key: "text", name: "文本", type: "input", style: { width: "15%" }, options: [
            { key: "1", text: "字符串" },
            { key: "2", text: "数字" },
            { key: "3", text: "日期" },
            { key: "4", text: "浮点" },
          ]
        },
      ],
      events: {
        // clickRow:function(index,item){}, //点击行
        // changePager:function(pager){},//触发分页
      }
    };
  }

  /**
   * 描述:初始化父节点
   * @param parentContainer
   */
  initPanel(parentContainer, table, callback) {
    this.callback = callback;
    if (parentContainer) {
      this.container = $(`<div>
      <div class="ltab-wrap">
        <div class="ltab-tool"></div>
        <table class="ltab-table">
          <thead class="ltab-thead"></thead>
          <tbody class="ltab-tbody"></tbody>
        </table>
      </div>
      <div class="ltab-pager"></div>
    </div>`);
      parentContainer.append(this.container);
      //1.工具栏
      this.toolContainer = $(this.container).find(".ltab-tool");
      //2.表格
      this.tableContainer = $(this.container).find(".ltab-table");
      this.theadContainer = $(this.container).find(".ltab-thead");
      this.tbodyContainer = $(this.container).find(".ltab-tbody");
      //3.分页
      this.pagerContainer = $(this.container).find(".ltab-pager");
      //config
      this.table = table;
      //分页对象
      this.pager = new Pager();
      //刷新头
      this.refreshThead();
    }
  }
  refreshPanel(data) {
    this.list = data || [];
    this.refreshBody(this.list);
  }
  /**
   * 描述:重置分页信息
   */
  resetPager() {
    if (this.pager) {
      this.pager.reset();
    }
  }
  /**
   * 描述:刷新页面信息
   * @param page
   */
  refreshPager(page) {
    if (this.pagerContainer) {
      this.pagerContainer.empty();
      const self = this;
      this.pager.refresh(this.pagerContainer, page, function (pager) {
        if (self.table && self.table.events && self.table.events.changePager) {
          self.table.events.changePager(pager);
        }
      });
    }
  }
  /**
  * 描述:Tool
  */
  // refreshTool () {
  //   if (this.toolContainer) {
  //     this.toolContainer.empty();
  //     const self = this;
  //     for (let i = 0; i < this.table.tools.length; i++) {
  //       const toolItem = this.table.tools[i];
  //       if (toolItem && toolItem === "add") {
  //         const addContainer = $(`<div class="lsd-ptab-btn">新增</div>`);
  //         this.toolContainer.append(addContainer);
  //         addContainer.on("click", function () {
  //           //添加
  //           self.add();
  //           //刷新
  //           self.refreshBody();
  //         });
  //       } else if (toolItem && toolItem === "edit") {
  //         const editContainer = $(`<div class="lsd-ptab-btn">编辑</div>`);
  //         this.toolContainer.append(editContainer);
  //         editContainer.on("click", function () {
  //           self.edit(self.item);
  //         });
  //       } else if (toolItem && toolItem === "del") {
  //         const delContainer = $(`<div class="lsd-ptab-btn">删除</div>`);
  //         this.toolContainer.append(delContainer);
  //         delContainer.on("click", function () {
  //           //删除
  //           self.del(self.index);
  //           //刷新
  //           self.refreshBody();
  //         });
  //       } else {
  //         console.error("不支持工具栏类型：" + toolItem);
  //       }
  //     }
  //   }
  // }
  /**
   * 描述:刷新thead对象
   */
  refreshThead() {//刷新表头信息
    if (this.theadContainer) {
      this.theadContainer.empty();
      const trContainer = $(`<tr></tr>`);
      this.theadContainer.append(trContainer);
      for (let i = 0; i < this.table.columns.length; i++) {
        const column = this.table.columns[i];
        const thContainer = $(`<th>` + column.name + `</th>`);
        trContainer.append(thContainer);
        if (column.style) {
          refreshCss(thContainer, column.style);
        }
      }
      if (!this.isBind) {
        // 添加单选按钮列
        const radioTh = $(`<th class="th_radioList" style="text-align:center"><div class="th-radio-container">
       </div></th>`);
        trContainer.prepend(radioTh);  // 将单选按钮列插入到表头的最前面
      }

      this.theadContainer.append(trContainer);
    }
  }

  /**
   * 描述:刷新表格
   * @param data
   */
  refreshBody(list) {
    if (this.tbodyContainer) {
      this.tbodyContainer.empty();
      if (list && (list.length || list.length == 0)) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          const index = i;
          this.refreshRow(this.tbodyContainer, item, index);
        }
      }
    }
  }

  /**
   * 描述:添加一行数据
   * @param item
   */
  refreshRow(parentContainer, item, index) {
    const self = this;
    const rowContainer = $(`<tr></tr>`);
    parentContainer.append(rowContainer);

    if (!this.isBind) {
      // 添加单选按钮列
      const radioTd = $(`<td class="td_radioList">
      <div class="radio-container">
       <div class="checkMark"></div>
     </div></td>`);
      rowContainer.append(radioTd);
    }

    for (let i = 0; i < this.table.columns.length; i++) {
      const column = this.table.columns[i];
      const tdContainer = this.createTd(column, item);
      rowContainer.append(tdContainer);
      //刷新数据
      refreshModel(rowContainer, item, (key, value) => {
        this.callback && this.callback(key, value)
      }, "t-model");
      //绑定数据
      bindModel(rowContainer, item, (key, value) => {
        this.callback && this.callback(key, value)
      }, "t-model");
    }
    //添加 选中 监听事件
    rowContainer.on("click", function () {
      self.item = item;
      self.index = index;
      if (self.table.events && self.table.events.clickRow) {
        self.refreshChecked(rowContainer);
        self.table.events.clickRow(index, item);
      }
    });
  }
  refreshChecked(rowContainer) {
    // const radioContainer = 
    if (this.tbodyContainer) {
      this.tbodyContainer.find("tr").each(function (index, element) { 
        $(element).find(".radio-container").removeClass("radio-selected");
        $(element).find(".checkMark").removeClass("checkMark-selected");
      });
      if (rowContainer) {
        $(rowContainer).find(".radio-container").addClass("radio-selected");
        $(rowContainer).find(".checkMark").addClass("checkMark-selected");
      }
    }
  }
  createTd(column, item) {
    let tdContainer = $(`<td></td>`);
    if (column && column.key && column.name && column.type) {
      if (item[column.key] !== 0 && !item[column.key]) {
        item[column.key] = "";
      }
      const self = this;
      let columnContainer;
      if (column.type.toUpperCase && column.type.toUpperCase() === "TEXT") {
        columnContainer = $(`<div t-model="` + column.key + `"></div>`);
        if (column.options) {
          columnContainer = $(`<div>` + this.refreshText(item[column.key], column.options) + `</div>`);
        }
      } else if (column.type.toUpperCase && column.type.toUpperCase() === "INPUT") {
        columnContainer = $(`<input t-model="` + column.key + `" type="text"/>`);
      } else if (column.type.toUpperCase && column.type.toUpperCase() === "SELECT") {
        columnContainer = $(`<select t-model="` + column.key + `"></select>`);
        if(column.options){
          //刷新选项
          this.refreshOption(columnContainer, column.options);
        }

      } else {
        console.error("不支持字段定义属性type!", column);
      }
      tdContainer.append(columnContainer);
    }
    return tdContainer;
  }
  refreshText(key, list) {
    let dataMap;
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        if (!dataMap) {
          dataMap = {};
        }
        dataMap[item["key"]] = item["text"];
      }
    }
    if (key && dataMap && dataMap[key]) {
      return dataMap[key];
    } else {
      return "";
    }
  }
  refreshOption(selectContainer, list) {
    if (selectContainer) {
      $(selectContainer).empty();
      selectContainer.prepend("<option value='-1'>请选择</option>");//添加第一个option值
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          if (item.key && item.text) {
            selectContainer.append("<option value='" + item.key + "'>" + item.text + "</option>");
          } else {
            selectContainer.append("<option value='${" + item + "}'>${" + item + "}</option>");
          }

        }
      }
    }
  }
  add() {
    const item = {};
    if (this.list) {
      this.list.push(item);
    } else {
      this.list = [item];
    }
  }
  del(index) {
    if (this.list && this.list.length) {
      this.list.splice(index, 1);
    }
  }
  edit(item) {
    if (this.table.events && this.table.events.clickItem) {
      this.table.events.clickItem(item);
    }
  }
}
