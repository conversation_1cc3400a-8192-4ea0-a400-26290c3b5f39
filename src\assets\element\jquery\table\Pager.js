/**
 * 描述:分页页面信息
 *{
 *      currentPage:1
 *      pageSize:10
 *      totalPage:
 *      totalCount:44
 *}
 */
import {bindModel, refreshModel} from "../../../../utils/ModelUtil";

export default class Pager {
    constructor(){
        this.container = $(`<div class="pager-container"></div>`);
    }
    /**
     * 描述:刷新分页信息
     * @param parentContainer
     */
    refresh(parentContainer,page,callback){
        if(this.container){
            this.container.empty();
            parentContainer.append(this.container);
            if(!page && !page.currentPage && !page.pageSize && !page.totalCount){
                console.error("分页信息初始化出错了!");
            }
            const self = this;
            const modelName = this.getModelName();
            this.compute(page);

            // const pageInfoContainer = $(`<div class="pager-info">
            //     <div>显示 &nbsp [`+page.first+`&nbsp - &nbsp`+page.last+`] ，共计 &nbsp`+page.totalCount+`  &nbsp条</div>
            // </div>`);

            const pageInfoContainer = $(`<div class="pager-info">
                <div>共 <span>`+page.totalCount+`</span> 条</div>
            </div>`);            
            this.container.append(pageInfoContainer);

            const pageSelectContainer = $(`<div class="page-size">
                <select class="pager-size-select" `+modelName+`="pageSize">
                    <option value="5">5条/页</option>
                    <option value="10">10条/页</option>
                    <option value="20">20条/页</option>
                    <option value="50">50条/页</option>
                    <option value="100">100条/页</option>
                    <option value="200">200条/页</option>
                </select>
            </div>`);
            this.container.append(pageSelectContainer);

            //上一页 下一页配置
            const pageButtonContainer = $(`<div class="pager-btn"></div>`);
            const firstPagerBtnContainer = $(`<div class="pager-layout">首页</div>`);
            const previousPagerBtnContainer = $(`<div class="pager-icon fd-font fd-arrow-left"></div>`);
            const pageNumContainer = $(`<div class="pager-info"><div>`+ page.first+`&nbsp - &nbsp`+ page.last +`</div>`);
            const nextPagerBtnContainer = $(`<div class="pager-icon fd-font fd-arrow-right"></div>`);
            const lastPagerBtnContainer = $(`<div class="pager-layout">尾页</div>`);
            pageButtonContainer.append(firstPagerBtnContainer).append(previousPagerBtnContainer).append(pageNumContainer).append(nextPagerBtnContainer).append(lastPagerBtnContainer);
            //首页处理 当不为第一页可以点击
            if(this.page && this.page.currentPage && this.page.currentPage!==1){
                //点击上一页
                firstPagerBtnContainer.click(function(event){
                    if(callback && self.page && self.page.pageSize){
                        callback({currentPage:1,pageSize:self.page.pageSize});
                    }
                });
            }else{
                // firstPagerBtnContainer.addClass("btn-disabled");
            }
            //处理最后一页
            if(this.page && this.page.currentPage && this.page.totalPage && this.page.currentPage!==this.page.totalPage){
                lastPagerBtnContainer.click(function(event){
                    if(callback && self.page && self.page.totalPage){
                        callback({currentPage:self.page.totalPage,pageSize:self.page.pageSize});
                    }
                });
            }else{
                // lastPagerBtnContainer.addClass("btn-disabled");
            }
            //处理上一页
            if(this.page && this.page.isHasPrevious){
                //点击上一页
                previousPagerBtnContainer.click(function(event){
                    if(callback){
                        callback({currentPage:(self.page.currentPage-1),pageSize:self.page.pageSize});
                    }
                });
            }else{
                previousPagerBtnContainer.addClass("btn-disabled");
            }
            if(this.page && this.page.isHasNext){
                //点击上一页
                nextPagerBtnContainer.click(function(event){
                    if(callback){
                        callback({currentPage:(self.page.currentPage+1),pageSize:self.page.pageSize})
                    }
                });
            }else{
                nextPagerBtnContainer.addClass("btn-disabled");
            }
            this.container.append(pageButtonContainer);
            refreshModel(this.container,this.page,null,modelName);
            bindModel(this.container,this.page,function(name,value){
                if(callback){
                    callback({currentPage:self.page.currentPage,pageSize:self.page.pageSize});
                }
            },modelName)
        }
    }

    /**
     * 描述:计算分页
     * @param page
     */
    compute(page){
        if(page){
            page.first=(page.currentPage-1)*page.pageSize;
            page.last=page.currentPage*page.pageSize;
            page.totalPage=Math.ceil(page.totalCount/page.pageSize);
            if(page.currentPage>1){
                page.isHasPrevious=true;
            }else{
                page.isHasPrevious=false;
            }
            if(page.totalPage>page.currentPage){
                page.isHasNext=true;
            }else{
                page.isHasNext=false;
            }
            this.page=page;
        }
    }
    getModelName(){
        return "pager-model";
    }

    reset(){
        if(this.page ){
            this.page.currentPage=1;
            this.page.pageSize=10;
        }
    }
}