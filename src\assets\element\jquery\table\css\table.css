/**####################pageTable#####################**/
.ltab-wrap {
  width: 100%;
  padding-left: 24px;
  /* max-height: 315px; */
  overflow: auto;
}

.ltab-table {
  width: 100%;
  border-collapse: collapse;
  background-color: transparent;
}

.ltab-table thead th {
  height: 52px;
  min-width: 60px;
  padding: 16px 12px;
  border: 1px solid #363637;
  text-align: left;
  font-weight: normal;
  font-size: 14px;
  color: #A3A6AD;
  background: #262727;
  /*f8f8f8 353f50 3F4B5F*/
  color: #fff;
  position: sticky;
  top: 0; /* 固定表头在顶部 */
  z-index: 1;
}

.ltab-table tbody td {
  height: 52px;
  padding: 16px 12px;
  border: 1px solid #363637;
  font-weight: normal;
  font-size: 14px;
  color: #CFD3DC;
  cursor: pointer;
  word-break: keep-all;
  /* 不换行 */
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 内容超出宽度时隐藏超出部分的内容 */
  text-overflow: ellipsis;
  /* 当对象内文本溢出时显示省略标记(...) ；需与overflow:hidden;一起使用。*/

}

.ltab-table tbody tr:hover {
  background: #262727;
}

/* 表格单选列表 */
.td_radioList{
  display: flex;
  justify-content: center;
  /* 移除iOS上的内置样式 */
  display: -webkit-flex;
  -webkit-justify-content: center;
}
.ltab-table .radio-container {
  height: 14px;
  width: 14px;
  background: #000000;
  border: 1px solid #4C4D4F;
  border-radius: 50%;
  position: relative;
  transition: background-color 0.3s, border-color 0.3s;
}

.ltab-table .th-radio-container {
  display: inline-block;
  height: 14px;
  width: 14px;
  background: #000000;
  border: 1px solid #4C4D4F;
  border-radius: 50%;
}

.ltab-table .radio-container .checkMark {
  position: absolute;
  height: 5px;
  width: 5px;
  border-radius: 50%;
  top: 4px;
  left: 3.5px;
}

.ltab-table .radio-selected {
  background-color: #2196F3;
  border-color: #2196F3;
}

.ltab-table .checkMark-selected {
  background-color: #ffffff;
}

.ltab-selected {
  background-color: #23b7e5;
}


.ltab-table tbody td select:hover,
.ltab-table tbody td select:focus,
.ltab-table tbody td input:hover,
.ltab-table tbody td input:focus {
  border: solid 1px #23b7e5;
}

/**####################分页#####################**/
.pager-container {
  width: 100%;
  display: flex;
  justify-content: end;
  margin-top: 24px;
}

.pager-size-select {
  height: 30px;
  width: 85px;
  margin-right: 10px;
  border: 1px solid #8D9095;
  border-radius: 4px;
  background: #141414;
  color: #CFD3DC;
  font-size: 14px;
  padding-left: 10px;

}

.pager-info {
  font-size: 14px;
  color: #CFD3DC;
  line-height: 30px;
  margin-right: 15px;
}

.pager-info i {
  color: #409EFF;
}

.pager-btn {
  display: flex;
}


.pager-btn .pager-layout {
  padding: 5px 10px;
  margin-right: 10px;
  font-size: 14px;
  border: 1px solid #8D9095;
  border-radius: 4px;
  background: #141414;
  color: #CFD3DC;
  cursor: pointer;
}

.pager-btn .pager-icon{
  padding: 5px 10px;
  margin-right: 10px;
  font-size: 18px;
  border-radius: 4px;
  background: #141414;
  color: #CFD3DC;
  cursor: pointer;
}

.pager-btn .btn-disabled {
  color: #409EFF;
}


.pager-size-select:hover,
.pager-btn .pager-layout:hover {
  border: solid 1px #409EFF;
}
.pager-btn .pager-icon:hover{
  color: #409EFF;
}