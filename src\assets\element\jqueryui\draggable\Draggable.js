import "./css/draggable.css";
export default class Draggable {
  constructor() {

  }
  static getInstance () {
    if (!this.instance) {
      this.instance = new Draggable();
    }
    return this.instance;
  }
  /**
   * 拖拽-克隆
   * @param {*} sourceContainer 
   */
  addDraggableClone (sourceContainer) {
    $(sourceContainer).draggable({ helper: "clone", appendTo: "body", scroll: false });
  }
  /**
   * 
   * @param {*} sourceContainer 
   * @param {*} targetContainer 
   * @param {*} callback 
   */
  addDraggableHelper (sourceContainer, targetContainer, callback) {
    $(sourceContainer).draggable({
      containment: targetContainer,
      helper: function () {
        let moveContainer;
        if (callback) {
          moveContainer = callback();
        }
        if (moveContainer) {
          return moveContainer;
        } else {
          return $(`<div class="draggable-module">移动块</div>`);
        }
      }
    });
  }
  /**
   * droppable
   * @param {*} targetContainer 
   * @param {*} callback ui.draggable,ui.helper,ui.position,ui.offset
   */
  addDraggableStop (targetContainer, callback) {
    $(targetContainer).droppable({
      drop: function (event, ui) {
        if (event && ui) {
          if (callback) {
            callback(event,ui);
          }
        }
      }
    });
  }
}