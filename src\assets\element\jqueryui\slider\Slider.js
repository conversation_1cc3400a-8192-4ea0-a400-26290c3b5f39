import "./css/slider.css"
export default class Slider {
  constructor(context) {
    this.context = context;
    this.designer = context.getDesigner();
  }
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="slide-container"></div>`);
      parentContainer.append(this.container);
    }
  }
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      const tempContainer = $(`<div id="template">
      <div id="slider-text" style="border:0; color:#f6931f; font-weight:bold;"></div>
      <div id="slider-range"></div>
      </div>`);
      this.container.append(tempContainer);
      this.textContainer = $(tempContainer).find("#slider-text");
      this.rangeContainer = $(tempContainer).find("#slider-range");
      this.addSlider(this.rangeContainer, this.textContainer);
      const scale = this.getSliderValue(this.rangeContainer);
      this.refreshText(this.textContainer,scale);
    }
  }
  refreshScale (scale) {
    if ((scale||scale==0) && this.rangeContainer && this.textContainer) {
      const size=scale*100;
      this.setSliderValue(this.rangeContainer, size);
      this.refreshText(this.textContainer, size);
    }
  }
  refreshEvent(event){
    if(event){
      this.event=event
    }
  }
  /**
   * 刷新-文本
   * @param {*} textContainer 
   * @param {*} value 
   */
  refreshText (textContainer, value) {
    $(textContainer).text("PCT: " + value + " %");
  }
  /**
   * 添加-滑动条
   * @param {*} sliderContainer 
   * @param {*} textContainer 
   */
  addSlider (sliderContainer, textContainer) {
    if (sliderContainer && textContainer) {
      const self = this;
      $(sliderContainer).slider({
        orientation: "horizontal",//方向：水平
        range: "min",
        min: 0,
        max: 100,
        value: 100,
        animate: true,
        slide: function (event, ui) {
          // $(textContainer).val("$" + ui.value);
          self.refreshText(textContainer, ui.value);
          if(self.event && self.event.slide){
            const value=ui.value/100;
            self.event.slide(value);
          }
        }
      });
    }
  }
  getSliderValue (sliderContainer) {
    if (sliderContainer) {
      return $(sliderContainer).slider("value");
    }
  }
  setSliderValue (sliderContainer, value) {
    if (sliderContainer && (value || value == 0)) {
      $(sliderContainer).slider("value", value);
    }
  }

}