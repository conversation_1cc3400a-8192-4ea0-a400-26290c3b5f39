@font-face {
  font-family: "fd-font"; /* Project id 2529443 */
  src: url('./fd-font/fd-font.woff2?t=1621414825884') format('woff2'),
       url('./fd-font/fd-font.woff?t=1621414825884') format('woff'),
       url('./fd-font/fd-font.ttf?t=1621414825884') format('truetype');
}

.fd-font {
  font-family: "fd-font" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fd-cameraswitching:before {
  content: "\e75b";
}

.fd-Rightbutton-fill:before {
  content: "\e85b";
}

.fd-cecurity-protection:before {
  content: "\e75c";
}

.fd-RFQ-logo-fill:before {
  content: "\e85c";
}

.fd-category:before {
  content: "\e75d";
}

.fd-RFQ-word-fill:before {
  content: "\e85d";
}

.fd-close:before {
  content: "\e75e";
}

.fd-searchcart-fill:before {
  content: "\e85e";
}

.fd-certified-supplier:before {
  content: "\e75f";
}

.fd-salescenter-fill:before {
  content: "\e85f";
}

.fd-cart-Empty:before {
  content: "\e760";
}

.fd-save-fill:before {
  content: "\e860";
}

.fd-code1:before {
  content: "\e761";
}

.fd-security-fill:before {
  content: "\e861";
}

.fd-color:before {
  content: "\e762";
}

.fd-Similarproducts-fill:before {
  content: "\e862";
}

.fd-conditions:before {
  content: "\e763";
}

.fd-signboard-fill:before {
  content: "\e863";
}

.fd-confirm:before {
  content: "\e764";
}

.fd-service-fill:before {
  content: "\e864";
}

.fd-company:before {
  content: "\e765";
}

.fd-shuffling-banner-fill:before {
  content: "\e865";
}

.fd-ali-clould:before {
  content: "\e766";
}

.fd-supplier-features-fill:before {
  content: "\e866";
}

.fd-copy1:before {
  content: "\e767";
}

.fd-store-fill:before {
  content: "\e867";
}

.fd-credit-level:before {
  content: "\e768";
}

.fd-smile-fill:before {
  content: "\e868";
}

.fd-coupons:before {
  content: "\e769";
}

.fd-success-fill:before {
  content: "\e869";
}

.fd-connections:before {
  content: "\e76a";
}

.fd-sound-filling-fill:before {
  content: "\e86a";
}

.fd-cry:before {
  content: "\e76b";
}

.fd-sound-Mute1:before {
  content: "\e86b";
}

.fd-costoms-alearance:before {
  content: "\e76c";
}

.fd-suspended-fill:before {
  content: "\e86c";
}

.fd-clock:before {
  content: "\e76d";
}

.fd-tool-fill:before {
  content: "\e86d";
}

.fd-CurrencyConverter:before {
  content: "\e76e";
}

.fd-task-management-fill:before {
  content: "\e86e";
}

.fd-cut:before {
  content: "\e76f";
}

.fd-unlock-fill:before {
  content: "\e86f";
}

.fd-data1:before {
  content: "\e770";
}

.fd-trust-fill:before {
  content: "\e870";
}

.fd-Customermanagement:before {
  content: "\e771";
}

.fd-vip-fill:before {
  content: "\e871";
}

.fd-descending:before {
  content: "\e772";
}

.fd-set1:before {
  content: "\e872";
}

.fd-double-arro-right:before {
  content: "\e773";
}

.fd-Top-fill:before {
  content: "\e873";
}

.fd-customization:before {
  content: "\e774";
}

.fd-viewlarger1:before {
  content: "\e874";
}

.fd-double-arrow-left:before {
  content: "\e775";
}

.fd-voice-fill:before {
  content: "\e875";
}

.fd-discount:before {
  content: "\e776";
}

.fd-warning-fill:before {
  content: "\e876";
}

.fd-download:before {
  content: "\e777";
}

.fd-warehouse-fill:before {
  content: "\e877";
}

.fd-dollar1:before {
  content: "\e778";
}

.fd-zip-fill:before {
  content: "\e878";
}

.fd-default-template:before {
  content: "\e779";
}

.fd-trade-assurance-fill:before {
  content: "\e879";
}

.fd-editor1:before {
  content: "\e77a";
}

.fd-vs-fill:before {
  content: "\e87a";
}

.fd-eletrical:before {
  content: "\e77b";
}

.fd-video1:before {
  content: "\e87b";
}

.fd-electronics:before {
  content: "\e77c";
}

.fd-template-fill:before {
  content: "\e87c";
}

.fd-etrical-equipm:before {
  content: "\e77d";
}

.fd-wallet1:before {
  content: "\e87d";
}

.fd-ellipsis:before {
  content: "\e77e";
}

.fd-training1:before {
  content: "\e87e";
}

.fd-email:before {
  content: "\e77f";
}

.fd-packing-labeling-fill:before {
  content: "\e87f";
}

.fd-falling:before {
  content: "\e780";
}

.fd-Exportservices-fill:before {
  content: "\e880";
}

.fd-earth:before {
  content: "\e781";
}

.fd-brand-fill:before {
  content: "\e881";
}

.fd-filter:before {
  content: "\e782";
}

.fd-collection:before {
  content: "\e882";
}

.fd-furniture:before {
  content: "\e783";
}

.fd-consumption-fill:before {
  content: "\e883";
}

.fd-folder:before {
  content: "\e784";
}

.fd-collection-fill:before {
  content: "\e884";
}

.fd-feeds:before {
  content: "\e785";
}

.fd-brand:before {
  content: "\e885";
}

.fd-history1:before {
  content: "\e786";
}

.fd-rejected-order-fill:before {
  content: "\e886";
}

.fd-hardware:before {
  content: "\e787";
}

.fd-homepage-ads-fill:before {
  content: "\e887";
}

.fd-help:before {
  content: "\e788";
}

.fd-homepage-ads:before {
  content: "\e888";
}

.fd-good:before {
  content: "\e789";
}

.fd-scenes-fill:before {
  content: "\e889";
}

.fd-Householdappliances:before {
  content: "\e78a";
}

.fd-scenes:before {
  content: "\e88a";
}

.fd-gift1:before {
  content: "\e78b";
}

.fd-similar-product-fill:before {
  content: "\e88b";
}

.fd-form:before {
  content: "\e78c";
}

.fd-topraning-fill:before {
  content: "\e88c";
}

.fd-image-text:before {
  content: "\e78d";
}

.fd-consumption:before {
  content: "\e88d";
}

.fd-hot:before {
  content: "\e78e";
}

.fd-topraning:before {
  content: "\e88e";
}

.fd-inspection:before {
  content: "\e78f";
}

.fd-gold-supplier:before {
  content: "\e88f";
}

.fd-leftbutton:before {
  content: "\e790";
}

.fd-messagecenter-fill:before {
  content: "\e890";
}

.fd-jewelry:before {
  content: "\e791";
}

.fd-quick:before {
  content: "\e891";
}

.fd-ipad:before {
  content: "\e792";
}

.fd-writing:before {
  content: "\e892";
}

.fd-leftarrow:before {
  content: "\e793";
}

.fd-docjpge-fill:before {
  content: "\e893";
}

.fd-integral1:before {
  content: "\e794";
}

.fd-jpge-fill:before {
  content: "\e894";
}

.fd-kitchen:before {
  content: "\e795";
}

.fd-gifjpge-fill:before {
  content: "\e895";
}

.fd-inquiry-template:before {
  content: "\e796";
}

.fd-bmpjpge-fill:before {
  content: "\e896";
}

.fd-link:before {
  content: "\e797";
}

.fd-tifjpge-fill:before {
  content: "\e897";
}

.fd-libra:before {
  content: "\e798";
}

.fd-pngjpge-fill:before {
  content: "\e898";
}

.fd-loading:before {
  content: "\e799";
}

.fd-Hometextile:before {
  content: "\e899";
}

.fd-listing-content:before {
  content: "\e79a";
}

.fd-home:before {
  content: "\e89a";
}

.fd-lights:before {
  content: "\e79b";
}

.fd-sendinquiry-fill:before {
  content: "\e89b";
}

.fd-logistics-icon:before {
  content: "\e79c";
}

.fd-comments-fill:before {
  content: "\e89c";
}

.fd-messagecenter:before {
  content: "\e79d";
}

.fd-account-fill:before {
  content: "\e89d";
}

.fd-mobile-phone:before {
  content: "\e79e";
}

.fd-feed-logo-fill:before {
  content: "\e89e";
}

.fd-manage-order:before {
  content: "\e79f";
}

.fd-feed-logo:before {
  content: "\e89f";
}

.fd-move:before {
  content: "\e7a0";
}

.fd-home-fill:before {
  content: "\e8a0";
}

.fd-Moneymanagement:before {
  content: "\e7a1";
}

.fd-add-select:before {
  content: "\e8a1";
}

.fd-namecard:before {
  content: "\e7a2";
}

.fd-sami-select:before {
  content: "\e8a2";
}

.fd-map:before {
  content: "\e7a3";
}

.fd-camera:before {
  content: "\e8a3";
}

.fd-Newuserzone:before {
  content: "\e7a4";
}

.fd-arrow-down:before {
  content: "\e8a4";
}

.fd-multi-language:before {
  content: "\e7a5";
}

.fd-account:before {
  content: "\e8a5";
}

.fd-office:before {
  content: "\e7a6";
}

.fd-comments:before {
  content: "\e8a6";
}

.fd-notice:before {
  content: "\e7a7";
}

.fd-cart-Empty1:before {
  content: "\e8a7";
}

.fd-ontimeshipment:before {
  content: "\e7a8";
}

.fd-favorites:before {
  content: "\e8a8";
}

.fd-office-supplies:before {
  content: "\e7a9";
}

.fd-order:before {
  content: "\e8a9";
}

.fd-password:before {
  content: "\e7aa";
}

.fd-search:before {
  content: "\e8aa";
}

.fd-Notvisible1:before {
  content: "\e7ab";
}

.fd-trade-assurance:before {
  content: "\e8ab";
}

.fd-operation:before {
  content: "\e7ac";
}

.fd-usercenter1:before {
  content: "\e8ac";
}

.fd-packaging:before {
  content: "\e7ad";
}

.fd-tradingdata:before {
  content: "\e8ad";
}

.fd-online-tracking:before {
  content: "\e7ae";
}

.fd-microphone:before {
  content: "\e8ae";
}

.fd-packing-labeling:before {
  content: "\e7af";
}

.fd-txt:before {
  content: "\e8af";
}

.fd-phone:before {
  content: "\e7b0";
}

.fd-xlsx:before {
  content: "\e8b0";
}

.fd-pic1:before {
  content: "\e7b1";
}

.fd-banzhengfuwu:before {
  content: "\e8b1";
}

.fd-pin:before {
  content: "\e7b2";
}

.fd-cangku:before {
  content: "\e8b2";
}

.fd-play1:before {
  content: "\e7b3";
}

.fd-daibancaishui:before {
  content: "\e8b3";
}

.fd-logistic-logo:before {
  content: "\e7b4";
}

.fd-jizhuangxiang:before {
  content: "\e8b4";
}

.fd-print:before {
  content: "\e7b5";
}

.fd-jiaobiao:before {
  content: "\e8b5";
}

.fd-product:before {
  content: "\e7b6";
}

.fd-kehupandian:before {
  content: "\e8b6";
}

.fd-machinery:before {
  content: "\e7b7";
}

.fd-dongtai:before {
  content: "\e8b7";
}

.fd-process:before {
  content: "\e7b8";
}

.fd-daikuan:before {
  content: "\e8b8";
}

.fd-prompt:before {
  content: "\e7b9";
}

.fd-shengyijing:before {
  content: "\e8b9";
}

.fd-QRcode1:before {
  content: "\e7ba";
}

.fd-jiehui:before {
  content: "\e8ba";
}

.fd-reeor:before {
  content: "\e7bb";
}

.fd-fencengpeizhi:before {
  content: "\e8bb";
}

.fd-reduce:before {
  content: "\e7bc";
}

.fd-shenqingjilu:before {
  content: "\e8bc";
}

.fd-Non-staplefood:before {
  content: "\e7bd";
}

.fd-shangchuanbeiandanzheng:before {
  content: "\e8bd";
}

.fd-rejected-order:before {
  content: "\e7be";
}

.fd-shangchuan1:before {
  content: "\e8be";
}

.fd-resonserate:before {
  content: "\e7bf";
}

.fd-kehuquanyi:before {
  content: "\e8bf";
}

.fd-remind:before {
  content: "\e7c0";
}

.fd-suoxiao:before {
  content: "\e8c0";
}

.fd-responsetime:before {
  content: "\e7c1";
}

.fd-quanyipeizhi:before {
  content: "\e8c1";
}

.fd-return:before {
  content: "\e7c2";
}

.fd-shuangshen:before {
  content: "\e8c2";
}

.fd-paylater:before {
  content: "\e7c3";
}

.fd-tongguan:before {
  content: "\e8c3";
}

.fd-rising1:before {
  content: "\e7c4";
}

.fd-tuishui:before {
  content: "\e8c4";
}

.fd-Rightarrow:before {
  content: "\e7c5";
}

.fd-tongguanshuju:before {
  content: "\e8c5";
}

.fd-rmb1:before {
  content: "\e7c6";
}

.fd-kuaidiwuliu:before {
  content: "\e8c6";
}

.fd-RFQ-logo:before {
  content: "\e7c7";
}

.fd-wuliuchanpin:before {
  content: "\e8c7";
}

.fd-save:before {
  content: "\e7c8";
}

.fd-waihuishuju:before {
  content: "\e8c8";
}

.fd-scanning:before {
  content: "\e7c9";
}

.fd-xinxibar_shouji:before {
  content: "\e8ca";
}

.fd-security:before {
  content: "\e7ca";
}

.fd-xinwaizongyewu:before {
  content: "\e8cb";
}

.fd-salescenter:before {
  content: "\e7cb";
}

.fd-wuliudingdan:before {
  content: "\e8cc";
}

.fd-selected:before {
  content: "\e7cc";
}

.fd-zhongjianren:before {
  content: "\e8cd";
}

.fd-searchcart:before {
  content: "\e7cd";
}

.fd-xinxibar_zhanghu:before {
  content: "\e8ce";
}

.fd-raw:before {
  content: "\e7ce";
}

.fd-yidatong:before {
  content: "\e8cf";
}

.fd-service:before {
  content: "\e7cf";
}

.fd-zhuanyequanwei:before {
  content: "\e8d0";
}

.fd-share:before {
  content: "\e7d0";
}

.fd-zhanghucaozuo:before {
  content: "\e8d1";
}

.fd-signboard:before {
  content: "\e7d1";
}

.fd-xuanzhuandu:before {
  content: "\e8d2";
}

.fd-shuffling-banner:before {
  content: "\e7d2";
}

.fd-tuishuirongzi:before {
  content: "\e8d3";
}

.fd-Rightbutton:before {
  content: "\e7d3";
}

.fd-AddProducts:before {
  content: "\e8d4";
}

.fd-sorting:before {
  content: "\e7d4";
}

.fd-ziyingyewu:before {
  content: "\e8d5";
}

.fd-sound-Mute:before {
  content: "\e7d5";
}

.fd-addcell:before {
  content: "\e8d6";
}

.fd-Similarproducts:before {
  content: "\e7d6";
}

.fd-background-color:before {
  content: "\e8d7";
}

.fd-sound-filling:before {
  content: "\e7d7";
}

.fd-cascades:before {
  content: "\e8d8";
}

.fd-suggest:before {
  content: "\e7d8";
}

.fd-beijing:before {
  content: "\e8d9";
}

.fd-stop:before {
  content: "\e7d9";
}

.fd-bold:before {
  content: "\e8da";
}

.fd-success:before {
  content: "\e7da";
}

.fd-zijin:before {
  content: "\e8db";
}

.fd-supplier-features:before {
  content: "\e7db";
}

.fd-eraser:before {
  content: "\e8dc";
}

.fd-switch:before {
  content: "\e7dc";
}

.fd-centeralignment:before {
  content: "\e8dd";
}

.fd-survey:before {
  content: "\e7dd";
}

.fd-click:before {
  content: "\e8de";
}

.fd-template:before {
  content: "\e7de";
}

.fd-aspjiesuan:before {
  content: "\e8df";
}

.fd-text:before {
  content: "\e7df";
}

.fd-flag:before {
  content: "\e8e0";
}

.fd-suspended:before {
  content: "\e7e0";
}

.fd-falg-fill:before {
  content: "\e8e1";
}

.fd-task-management:before {
  content: "\e7e1";
}

.fd-Fee:before {
  content: "\e8e2";
}

.fd-tool:before {
  content: "\e7e2";
}

.fd-filling:before {
  content: "\e8e3";
}

.fd-Top:before {
  content: "\e7e3";
}

.fd-Foreigncurrency:before {
  content: "\e8e4";
}

.fd-smile:before {
  content: "\e7e4";
}

.fd-guanliyuan1:before {
  content: "\e8e5";
}

.fd-textile-products:before {
  content: "\e7e5";
}

.fd-language:before {
  content: "\e8e6";
}

.fd-tradealert:before {
  content: "\e7e6";
}

.fd-leftalignment:before {
  content: "\e8e7";
}

.fd-topsales:before {
  content: "\e7e7";
}

.fd-extra-inquiries:before {
  content: "\e8e8";
}

.fd-tradingvolume:before {
  content: "\e7e8";
}

.fd-Italic:before {
  content: "\e8e9";
}

.fd-training:before {
  content: "\e7e9";
}

.fd-pcm:before {
  content: "\e8ea";
}

.fd-upload:before {
  content: "\e7ea";
}

.fd-reducecell:before {
  content: "\e8eb";
}

.fd-RFQ-word:before {
  content: "\e7eb";
}

.fd-rightalignment:before {
  content: "\e8ec";
}

.fd-viewlarger:before {
  content: "\e7ec";
}

.fd-pointerleft:before {
  content: "\e8ed";
}

.fd-viewgallery:before {
  content: "\e7ed";
}

.fd-subscript:before {
  content: "\e8ee";
}

.fd-vehivles:before {
  content: "\e7ee";
}

.fd-square:before {
  content: "\e8ef";
}

.fd-trust:before {
  content: "\e7ef";
}

.fd-superscript:before {
  content: "\e8f0";
}

.fd-warning:before {
  content: "\e7f0";
}

.fd-tag-subscript:before {
  content: "\e8f1";
}

.fd-warehouse:before {
  content: "\e7f1";
}

.fd-danjuzhuanhuan:before {
  content: "\e8f2";
}

.fd-shoes:before {
  content: "\e7f2";
}

.fd-Transfermoney:before {
  content: "\e8f3";
}

.fd-video:before {
  content: "\e7f3";
}

.fd-under-line:before {
  content: "\e8f4";
}

.fd-viewlist:before {
  content: "\e7f4";
}

.fd-xiakuangxian:before {
  content: "\e8f5";
}

.fd-set:before {
  content: "\e7f5";
}

.fd-shouqi1:before {
  content: "\e8f6";
}

.fd-store:before {
  content: "\e7f6";
}

.fd-zhankai1:before {
  content: "\e8f7";
}

.fd-tool-hardware:before {
  content: "\e7f7";
}

.fd-tongxunlu:before {
  content: "\e8f8";
}

.fd-vs:before {
  content: "\e7f8";
}

.fd-yiguanzhugongyingshang:before {
  content: "\e8f9";
}

.fd-toy:before {
  content: "\e7f9";
}

.fd-goumaipianhao:before {
  content: "\e8fa";
}

.fd-sport:before {
  content: "\e7fa";
}

.fd-Subscribe:before {
  content: "\e8fb";
}

.fd-creditcard:before {
  content: "\e7fb";
}

.fd-becomeagoldsupplier:before {
  content: "\e8fc";
}

.fd-contacts:before {
  content: "\e7fc";
}

.fd-new:before {
  content: "\e8fd";
}

.fd-checkstand:before {
  content: "\e7fd";
}

.fd-free:before {
  content: "\e8fe";
}

.fd-aviation:before {
  content: "\e7fe";
}

.fd-cad-fill:before {
  content: "\e8ff";
}

.fd-Daytimemode:before {
  content: "\e7ff";
}

.fd-robot:before {
  content: "\e900";
}

.fd-infantmom:before {
  content: "\e800";
}

.fd-inspection1:before {
  content: "\e901";
}

.fd-discounts:before {
  content: "\e801";
}

.fd-invoice:before {
  content: "\e802";
}

.fd-insurance:before {
  content: "\e803";
}

.fd-nightmode:before {
  content: "\e804";
}

.fd-usercenter:before {
  content: "\e805";
}

.fd-unlock:before {
  content: "\e806";
}

.fd-vip:before {
  content: "\e807";
}

.fd-wallet:before {
  content: "\e808";
}

.fd-landtransportation:before {
  content: "\e809";
}

.fd-voice:before {
  content: "\e80a";
}

.fd-exchangerate:before {
  content: "\e80b";
}

.fd-contacts-fill:before {
  content: "\e80c";
}

.fd-add-account1:before {
  content: "\e80d";
}

.fd-years-fill:before {
  content: "\e80e";
}

.fd-add-cart-fill:before {
  content: "\e80f";
}

.fd-add-fill:before {
  content: "\e810";
}

.fd-all-fill1:before {
  content: "\e811";
}

.fd-ashbin-fill:before {
  content: "\e812";
}

.fd-calendar-fill:before {
  content: "\e813";
}

.fd-bad-fill:before {
  content: "\e814";
}

.fd-bussiness-man-fill:before {
  content: "\e815";
}

.fd-atm-fill:before {
  content: "\e816";
}

.fd-cart-full-fill:before {
  content: "\e817";
}

.fd-cart-Empty-fill:before {
  content: "\e818";
}

.fd-cameraswitching-fill:before {
  content: "\e819";
}

.fd-atm-away-fill:before {
  content: "\e81a";
}

.fd-certified-supplier-fill:before {
  content: "\e81b";
}

.fd-calculator-fill:before {
  content: "\e81c";
}

.fd-clock-fill:before {
  content: "\e81d";
}

.fd-ali-clould-fill:before {
  content: "\e81e";
}

.fd-color-fill:before {
  content: "\e81f";
}

.fd-coupons-fill:before {
  content: "\e820";
}

.fd-cecurity-protection-fill:before {
  content: "\e821";
}

.fd-credit-level-fill:before {
  content: "\e822";
}

.fd-auto:before {
  content: "\e6eb";
}

.fd-default-template-fill:before {
  content: "\e823";
}

.fd-all:before {
  content: "\e6f4";
}

.fd-CurrencyConverter-fill:before {
  content: "\e824";
}

.fd-bussiness-man:before {
  content: "\e6f5";
}

.fd-Customermanagement-fill:before {
  content: "\e825";
}

.fd-component:before {
  content: "\e6f6";
}

.fd-discounts-fill:before {
  content: "\e826";
}

.fd-code:before {
  content: "\e6f7";
}

.fd-Daytimemode-fill:before {
  content: "\e827";
}

.fd-copy:before {
  content: "\e702";
}

.fd-exl-fill:before {
  content: "\e828";
}

.fd-dollar:before {
  content: "\e704";
}

.fd-cry-fill:before {
  content: "\e829";
}

.fd-history:before {
  content: "\e705";
}

.fd-email-fill:before {
  content: "\e82a";
}

.fd-editor:before {
  content: "\e706";
}

.fd-filter-fill:before {
  content: "\e82b";
}

.fd-data:before {
  content: "\e707";
}

.fd-folder-fill:before {
  content: "\e82c";
}

.fd-gift:before {
  content: "\e708";
}

.fd-feeds-fill:before {
  content: "\e82d";
}

.fd-integral:before {
  content: "\e709";
}

.fd-gold-supplie-fill:before {
  content: "\e82e";
}

.fd-nav-list:before {
  content: "\e70a";
}

.fd-form-fill:before {
  content: "\e82f";
}

.fd-pic:before {
  content: "\e70b";
}

.fd-camera-fill:before {
  content: "\e830";
}

.fd-Notvisible:before {
  content: "\e70c";
}

.fd-good-fill:before {
  content: "\e831";
}

.fd-play:before {
  content: "\e70d";
}

.fd-image-text-fill:before {
  content: "\e832";
}

.fd-rising:before {
  content: "\e70e";
}

.fd-inspection-fill:before {
  content: "\e833";
}

.fd-QRcode:before {
  content: "\e70f";
}

.fd-hot-fill:before {
  content: "\e834";
}

.fd-rmb:before {
  content: "\e710";
}

.fd-company-fill:before {
  content: "\e835";
}

.fd-similar-product:before {
  content: "\e711";
}

.fd-discount-fill:before {
  content: "\e836";
}

.fd-Exportservices:before {
  content: "\e712";
}

.fd-insurance-fill:before {
  content: "\e837";
}

.fd-sendinquiry:before {
  content: "\e713";
}

.fd-inquiry-template-fill:before {
  content: "\e838";
}

.fd-all-fill:before {
  content: "\e718";
}

.fd-leftbutton-fill:before {
  content: "\e839";
}

.fd-favorites-fill:before {
  content: "\e724";
}

.fd-integral-fill1:before {
  content: "\e83a";
}

.fd-integral-fill:before {
  content: "\e726";
}

.fd-help1:before {
  content: "\e83b";
}

.fd-namecard-fill:before {
  content: "\e72a";
}

.fd-listing-content-fill:before {
  content: "\e83c";
}

.fd-pic-fill:before {
  content: "\e72e";
}

.fd-logistic-logo-fill:before {
  content: "\e83d";
}

.fd-play-fill:before {
  content: "\e72f";
}

.fd-Moneymanagement-fill:before {
  content: "\e83e";
}

.fd-prompt-fill:before {
  content: "\e730";
}

.fd-manage-order-fill:before {
  content: "\e83f";
}

.fd-stop-fill:before {
  content: "\e738";
}

.fd-multi-language-fill:before {
  content: "\e840";
}

.fd-column:before {
  content: "\e741";
}

.fd-logistics-icon-fill:before {
  content: "\e841";
}

.fd-add-account:before {
  content: "\e742";
}

.fd-Newuserzone-fill:before {
  content: "\e842";
}

.fd-column1:before {
  content: "\e743";
}

.fd-nightmode-fill:before {
  content: "\e843";
}

.fd-add:before {
  content: "\e744";
}

.fd-office-supplies-fill:before {
  content: "\e844";
}

.fd-agriculture:before {
  content: "\e745";
}

.fd-notice-fill:before {
  content: "\e845";
}

.fd-years:before {
  content: "\e746";
}

.fd-mute:before {
  content: "\e846";
}

.fd-add-cart:before {
  content: "\e747";
}

.fd-order-fill:before {
  content: "\e847";
}

.fd-arrow-right:before {
  content: "\e748";
}

.icon-arrow-right {
  font-family: "fd-font" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-right:before {
  content: "\e748";
}

.fd-password1:before {
  content: "\e848";
}

.fd-arrow-left:before {
  content: "\e749";
}

.icon-arrow-left {
  font-family: "fd-font" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-left:before {
  content: "\e749";
}

.fd-map1:before {
  content: "\e849";
}

.fd-apparel:before {
  content: "\e74a";
}

.fd-paylater-fill:before {
  content: "\e84a";
}

.fd-all1:before {
  content: "\e74b";
}

.fd-phone-fill:before {
  content: "\e84b";
}

.fd-arrow-up:before {
  content: "\e74c";
}

.fd-online-tracking-fill:before {
  content: "\e84c";
}

.fd-ascending:before {
  content: "\e74d";
}

.fd-play-fill1:before {
  content: "\e84d";
}

.fd-ashbin:before {
  content: "\e74e";
}

.fd-pdf-fill:before {
  content: "\e84e";
}

.fd-atm:before {
  content: "\e74f";
}

.fd-phone1:before {
  content: "\e84f";
}

.fd-bad:before {
  content: "\e750";
}

.fd-pin-fill:before {
  content: "\e850";
}

.fd-attachent:before {
  content: "\e751";
}

.fd-product-fill:before {
  content: "\e851";
}

.fd-browse:before {
  content: "\e752";
}

.fd-rankinglist-fill:before {
  content: "\e852";
}

.fd-beauty:before {
  content: "\e753";
}

.fd-reduce-fill:before {
  content: "\e853";
}

.fd-atm-away:before {
  content: "\e754";
}

.fd-reeor-fill:before {
  content: "\e854";
}

.fd-assessed-badge:before {
  content: "\e755";
}

.fd-pic-fill1:before {
  content: "\e855";
}

.fd-auto1:before {
  content: "\e756";
}

.fd-rankinglist:before {
  content: "\e856";
}

.fd-bags:before {
  content: "\e757";
}

.fd-product1:before {
  content: "\e857";
}

.fd-calendar:before {
  content: "\e758";
}

.fd-prompt-fill1:before {
  content: "\e858";
}

.fd-cart-full:before {
  content: "\e759";
}

.fd-resonserate-fill:before {
  content: "\e859";
}

.fd-calculator:before {
  content: "\e75a";
}

.fd-remind-fill:before {
  content: "\e85a";
}

.fd-icon-test:before {
  content: "\e602";
}

.fd-fuxuananniu:before {
  content: "\e8c9";
}

.fd-zujian:before {
  content: "\e634";
}

.fd-kaifazujian:before {
  content: "\e640";
}

.fd-zujian1:before {
  content: "\e609";
}

.fd-checkBoxList:before {
  content: "\e630";
}

.fd-bumenxuanze:before {
  content: "\e6b7";
}

.fd-pingfenqia:before {
  content: "\e684";
}

.fd-pingfen:before {
  content: "\e60f";
}

.fd-score:before {
  content: "\e642";
}

.fd-wenjianjia-zhankai:before {
  content: "\e68d";
}

.fd-wenjianjia-shouqi:before {
  content: "\e68e";
}

.fd-tianjia-yin:before {
  content: "\e68f";
}

.fd-chenggong-yin:before {
  content: "\e690";
}

.fd-shibai-yin:before {
  content: "\e691";
}

.fd-zhuyi:before {
  content: "\e693";
}

.fd-chenggong:before {
  content: "\e694";
}

.fd-shibai:before {
  content: "\e695";
}

.fd-guanliyuan:before {
  content: "\e696";
}

.fd-lingcun:before {
  content: "\e69c";
}

.fd-baocun:before {
  content: "\e69d";
}

.fd-shouqi:before {
  content: "\e69e";
}

.fd-zhankai:before {
  content: "\e69f";
}

.fd-suo:before {
  content: "\e6a0";
}

.fd-jiesuo:before {
  content: "\e6a1";
}

.fd-xiazai:before {
  content: "\e6a2";
}

.fd-shangchuan2:before {
  content: "\e6a3";
}

.fd-fabu:before {
  content: "\e6a4";
}

.fd-bianji:before {
  content: "\e6a5";
}

.fd-biaoqian:before {
  content: "\e6a6";
}

.fd-fenxiang:before {
  content: "\e6a7";
}

.fd-shaixuan:before {
  content: "\e6a8";
}

.fd-shoucang:before {
  content: "\e6a9";
}

.fd-shanchu:before {
  content: "\e6aa";
}

.fd-tuding:before {
  content: "\e6ab";
}

.fd-tuodong:before {
  content: "\e6ac";
}

.fd-shezhi:before {
  content: "\e6ad";
}

.fd-yulan:before {
  content: "\e6ae";
}

.fd-shuju:before {
  content: "\e6af";
}

.fd-tianjiashujuyuan:before {
  content: "\e6b0";
}

.fd-shujuji:before {
  content: "\e6b1";
}

.fd-fanhuishujuji:before {
  content: "\e6b2";
}

.fd-suodingshujuji:before {
  content: "\e6b3";
}

.fd-menhu:before {
  content: "\e6b4";
}

.fd-biaoge:before {
  content: "\e6b5";
}

.fd-xinjianbiaoge:before {
  content: "\e6b6";
}

.fd-jingdianyibiaoban:before {
  content: "\e6b9";
}

.fd-zifuchuanshujuji:before {
  content: "\e6bd";
}

.fd-shuzhixingshujuji:before {
  content: "\e6be";
}

.fd-jisuanzhibiao:before {
  content: "\e6c0";
}

.fd-shijianweidu:before {
  content: "\e6c2";
}

.fd-jiaochabiao:before {
  content: "\e6c4";
}

.fd-shijian:before {
  content: "\e6c6";
}

.fd-riqi:before {
  content: "\e6c7";
}

.fd-riqishijian:before {
  content: "\e6c8";
}

.fd-tongbubiaojiegou:before {
  content: "\e6c9";
}

.fd-youduiqi:before {
  content: "\e6cd";
}

.fd-juzhong:before {
  content: "\e6ce";
}

.fd-zuoduiqi:before {
  content: "\e6cf";
}

.fd-shangebuju:before {
  content: "\e6d3";
}

.fd-liushibuju:before {
  content: "\e6d4";
}

.fd-baobiao:before {
  content: "\e6d6";
}

.fd-kongjian:before {
  content: "\e6d7";
}

.fd-peizhi:before {
  content: "\e6d8";
}

.fd-tubiao-bingtu:before {
  content: "\e6da";
}

.fd-tubiao-zhuzhuangtu:before {
  content: "\e6db";
}

.fd-hanshu:before {
  content: "\e6dc";
}

.fd-fuzhi:before {
  content: "\e6dd";
}

.fd-niantie:before {
  content: "\e6de";
}

.fd-wenbenduiqi:before {
  content: "\e6e4";
}

.fd-duohangwenben:before {
  content: "\e6e5";
}

.fd-danhangwenben:before {
  content: "\e6e6";
}

.fd-duohangwenben1:before {
  content: "\e6e7";
}

.fd-hebingdanyuange:before {
  content: "\e6ea";
}

.fd-charulie:before {
  content: "\e6ed";
}

.fd-charuhang:before {
  content: "\e6ee";
}

.fd-shanchulie:before {
  content: "\e6ef";
}

.fd-shanchuhang:before {
  content: "\e6f0";
}

.fd-fenlie:before {
  content: "\e6f1";
}

.fd-hebingjisuan:before {
  content: "\e6f2";
}

.fd-fenxihuanyuan:before {
  content: "\e6f3";
}

.fd-beijingyanse:before {
  content: "\e6f8";
}

.fd-zitiyanse:before {
  content: "\e6f9";
}

.fd-hanshu1:before {
  content: "\e6fa";
}

.fd-huobi:before {
  content: "\e6fb";
}

.fd-paixu:before {
  content: "\e6fc";
}

.fd-xiexianbiaotou:before {
  content: "\e6fd";
}

.fd-jiacu:before {
  content: "\e6fe";
}

.fd-shanchuxian:before {
  content: "\e6ff";
}

.fd-xieti:before {
  content: "\e700";
}

.fd-xiahuaxian:before {
  content: "\e701";
}

.fd-quxiaoguanlian:before {
  content: "\e703";
}

.fd-hengxiangbuju:before {
  content: "\e71d";
}

.fd-jilianxuanze:before {
  content: "\e71e";
}

.fd-kaiguan:before {
  content: "\e71f";
}

.fd-xuanzeqi:before {
  content: "\e720";
}

.fd-riqixuanze:before {
  content: "\e721";
}

.fd-shuxiangbuju:before {
  content: "\e722";
}

.fd-anniu:before {
  content: "\e723";
}

.fd-bianhao:before {
  content: "\e614";
}

.fd-bangdingyonghu:before {
  content: "\e615";
}

.fd-fuwenben:before {
  content: "\e616";
}

.fd-dizhixuanze:before {
  content: "\e617";
}

.fd-dingwei:before {
  content: "\e618";
}

.fd-danxiangxuanze:before {
  content: "\e619";
}

.fd-biaotizujian:before {
  content: "\e61a";
}

.fd-duoxiangxuanze:before {
  content: "\e61b";
}

.fd-riqixuanze1:before {
  content: "\e61c";
}

.fd-jichubiaodan:before {
  content: "\e61d";
}

.fd-biaodanbiaoti:before {
  content: "\e61e";
}

.fd-liuchengcailiaobiao:before {
  content: "\e61f";
}

.fd-jiliandongxuanzeqi:before {
  content: "\e620";
}

.fd-tishi:before {
  content: "\e621";
}

.fd-shuzhuangjiegou:before {
  content: "\e622";
}

.fd-liebiaobiaodan:before {
  content: "\e623";
}

.fd-shangchuan:before {
  content: "\e624";
}

.fd-shixian:before {
  content: "\e625";
}

.fd-liushuihao:before {
  content: "\e626";
}

.fd-wujiliandongdizhi:before {
  content: "\e627";
}

.fd-xialaxuanze:before {
  content: "\e628";
}

.fd-sousuokuang:before {
  content: "\e629";
}

.fd-zhankaibiaodan:before {
  content: "\e62a";
}

.fd-zidongbuquanshurukuang:before {
  content: "\e62b";
}

