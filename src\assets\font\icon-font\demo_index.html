<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://gtms04.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1591582" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe682;</span>
                <div class="name">百分比</div>
                <div class="code-name">&amp;#xe682;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62b;</span>
                <div class="name">置顶</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64f;</span>
                <div class="name">圆形</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fc;</span>
                <div class="name">指标检测器</div>
                <div class="code-name">&amp;#xe7fc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">隐藏</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe769;</span>
                <div class="name">锁定</div>
                <div class="code-name">&amp;#xe769;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69f;</span>
                <div class="name">符号-文本</div>
                <div class="code-name">&amp;#xe69f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f7;</span>
                <div class="name">百分比</div>
                <div class="code-name">&amp;#xe6f7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">置顶</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a0;</span>
                <div class="name">播放器-弹幕滚动（开）_44</div>
                <div class="code-name">&amp;#xe6a0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">进度</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe677;</span>
                <div class="name">视频</div>
                <div class="code-name">&amp;#xe677;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67b;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe67b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">隐藏</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ae;</span>
                <div class="name">关键指标</div>
                <div class="code-name">&amp;#xe7ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">指标模板</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe632;</span>
                <div class="name">图表</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe627;</span>
                <div class="name">首页轮播图</div>
                <div class="code-name">&amp;#xe627;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70f;</span>
                <div class="name">上移</div>
                <div class="code-name">&amp;#xe70f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">fsux_图表_水球图</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ec;</span>
                <div class="name">多媒体-2</div>
                <div class="code-name">&amp;#xe6ec;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">指标库查询</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe790;</span>
                <div class="name">矩形</div>
                <div class="code-name">&amp;#xe790;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe791;</span>
                <div class="name">椭圆形</div>
                <div class="code-name">&amp;#xe791;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">上移</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe742;</span>
                <div class="name">超链接</div>
                <div class="code-name">&amp;#xe742;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">图标</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">移动</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">文本</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fd;</span>
                <div class="name">多媒体</div>
                <div class="code-name">&amp;#xe6fd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">文本颜色</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">圆形</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">带百分比的环形图</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fe;</span>
                <div class="name">双底框线_边框操作_jurassic</div>
                <div class="code-name">&amp;#xe6fe;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ff;</span>
                <div class="name">上框线和双下框线_边框操作_jurassic</div>
                <div class="code-name">&amp;#xe6ff;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8c3;</span>
                <div class="name">文本域</div>
                <div class="code-name">&amp;#xe8c3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8c4;</span>
                <div class="name">数字</div>
                <div class="code-name">&amp;#xe8c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">形状0</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-80"></span>
            <div class="name">
              百分比
            </div>
            <div class="code-name">.icon-80
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhiding"></span>
            <div class="name">
              置顶
            </div>
            <div class="code-name">.icon-zhiding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-circle"></span>
            <div class="name">
              圆形
            </div>
            <div class="code-name">.icon-circle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhibiaojianceqi"></span>
            <div class="name">
              指标检测器
            </div>
            <div class="code-name">.icon-zhibiaojianceqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hidden"></span>
            <div class="name">
              隐藏
            </div>
            <div class="code-name">.icon-hidden
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ali-"></span>
            <div class="name">
              锁定
            </div>
            <div class="code-name">.icon-ali-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenben1"></span>
            <div class="name">
              符号-文本
            </div>
            <div class="code-name">.icon-wenben1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baifenbi"></span>
            <div class="name">
              百分比
            </div>
            <div class="code-name">.icon-baifenbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhiding1"></span>
            <div class="name">
              置顶
            </div>
            <div class="code-name">.icon-zhiding1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bofangqi-danmugundongkai"></span>
            <div class="name">
              播放器-弹幕滚动（开）_44
            </div>
            <div class="code-name">.icon-bofangqi-danmugundongkai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jindu"></span>
            <div class="name">
              进度
            </div>
            <div class="code-name">.icon-jindu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shipin"></span>
            <div class="name">
              视频
            </div>
            <div class="code-name">.icon-shipin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tupian"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.icon-tupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yincang"></span>
            <div class="name">
              隐藏
            </div>
            <div class="code-name">.icon-yincang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guanjianzhibiao"></span>
            <div class="name">
              关键指标
            </div>
            <div class="code-name">.icon-guanjianzhibiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhibiaomoban"></span>
            <div class="name">
              指标模板
            </div>
            <div class="code-name">.icon-zhibiaomoban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tubiao2"></span>
            <div class="name">
              图表
            </div>
            <div class="code-name">.icon-tubiao2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouyelunbotu"></span>
            <div class="name">
              首页轮播图
            </div>
            <div class="code-name">.icon-shouyelunbotu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangyi"></span>
            <div class="name">
              上移
            </div>
            <div class="code-name">.icon-shangyi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fsux_tubiao_shuiqiutu"></span>
            <div class="name">
              fsux_图表_水球图
            </div>
            <div class="code-name">.icon-fsux_tubiao_shuiqiutu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duomeiti-"></span>
            <div class="name">
              多媒体-2
            </div>
            <div class="code-name">.icon-duomeiti-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhibiaokuchaxun"></span>
            <div class="name">
              指标库查询
            </div>
            <div class="code-name">.icon-zhibiaokuchaxun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-juxing"></span>
            <div class="name">
              矩形
            </div>
            <div class="code-name">.icon-juxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuoyuanxing"></span>
            <div class="name">
              椭圆形
            </div>
            <div class="code-name">.icon-tuoyuanxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-upon"></span>
            <div class="name">
              上移
            </div>
            <div class="code-name">.icon-upon
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chaolianjie"></span>
            <div class="name">
              超链接
            </div>
            <div class="code-name">.icon-chaolianjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tubiao"></span>
            <div class="name">
              图标
            </div>
            <div class="code-name">.icon-tubiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yidong"></span>
            <div class="name">
              移动
            </div>
            <div class="code-name">.icon-yidong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenben"></span>
            <div class="name">
              文本
            </div>
            <div class="code-name">.icon-wenben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duomeiti"></span>
            <div class="name">
              多媒体
            </div>
            <div class="code-name">.icon-duomeiti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ziyuan"></span>
            <div class="name">
              文本颜色
            </div>
            <div class="code-name">.icon-ziyuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yuanxing"></span>
            <div class="name">
              圆形
            </div>
            <div class="code-name">.icon-yuanxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daibaifenbidehuanxingtu"></span>
            <div class="name">
              带百分比的环形图
            </div>
            <div class="code-name">.icon-daibaifenbidehuanxingtu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jurassic_border-double"></span>
            <div class="name">
              双底框线_边框操作_jurassic
            </div>
            <div class="code-name">.icon-jurassic_border-double
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jurassic_border-sdxs"></span>
            <div class="name">
              上框线和双下框线_边框操作_jurassic
            </div>
            <div class="code-name">.icon-jurassic_border-sdxs
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenbenyu"></span>
            <div class="name">
              文本域
            </div>
            <div class="code-name">.icon-wenbenyu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuzi"></span>
            <div class="name">
              数字
            </div>
            <div class="code-name">.icon-shuzi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuang"></span>
            <div class="name">
              形状0
            </div>
            <div class="code-name">.icon-xingzhuang
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-80"></use>
                </svg>
                <div class="name">百分比</div>
                <div class="code-name">#icon-80</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhiding"></use>
                </svg>
                <div class="name">置顶</div>
                <div class="code-name">#icon-zhiding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-circle"></use>
                </svg>
                <div class="name">圆形</div>
                <div class="code-name">#icon-circle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhibiaojianceqi"></use>
                </svg>
                <div class="name">指标检测器</div>
                <div class="code-name">#icon-zhibiaojianceqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hidden"></use>
                </svg>
                <div class="name">隐藏</div>
                <div class="code-name">#icon-hidden</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ali-"></use>
                </svg>
                <div class="name">锁定</div>
                <div class="code-name">#icon-ali-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenben1"></use>
                </svg>
                <div class="name">符号-文本</div>
                <div class="code-name">#icon-wenben1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baifenbi"></use>
                </svg>
                <div class="name">百分比</div>
                <div class="code-name">#icon-baifenbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhiding1"></use>
                </svg>
                <div class="name">置顶</div>
                <div class="code-name">#icon-zhiding1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bofangqi-danmugundongkai"></use>
                </svg>
                <div class="name">播放器-弹幕滚动（开）_44</div>
                <div class="code-name">#icon-bofangqi-danmugundongkai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jindu"></use>
                </svg>
                <div class="name">进度</div>
                <div class="code-name">#icon-jindu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shipin"></use>
                </svg>
                <div class="name">视频</div>
                <div class="code-name">#icon-shipin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tupian"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#icon-tupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yincang"></use>
                </svg>
                <div class="name">隐藏</div>
                <div class="code-name">#icon-yincang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanjianzhibiao"></use>
                </svg>
                <div class="name">关键指标</div>
                <div class="code-name">#icon-guanjianzhibiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhibiaomoban"></use>
                </svg>
                <div class="name">指标模板</div>
                <div class="code-name">#icon-zhibiaomoban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tubiao2"></use>
                </svg>
                <div class="name">图表</div>
                <div class="code-name">#icon-tubiao2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouyelunbotu"></use>
                </svg>
                <div class="name">首页轮播图</div>
                <div class="code-name">#icon-shouyelunbotu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangyi"></use>
                </svg>
                <div class="name">上移</div>
                <div class="code-name">#icon-shangyi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fsux_tubiao_shuiqiutu"></use>
                </svg>
                <div class="name">fsux_图表_水球图</div>
                <div class="code-name">#icon-fsux_tubiao_shuiqiutu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duomeiti-"></use>
                </svg>
                <div class="name">多媒体-2</div>
                <div class="code-name">#icon-duomeiti-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhibiaokuchaxun"></use>
                </svg>
                <div class="name">指标库查询</div>
                <div class="code-name">#icon-zhibiaokuchaxun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-juxing"></use>
                </svg>
                <div class="name">矩形</div>
                <div class="code-name">#icon-juxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuoyuanxing"></use>
                </svg>
                <div class="name">椭圆形</div>
                <div class="code-name">#icon-tuoyuanxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-upon"></use>
                </svg>
                <div class="name">上移</div>
                <div class="code-name">#icon-upon</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaolianjie"></use>
                </svg>
                <div class="name">超链接</div>
                <div class="code-name">#icon-chaolianjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tubiao"></use>
                </svg>
                <div class="name">图标</div>
                <div class="code-name">#icon-tubiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yidong"></use>
                </svg>
                <div class="name">移动</div>
                <div class="code-name">#icon-yidong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenben"></use>
                </svg>
                <div class="name">文本</div>
                <div class="code-name">#icon-wenben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duomeiti"></use>
                </svg>
                <div class="name">多媒体</div>
                <div class="code-name">#icon-duomeiti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ziyuan"></use>
                </svg>
                <div class="name">文本颜色</div>
                <div class="code-name">#icon-ziyuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuanxing"></use>
                </svg>
                <div class="name">圆形</div>
                <div class="code-name">#icon-yuanxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daibaifenbidehuanxingtu"></use>
                </svg>
                <div class="name">带百分比的环形图</div>
                <div class="code-name">#icon-daibaifenbidehuanxingtu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jurassic_border-double"></use>
                </svg>
                <div class="name">双底框线_边框操作_jurassic</div>
                <div class="code-name">#icon-jurassic_border-double</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jurassic_border-sdxs"></use>
                </svg>
                <div class="name">上框线和双下框线_边框操作_jurassic</div>
                <div class="code-name">#icon-jurassic_border-sdxs</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenyu"></use>
                </svg>
                <div class="name">文本域</div>
                <div class="code-name">#icon-wenbenyu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuzi"></use>
                </svg>
                <div class="name">数字</div>
                <div class="code-name">#icon-shuzi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuang"></use>
                </svg>
                <div class="name">形状0</div>
                <div class="code-name">#icon-xingzhuang</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
