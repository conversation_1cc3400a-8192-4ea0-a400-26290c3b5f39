/**公共基础样式*/

.flex {
  display: flex;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.justify-evenly {
  justify-content: space-evenly;
}
.flex-1 {
  flex: 1;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.w-15 {
  width: 15%;
}

.w-18 {
  width: 18% !important;
}

.w-20 {
  width: 20%;
}

.w-30 {
  width: 30%;
}

.w-33 {
  width: 33.3333%;
}

.w-35 {
  width: 35%;
}

.w-40 {
  width: 40%;
}

.w-50 {
  width: 50%;
}

.w-60 {
  width: 60%;
}

.w-65 {
  width: 65%;
}

.w-66 {
  width: 66.6666%;
}

.w-70 {
  width: 70%;
}

.w-80 {
  width: 80%;
}

.w-82 {
  width: 82% !important;
}

.w-85 {
  width: 85%;
}

.w-100 {
  width: 100%;
}

.fs-8 {
  font-size: 8px;
}

.fs-9 {
  font-size: 9px;
}

.fs-10 {
  font-size: 10px;
}

.fs-12 {
  font-size: 12px;
}

.fs-16 {
  font-size: 16px;
}

.fs-18 {
  font-size: 18px;
}

.fs-24 {
  font-size: 24px;
}

.fs-36 {
  font-size: 36px;
}

.c-333 {
  color: #333;
}

.c-666 {
  color: #666;
}

.c-999 {
  color: #999;
}

.m0 {
  margin: 0;
}
.mt0 {
  margin-top: 0;
}
.mb0 {
  margin-bottom: 0;
}
.ml0 {
  margin-left: 0;
}
.mr0 {
  margin-right: 0;
}

.m5 {
  margin: 5px 5px;
}

.m10 {
  margin: 10px 10px;
}

.mt10 {
  margin-top: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.mr5 {
  margin-right: 5px;
}

.mr6 {
  margin-right: 6px;
}

.mr8 {
  margin-right: 8px;
}

.mr10 {
  margin-right: 10px;
}

.mr12 {
  margin-right: 12px;
}

.mr14 {
  margin-right: 14px;
}

.mr16 {
  margin-right: 16px;
}

.mr18 {
  margin-right: 18px;
}

.mr20 {
  margin-right: 20px;
}

.mr22 {
  margin-right: 22px;
}

.mr24 {
  margin-right: 24px;
}

.mr26 {
  margin-right: 26px;
}

.mr28 {
  margin-right: 28px;
}

.ml2 {
  margin-left: 2px;
}

.ml4 {
  margin-left: 4px;
}

.ml6 {
  margin-left: 6px;
}

.ml8 {
  margin-left: 8px;
}

.ml10 {
  margin-left: 10px;
}

.ml12 {
  margin-left: 12px;
}

.ml14 {
  margin-left: 14px;
}

.ml16 {
  margin-left: 16px;
}

.ml18 {
  margin-left: 18px;
}

.ml20 {
  margin-left: 20px;
}

.mlr5 {
  margin-left: 5px;
  margin-right: 5px;
}

.mlr10 {
  margin-left: 10px;
  margin-right: 10px;
}

.lh-5 {
  line-height: 5px;
}

.lh-10 {
  line-height: 10px;
}

.lh-16 {
  line-height: 16px;
}

.lh-20 {
  line-height: 20px;
}

.lh-24 {
  line-height: 24px;
}

.lh-30 {
  line-height: 30px;
}

.p5 {
  padding: 5px 5px;
}

.pl5 {
  padding-left: 5px;
}

.pr5 {
  padding-right: 5px;
}
.pl8 {
  padding-left: 8px;
}
.pr8 {
  padding-right: 8px;
}
.p10 {
  padding: 10px 10px;
}
.plr16 {
  padding: 0 16px;
}

.pl10 {
  padding-left: 10px;
}

.pr10 {
  padding-right: 10px;
}

.plr10 {
  padding: 0px 10px;
}

.pl25 {
  padding-left: 25px;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.col-2 {
  width: 16.66666666%;
}

.col-3 {
  width: 25%;
}

.col-4 {
  width: 33.33333333%;
}

.col-5 {
  width: 41.66666666%;
}

.col-6 {
  width: 50%;
}

.col-7 {
  width: 58.33333333%;
}

.col-8 {
  width: 66.66666666%;
}

.col-9 {
  width: 75%;
}

.col-10 {
  width: 83.33333333%;
}

.bw-1 {
  border-width: 1px !important;
}

.bw-2 {
  border-width: 2px;
}

.opacity50 {
  opacity: .5;
}

.no-select-pointer {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  cursor: pointer;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
/* 
*::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

*::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: #5E708D;
}

*::-webkit-scrollbar-track {
  border-radius: 4px;
  background: #ddd;
} */

/*=============================全局滚动条滑块样式修改============================*/
/* 滚动条 */
::-webkit-scrollbar-track-piece {
  -webkit-border-radius: 999;
  /*滚动条的圆角宽度*/
}


::-webkit-scrollbar-track {
  background: transparent;
  /* 滚动条轨道背景颜色 */
}

::-webkit-scrollbar {
  width: 4px;
  /*滚动条的宽度*/
  height: 8px;
  /*滚动条的高度*/
}

::-webkit-scrollbar-thumb:vertical {
  /*垂直滚动条的样式*/
  height: 50px;
  background: #424243;
  -webkit-border-radius: 4px;
  outline-offset: -2px;
}

::-webkit-scrollbar-thumb {
  /*滚动条的hover样式*/
  background-color: rgba(159, 159, 159, 0.3);
  -webkit-border-radius: 4px;
}

/*=============================设置滑动条透明(表格图层使用)============================*/
.overflow-transparent::-webkit-scrollbar{
  width: 0px;
}

.overflow-transparent::-webkit-scrollbar-track {
  background-color: transparent;
}

.overflow-transparent::-webkit-scrollbar-thumb {
  background-color: transparent;
}

/*散点1*/
@keyframes A-S5WA7OWIK6EY {
  0% {
    opacity: 100;
    -webkit-opacity: 100;
    -moz-opacity: 100;
    -o-opacity: 100;
    -ms-opacity: 100;
  }
  33% {
    opacity: 0;
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -o-opacity: 0;
    -ms-opacity: 0;
  }

  66% {
    opacity: 0;
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -o-opacity: 0;
    -ms-opacity: 0;
  }

  99% {
    opacity: 0;
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -o-opacity: 0;
    -ms-opacity: 0;
  }
}

/*散点2*/
@keyframes A-DG2E62MP0ESX {
  0% {
    opacity: 0;
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -o-opacity: 0;
    -ms-opacity: 0;
  }
  33% {
    opacity: 0;
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -o-opacity: 0;
    -ms-opacity: 0;
  }

  66% {
    opacity: 100;
    -webkit-opacity: 100;
    -moz-opacity: 100;
    -o-opacity: 100;
    -ms-opacity: 100;
  }

  99% {
    opacity: 0;
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -o-opacity: 0;
    -ms-opacity: 0;
  }
}

/*散点3*/
@keyframes A-U1TDE31QCWAQ {
  0% {
    opacity: 0;
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -o-opacity: 0;
    -ms-opacity: 0;
  }
  33% {
    opacity: 0;
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -o-opacity: 0;
    -ms-opacity: 0;
  }

  66% {
    opacity: 0;
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -o-opacity: 0;
    -ms-opacity: 0;
  }

  99% {
    opacity: 100;
    -webkit-opacity: 100;
    -moz-opacity: 100;
    -o-opacity: 100;
    -ms-opacity: 100;
  }
}