/*############数据集新增弹窗###########*/
.data-set-add-wrap {
  width: 100%;
  height: 100%;
  padding: 20px 20px 0 20px;
  display: flex;
}
.data-set-add-wrap .add-item-wrap {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-left: 12px;
  height: 100%;
  background: #141414;
  cursor: pointer;
}
.data-set-add-wrap .add-item-wrap:first-child {
  margin-left: 0;
}
.add-item-wrap .add-item-text {
  width: 100%;
  height: 20%;
  font-weight: 400;
  font-size: 14px;
  color: #CFD3DC;
  display: flex;
  align-items: center;
  justify-content: center;
}
.add-item-wrap .add-item {
  width: 100%;
  height: 80%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #262727;
}
.add-item-wrap .add-item:hover {
  background: #333333;
}
.add-item-wrap .add-item-svg {
  display: inline-block;
  width: 80px;
  height: 80px;
  color: #409eff;
}
.add-item-wrap .add-item-icon-excel {
  background: url("../assets/svg/excel.svg") no-repeat 100% 100%;
}
.add-item-wrap .add-item-icon-csv {
  background: url("../assets/svg/csv.svg") no-repeat 100% 100%;
}
.add-item-wrap .add-item-icon-json {
  background: url("../assets/svg/json.svg") no-repeat 100% 100%;
}
.add-item-wrap .add-item-icon-model {
  background: url("../assets/svg/model.svg") no-repeat 100% 100%;
}
.file-import-input {
  width: 0;
  height: 0;
  opacity: 0;
}
/*############弹框内容[左右结构]###########*/
.data-wrap {
  box-sizing: border-box;
  display: flex;
  height: 100%;
  width: 100%;
  padding: 5px;
}

.data-wrap .data-left {
  box-sizing: border-box;
  height: 100%;
  min-width: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 13px;
  border-right: 1px solid #414243;
  /* box-shadow: 1px 0 1px rgba(42, 171, 210, 0.80); */
}

.data-wrap .data-right {
  box-sizing: border-box;
  height: 100%;
  width: 100%;
}

.data-wrap .data-middle {
  height: 100%;
  width: 100%;
}

/*############弹框内容[卡片结构/标题结构]###########*/
.com-card .com-head {
  line-height: 36px;
  /* box-shadow: 0 1px 2px #2B3340; */
  background-color: #3F4B5F;
}

.com-card .com-body {
  padding: 5px 0px;
  width: 100%;
  overflow-y: auto;
}

.com-head .head-title {
  font-size: 16px;
  /* text-indent: 1em; */
  color: #fff;
}

/*############弹框内容[数据模型]###########*/
.com-wrap .com-head {
  margin-left: 10px;
  margin-bottom: 10px;
}

/*############弹框内容[树结构]###########*/
.com-tree .tree-head {
  height: 40px;
  line-height: 40px;
  border-radius: 0;
}

.com-tree .tree-head .head-icon {
  width: 20px;
  font-size: 8px;
  text-align: center;
  color: #CFD3DC;
  margin-left: 8px;
}

.com-tree .icon-xiala {
  transform: rotate(90deg);
}

.com-tree .tree-body {
  padding-left: 10px;
}

.com-tree .body-line {
  box-shadow: -1px 0px 0px rgba(42, 171, 210, 0.50);
}

.com-tree .tree-head:hover {
  background: #1D3043;
  cursor: pointer;
}

.tree-head .head-title {
  font-size: 14px;
  color: #CFD3DC;
  margin-left: 8px;
}

/* 树形选中 */
.com-tree .tree-selected {
  color: #409EFF;
}

/* 弹窗树形单选/多选 */
/* 单选 */
.com-tree .radio-container {
  height: 14px;
  width: 14px;
  background-color: #ffffff;
  border-radius: 50%;
  position: relative;
  top: 13px;
  transition: background-color 0.3s, border-color 0.3s;
}

.com-tree .tree-checked {
  background-color: #2196F3;
  border-color: #2196F3;
}

.com-tree .radio-container .checkMark {
  position: absolute;
  height: 5px;
  width: 5px;
  background-color: #ffffff;
  border-radius: 50%;
  top: 5px;
  left: 4px;
}

/* 多选 */

.com-tree .multiple-container {
  height: 14px;
  width: 14px;
  background-color: #ffffff;
  position: relative;
  top: 13px;
  transition: background-color 0.3s, border-color 0.3s;
  border-radius: 2px 2px 2px 2px;
}

.com-tree .tree-checked {
  background-color: #2196F3;
  border-color: #2196F3;
}

.com-tree .multiple-container .multiple {
  position: absolute;
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  top: -13px;
  left: 1px;
}

/*############通用[表格抽象类使用]###########*/
.com-body {
  box-sizing: border-box;
}

/* .com-list {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 5px;
} */

/*############弹框[图片]列表###########*/
.com-image-list {
  overflow: auto;
  box-sizing: border-box;
  margin-left: 24px;
  display: flex;
  flex-wrap: wrap;
  gap: 18px;
  padding: 2px;

}

.com-image-list .image-item {
  box-sizing: border-box;
  position: relative;
  width: 31%;
}

.com-image-list .image-item .item-block {
  height: 130px;
  text-align: center;
  background-color: #3F4B5F;
}

.com-image-list .image-item .item-img {
  height: 100px;
  width: 100%;
}

.com-image-list .image-item .item-title {
  line-height: 30px;
  font-size: 13px;
  color: #ccc;
  /* text-align: center; */
}

.com-image-list .item-triangle {
  width: 0;
  height: 0;
  border-top: 16px solid transparent;
  border-bottom: 16px solid transparent;
  border-left: 16px solid #67C23A;
  transform: rotateZ(-45deg);
  position: absolute;
  top: -9px;
  right: -2px;
  display: none;
}

.com-image-list .item-icon {
  color: #fff;
  transform: rotateZ(45deg);
  position: absolute;
  top: -5px;
  right: 4px;
  font-size: 12px;
  font-weight: bold;
}

.com-image-list .image-item .item-title {
  line-height: 30px;
  font-size: 13px;
  color: #ccc;
}

.com-image-list .image-item:hover {
  outline: 1px solid #409EFF;
}

/*===========弹框[组件/模板](生成缩略图-测试)===========*/
/* .com-col {
  width: 100%;
}

.com-col .com-img-btn {
  width: 100%;
}

.com-col .com-img {
  height: 160px;
  width: 100%;
  background-color: #ccc;
} */

/*===========弹框[内容](编辑行)===========*/
.com-row {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 22px;
  line-height: 30px;
  display: flex;
}

.com-row:last-child {
  margin-bottom: 0px;
}

.com-row .com-label {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  min-width: 140px;
  font-size: 14px;
  color: #CFD3DC;
  margin-right: 12px;
}

.com-row .com-label .com-label-icon__info {
  font-size: 13px;
  margin-left: 5px;
  color: #909399;
  cursor: pointer;
  position: relative;
}

.com-row .com-label .com-label-icon__info::after {
  content: attr(data-tooltip);
  width: 156px;
  height: 57px;
  padding: 12px;
  position: absolute;
  top: 30px;
  left: -65px;
  z-index: 999999;
  background: rgba(0,0,0,0.8);
  border-radius: 4px 4px 4px 4px;
  font-size: 12px;
  color: #FFFFFF;
  line-height: 16px;
  visibility: hidden;
  word-break: break-all;
}
.com-row .com-label .com-label-icon__info:hover::after {
  visibility: visible;
}

.com-row .com-control {
  /* float: left;
  flex: 1; */
  width: 100%;
  display: flex;
  position: relative;
  align-items: center;
}

.com-control .radio-group {
  display: flex;
  align-items: center;
  position: relative;
  white-space: nowrap;
}
.radio-group .auth_login_tip {
  position: absolute;
  top: 23px;
  left: 0;
  font-weight: 400;
  font-size: 12px;
  color: #F56C6C;
  display: none;
  white-space: nowrap;
}
.radio-group > input[type='radio'] {
  width: 14px;
  height: 14px;
  color: white;
  margin-right: 8px;
  cursor: pointer;
  /* 去掉焦点时的轮廓 */
  outline: none;
  /* 移除iOS上的内置样式 */
  -webkit-appearance: none;
  /* 移除Firefox上的内置样式 */
  -moz-appearance: none;
  /* 将来浏览器标准化后使用 */
  appearance: none;

  background: #ffffff;
  border-radius: 999px 999px 999px 999px;
  border: 1px solid #4C4D4F;

}
.radio-group > input[type='radio']:checked {
  border: 4px solid #409eff;
}
.radio-group > label {
  font-weight: 600;
  font-size: 14px;
  color: #CFD3DC;
  margin-right: 24px;
  cursor: pointer;
  user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
}
.radio-group > label:last-child {
  margin-right: 0;
}

.com-row .com-control .lsd-date-filter {
  font-weight: 400;
  font-size: 14px;
  color: #8D9095;
  position: relative;
  padding: 10px 0 10px 20px;
}

.com-row .com-control:has(.lsd-date-filter):after {
  content: "\e63a";
  font-family: "ft-font";
  font-size: 13px;
  color: #8D9095;
  position: absolute;
  z-index: 1;
  display: block;
  top: 2px;
  left: 13px;
}

/*只针对输入框右侧有按钮*/
.com-row .com-control:has(.text-btn) > .com-text {
  padding-right: 98px;
}

.com-row .com-control .com-text {
  color: #CFD3DC;
}

.com-row .com-control .chart-text {
  color: #CFD3DC;
  font-size: 14px;
}

.com-row .com-control .com-number {
  color: #CFD3DC;
}

.com-row .com-control input::-webkit-textfield-decoration-container {
  height: 32px;
}

.com-row .com-control .chart-number {
  color: #CFD3DC;
}
/* 自定义[select]的样式 */

.com-row .com-control select {
  line-height: 32px;
  width: 100%;
  /* 去掉焦点时的轮廓 */
  outline: none;
  /* 移除iOS上的内置样式 */
  -webkit-appearance: none;
  /* 移除Firefox上的内置样式 */
  -moz-appearance: none;
  /* 将来浏览器标准化后使用 */
  appearance: none;
  /* 设置背景色为透明 */
  background: #000000;

  height: 32px;
  font-weight: 400;
  font-size: 14px;
  color: #8D9095;

  border-radius: 4px 4px 4px 4px;
  border: 1px solid #4C4D4F;
  padding: 0 8px;
}
.com-row .com-control select option {
  outline: none;
  line-height: 32px;
  width: 100%;
  /* 去掉焦点时的轮廓 */
  /* 移除iOS上的内置样式 */
  -webkit-appearance: none;
  /* 移除Firefox上的内置样式 */
  -moz-appearance: none;
  /* 将来浏览器标准化后使用 */
  appearance: none;
  /* 去除边框 */
  border: none;
  /* 设置背景色为透明 */
  background-color: #191919;

  height: 32px;
  font-weight: 400;
  font-size: 14px;
  color: #CFD3DC;
}

.com-row .com-control:has(.com-select):after {
  content: "\e790";
  font-family: "ft-font";
  font-size: 12px;
  transform: rotateZ(-90deg);
  color: #A3A6AD;
  position: absolute;
  z-index: 1;
  display: block;
  top: 2px;
  right: 15px;
}


/* 编辑内容tab切换 */
.com-row .bgType-tabs {
  display: flex;
}

.com-row .bgType-tabs input {
  display: none;
}

.com-row .bgType-tabs label {
  padding: 1px 16px;
  font-weight: 400;
  font-size: 14px;
  color: #CFD3DC;
  background: #000000;
  border: 1px solid #4C4D4F;
  cursor: pointer;
}

.com-row .tab-content {
  display: none;
}

.com-row .bgType-tabs input:checked+label {
  background: #409EFF;
}

.com-row .tabs input:checked+label+.tab-content {
  display: block;
}

/* 编辑内容开关样式 */
.com-row .preview-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.com-row .preview-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.com-row .preview-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.com-row .preview-switch .slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  top: 2px;
  border-radius: 50%;
  background-color: white;
  transition: .4s;
}

.com-row .preview-switch input:checked+.slider {
  background-color: #2196F3;
}

.com-row .preview-switch input:checked+.slider:before {
  transform: translateX(20px);
}

.com-row .preview-switch input:focus+.slider {
  box-shadow: 0 0 1px #2196F3;
}

.com-row .com-btn {
  width: 12px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  color: #fff;
  position: absolute;
  right: 0px;
  /* Green */
}

.com-row .com-icon {
  cursor: pointer;
  font-size: 20px;
  color: #A3A6AD;
  position: absolute;
  right: 10px;
}


.com-row .text-btn {
  width: 88px;
  margin-right: 1px;
  background: #262727;
  font-weight: 400;
  font-size: 14px;
  color: #909399;
  border-radius: 0 4px 4px 0;
  border-left: 1px solid #4C4D4F;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

.com-row .com-btn:hover {
  /* background-color: #23b7e5; */
  /* background-color:#2aabd2; */
}

.com-row .com-font {
  font-size: 20px;
  color: #A3A6AD;
}

.com-row .sp-replacer {
  height: 32px;
  border: 1px solid #4C4D4F;
  border-radius: 4px 4px 4px 4px;

  padding: 0px;
}

.com-row .sp-replacer .sp-preview {
  margin: 6px 0px 6px 12px;
  border-radius: 4px 4px 4px 4px;
  right: 44px;
}

.com-row .sp-replacer .sp-preview .sp-preview-inner {
  border-radius: 4px 4px 4px 4px;
}

.com-row .sp-replacer .sp-dd {
  padding: 0;
  width: 32px;
  height: 32px;
  line-height: 32px;
  background: #262727;
  color: #8D9095;
  font-size: 10px;
  border-left: 1px solid #4C4D4F;
  border-radius: 0px 4px 4px 0px;
}

.com-row select {
  /* line-height: 30px; */
  height: 32px;
  background: #000000;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #4C4D4F;
  color: #8D9095;
  /* color: #CFD3DC; */
  width: 100%;
  outline: none;
}


.com-row select option {
  background-color: #000000;
}

.com-row input {
  /* line-height: 30px; */
  height: 32px;
  background: #000000;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #4C4D4F;
  width: 100%;
  outline: none;
  text-indent: 12px;
}

.com-row input[type="checkbox"] {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  /* line-height: 16px; */
  position: relative;
  top: -3px;
  width: 18px;
  height: 18px;
  margin-right: 5px;
}

.com-row textarea {
  height: 100px;
  border: solid 1px #cfdadd;
  width: 100%;
  outline: none;
  color: rgb(100, 100, 100);
}

.com-control img {
  width: 100%;
  height: 100%;
  outline: none;
}

/* 错误校验 */
.com-verify .errorHint {
  position: absolute;
  top: 25px;
  font-size: 12px;
  color: #F56C6C;
}

.com-verify .errorLine {
  border: solid 1px #F56C6C !important;
}

/*.com-row button:active*/
.com-control select:hover,
.com-control select:focus,
.com-control input:hover,
.com-control input:focus,
.com-control img:focus {
  border: solid 1px #23b7e5;
}

/*===========弹框过滤输入框===========*/
.model-list-wrap .model-content {
  position: relative;
}

.model-list-wrap .model-content input {
  padding-left: 28px;
  margin-right: 78px;
  margin-left: 24px;
}

.model-list-wrap .model-content .com-font {
  position: absolute;
  font-size: 12px;
  left: 36px;
}

.model-list-wrap .model-content .com-btn {
  width: 60px;
  height: 32px;
  background: #409EFF;
  border-radius: 4px 4px 4px 4px;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 32px;
}

/*===========弹框输入框===========*/
.data-wrap .data-title {
  height: 22px;
  margin-bottom: 8px;
  margin-left: 10px;
  font-weight: 400;
  font-size: 16px;
  color: #CFD3DC;
  line-height: 22px;
  text-align: left;
}

.data-wrap .data-content {
  height: 32px;
  margin-left: 10px;
  background: #000000;
}

.data-wrap .data-content .data-input {
  width: 100%;
  padding: 6px 13px;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  background: #000000;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #000000;
  text-align: left;
  /* 去掉焦点时的轮廓 */
  outline: none;
  /* 移除iOS上的内置样式 */
  -webkit-appearance: none;
  /* 移除Firefox上的内置样式 */
  -moz-appearance: none;
  /* 将来浏览器标准化后使用 */
  appearance: none;
}

.data-wrap .data-content .data-input:focus {
  border: 1px solid #409EFF;
}

/*===========消息中心===========*/
.dialog-model-add-wrap {
  padding: 8px 12px;
  margin: 8px;
  background-color: #262727;
  border-radius: 4px 4px 4px 4px;
  display: flex;
  justify-content: space-between;
  color: #a3a6ad;
}
.dialog-model-add-wrap .add-title {
  font-size: 14px;
  margin-right: 8px;
  display: flex;
  align-items: center;
}
.dialog-model-add-wrap .add-icon {
  font-size: 20px;
  padding: 6px;
  cursor: pointer;
}
.message-list-wrap {
  background-color: #262727;
  border-radius: 10px;
}
.message-list-wrap .message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #a3a6ad;
  cursor: pointer;
}
.message-header .header-text {
  font-size: 14px;
}
.message-header .chart-control {
  background-color: #141414;
  display: flex;
  align-items: center;
}
.message-header .chart-control .chart-text {
  font-size: 14px;
  height: 24px;
}
.message-header .header-icon {
  cursor: pointer;
  margin-right: 5px;
  font-size: 16px;
}
/* 鼠标经过显示编辑图标 */
.message-header:hover .header-wrap .header-icon {
  visibility: visible;
}
/* 默认隐藏编辑图标 */
.message-header .header-wrap .header-icon {
  visibility: hidden;
}
/* mode='view'隐藏输入框 */
.message-header[mode='view'] .chart-control {
  display: none;
}
/* mode='edit'隐藏标题 */
.message-header[mode='edit'] .header-wrap {
  display: none;
}
.message-list-wrap .header-icon[switch="open"] {
  transform: rotate(0deg);
}
.message-list-wrap .header-icon[switch="close"] {
  transform: rotate(90deg);
}
.message-list-wrap .message-body-wrap {
  margin-top: 10px;
  padding: 10px;
}

.link-add-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #a3a6ad;
  margin-top: 10px;
}
.link-add-wrap .add-title {
  font-size: 13px;
  margin-right: 8px;
  display: flex;
  align-items: center;
}
.link-add-wrap .add-icon {
  font-size: 19px;
  padding: 6px;
  cursor: pointer;
}

.link-list-wrap {
  margin-top: 10px;
}
.link-list-wrap .link-item-wrap {
  border: 1px solid #a3a6ad;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
}
.link-item-wrap .item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 0;
  cursor: pointer;
}
.link-item-wrap .item-title {
  font-size: 12px;
  color: #a3a6ad;
}

.link-item-wrap .header-icon {
  font-size: 14px;
  color: #a3a6ad;
  margin-right: 5px;
  cursor: pointer;
}