/*############通用[结构]###########*/
.comm-wrap .comm-head {
  box-sizing: border-box;
  width: 100%;
  line-height: 30px;
  display: flex;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
}

.comm-wrap .comm-body {
  box-sizing: border-box;
  /* padding: 5px; */
}

/*属性面板表格去掉内边距*/
.comm-wrap .comm-body .ltab-wrap {
  padding: 0;
}

.comm-head .head-title {
  width: 100%;
  /* text-indent: 1em; */
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.comm-head .head-icon {
  width: 20px;
  text-align: center;
  color: #2aabd2;
  margin-left: 8px;
}

.comm-head .head-btn {
  width: 50px;
  text-align: center;
  font-size: 13px;
  color: #ccc;
}

.comm-head .head-add {
  font-weight: 600;
  font-size: 12px;
  color: #409EFF;
}

.comm-head .head-del {
  font-weight: 600;
  font-size: 12px;
  color: #F56C6C;
}

.tree-selected {
  color: #2aabd2;
}
/*############通用[结构-面板]###########*/
.panel-wrap .panel-head {
  line-height: 36px;
  box-shadow: 0 1px 2px #2B3340;
}

.panel-head .head-title {
  font-size: 16px;
  text-align: center;
}

.panel-wrap .panel-body {
  padding: 5px;
}

/*############通用[结构-菜单]###########*/
.menu-wrap .menu-head {
  line-height: 40px;
  background: #303030;
  font-weight: 400;
  font-size: 14px;
  color: #E5EAF3;
  border-bottom: 1px solid #0e0e0e;
}
.menu-wrap .menu-body {
  padding: 9px;
}

.menu-head .head-title {
  font-size: 16px;
  text-indent: 4px;
}

/*############通用[结构-卡片]###########*/

.card-wrap .card-head {
  line-height: 30px;
  border-bottom: 1px solid #323334;
  padding: 6px 16px;
  background: #262727;
}

.card-head .head-title {
  font-weight: 400;
  font-size: 14px;
  color: #CFD3DC;
  line-height: 30px;
}

.card-wrap .card-head .head-icon {
  width: 12px;
  font-size: 12px;
  text-align: center;
  color: #ffffff;
  margin-left: 8px;
}

.card-wrap .card-head .head-icon[switch='close'] {
  transform: rotateZ(90deg);
}
.card-wrap .card-head .head-icon[switch='open'] {
  transform: rotateZ(0deg);
}

.card-wrap .card-body {
  margin: 8px;
  padding: 10px 12px;
  background: #262727;
  border-radius: 4px 4px 4px 4px;
}

.card-wrap .head-bg {
  background-color: #0a859f;
}

/*############通用[结构-树]###########*/
.tree-wrap {
  background: #262727;
}
.tree-wrap .tree-head {
  height: 40px;
  line-height: 40px;
  border-radius: 0;
}

.tree-wrap .tree-head .head-icon {
  width: 10px;
  font-size: 10px;
  text-align: center;
  color: #ffffff;
  margin: 0 8px;
}
.tree-wrap .tree-head .head-icon[switch='close'] {
  transform: rotateZ(-90deg);
}
.tree-wrap .tree-head .head-icon[switch='open'] {
  transform: rotateZ(0deg);
}

.tree-wrap .tree-head:hover {
  background: #1D3043;
  cursor: pointer;
}

.tree-head .head-title {
  font-size: 14px;
  color: #CFD3DC;
}

.tree-wrap .tree-body {
  padding-left: 10px;
  display: flex;
  flex-direction: column;
}
.tree-wrap .tree-body .chart-item:first-child {
  margin-top: 10px;
}
.tree-wrap .tree-body .chart-item .chart-control {
  margin-right: 10px;
}

.tree-wrap .body-line {
  box-shadow: -1px 0px 0px rgba(42, 171, 210, 0.50);
}

/*############通用[抽象类使用]###########*/
.model-body {
  box-sizing: border-box;
  background: #262727;
  padding: 10px 12px;
  margin: 8px;
  border-radius: 4px 4px 4px 4px;
}
/*############通用[属性-块/背景]###########*/
.chart-wrap {
  box-sizing: border-box;
  width: 100%;
  border-radius: 3px;
}

/*############属性[绑定-字段]###########*/
.bind-table {
  border-collapse: collapse;
  font-size: 12px;
  width: 100%;
  color: #cfdadd;
}

.bind-table td {
  border-bottom: 1px solid #363637;
  line-height: 24px;
  text-indent: 4px;
  padding-left: 12px;
}
.bind-table tr:hover {
  background: #000000;
}

.bind-table .table-title {
  text-indent: 0;
  font-weight: bold;
  background: #000000;
}

/*############属性[绑定-图层]###########*/
.bind-layer {
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 0 2px rgba(42, 171, 210, 0.80);
}

.bind-layer .bind-head {
  width: 100%;
  box-sizing: border-box;
  height: 30px;
}
.bind-layer .bind-head .chart-item {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 8px;
  border-bottom: 1px solid #144655;
}
.bind-layer .bind-head .chart-item .chart-label,
.bind-layer .bind-head .chart-item .chart-control{
  font-size: 14px;
}

/*############通用[内容/编辑]###########*/
.chart-item {
  width: 100%;
  line-height: 20px;
  box-sizing: border-box;
  display: flex;
  margin-bottom: 10px;
}
.card-wrap .card-body .chart-item:last-child {
  margin-bottom: 0;
}

.chart-item .chart-label {
  min-width: 50px;
  margin-right: 5px;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  font-weight: 400;
  font-size: 12px;
  color: #A3A6AD;
}

.chart-item .chart-control {
  flex: 1;
  min-height: 20px;
  display: flex;
  /* 垂直居中 */
  align-items: center;
  /* 水平居中 */
  justify-content: center;
  background: #1D1D1D;
  position: relative;
  /* overflow: hidden; */
}

/****************自定义[color]的样式 ****************/
.chart-control .sp-replacer {
  height: 20px;
  border: none;
  padding: 0;

}

/****************自定义[select]的样式 ****************/
.chart-control select {
  line-height: 20px;
  width: 100%;
  /* 去掉焦点时的轮廓 */
  outline: none;
  /* 移除iOS上的内置样式 */
  -webkit-appearance: none;
  /* 移除Firefox上的内置样式 */
  -moz-appearance: none;
  /* 将来浏览器标准化后使用 */
  appearance: none;
  /* 设置背景色为透明 */
  background-color: transparent;

  height: 20px;
  font-weight: 400;
  font-size: 12px;
  color: #CFD3DC;

  border-radius: 2px 2px 2px 2px;
  border: none;
  padding: 0 8px;
}
.chart-control:has(.chart-select):after {
  content:"\e790";
  font-family: "ft-font";
  font-size: 8px;
  transform: rotateZ(-90deg);
  color: #56585b;
  position: absolute;
  z-index: 1;
  display: block;
  top: 0;
  right: 10px;
}

.chart-control select option {
  outline: none;
  line-height: 20px;
  width: 100%;
  /* 去掉焦点时的轮廓 */
  /* 移除iOS上的内置样式 */
  -webkit-appearance: none;
  /* 移除Firefox上的内置样式 */
  -moz-appearance: none;
  /* 将来浏览器标准化后使用 */
  appearance: none;
  /* 去除边框 */
  border: none;
  /* 设置背景色为透明 */
  background-color: #191919;

  height: 20px;
  font-weight: 400;
  font-size: 12px;
  color: #CFD3DC;
}

/****************自定义[input]的样式 ****************/
.chart-control input[type="text"],
.chart-control input[type="number"] {
  width: 100%;
  /* 去除点击输入框时产生的默认边框 */
  outline: none;
  /* 移除iOS上的内置样式 */
  -webkit-appearance: none;
  /* 移除Firefox上的内置样式 */
  -moz-appearance: none;
  /* 将来浏览器标准化后使用 */
  appearance: none;
  /* 设置背景色为透明 */
  background-color: transparent;

  height: 17px;
  font-weight: 400;
  font-size: 12px;
  color: #CFD3DC;

  border-radius: 2px 2px 2px 2px;
  border: none;
  padding: 2px 8px;
}
.chart-control input[type="text"]::-webkit-input-placeholder,
.chart-control input[type="number"]::-webkit-input-placeholder{
  color: #8D9095;
}
.chart-control input[type="text"]::-moz-placeholder,
.chart-control input[type="number"]::-moz-placeholder{
  color: #8D9095;
}
.chart-control input[type="text"]::-ms-input-placeholder,
.chart-control input[type="number"]::-ms-input-placeholder{
  color: #8D9095;
}

/* ##############自定义[textarea]样式############## */
.chart-control textarea {
  resize: vertical;
  min-height: 20px;
  min-width: 100%;
  outline: none;
  /* 设置背景色为透明 */
  background-color: transparent;
  border-radius: 2px 2px 2px 2px;
  border: none;
  padding: 2px 8px;

  height: 17px;
  font-weight: 400;
  font-size: 12px;
  color: #CFD3DC;
}

/* ##############自定义[checkbox]的样式############## */
.chart-item input[type='checkbox'] {
  text-align: center;
  vertical-align: middle;
  position: relative;
  top: -3px;
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.chart-item input[type='checkbox']::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  background: #fff;
  width: 100%;
  height: 100%;
  border: 1px solid #d9d9d9;
}

.chart-item input[type='checkbox']:checked::before {
  content: '\2713';
  font-size: 12px;
  background-color: #409EFF;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  border: 1px solid #409EFF;
  color: #fff;
  font-weight: bold;
  cursor: pointer;
}

.chart-item .chart-span {
  /*作用checkbox字体*/
  cursor: pointer;
  margin-right: 8px;
  font-weight: 400;
  font-size: 12px;
  color: #A3A6AD;
}

/* ##############去除[number]的[加/减]样式############## */
/* 谷歌 */
.chart-control input::-webkit-outer-spin-button,
.chart-control input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

/* 火狐 */
.chart-control input[type="number"] {
  -moz-appearance: textfield;
}

/*=============================自定义switch组件============================*/
.chart-item .chart-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}
.chart-item .chart-switch input {
  display:none;
}
.chart-item .chart-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #8D9095;
  -webkit-transition: .4s;
  transition: .4s;
}
.chart-item .chart-switch .slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: #ffffff;
  -webkit-transition: .4s;
  transition: .4s;
}
.chart-item .chart-switch input:checked + .slider {
  background: #409EFF;
}
.chart-item .chart-switch input:focus + .slider {
  box-shadow: 0 0 1px #409EFF;
}
.chart-item .chart-switch input:checked + .slider:before {
  -webkit-transform: translateX(20px);
  -ms-transform: translateX(20px);
  transform: translateX(20px);
}
.chart-item .chart-switch .slider.round {
  border-radius: 100px;
}
.chart-item .chart-switch .slider.round:before {
  border-radius: 50%;
}
/*=============================自定义switch组件============================*/

/*=============================属性编辑[图片选择]============================*/
.chart-control .chart-img {
  width: 100%;
  height: 100%;
  background-color: #ccc;
  display: flex;
  position: relative;
}

.chart-control .chart-img .chart-svg-preview {
  color: black;
  font-size: 10px;
  width: 100%;
  text-align: center;
  cursor: pointer;
  user-select: none;
}

.chart-control img {
  width: 100%;
  height: 100%;
  outline: none;
  text-align: center;
  cursor: pointer;
}

.chart-img-close {
  position: absolute;
  right: 0;
  top: 0;
  padding: 0 5px;
  height: 20px;
  width: 20px;
  text-align: center;
  line-height: 20px;
  background-color: #191919;
  display: none;
  color: #fff;
  font-size: 13px;
  cursor: pointer;
}

.chart-img:hover .chart-img-close {
  display: block;
}


/*=============================属性编辑[图标选择]============================*/
.lsd-data-header {
  width: 100%;
  height: 30px;
  padding: 5px;
  font-size: 13px;
  background-color: rgba(63, 75, 95, 0.5);
}

.lsd-data-header .lsd-data-text {
  color: #23b7e5;
}

.lsd-icon-table {
  margin-top: 5px;
  padding: 5px;
  width: 100%;
  height: 250px;
  overflow-x: hidden;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
}

.lsd-icon-table .lsd-icon-item {
  width: 20%;
  line-height: 55px;
  height: 55px;
  font-size: 28px;
  color: #0a9dc7;
  float: left;
  text-align: center;
  cursor: pointer;
  outline: none;
  border: 1px solid #414243;
  border-collapse: collapse;
  margin-left: -1px;
  margin-bottom: -1px;
}

.lsd-icon-table .lsd-icon-selected {
  color: #FFF;
  background-color: #0a9dc7;
}