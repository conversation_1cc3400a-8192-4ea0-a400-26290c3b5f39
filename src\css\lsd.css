.design-container{
  width: 100%;
  height: 100%;
}
.design-head {
  height: 52px;
  color: #fff;
  overflow: hidden;
  margin: 0;
  font-size: 18px;
  line-height: 52px;
  background: #191919;
  /* box-shadow: inset 0 0 1em #2B3340; */
  box-sizing: border-box;
}
.design-body {
  height: calc(100% - 52px);
  background:url('../assets/port.png') repeat;
}
.design-body .design-menu{
  float: left;
  width: 265px;
  height: 100%;
  overflow: visible;/*允许溢出*/
}
.design-body .design-draw{
  float: left;
  position: relative;
  width: calc(100% - 265px - 320px);
  height: 100%;
  overflow: hidden;
}
.design-body .design-property{
  float: right;
  width: 320px;
  height: 100%;
  overflow: hidden;
  /* background: #191919; */
  /* background-color: #FFF; */
}
.preview-container{
  width: 100%;
  height: 100%;
}
.preview-canvas{
  position: relative;
  transform-origin:0 0;
  /* width: 100%; */
  /* height: 100%; */
  /* transform: scale(0.45); */
  /* background-color: rgb(43, 51, 64); */
  /* margin: 0 10px; */
  /* background-size:100% 100%; */
}
