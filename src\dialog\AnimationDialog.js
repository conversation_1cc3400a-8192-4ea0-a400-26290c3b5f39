import Dialog from "../assets/element/jquery/dialog/Dialog";
import AnimationDialogStyle from "./style/AnimationDialogStyle";
export default class AnimationDialog {
  constructor(context) {
    this.context = context;
    this.container = $(`<div class="content-container"></div>`);
  }
  /**
   * 打开
   * @param title 弹窗标题
   * @param {*} callback 确认回调
   */
  open (title, callback, val) {
    const self = this;
    this.refreshPanel(val);
    const dialog = Dialog.getInstance();
    dialog.addModel(this.container, {
      title: title,
      height: 'auto',
      width: ($(window).width() * 0.27),
      button: {
        // close: { text: "取消", click: function () { } },
        submit: {
          text: "确定", click: function () {
            const result = self.getResult();
            if (callback) {
              callback(result);
            }
          }
        },
      }
    });
  }

  refreshPanel (val) {
    if (this.container) {
      this.container.empty();
      this.dialogStyle = new AnimationDialogStyle(this.context);
      this.dialogStyle.initPanel(this.container, val);
    }
  }
  getResult () {
    if (this.context && this.dialogStyle) {
      return this.dialogStyle.getResult();
    }
  }

}