import Dialog from "../assets/element/jquery/dialog/Dialog";
import BindDataDialogStyle from "./style/BindDataDialogStyle";

export default class BindDataDialog {
  constructor(context,bind) {
    this.context = context;
    this.bind=bind;
    this.container = $(`<div class="content-container"></div>`);
  }
  /**
   * 打开
   * @param {*} callback 
   */
  open (callback) {
    const self=this;
    this.refreshPanel();
    const dialog = Dialog.getInstance();
    dialog.addModel(this.container, {
      title: "数据模型",
      height: ($(window).height() - 250),
      width: ($(window).width() * 0.625),
      button: {
        submit: {
          text: "确定", className:"foot-save" , click: function () {
            const result=self.getResult();
            if (callback) {
              callback(result);
            }
          }
        },
        close: { text: "取消", click: function () { } },
      }
    });
  }
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      const item=this.bind;
      this.dialogStyle=new BindDataDialogStyle(this.context);
      this.dialogStyle.initPanel(this.container,item);
    }
  }
  getResult(){
    if(this.dialogStyle){
      return this.dialogStyle.getResult();
    }
  }
}