import Dialog from "../assets/element/jquery/dialog/Dialog";
import ConfigDialogStyle from "./style/ConfigDialogStyle";
import MaterialModel from "../model/MaterialModel";
export default class ConfigDialog {
  constructor(context) {
    this.context = context;
    this.container = $(`<div class="content-container"></div>`);
  }
  /**
   * 打开
   * @param {*} callback 
   */
  open(callback) {
    const self = this;
    this.refreshPanel();
    const dialog = Dialog.getInstance();
    dialog.addModel(this.container, {
      title: "配置",
      maxHeight: ($(window).height() - 100),
      width: ($(window).width() * 0.625),
      button: {
        // close: { text: "取消", click: function () { } },
        submit: {
          text: "确定", className: "foot-save", click: function () {
            const result = self.getResult();
            if (callback) {
              callback(result);
            }
          }
        },
      }
    });
  }
  refreshPanel() {
    if (this.container) {
      this.container.empty();
      if (this.context) {
        // this.config={background:{type:"color"}};
        const config = this.context.getConfig();
        this.dialogStyle = new ConfigDialogStyle(this.context);
        this.dialogStyle.initPanel(this.container, config);
      }
    }
  }
  getResult() {
    if (this.context && this.dialogStyle) {
      const bg = this.context.getBackground();
      if (bg && bg["bgType"] === "image") {
        const image = this.dialogStyle.getIamge();
        if (image) {
          const model = new MaterialModel(this.context);
          model.refreshProperty(image);
          this.context.addMaterial(model);
          const imageId = model.getId();
          bg["image"] = { id: imageId };
        }
      }
      return this.context.getConfig();
    }
  }

}