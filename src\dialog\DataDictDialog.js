import Dialog from "../assets/element/jquery/dialog/Dialog";
import DataDictDialogStyle from "./style/DataDictDialogStyle";

export default class DataDictDialog {
  constructor(context,bind) {
    this.context = context;
    this.bind=bind;
    this.container = $(`<div class="content-container"></div>`);
  }
  /**
   * 打开
   * @param {*} callback 
   */
  open (callback) {
    const self=this;
    this.refreshPanel();
    const dialog = Dialog.getInstance();
    dialog.addModel(this.container, {
      title: "数据字典",
      maxHeight: ($(window).height() - 250),
      width: ($(window).width() * 0.625),
      button: {
        submit: {
          text: "确定", className:"foot-save" , click: function () {
            const result=self.getResult();
            if (callback) {
              callback(result);
            }
          }
        },
        close: { text: "取消", click: function () { } },
      }
    });
  }
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      const item=this.bind;
      this.dialogStyle=new DataDictDialogStyle(this.context);
      this.dialogStyle.initPanel(this.container,item);
    }
  }
  getResult(){
    if(this.dialogStyle){
      return this.dialogStyle.getResult();
    }
  }
}