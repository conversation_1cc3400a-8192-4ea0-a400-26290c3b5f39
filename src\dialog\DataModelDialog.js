import Dialog from "../assets/element/jquery/dialog/Dialog";
import DataModelDialogStyle from "./style/DataModelDialogStyle";

export default class DataModelDialog {
  constructor(context,model) {
    this.context = context;
    this.model=model;
    this.container = $(`<div class="content-container"></div>`);
  }
  /**
   * 打开
   * @param {*} callback 
   */
  open (callback) {
    const self=this;
    this.refreshPanel();
    const dialog = Dialog.getInstance();
    dialog.addModel(this.container, {
      title: "数据模型",
      height: ($(window).height() - 250),
      width: ($(window).width() * 0.625),
      button: {
        submit: {
          text: "确定", className:"foot-save" , click: function () {
            const result=self.getResult();
            if (callback) {
              callback(result);
            }
          }
        },
        close: { text: "取消", click: function () { } },
      }
    });
  }
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      const model=this.model;
      this.dialogStyle=new DataModelDialogStyle(this.context);
      this.dialogStyle.initPanel(this.container,model);
    }
  }
  getResult(){
    if(this.dialogStyle){
      return this.dialogStyle.getResult();
    }
  }
}