import Dialog from "../assets/element/jquery/dialog/Dialog";
import DataSetAddDialogStyle from "./style/DataSetAddDialogStyle";

export default class DataSetAddDialog {
  constructor(context) {
    this.context = context;
    this.container = $(`<div class="content-container"></div>`);
  }
  /**
   * 打开
   * @param {*} callback 
   */
  open (callback) {
    this.refreshPanel(callback);
    const dialog = Dialog.getInstance();
    dialog.addModel(this.container, {
      title: "新增数据集",
      height: ($(window).height() * 0.40),
      width: ($(window).width() * 0.55),
      button: {}
    });
  }
  refreshPanel (callback) {
    if (this.container) {
      this.container.empty();
      this.dialogStyle=new DataSetAddDialogStyle(this.context);
      this.dialogStyle.initPanel(this.container, null, callback);
    }
  }
}