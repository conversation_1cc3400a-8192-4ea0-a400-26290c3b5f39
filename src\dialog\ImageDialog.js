import Dialog from "../assets/element/jquery/dialog/Dialog";
import ImageDialogStyle from "./style/ImageDialogStyle";
import MaterialModel from "../model/MaterialModel";
export default class ImageDialog {
  constructor(context, model) {
    this.context = context;
    this.model = model || { type: "1" };//{type:类型（1图片，2背景，3边框，4.svg，5.主题，6.图标）}
    this.container = $(`<div class="content-container"></div>`);
  }
  /**
   * 打开
   * @param {*} callback 
   */
  open (callback) {
    const self = this;
    this.refreshPanel();
    const dialog = Dialog.getInstance();
    dialog.addModel(this.container, {
      title: this.model.type === '7' ? '选择SVG动画' : '选择图片',
      height: ($(window).height() - 250),
      width: ($(window).width() * 0.625),
      button: {
        // close: { text: "取消", click: function () { } },
        submit: {
          text: "确定", click: function () {
            const result = self.getResult();
            if (callback) {
              callback(result);
            }
          }
        },
      }
    });
  }

  refreshPanel () {
    if (this.container) {
      this.container.empty();
      const model = this.model;
      this.dialogStyle = new ImageDialogStyle(this.context, false);
      this.dialogStyle.initPanel(this.container, model);
    }
  }
  getResult () {
    if (this.context && this.dialogStyle) {
      const image = this.dialogStyle.getResult();
      if (image) {
        const model = new MaterialModel(this.context);
        model.refreshProperty(image);
        this.context.addMaterial(model);
        // bg["materialId"]=model["property"]["id"];
      }
      return this.dialogStyle.getResult();
    }
  }

}