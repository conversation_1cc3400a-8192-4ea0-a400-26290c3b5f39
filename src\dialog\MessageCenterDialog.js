import Dialog from "../assets/element/jquery/dialog/Dialog";
import MessageCenterDialogStyle from "./style/message-center/MessageCenterDialogStyle";

export default class MessageCenterDialog {
  constructor(context) {
    this.context = context;
    if (this.context) {
      this.list = this.context.getMessageCenters();
    }
    this.container = $(`<div class="content-container"></div>`);
  }
  /**
   * 打开
   * @param {*} callback
   */
  open (callback) {
    const self=this;
    this.refreshPanel();
    const dialog = Dialog.getInstance();
    dialog.addModel(this.container, {
      title: "消息中心",
      height: $(window).height() * 0.9,
      width: $(window).width() * 0.7,
      button: {
        submit: {
          text: "保存",
          className:"foot-save",
          click: () => {
            callback && callback()
          }
        },
      }
    });
  }
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      this.dialogStyle = new MessageCenterDialogStyle(this.context);
      this.dialogStyle.initPanel(this.container, this.list, () => {})
    }
  }

}