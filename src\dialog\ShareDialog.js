import Dialog from "../assets/element/jquery/dialog/Dialog";
import ShareDialogStyle from "./style/ShareDialogStyle";
import ResourcesService from "../service/ResourcesService";
import {getUrlParameter} from "../utils/Util";
export default class ShareDialog {
  constructor(context) {
    this.context = context;
    this.item = this.getDefualProperty();
    this.container = $(`<div class="content-container"></div>`);
  }
  getDefualProperty () {
    return {
      // 访问类型初始化值
      accessType: '1',
      // 有效期至初始化值
      expireOption: '99',
      datefilter: {
        height: "30px",
        width: "200px",
        language: 'zh-CN',
        format: 'yyyy-mm-dd',
        // https://blog.csdn.net/qq_42354773/article/details/80926225
        // 参考源码1819行方法
        // \bootstrap-datetimepicker\sample in bootstrap v2\index.html
        // 选完日期弹窗就结束了
        minView: 'month',
        autoclose: true,
        realDate: {
          inputFormat: 'yyyy-mm-dd',
          outputFormat: 'yyyy-mm-dd',
          initDate: "2021-07-25",
          globalDate: "",
          initDateType: "0",
          futureDay: false
        }
      },
    }
  }
  
  /**
   * 打开
   * @param {*} callback 
   */
  open (callback) {
    const self=this;
    this.refreshPanel();
    const dialog = Dialog.getInstance();
    dialog.addModel(this.container, {
      title: "分享",
      // height: ($(window).height() - 100),
      width: "720px",
      button: {
        submit: {
          text: "确定", className:"foot-save" , click: function () {
            const result=self.getResult();
            if (callback) {
              callback(result);
            }
          }
        },
        close: { text: "取消", click: function () { } },
      }
    });
  }
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      const item=this.item;
      const self = this;
      this.getUrlApi(function (result) {
        if (result) {
          item.shareUrl = result;
          self.dialogStyle = new ShareDialogStyle(self.context, false);
          self.dialogStyle.initPanel(self.container, item);
        }

      });
    }
  }

  getUrlApi(callback){
    let self = this;
    const resourcesService = new ResourcesService(self.context)
    self.item.id = getUrlParameter("id")
      let params = {
        businessId: self.item.id,
        shareConfigCode: "DEV_LSD",
      };
      
      resourcesService.generateLink(params,function(res) {
        var shareUrl =  res.url;
        shareUrl.includes("?")
          ? (shareUrl += "&shareType=share")
          : (shareUrl += "?shareType=share");
          if(callback){
            callback(shareUrl)
          }
          self.item.uuid = res.uid
      },function(error){
        if(callback){
          callback()
        }
      })
  }

  getResult () {
    if(this.dialogStyle){
      return this.dialogStyle.getResult();
    }
  }

}