import TemplDialogStyle from "./style/TemplDialogStyle";
import Submit from "../Submit";
import Dialog from "../assets/element/jquery/dialog/Dialog";
export default class TemplDialog {
  constructor(context, layer) {
    this.context = context;
    this.layer = layer;
    this.submit = Submit.getInstance(context);
    this.container = $(`<div class="content-container"></div>`);
  }
  /**
   * 打开
   * @param {*} callback 
   */
  open (callback) {
    const self = this;
    this.refreshPanel();
    let text = "生成模板/组件"
    if (this.layer) {
      text = "生成组件";
    } else {
      text = "生成模板";
    }
    // 初始隐藏校验
    const inputVerify = this.container.find('.com-verify input')
    const errorHint = this.container.find('.errorHint');
    errorHint.hide()
    const dialog = Dialog.getInstance();
    dialog.addModel(this.container, {
      title: text,
      height: '100%',
      width: "720px",
      button: {
        save: {
          text: "保存", className: "foot-save", click: function (id) {
            const result = self.getResult();
            if (result.screen.name) {
              inputVerify.removeClass('errorLine')
              errorHint.hide()
              if (self.submit && !self.isClick) {
                self.isClick = true;
                self.submit.saveTemp(result, function () {
                  dialog.delModel(id);
                });
              }
            } else {
              inputVerify.addClass('errorLine')
              errorHint.show()
            }
          }
        },
        close: { text: "取消", className: "foot-cancel", click: function () { } },
      }
    });
  }
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      if (this.submit) {
        const model = {};
        if (this.layer) {
          model["type"] = "1";
          model["dirType"] = "lsdComp";//组件的目录
          model["screen"] = this.submit.getJsonScreen(true, "1", this.layer);
        } else {
          model["type"] = "2";
          model["dirType"] = "lsdMould";//模板的目录
          model["screen"] = this.submit.getJsonScreen(true, "2");
        }
        if (model["screen"]) {
          model["screen"]["type"] = model.type;
          model["screen"]["ratioType"] = "1";
        }
        this.dialogStyle = new TemplDialogStyle(this.context);
        this.dialogStyle.initPanel(this.container, model);
        // this.dialogStyle.refreshData();
      }
    }
  }
  getResult () {
    if (this.dialogStyle) {
      return this.dialogStyle.getResult();
    }
  }
}