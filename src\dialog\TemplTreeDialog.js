import Dialog from "../assets/element/jquery/dialog/Dialog";
import TemplTreeDialogStyle from "./style/TemplTreeDialogStyle";
import DirLabelService from "../service/DirLabelService";
import {isArray, toTree} from "../utils/Util";
export default class TemplTreeDialog {
  constructor(context, model) {
    this.context = context;
    this.model = model || { type: "1", dirType: "lsdComp" };//type：1目录 需要目录类型 ( 目录类型 dirType:"lsdComp"组件目录/"lsdMould"模板目录) 2标签  
    this.container = $(`<div class="content-container"></div>`);
  }
  /**
   * 打开
   * @param {*} callback 
   */
  open(callback) {
    const self = this;
    this.refreshPanel();
    const isFolder = this.model.type === '1';
    const text = isFolder ? "选择目录" : "选择标签";
    const dialog = Dialog.getInstance();
    dialog.addModel(this.container, {
      title: text,
      height: ($(window).height() - 200),
      width: "720px",
      button: {
        submit: {
          text: "确定", className: "foot-save", click: function () {
            const result = self.getResult();
            if (callback) {
              callback(result);
            }
          }
        },
        close: { text: "取消", click: function () { } },
      }
    });
  }
  refreshPanel() {
    if (this.container) {
      this.container.empty();
      const self = this;
      const model = this.model;
      const isTag = model.type === '2';
      this.queryData(model, list => {
        let flag = false;
        if (isTag) {
          flag = true;
        }
        if (list) {
          self.dialogStyle = new TemplTreeDialogStyle(self.context, flag);
          self.dialogStyle.initPanel(self.container, list);
          if (isTag) {
            // 选择标签添加一个提示区域
            Dialog.getInstance().addAlert(Dialog.getInstance().property.id, tagsEl => {
              if (tagsEl) {
                self.dialogStyle.setOnCheckedChange(() => {
                  const result = this.getResult();
                  if (isArray(result)) {
                    $(tagsEl).text(result.map(r => r.name).join('、'))
                  }
                })
              }
            });
          }
        }
      });
      // if (model.type && model.type == "1") {
      //   self.queryDataToDir(model, function (list) {
      //     if (list && list.length) {
      //       self.dialogStyle = new TemplTreeDialogStyle(self.context, false);
      //       self.dialogStyle.initPanel(self.container, list);
      //     }

      //   });
      // } else if (model.type && model.type == "2") {
      //   self.queryDataToTag(model, function (list) {
      //     if (list && list.length) {
      //       self.dialogStyle = new TemplTreeDialogStyle(self.context, true);
      //       self.dialogStyle.initPanel(self.container, list);
      //     }
      //   });
      // }
    }
  }
  getResult() {
    if (this.dialogStyle) {
      return this.dialogStyle.getResult();
    }
  }
  queryData(model, callback) {
    const self = this;
    const params = { status: "1" };
    if (model) {
      for (let key in model) {
        params[key] = model[key];
      }
    }
    const dirLabelService = new DirLabelService(self.context);
    dirLabelService.queryListPath(params, function (result) {
      if (result && result.length) {
        const json = toTree(result, "id", "parentId", "childs");
        if (json && json.tree) {
          if (callback) {
            callback(json.tree);
          }
        }
      }
    });
  }
  // /**
  //  * 查询目录，并且转为树结构
  //  * @param {*} callback 
  //  */
  // queryDataToDir (model, callback) {
  //   const self = this;
  //   const params = { status: 1 };
  //   if (model && model.dirType) {
  //     const dirType = self.model.dirType;
  //     const params = { type: "1", status: 1 };
  //     if (dirType === 1) {
  //       params["dirType"] = "lsdComp";
  //     } else {
  //       //组件
  //       params["dirType"] = "lsdMould";
  //     }

  //   }
  // }

  // /**
  //  * 查询标签，并且转为树结构
  //  * @param {*} callback 
  //  */
  // queryDataToTag (callback) {
  //   const self = this;
  //   const screenService = new ScreenService(self.context);
  //   const params = { type: "2" };
  //   screenService.queryCatalog(params, function (result) {
  //     if (result && result.length) {
  //       const json = toTree(result, "id", "parentId", "childs");
  //       if (json && json.tree) {
  //         if (callback) {
  //           callback(json.tree);
  //         }
  //       }
  //     }
  //   });
  // }
}