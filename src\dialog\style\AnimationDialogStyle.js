import AbstractStyle from "../../AbstractStyle";
export default class AnimationDialogStyle extends AbstractStyle {
  constructor(context) {
    super(context);
  }

  initModel(container) {
    if (container) {
      const modelContainer = $(`<div class="data-wrap flex-col">
        <div class="data-title">标题名称</div>
        <div class="data-content">
            <input class="data-input" type="text" placeholder="请输入" />
        </div>
      </div>`);

      this.input = modelContainer.find('.data-input');
      container.append(modelContainer);

      if (this.item) {
        this.input.val(this.item);
      }
    }
  }

  getResult() {
    if (this.input && this.input.length) {
      return this.input.val();
    }
  }

  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "input";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "输入"
  }
}