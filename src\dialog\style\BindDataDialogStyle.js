
import DataModelService from "../../service/DataModelService";
import ResourcesService from "../../service/ResourcesService";
import BindQueryHandler from "../../service/BindQueryHandler";
import PageTable from "../../assets/element/jquery/table/PageTable";
import AbstractStyle from "../../AbstractStyle";
export default class BindDataDialogStyle extends AbstractStyle {
  constructor(context) {
    super(context);
    // this.tree = [
    //   { id: "1", name: "测试1", childs: [] },
    //   {
    //     id: "2", name: "测试2", childs: [
    //       {
    //         id: "2-1", name: "测试2-1", childs: [
    //           {
    //             id: "2-1-1", name: "测试2-1-1", isLeaf: true, params: [
    //               { name: "123", type: "STRING", defValue: "" },
    //               { name: "456", type: "STRING", defValue: "" },
    //               { name: "789", type: "STRING", defValue: "" },
    //             ]
    //           },
    //           {
    //             id: "2-1-2", name: "测试2-1-2", isLeaf: true, params: [
    //               { name: "123", type: "STRING", defValue: "" },
    //               { name: "456", type: "STRING", defValue: "" },
    //               { name: "789", type: "STRING", defValue: "" },
    //             ]
    //           },
    //           {
    //             id: "2-1-3", name: "测试2-1-3", isLeaf: true, params: [
    //               { name: "123", type: "STRING", defValue: "" },
    //               { name: "456", type: "STRING", defValue: "" },
    //               { name: "789", type: "STRING", defValue: "" },
    //             ]
    //           },
    //           {
    //             id: "2-1-4", name: "测试2-1-4", isLeaf: true, params: [
    //               { name: "123", type: "STRING", defValue: "" },
    //               { name: "456", type: "STRING", defValue: "" },
    //               { name: "789", type: "STRING", defValue: "" },
    //             ]
    //           },
    //         ]
    //       },
    //       { id: "2-2", name: "测试2-2", isLeaf: true },
    //       { id: "2-3", name: "测试2-3", isLeaf: true },
    //       { id: "2-4", name: "测试2-4", isLeaf: true },
    //     ]
    //   },
    //   {
    //     id: "3", name: "测试3", childs: [
    //       { id: "3-1", name: "测试3-1", isLeaf: true },
    //       { id: "3-2", name: "测试3-2", isLeaf: true },
    //       { id: "3-3", name: "测试3-3", isLeaf: true },
    //       { id: "3-4", name: "测试3-4", isLeaf: true },
    //     ]
    //   },
    //   {
    //     id: "4", name: "测试4", childs: [
    //       { id: "4-1", name: "测试4-1", isLeaf: true },
    //       { id: "4-2", name: "测试4-2", isLeaf: true },
    //       { id: "4-3", name: "测试4-3", isLeaf: true },
    //       { id: "4-4", name: "测试4-4", isLeaf: true },
    //     ]
    //   },
    // ];
  }
  initModel(container) {
    if (container) {
      const modelContainer = $(`<div class="data-wrap">
        <div class="data-left"></div>
        <div class="data-right"></div>
      </div>`);
      container.append(modelContainer);
      this.leftContainer = $(modelContainer).find(".data-left");
      this.rightContainer = $(modelContainer).find(".data-right");
      // 绑定模板数据样式初始化
      this.rightContainer.css({
         "padding":"0px 10px 0px 20px",
         'max-height': ($(window).height() - 390),
          "overflow" : "auto"
      })
      // this.refreshLeft(this.tree);
      // this.refreshRight();
      //查询数据
      const self = this;
      this.queryData(function (result) {
        if (self.isAllLoadedToModel()) {
          // console.info("==============================加载模型数据完成==========================!");
          self.refreshTree(self.item, self.map, result);
          self.refreshLeft(result);
          self.refreshRight();
        }
      });
    }
  }

  /*##############################[queryData]################################# */
  queryData(callback) {
    const self = this;
    const params = { dirTypeCodes: ['DATA_MODEL_DIR'] };
    const resourcesService = new ResourcesService(this.context);
    resourcesService.queryAuthResourcesDir(params, function (result) {
      //存储Map
      self.map = {};
      //刷新结果
      if (result && result.length) {
        for (var i = 0; i < result.length; i++) {
          const resItem = result[i];
          //修改结构
          resItem["childs"] = resItem.children;
          resItem["name"] = resItem.showName;
          //存储Map
          self.map[resItem.id] = resItem;
          //遍历
          const childs = resItem.children;
          if (childs && childs.length) {
            for (let child of childs) {
              //添加属性-名称
              if (child && child.showName) {
                child["name"] = child.showName;
              }
              //存储Map
              self.map[child.id] = child;
              //添加属性-是否记载
              child.$$isLoading = true;
              //数据模型
              const _params = { status: "1", resId: child.id };
              const dataModelService = new DataModelService(self.context);
              dataModelService.queryList(_params, function (list) {
                if (list && list.length) {
                  for (let item of list) {
                    //是否开启
                    item["isOpen"] = false;
                    //是否叶子节点
                    item["isLeaf"] = true;
                    //是否选中
                    item["isChecked"] = false;
                    //显示名称
                    if (item && item.name) {
                      item["name"] = item.name;
                    }
                  }
                  child["childs"] = list;
                }
                child.$$isLoading = false;
                if (callback) {
                  callback(result);
                }
              });
            }
          }
        }
      }
    });
  }
  /**
     * 描述:加载模型数据是否全部完成
     * @returns 
     */
  isAllLoadedToModel() {
    let flag = true;
    if (this.map) {
      flag = this.isAllLoaded(this.map);
    }
    return flag;
  }
  /**
   * 描述:是否全部加载完
   * @param loadMap
   * @returns {boolean}
   */
  isAllLoaded(loadMap) {
    let flag = true;
    if (loadMap) {
      for (let key in loadMap) {
        const obj = loadMap[key];
        if (obj.$$isLoading) {
          flag = false;
        }
      }
    }
    return flag;
  }
  refreshTree(data, map, list, parent) {
    if (data && data.id) {

      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          //处理路径
          if (parent) {
            item["path"] = item["id"] + "," + parent["path"];
          } else {
            item["path"] = item["id"];
          }
          //比对
          if (data.id == item["id"]) {
            //选中
            item["isChecked"] = true;
            //展开
            if (item["path"]) {
              const ids = item["path"].split(",");
              if (ids && ids.length) {
                for (let id of ids) {
                  if (id && map && map[id]) {
                    const itemMap = map[id];
                    itemMap["isOpen"] = true;
                  }
                }
              }
            }
          }
          //递归
          const childs = item["childs"];
          this.refreshTree(data, map, childs, item);
        }
      }

    }

  }
  /*##############################[左边]################################# */
  /**
  * 
  */
  refreshLeft(list) {
    if (this.leftContainer) {
      this.leftContainer.empty();
      this.tree = list;
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          this.refreshNode(this.leftContainer, item);
        }
      }
    }
  }
  refreshNode(parentContainer, item) {
    if (parentContainer && item) {
      const modelContainer = $(`<div class="comm-wrap com-tree" id="` + item.id + `">
      <div class="comm-head tree-head">
        <div class="head-icon ft-font" id="icon`+ item.id + `"></div>
        <div class="head-title">`+ item.name + `</div>
      </div>
      <div class="comm-body tree-body" id="body`+ item.id + `"></div>
      </div>`);
      parentContainer.append(modelContainer);
      const headContainer = $(modelContainer).find(".tree-head");
      const bodyContainer = $(modelContainer).find(".tree-body");
      const iconContainer = $(modelContainer).find(".tree-head .head-icon");
      const titleContainer = $(modelContainer).find(".tree-head .head-title");

      //初始化

      this.refreshOpen(modelContainer, item);
      this.refreshChecked(modelContainer, item);
      //点击open
      const self = this;
      $(iconContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        item.isOpen = !item.isOpen;
        self.refreshOpen(modelContainer, item);
      });
      //点击选中
      $(titleContainer).on("click", function (event) {
        // 多选
        // item.isChecked = !item.isChecked;
        // self.refreshChecked(titleContainer, item);
        // 单选
        self.cleanCheckedData(self.tree);
        self.cleanCheckedStyle(self.leftContainer);
        item.isChecked = true;
        self.refreshChecked(titleContainer, item);
      });

      //孩子 递归
      const childs = item["childs"];
      if (childs && childs.length) {
        for (let i = 0; i < childs.length; i++) {
          const child = childs[i];
          this.refreshNode(bodyContainer, child);
        }
      }
    }
  }

  refreshOpen(container, item) {
    if (container && item) {
      //用判断是否有孩子
      const childs = item["childs"];
      if (childs && childs.length) {
        //改用Id 防止向下穿透
        const iconContainer = $(container).find("#icon" + item.id);
        const bodyContainer = $(container).find("#body" + item.id);
        if (item.isOpen) {
          $(bodyContainer).show();
          $(iconContainer).removeClass("icon-xiala");
          $(iconContainer).addClass("icon-shangla");
        } else {
          $(bodyContainer).hide();
          $(iconContainer).removeClass("icon-shangla");
          $(iconContainer).addClass("icon-xiala");
        }
      }
    }
  }
  cleanCheckedData(list) {
    // const list=this.tree;
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        item["isChecked"] = false;
        const childs = item["childs"];
        this.cleanCheckedData(childs);
      }
    }
  }
  cleanCheckedStyle(container) {
    // const container=this.leftContainer;
    if (container) {
      const listContainer = $(container).find(".tree-selected");
      $(listContainer).each(function (index, element) {
        $(element).removeClass("tree-selected");
      });
    }
  }
  refreshChecked(container, item) {
    if (container && item) {
      if (item.isChecked) {
        $(container).addClass("tree-selected");
        //刷新右边
        this.refreshRight(item);
      } else {
        //多选情况
        $(container).removeClass("tree-selected");
      }
    }
  }

  /*##############################[右边]################################# */
  refreshRight(model) {
    if (this.rightContainer) {
      this.rightContainer.empty();
      const bind = JSON.parse(JSON.stringify(this.item));
      //临时绑定对象
      this.tempBind = bind;
      const self = this;
      //刷新bind数据
      this.refreshBindData(model, bind, function () {
        //刷新绑定配置
        self.refreshBind(self.rightContainer, bind);
        //刷新绑定参数
        self.refreshBindParams(self.rightContainer, bind.params);
        //刷新绑定预览
        self.refreshBindPreview(self.rightContainer, bind);
      });
    }
  }
  refreshBindData(model, bind, callback) {
    //数据处理
    if (bind && model && bind.id != model.id) {
      bind.id = model.id;
      bind.name = model.name;
      //参数
      if (!model.params) {
        const params = { modelId: model.id };
        const dataModelService = new DataModelService(this.context);
        dataModelService.queryByModelId(params, function (result) {
          model.params = result;
          bind.params = result;
          if (callback) {
            callback();
          }
        });
      } else {
        bind.params = model.params;
        if (callback) {
          callback();
        }
      }
    } else {
      if (callback) {
        callback();
      }
    }
  }
  getResult() {
    if (this.tempBind) {
      return this.tempBind;
    }
  }
  /**
   * 绑定预览
   * @param {*} parentContainer 
   * @param {*} bind 
   */
  refreshBindPreview(parentContainer, bind) {
    if (parentContainer && bind && bind.id) {
      const modelContainer = $(`<div class="comm-wrap com-wrap">
          <div class="comm-head com-head" >
            <div class="head-title mt10">绑定预览</div>
            <div class="head-btn head-add mt10">查询</div>
          </div>
          <div class="comm-body com-body"></div>
      </div>`);
      parentContainer.append(modelContainer);
      const bodyContainer = $(modelContainer).find(".comm-body");
      const btnContainer = $(modelContainer).find(".head-btn");
      const self = this;
      $(btnContainer).on("click", function (event) {
        const bindQueryHandler = new BindQueryHandler(self.context, bind);
        bindQueryHandler.queryPreview(function (result) {
          if (result && result.columns && result.data) {
            //字段
            bind["columns"] = result.columns;
            bind["fields"] = result.fields;//[{name:"NAME",dataType:"STRING",remark:"名称",columnName:"NAME"}]
            //表格字段处理
            let tableCols;
            const columns = result.columns;
            for (let i = 0; i < columns.length; i++) {
              const column = columns[i];
              const tableCol = { key: column, name: column, type: "text" };
              if (!tableCols) {
                tableCols = [];
              }
              tableCols.push(tableCol);
            }
            //表格实现
            if (bodyContainer && tableCols) {
              bodyContainer.empty();
              const table = new PageTable();
              table.initPanel(bodyContainer, { columns: tableCols, });
              table.refreshPanel(result.data);
              console.log(bodyContainer,'dadadadada');
              // 绑定数据伤处表格单选
              bodyContainer.find('.th_radioList').hide();
              bodyContainer.find('.td_radioList').hide();
            }
          }
        });
      });
    }
  }
  /**
   * 绑定参数
   * @param {*} parentContainer 
   * @param {*} datas 
   */
  refreshBindParams(parentContainer, params) {
    if (parentContainer && params && params.length) {
      const modelContainer = $(`<div class="comm-wrap com-wrap">
        <div class="comm-head com-head" >
          <div class="comm-head head-title">绑定参数</div>
        </div>
        <div class="comm-body com-body"></div>
      </div>`);
      parentContainer.append(modelContainer);
      const bodyContainer = $(modelContainer).find(".comm-body");
      if (bodyContainer) {
        bodyContainer.empty();
        //表格
        const table = new PageTable();
        table.initPanel(bodyContainer, {
          columns: [
            { key: "name", name: "名称", type: "text", },
            { key: "type", name: "类型", type: "text", options: [{ key: "1", text: "字符串" }, { key: "2", text: "数字" }, { key: "3", text: "日期" }, { key: "4", text: "浮点" },] },
            { key: "defValue", name: "属性值|表达式", type: "input", },
          ],
        });
        table.refreshPanel(params);
      }

    }
  }
  /**
   * 绑定
   * @param {*} parentContainer 
   * @param {*} bind 
   */
  refreshBind(parentContainer, bind) {
    if (parentContainer && bind) {
      const modelContainer = $(`<div class="comm-wrap com-wrap">
        <div class="comm-head com-head" >
          <div class="comm-head head-title">绑定</div>
        </div>
        <div class="comm-body com-body"></div>
      </div>`);
      parentContainer.append(modelContainer);
      const bodyContainer = $(modelContainer).find(".comm-body");
      //配置(行号)
      const modelName = this.getModelName();
      this.refreshPanel(bodyContainer, modelName, bind);
      //配置(映射)
      if (bind.mappings && bind.mappings.length) {
        //表格
        const table = new PageTable();
        table.initPanel(bodyContainer, {
          columns: [
            { key: "key", name: "键(映射)", type: "text", },
            { key: "desc", name: "名称", type: "text", },
            { key: "value", name: "值(字段)", type: "input", },
          ],
        });
        table.refreshPanel(bind.mappings);
      }
    }
  }
  /*##############################[图层-绑定]################################# */
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //行号
      const rowNumContainer = $(`<div class="com-row flex">
          <div class="com-label">行号</div>
          <div class="com-control">
            <input type="number" class="com-number"  min="0" ` + modelName + `="rowNum" />
          </div>
      </div>`);
      chartBody.append(rowNumContainer);
      //刷新/绑定
      this.refreshModel(item);
      this.bindModel(item, callback);
      // 绑定模型表格刷新删除单选列表
      // 使用 setTimeout 确保 DOM 更新
      setTimeout(() => {
        // 确保表格渲染完成后再操作
        chartBody.find('.th_radioList').hide();
        chartBody.find('.td_radioList').hide();
         // 调整表格最小高度
         chartBody.find('.ltab-wrap').css({
          'max-height': '305px',
        });
      }, 0); // 根据需要调整延迟时间
  
  }
}
refreshEvent(key, value) {

}
/**
 *
 * @returns {string}
 */
getModelName() {
  return "bind-model";
}

/**
 * 描述:标题信息
 * @returns {string}
 */
getTitle() {
  return "绑定-数据"
}
}