import MaterialService from "../../service/MaterialService";
import AbstractStyle from "../../AbstractStyle";
export default class ConfigDialogStyle extends AbstractStyle {
  constructor(context) {
    super(context);
    // this.image={};
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // 修改dialog中model-body样式
      chartBody.css({
        'background': 'none',
        'margin': '0px',
      });
      //类型
      // const typeContainer = $(`<div class="com-row">
      //     <select class="com-select" ` + modelName + `="background.bgType" >
      //       <option value="">--请选择--</option>
      //       <option value="color">背景颜色</option>
      //       <option value="image">背景图片</option>
      //     </select>
      // </div>`);
      // chartBody.append(typeContainer);

      const typeContainer = $(`<div class="com-row">
           <div class="com-label">背景选择</div>
               <div class="bgType-tabs com-control">
                  <input type="radio" id="color" value="color" name="tab" ` + modelName + `="background.bgType">
                  <label for="color">颜色</label>
                  <input type="radio" id="image" value="image" name="tab" ` + modelName + `="background.bgType">
                  <label for="image">图片</label>
               </div>
          </div>`);
      chartBody.append(typeContainer);

      //1.1  大屏背景颜色
      this.colorContainer = $(`<div class="com-row">
        <div class="com-label"></div>
        <input type="text" class="chart-color" ` + modelName + `="background.color" />
      </div>`);
      chartBody.append(this.colorContainer);
      //1.2  大屏背景图片
      this.imageListContainer = $(`<div class="com-config-image"></div>`);
      chartBody.append(this.imageListContainer);

      //
      const blur_opacity_Container = $(`<div class="com-row flex">
        <div class="w-50 pr5 flex">
          <div class="com-label">模糊度</div>
          <div class="com-control">
            <input class="com-number" min="0" ` + modelName + `="background.blur" />
          </div>
        </div>
        <div class="w-50 pl5 flex">
          <div class="com-label">透明度</div>
          <div class="com-control">
            <input class="com-number" min="0" ` + modelName + `="background.opacity" />
          </div>
        </div>

      </div>`);
      chartBody.append(blur_opacity_Container);

      //
      const width_height_Container = $(`<div class="com-row flex">
        <div class="w-50 pr5 flex">
          <div class="com-label">宽度</div>
          <div class="com-control">
            <input class="com-number" min="0" ` + modelName + `="width" />
          </div>
        </div>
        <div class="w-50 pl5 flex">
          <div class="com-label">高度</div>
          <div class="com-control">
            <input class="com-number" min="0" ` + modelName + `="height" />
          </div>
        </div> 

      </div>`);
      chartBody.append(width_height_Container);

      //
      const speedContainer = $(`<div class="com-row flex">
        <div class="w-50 pr5 flex">
          <div class="com-label">速度</div>
          <div class="com-control">
            <input class="com-number" min="0" ` + modelName + `="speed" />
          </div>
        </div>
        <div class="w-50 pl5 flex">
          <div class="com-label">缩放</div>
          <div class="com-control">
            <select class="com-select" ` + modelName + `="resizeType" >
              <option value="">--请选择--</option>
              <option value="1">自适应</option>
              <option value="2">宽度铺满</option>
              <option value="3">高度铺满</option>
            </select>
          </div>
        </div> 

      </div>`);
      chartBody.append(speedContainer);

      const showTopContainer = $(`<div class="com-row flex">
        <div class="w-50 pr5 flex">
            <div class="com-label">开启(预览工具)</div>
            <div class="com-control">
              <label class="preview-switch">
              <input type="checkbox" class="com-checkbox" ` + modelName + `="previewTool.isShow" />
              <span class="slider"></span>
              </label>
            </div>
        </div>
      </div>`);
      chartBody.append(showTopContainer);
      this.previewToolContainer = $(`<div class="preview-tool"></div>`);
      chartBody.append(this.previewToolContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
      console.log(item);

    }
  }
  refreshPreviewToolPanel(parentContainer, item, callback) {
    if (parentContainer && item) {
      $(parentContainer).empty();
      const chartBody = parentContainer;
      const modelName = "previewTool-model";
      if (!item["previewTool"]) {
        item["previewTool"] = {};
      }
      const fontSizeColorContainer = $(`<div class="com-row flex">
        <div class="w-50 pr5 flex">
            <div class="com-label">距顶</div>
            <div class="com-control">
              <input class="chart-number"  min="0" placeholder="请输入" ` + modelName + `="top" />
            </div>
        </div>
        <div class="w-50 pl5 flex">
            <div class="com-label">背景</div>
            <div class="com-control">
                <input type="text" class="chart-color" ` + modelName + `="backgroundColor" />
            </div>
        </div>
      </div>`);
      chartBody.append(fontSizeColorContainer);

      const colorContainer = $(`<div class="com-row flex">
        <div class="w-50 pr5 flex">
            <div class="com-label">字体大小</div>
            <div class="com-control">
                <input class="chart-number"  min="0" placeholder="请输入" ` + modelName + `="fontSize" />
            </div>
            
        </div>
        <div class="w-50 pl5 flex">
            <div class="com-label">字体颜色</div>
            <div class="com-control">
                <input type="text" class="chart-color" ` + modelName + `="color" />
            </div>
        </div>
      </div>`);
      chartBody.append(colorContainer);

      const btnStyleContainer = $(`<div class="com-row flex">
        <div class="w-50 pr5 flex">
          <div class="com-label">字体大小</div>
            <div class="com-control">
              <input class="chart-number" min="0" placeholder="请输入" ` + modelName + `="btnStyle.fontSize" />
            </div>
        </div> 
        <div class="w-50 pl5 flex">
            <div class="com-label">字体颜色</div>
            <div class="com-control">
              <input type="text" class="chart-color" ` + modelName + `="btnStyle.color" />
            </div>
        </div>
      </div>`);
      chartBody.append(btnStyleContainer);

      const dataShareContainer = $(`<div class="com-row flex">
        <div class="w-50 flex">
          <div class="com-label">显示(分享)按钮</div>
          <div class="com-control">
              <label class="preview-switch">
               <input type="checkbox" class="com-checkbox" ` + modelName + `="dataMap.share.isShow" />
               <span class="slider"></span>
              </label>
          </div>
        </div>
        <div class="w-50 pl5 flex">
            <div class="com-label">(分享)重命名</div>
            <div class="com-control">
                  <input type="text" class="chart-text" ` + modelName + `="dataMap.share.name" />
            </div>
        </div>
      </div>`);
      chartBody.append(dataShareContainer);

      const dataWarnContainer = $(`<div class="com-row flex">
        <div class="w-50 flex">
          <div class="com-label">显示(预警)按钮</div>
          <div class="com-control">
              <label class="preview-switch">
               <input type="checkbox" class="com-checkbox" ` + modelName + `="dataMap.warn.isShow"/>
               <span class="slider"></span>
              </label>
          </div>
        </div>
        <div class="w-50 pl5 flex">
            <div class="com-label">(预警)重命名</div>
            <div class="com-control">
                  <input type="text" class="chart-text" ` + modelName + `="dataMap.warn.name" />
            </div>
        </div>
      </div>`);
      chartBody.append(dataWarnContainer);

      this.refreshClazzColor(chartBody);
      this.refreshModel(item["previewTool"], null, modelName);
      this.bindModel(item["previewTool"], callback, modelName);

    }
  }

  refreshEvent(key, value) {
    if (key && key == "background.bgType") {
      if (value === "color") {
        this.colorContainer.show();
        this.imageListContainer.hide();
      } else if (value === "image") {
        this.colorContainer.hide();
        this.imageListContainer.show();
        this.refreshImage();
      } else {
        this.colorContainer.hide();
        this.imageListContainer.hide();
      }
    }
    if (key && key == "previewTool.isShow") {
      if (value === true) {
        this.previewToolContainer.show();
        this.refreshPreviewToolPanel(this.previewToolContainer, this.item);
      } else {
        this.previewToolContainer.hide();
      }
    }
  }
  getModelName() {
    return "config-model";
  }
  refreshImage() {
    //查询背景
    const self = this;
    const listContainer = this.imageListContainer;
    const params = { status: 1, type: 2 };
    const materialService = new MaterialService(this.context);
    materialService.queryList(params, function (result) {
      self.refreshItem(listContainer, result);
    });
  }
  refreshItem(listContainer, list) {
    if (listContainer) {
      listContainer.empty();
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          const itemContainer = $(`<div class='image-item'>
            <div class='item-block'>
              <img class='item-img' src="`+ item.fileUrl + `"/>
              <div class="item-triangle">
               <div class="item-icon fd-font fd-selected"></div>
              </div>
              <div class='item-title'>` + item.name + `</div>
              <div >
            </div>
          </div>`);
          listContainer.append(itemContainer);
          const self = this;
          $(itemContainer).on("click", function (event) {
            self.image = item;
            self.refreshChecked(itemContainer, item);
          });
        }
      }
    }
  }
  // refreshChecked(itemContainer, item) {
  //   if (this.imageListContainer) {
  //     const listContainer = $(this.imageListContainer).find(".image-item");
  //     $(listContainer).each(function (index, element) {
  //       $(element).find('.item-triangle').hide();
  //     });
  //     $(element).find('.item-triangle').show();
  //   }
  // }
  refreshChecked(itemContainer, item) {
    if (this.imageListContainer) {
      // Hide all triangles
      $(this.imageListContainer).find(".image-item .item-triangle").hide();
      
      // Show the triangle for the selected item
      $(itemContainer).find('.item-triangle').show();
    }
  }
  getIamge() {
    if (this.image) {
      return this.image;
    }
  }
}