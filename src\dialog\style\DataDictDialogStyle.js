
import DictService from "../../service/DictService";
import PageTable from "../../assets/element/jquery/table/PageTable";

import AbstractStyle from "../../AbstractStyle";
export default class DataDictDialogStyle extends AbstractStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName) {
      const params = { currentPage: 1, pageSize: 10 };
      const listContainer = $(`<div class="model-list-wrap">
        <div class="model-head com-row">
          <div class="model-content com-control">
            <input type="text" class="chart-text" placeholder="搜索名称" ` + modelName + `="name" />
            <div class="com-font ft-font icon-shurukuangsousuo"></div>
            <div class="com-btn">搜索</div>
          </div>
        </div>
        <div class="dict-body"></div>
      </div`);
      chartBody.append(listContainer);
      // 修改dialog中model-body样式
      chartBody.css({
        'background': 'none',
        'margin': '0px'
      });
      // .ltab-wrap表格高度自适应弹窗
      setTimeout(() => {
        chartBody.find(".ltab-wrap").css({
          'max-height': ($(window).height() - 520),
        });
      }, 100);
      this.refreshModel(params);
      this.bindModel(params);
      //表格
      const bodyContainer = $(listContainer).find(".dict-body");
      const table = new PageTable();
      table.initPanel(bodyContainer, {
        columns: [
          { key: "typeName", name: "分类名称", type: "text", },
          { key: "nodeKey", name: "分类key", type: "text", },
          { key: "dictType", name: "类型", type: "text", },
          { key: "dictType", name: "类型", type: "text", },
        ],
        events: {
          clickRow: function (index, item) {
            self.setResult(item);
          },
          clickPager: function (pager) {
            params["currentPage"] = pager["currentPage"];
            params["pageSize"] = pager["pageSize"];
            self.queryModel(params, function (result) {
              const pager = {};
              pager["currentPage"] = result.current;
              pager["pageSize"] = result.size;
              pager["totalCount"] = result.total;
              //刷新分页
              table.refreshPager(pager);
              //刷新数据
              table.refreshPanel(result.records);
            })
          }
        }
      });
      //查询-过滤
      const self = this;
      const btnContainer = $(listContainer).find(".dict-head .com-btn");
      $(btnContainer).on("click", function (event) {
        params["currentPage"] = 1;
        params["pageSize"] = 10;
        self.queryDict(params, function (result) {
          //重置分页信息
          table.resetPager();
          //刷新数据
          table.refreshPanel(result.data);
        });
      });
      //查询-列表
      this.queryDict(params, function (result) {
        const pager = {};
        pager["currentPage"] = result.currentPage;
        pager["pageSize"] = result.pageSize;
        pager["totalCount"] = result.totalCount;
        table.refreshPager(pager);
        table.refreshPanel(result.data);
      });

      //表格点击事件
      // $(bodyContainer).on("click", function (event) {
      //   const model = table.item;
      //   self.setTableResult(model);
      // });
    }
  }

  getResult() {
    if (this.model) {
      return this.model;
    }
  }
  setResult(item) {
    this.model = item;
  }

  queryDict(params, callback) {
    //数据模型
    if (params) {
      // const params = { status: "1", resId: dir.id };
      const dictService = new DictService(this.context);
      dictService.queryPage(params, function (list) {
        if (list && list.data.length) {
          for (let item of list.data) {
            //是否开启
            item["isOpen"] = false;
            //是否叶子节点
            item["isLeaf"] = true;
            //是否选中
            item["isChecked"] = false;
            //显示名称
            if (item && item.name) {
              item["name"] = item.name;
            }
          }
        }
        if (callback) {
          callback(list);
        }
      });
    }
  }

  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "dataDict-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "数据字典"
  }
}