
import DataModelService from "../../service/DataModelService";
import ResourcesService from "../../service/ResourcesService";
import PageTable from "../../assets/element/jquery/table/PageTable";

import AbstractStyle from "../../AbstractStyle";
export default class DataModelDialogStyle extends AbstractStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  initModel(container) {
    if (container) {
      const modelContainer = $(`<div class="data-wrap">
      <div class="data-left"></div>
      <div class="data-right"></div>
      </div>`);
      container.append(modelContainer);
      this.leftContainer = $(modelContainer).find(".data-left");
      this.rightContainer = $(modelContainer).find(".data-right");

      //查询数据
      const self = this;
      this.queryData(function (result) {
        if (self.isAllLoadedToModel()) {
          console.info("==============================加载模型数据完成==========================!");
          self.refreshLeft(result);
        }
      });
    }
  }

  /*##############################[queryData]################################# */
  queryData(callback) {
    const self = this;
    const params = { dirTypeCodes: ['DATA_MODEL_DIR'] };
    const resourcesService = new ResourcesService(this.context);
    resourcesService.queryAuthResourcesDir(params, function (result) {
      if (result && result.length) {
        for (var i = 0; i < result.length; i++) {
          const resItem = result[i];
          //修改结构
          resItem["childs"] = resItem.children;
          resItem["name"] = resItem.showName;
          //遍历
          const childs = resItem.children;
          if (childs && childs.length) {
            for (let child of childs) {
              //添加属性-名称
              if (child && child.showName) {
                child["name"] = child.showName;
              }
            }
          }
        }
      }
      if (callback) {
        callback(result);
      }
    });
  }
  /**
     * 描述:加载模型数据是否全部完成
     * @returns 
     */
  isAllLoadedToModel() {
    let flag = true;
    if (this.map) {
      flag = this.isAllLoaded(this.map);
    }
    return flag;
  }
  /**
   * 描述:是否全部加载完
   * @param loadMap
   * @returns {boolean}
   */
  isAllLoaded(loadMap) {
    let flag = true;
    if (loadMap) {
      for (let key in loadMap) {
        const obj = loadMap[key];
        if (obj.$$isLoading) {
          flag = false;
        }
      }
    }
    return flag;
  }
  /*##############################[左边]################################# */
  /**
  * 
  */
  refreshLeft(list) {
    if (this.leftContainer) {
      this.leftContainer.empty();
      this.tree = list;
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          this.refreshNode(this.leftContainer, item);
        }
      }
    }
  }
  refreshNode(parentContainer, item) {
    if (parentContainer && item) {
      const modelContainer = $(`<div class="comm-wrap com-tree" id="` + item.id + `">
      <div class="comm-head tree-head">
        <div class="head-icon ft-font" id="icon`+ item.id + `"></div>
        <div class="head-title">`+ item.name + `</div>
      </div>
      <div class="comm-body tree-body" id="body`+ item.id + `"></div>
      </div>`);
      parentContainer.append(modelContainer);
      const headContainer = $(modelContainer).find(".tree-head");
      const bodyContainer = $(modelContainer).find(".tree-body");
      const titleContainer = $(modelContainer).find(".tree-head .head-title");

      //初始化

      this.refreshOpen(modelContainer, item);
      this.refreshChecked(modelContainer, item);
      //点击
      const self = this;
      $(headContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        item.isOpen = !item.isOpen;
        self.refreshOpen(modelContainer, item);

        // 多选
        // item.isChecked = !item.isChecked;
        // self.refreshChecked(titleContainer, item);
        // 单选
        self.cleanCheckedData(self.tree);
        self.cleanCheckedStyle(self.leftContainer);
        item.isChecked = true;
        self.refreshChecked(titleContainer, item);
      });

      //孩子 递归
      const childs = item["childs"];
      if (childs && childs.length) {
        for (let i = 0; i < childs.length; i++) {
          const child = childs[i];
          this.refreshNode(bodyContainer, child);
        }
      }
    }
  }

  refreshOpen(container, item) {
    if (container && item) {
      //用判断是否有孩子
      const childs = item["childs"];
      if (childs && childs.length) {
        //改用Id 防止向下穿透
        const iconContainer = $(container).find("#icon" + item.id);
        const bodyContainer = $(container).find("#body" + item.id);
        if (item.isOpen) {
          $(bodyContainer).show();
          $(iconContainer).removeClass("icon-xiala");
          $(iconContainer).addClass("icon-shangla");
        } else {
          $(bodyContainer).hide();
          $(iconContainer).removeClass("icon-shangla");
          $(iconContainer).addClass("icon-xiala");
        }
      }
    }
  }
  cleanCheckedData(list) {
    // const list=this.tree;
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        item["isChecked"] = false;
        const childs = item["childs"];
        this.cleanCheckedData(childs);
      }
    }
  }
  cleanCheckedStyle(container) {
    // const container=this.leftContainer;
    if (container) {
      const listContainer = $(container).find(".tree-selected");
      $(listContainer).each(function (index, element) {
        $(element).removeClass("tree-selected");
      });
    }
  }
  refreshChecked(container, item) {
    if (container && item) {
      if (item.isChecked) {
        $(container).addClass("tree-selected");
        //刷新右边
        this.refreshRight(item);
      } else {
        //多选情况
        $(container).removeClass("tree-selected");
      }
    }
  }
  /*##############################[右边]################################# */
  refreshRight(dir) {
    if (this.rightContainer) {
      this.rightContainer.empty();
      const params = { status: "1", resId: dir.id, currentPage: 1, pageSize: 10, };
      const modelName = this.getModelName();
      const listContainer = $(`<div class="model-list-wrap">
        <div class="model-head com-row">
          <div class="model-content com-control">
            <input type="text" class="chart-text" placeholder="搜索名称" ` + modelName + `="name" />
            <div class="com-font ft-font icon-shurukuangsousuo"></div>
            <div class="com-btn">搜索</div>
          </div>
        </div>
        <div class="com-body"></div>
      </div`);
      this.rightContainer.append(listContainer);
      this.refreshModel(params);
      this.bindModel(params);

      const self = this;
      //表格
      const bodyContainer = $(listContainer).find(".com-body");
      // .ltab-wrap表格高度自适应弹窗
      setTimeout(() => {
        bodyContainer.find(".ltab-wrap").css({
          'max-height': ($(window).height() - 510),
        });
      }, 100);
      const table = new PageTable();
      table.initPanel(bodyContainer, {
        columns: [
          { key: "name", name: "名称", type: "text", },
          { key: "type", name: "类型", type: "text", },
        ],
        events: {
          clickRow: function (index, item) {
            self.setResult(item);
          },
          changePager: function (pager) {
            params["currentPage"] = pager["currentPage"];
            params["pageSize"] = pager["pageSize"];
            self.queryModel(params, function (result) {
              const pager = {};
              pager["currentPage"] = result.current;
              pager["pageSize"] = result.size;
              pager["totalCount"] = result.total;
              //刷新分页
              table.refreshPager(pager);
              //刷新数据
              table.refreshPanel(result.records);
            })
          }
        }
      });
      //查询-过滤
      const btnContainer = $(listContainer).find(".model-head .com-btn");
      $(btnContainer).on("click", function (event) {
        params["currentPage"] = 1;
        params["pageSize"] = 10;
        self.queryModel(params, function (result) {
          //重置分页信息
          table.resetPager();
          //刷新数据
          table.refreshPanel(result.records);
        });
      });
      //查询-列表
      this.queryModel(params, function (result) {
        const pager = {};
        pager["currentPage"] = result.current;
        pager["pageSize"] = result.size;
        pager["totalCount"] = result.total;
        //刷新分页
        table.refreshPager(pager);
        //刷新数据
        table.refreshPanel(result.records);
      });
    }
  }

  getResult() {
    if (this.model) {
      return this.model;
    }
  }
  setResult(item) {
    this.model = item;
  }

  queryModel(params, callback) {
    //数据模型
    if (params) {
      const dataModelService = new DataModelService(this.context);
      dataModelService.queryPage(params, function (list) {
        if (list && list.records.length) {
          for (let item of list.records) {
            //是否开启
            item["isOpen"] = false;
            //是否叶子节点
            item["isLeaf"] = true;
            //是否选中
            item["isChecked"] = false;
            //显示名称
            if (item && item.name) {
              item["name"] = item.name;
            }
          }
        }
        if (callback) {
          callback(list);
        }
      });
    }
  }

  refreshBody(BodyContainer) { }

  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "dataModel-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "数据模型"
  }
}