import AbstractStyle from "../../AbstractStyle";
import DataSetModelDialog from "../DataSetModelDialog";
import {readerCsv, readerJson, readerXlsx} from "../../utils/FileUtil";
export default class DataSetAddDialogStyle extends AbstractStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  initModel(container) {
    if (container) {
      const modelContainer = $(`<div class="data-set-add-wrap">
        <div class="add-item-wrap xlsx-import">
            <div class="add-item">
                <div class="add-item-svg add-item-icon-excel"></div>            
            </div>
            <div class="add-item-text">Excel</div>
        </div>
        <div class="add-item-wrap csv-import">
            <div class="add-item">
                <div class="add-item-svg add-item-icon-csv"></div>            
            </div>
            <div class="add-item-text">CSV</div>
        </div>
        <div class="add-item-wrap json-import">
            <div class="add-item">
                <div class="add-item-svg add-item-icon-json"></div>            
            </div>
            <div class="add-item-text">JSON</div>
        </div>
        <div class="add-item-wrap data-model-import">
            <div class="add-item">
                <div class="add-item-svg add-item-icon-model"></div>            
            </div>
            <div class="add-item-text">数据模型</div>
        </div>
        <input type="file" class="file-import-input file-import-input-xlsx" accept=".xlsx" />
        <input type="file" class="file-import-input file-import-input-csv" accept=".csv" />
        <input type="file" class="file-import-input file-import-input-json" accept=".json" />
      </div>`);
      container.append(modelContainer);

      const dataModelImport = modelContainer.find(".data-model-import");
      const xlsxImport = modelContainer.find(".xlsx-import");
      const csvImport = modelContainer.find(".csv-import");
      const jsonImport = modelContainer.find(".json-import");
      const fileImportXlsx = modelContainer.find(".file-import-input-xlsx");
      const fileImportCsv = modelContainer.find(".file-import-input-csv");
      const fileImportJson = modelContainer.find(".file-import-input-json");

      dataModelImport.click(() => {
        const dataSetModelDialog = new DataSetModelDialog(this.context);
        dataSetModelDialog.open((result) => {
          this.callback(result)
        })
      })
      xlsxImport.click(() => {
        fileImportXlsx.trigger('click')
      })
      csvImport.click(() => {
        fileImportCsv.trigger('click')
      })
      jsonImport.click(() => {
        fileImportJson.trigger('click')
      })
      fileImportXlsx.change((e) => {
        const files = e.target.files;
        console.log('files', files)
        if (files && files.length) {
          readerXlsx(files[0], (result) => {
            this.callback(result)
          })
        }
      })
      fileImportCsv.change((e) => {
        const files = e.target.files;
        console.log('files', files)
        if (files && files.length) {
          readerCsv(files[0], (result) => {
            this.callback(result)
          })
        }
      })
      fileImportJson.change((e) => {
        const files = e.target.files;
        console.log('files', files)
        if (files && files.length) {
          readerJson(files[0], (result) => {
            this.callback(result)
          })
        }
      })
    }
  }

  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "数据模型新增"
  }
}