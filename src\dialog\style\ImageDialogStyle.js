import MaterialService from "../../service/MaterialService";
import DirLabelService from "../../service/DirLabelService";
import { toTree } from "../../utils/Util";
import AbstractStyle from "../../AbstractStyle";
import Lottie from "lottie-web";
export default class ImageDialogStyle extends AbstractStyle {
  constructor(context) {
    super(context);
  }

  initModel(container) {
    if (container) {
      const modelContainer = $(`<div class="data-wrap">
        <div class="data-left"></div>
        <div class="data-right"></div>
      </div>`);
      container.append(modelContainer);

      this.leftContainer = $(modelContainer).find(".data-left");
      this.rightContainer = $(modelContainer).find(".data-right");

      const self = this;
      this.queryData(function (result) {
        self.refreshLeft(result);
        self.refreshRight();
      });
    }
  }

  /**
  * 查询目录，并且转为树结构
  * @param {*} callback 
  */
  queryData(callback) {
    const dirLabelService = new DirLabelService(this.context);
    const params = { type: "1", dirType: "material", status: "1" };
    dirLabelService.queryListPath(params, function (result) {
      if (result && result.length) {
        const json = toTree(result, "id", "parentId", "childs");
        if (json && json.tree) {
          if (callback) {
            callback(json.tree);
          }
        }
      }
    });
  }

  /*##############################[左边]################################# */
  /**
  * 
  */
  refreshLeft(list) {
    if (this.leftContainer) {
      this.leftContainer.empty();
      this.tree = list;
      this.treeContainer = this.leftContainer;
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          this.refreshNode(this.leftContainer, item);
        }
      }
    }
  }
  refreshNode(parentContainer, item) {
    if (parentContainer && item) {
      const modelContainer = $(`<div class="comm-wrap com-tree" id="` + item.id + `">
      <div class="comm-head tree-head">
        <div class="head-icon ft-font" id="icon`+ item.id + `"></div>
        <div class="head-title">`+ item.name + `</div>
      </div>
      <div class="comm-body tree-body" id="body`+ item.id + `"></div>
      </div>`);
      parentContainer.append(modelContainer);
      const headContainer = $(modelContainer).find(".tree-head");
      const bodyContainer = $(modelContainer).find(".tree-body");
      const titleContainer = $(modelContainer).find(".tree-head .head-title");

      //初始化
      this.refreshOpen(modelContainer, item);
      this.refreshChecked(modelContainer, item);
      //点击
      const self = this;
      $(headContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        item.isOpen = !item.isOpen;
        self.refreshOpen(modelContainer, item);

        // 单选
        self.cleanChecked(self.tree);
        self.cleanCheckedCss(self.treeContainer);
        if (!item.isChecked) {
          item.isChecked = true;
          self.refreshChecked(titleContainer, item);
        }
      });

      //孩子 递归
      const childs = item["childs"];
      if (childs && childs.length) {
        for (let i = 0; i < childs.length; i++) {
          const child = childs[i];
          this.refreshNode(bodyContainer, child);
        }
      }
    }
  }

  refreshOpen(container, item) {
    if (container && item) {
      //用判断是否有孩子
      const childs = item["childs"];
      if (childs && childs.length) {
        //改用Id 防止向下穿透
        const iconContainer = $(container).find("#icon" + item.id);
        const bodyContainer = $(container).find("#body" + item.id);
        if (item.isOpen) {
          $(bodyContainer).show();
          $(iconContainer).removeClass("icon-xiala");
          $(iconContainer).addClass("icon-shangla");
        } else {
          $(bodyContainer).hide();
          $(iconContainer).removeClass("icon-shangla");
          $(iconContainer).addClass("icon-xiala");
        }
      }
    }
  }
  cleanChecked(list) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        item["isChecked"] = false;
        //处理递归
        const childs = item["childs"];
        this.cleanChecked(childs);
      }
    }
  }
  cleanCheckedCss(container) {
    if (container) {
      const listContainer = $(container).find(".tree-selected");
      $(listContainer).each(function (index, element) {
        $(element).removeClass("tree-selected");
      });
    }
  }
  refreshChecked(container, item) {
    if (container && item) {
      if (item.isChecked) {
        $(container).addClass("tree-selected");
        //刷新右边
        this.refreshRight(item);
      } else {
        //多选情况
        $(container).removeClass("tree-selected");
      }
    }
  }

  /*##############################[右边]################################# */
  refreshRight(dir) {
    if (this.rightContainer) {
      this.rightContainer.empty();
      const modelName = this.getModelName();
      const listContainer = $(`<div class="model-list-wrap">
        <div class="model-head com-row">
          <div class="model-content com-control">
            <input type="text" class="chart-text" placeholder="搜索名称" ` + modelName + `="name" />
            <div class="com-font ft-font icon-shurukuangsousuo"></div>
            <div class="com-btn">搜索</div>
          </div>
        </div>
        <div class="com-body">
            <div class="com-image-list"></div>
            <div class="model-pager"></div>
        </div>
      </div`);
      this.rightContainer.append(listContainer);
      //图片
      this.imageBodyContainer = $(listContainer).find(".com-image-list");
      this.imageBodyContainer.css({
        'max-height': ($(window).height() - 458),
      });
      //3.分页
      this.pagerContainer = $(this.container).find(".model-pager");
      this.refreshModel(params);
      this.bindModel(params);

      const params = { status: "1" };
      if (this.item.type) {
        params["type"] = this.item.type;
      }
      if (dir && dir.id) {
        params["dirId"] = dir.id;
      }
      //查询-过滤
      const btnContainer = $(listContainer).find(".image-head .com-btn");
      const self = this;
      $(btnContainer).on("click", function (event) {
        // params["currentPage"] = 1;
        // params["pageSize"] = 10;
        self.queryImageData(params, function (result) {
          //重置分页信息
          // self.resetPager();
          //刷新数据
          self.refreshPanel(self.imageBodyContainer, modelName, result);
        });
      });

      this.queryImageData(params, function (result) {
        // const pager = {};
        // pager["currentPage"] = result.current;
        // pager["pageSize"] = result.size;
        // pager["totalCount"] = result.total;
        //刷新分页
        // self.refreshPager(pager);
        //刷新数据
        self.refreshPanel(self.imageBodyContainer, modelName, result);
      });
    }
  }

  refreshPanel(chartBody, modelName, list, callback) {
    if (chartBody && modelName) {
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          this.refreshItem(chartBody, item);
        }
      }
    }
  }

  refreshItem(parentContainer, item) {
    if (parentContainer && item) {

      const itemContainer = $(`
        <div class='image-item'>
          <div class='item-block'>
            ${this.item.type === "7" ? '<div class="item-img"></div>' : `<img class='item-img' src="${item.fileUrl}"/>`}
            <div class="item-triangle">
              <div class="item-icon fd-font fd-selected"></div>
            </div>
            <div class='item-title'>${item.name}</div>
          </div>
        </div>`);

      parentContainer.append(itemContainer);

      if (this.item.type === "7") {
        const itemSvg = itemContainer.find('.item-img');
        if (itemSvg && itemSvg.length && item.fileUrl) {
          Lottie.loadAnimation({
            container: itemSvg.get(0),
            loop: true,
            autoplay: true,
            path: item.fileUrl
          });
        }
      }

      $(itemContainer).on("click", (event) => {
        this.refreshImageChecked(itemContainer, item);
      });
    }
  }

  // refreshImageChecked(itemContainer, item) {
  //   if (this.imageBodyContainer) {
  //     const listContainer = $(this.imageBodyContainer).find(".image-item");
  //     $(listContainer).each(function (index, element) {
  //       $(element).removeClass("image-selected");
  //     });
  //     $(itemContainer).addClass("image-selected");
  //     this.setResult(item);
  //   }
  // }

  refreshImageChecked(itemContainer, item) {
    if (this.imageBodyContainer) {
      $(this.imageBodyContainer).find(".image-item .item-triangle").hide();
      $(itemContainer).find('.item-triangle').show();
      this.setResult(item);
    }
  }

  queryImageData(params, callback) {
    //数据模型
    if (params) {
      const materialService = new MaterialService(this.context);
      materialService.queryList(params, function (result) {
        if (result) {
          if (callback) {
            callback(result);
          }
        }
      });
    }
  }

  /**
   * 描述:重置分页信息
   */
  resetPager() {
    if (this.pager) {
      this.pager.reset();
    }
  }
  /**
   * 描述:刷新页面信息
   * @param page
   */
  refreshPager(page) {
    if (this.pagerContainer) {
      this.pagerContainer.empty();
      const self = this;
      this.pager.refresh(this.pagerContainer, page, function (pager) {
        params["currentPage"] = pager["currentPage"];
        params["pageSize"] = pager["pageSize"];
        self.queryImageData(params, function (result) {
          const pager = {};
          pager["currentPage"] = result.current;
          pager["pageSize"] = result.size;
          pager["totalCount"] = result.total;
          //刷新数据
          this.refreshPanel(result.records);
        })
      });
    }
  }

  setResult(item) {
    this.model = item;
  }

  getResult() {
    if (this.model) {
      return this.model;
    }
  }

  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "image-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "图片"
  }
}