import {getUrlParameter, formatDate, addDaysToDate} from "../../utils/Util";
import ResourcesService from "../../service/ResourcesService";
import AbstractStyle from "../../AbstractStyle";
export default class ShareDialogStyle extends AbstractStyle {
  constructor(context) {
    super(context);
    // this.image={};
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // 修改dialog中model-body样式
      chartBody.css({
        'background': 'none',
        'margin': '0px'
      });
      // 链接分享
      const showContainer = $(`<div class="com-row flex">
        <div class="w-50 pr5 flex">
            <div class="com-label">
                <span>链接分享</span>
                <div class="com-label-icon__info ft-font icon-_alert_icon" data-tooltip="此为链接总开关，若关闭他人将无法通过链接打开"></div>
            </div>
            <div class="com-control">
              <label class="preview-switch">
                <input type="checkbox" class="com-checkbox" ` + modelName + `="isEnable" />
                <span class="slider"></span>
              </label>
            </div>
        </div>
      </div>`);
      chartBody.append(showContainer);

      // 访问限制
      const accessResContainer = $(`<div class="com-row flex">
        <div class="w-50 pr5 flex">
            <div class="com-label">访问限制</div>
            <div class="com-control">
              <label class="preview-switch">
                <input type="checkbox" class="com-checkbox" ` + modelName + `="isAccessLimit" />
                <span class="slider"></span>
              </label>
            </div>
        </div>
      </div>`);
      chartBody.append(accessResContainer);

      // 分享类型
      this.limitContainer = $(`<div class="com-row flex">
        <div class="pr5 flex">
            <div class="com-label">分享类型</div>
            <div class="com-control">
              <div class="radio-group">
                <input type="radio" name="share_auth" value="1" id="radio_auth_login" `+ modelName + `="accessType" />
                <label for="radio_auth_login">登录授权访问</label>
                <input type="radio" name="share_auth" value="2" id="radio_auth_pass" `+ modelName + `="accessType" />
                <label for="radio_auth_pass">访问密码</label>
                <input type="radio" name="share_auth" value="3" id="radio_auth_token" `+ modelName + `="accessType" />
                <label for="radio_auth_token">token验证</label>
                <span class="auth_login_tip">
                    开启后，用户必须先进行用户账号密码登陆授权后，才可访问发布页面
                </span>
              </div>
            </div>
        </div>
      </div>`);
      chartBody.append(this.limitContainer);

      //登录授权访问
      this.loginContainer = $(`.radio-group .auth_login_tip`);

      //访问密码
      this.passwordContainer = $(`<div class="com-row">
          <div class="com-label">密码</div>
          <div class="com-control">
            <input type="text" class="com-text"  ` + modelName + `="accessPassword" disabled>
            <div class="com-btn text-btn">生成密钥</div>
          </div>
      </div>`);
      chartBody.append(this.passwordContainer);
      const passTextContainer=$(this.passwordContainer).find(".com-text");
      const passBtnContainer = $(this.passwordContainer).find(".com-btn");
      const self = this;
      $(passBtnContainer).on("click", function (event) {
        const accessPassword = self.getPassword();
        item.accessPassword = accessPassword;
        $(passTextContainer).val(accessPassword);
      })

      //token验证
      this.tokenContainer = $(`<div class="com-row">
          <div class="com-label">token</div>
          <div class="com-control">
            <input type="text" class="com-text"  ` + modelName + `="accessVerifyToken" disabled>
            <div class="com-btn text-btn">获取token</div>
          </div>
      </div>`);
      chartBody.append(this.tokenContainer); 
      const tokenTextContainer=$(this.tokenContainer).find(".com-text");
      const tokenBtnContainer = $(this.tokenContainer).find(".com-btn");
      $(tokenBtnContainer).on("click", function (event) {
        self.getToken(function (result) {
          if(result){
            const accessVerifyToken = result.token;
            item.accessVerifyToken = accessVerifyToken;
            $(tokenTextContainer).val(accessVerifyToken);
          }
        });
      })

      // 验证有效期
      const validPeriodContainer = $(`<div class="com-row flex">
        <div class="w-50 pr5 flex">
            <div class="com-label">验证有效期</div>
            <div class="com-control">
              <label class="preview-switch">
                <input type="checkbox" class="com-checkbox" ` + modelName + `="isVerifyDate" />
                <span class="slider"></span>
              </label>
            </div>
        </div>
      </div>`);
      chartBody.append(validPeriodContainer);

      // 有效期至
      this.expireOptionContainer = $(`<div class="com-row flex">
        <div class="pr5 flex">
            <div class="com-label">有效期至</div>
            <div class="com-control">
              <div class="radio-group">
                <input type="radio" name="expire_option" ` + modelName + `="expireOption" value="99" id="expire_option99" />
                <label for="expire_option99">永久有效</label>
                <input type="radio" name="expire_option" ` + modelName + `="expireOption" value="1" id="expire_option1" />
                <label for="expire_option1">1天</label>
                <input type="radio" name="expire_option" ` + modelName + `="expireOption" value="3" id="expire_option3" />
                <label for="expire_option3">3天</label>
                <input type="radio" name="expire_option" ` + modelName + `="expireOption" value="7" id="expire_option7" />
                <label for="expire_option7">7天</label>
                <input type="radio" name="expire_option" ` + modelName + `="expireOption" value="15" id="expire_option15" />
                <label for="expire_option15">15天</label>
                <input type="radio" name="expire_option" ` + modelName + `="expireOption" value="30" id="expire_option30" />
                <label for="expire_option30">30天</label>
                <input type="radio" name="expire_option" ` + modelName + `="expireOption" value="0" id="expire_option0" />
                <label for="expire_option0">自定义</label>
              </div>
            </div>
        </div>
      </div>`);
      chartBody.append(this.expireOptionContainer);

      // 链接失效时间
      this.periodContainer = $(`<div class="com-row">
      <div class="com-label">链接失效时间</div>
          <div class="com-control">
                <input type="text" class="lsd-date-filter" readonly='readonly' placeholder="请选择日期" ` + modelName + `="datefilter.realDate.initDate" />
          </div>
      </div>`);
      chartBody.append(this.periodContainer);
      this.dateContainer = $(this.periodContainer).find('.lsd-date-filter');
      this.refreshPicker(item.datefilter);
      //链接
      const shareUrlContainer = $(`<div class="com-row">
          <div class="com-label">链接</div>
          <div class="com-control">
            <input type="text" class="com-text"  ` + modelName + `="shareUrl" disabled>
            <div class="com-btn text-btn">复制链接</div>
          </div>
      </div>`);
      chartBody.append(shareUrlContainer);
      const copyContainer = $(shareUrlContainer).find(".com-btn");
      $(copyContainer).on("click", function (event) {
        self.copyToClipboard();
      })

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    if (key && key === "isAccessLimit") {
      if (value === true) {
        this.limitContainer.show();
      } else {
        this.limitContainer.hide();
        this.loginContainer.hide();
        this.passwordContainer.hide();
        this.tokenContainer.hide();
      }
    }

    if(key && key === "accessType" ){
      if(value === "1"){
        this.loginContainer.show();
        this.passwordContainer.hide();
        this.tokenContainer.hide();
      } else if(value === '2'){
        this.loginContainer.hide();
        this.passwordContainer.show();
        this.tokenContainer.hide();
      } else if(value === '3'){
        this.loginContainer.hide();
        this.passwordContainer.hide();
        this.tokenContainer.show();
      }else{
        this.loginContainer.hide();
        this.passwordContainer.hide();
        this.tokenContainer.hide();
      }
    }

    if(key && key === "isVerifyDate" ){
      if(value === true){
        this.expireOptionContainer.show();
        this.periodContainer.show();
      }else{
        this.expireOptionContainer.hide();
        this.periodContainer.hide();
      }
    }

    if (key && key === "expireOption") {
      // 自定义可编辑过期时间
      this.dateContainer.attr('disabled', value !== '0')
      const FormatStr = 'yyyy-MM-dd';
      const option2date = {
        '99': '2999-01-01',
        '1': formatDate(addDaysToDate(1), FormatStr),
        '3': formatDate(addDaysToDate(3), FormatStr),
        '7': formatDate(addDaysToDate(7), FormatStr),
        '15': formatDate(addDaysToDate(15), FormatStr),
        '30': formatDate(addDaysToDate(30), FormatStr),
      }
      this.dateContainer.val(option2date[value]);
      this.item.datefilter.realDate.initDate = option2date[value];
    }
  }
  getModelName () {
    return "share-model";
  }
  
  refreshPicker (datefilter) {
    if (this.dateContainer && datefilter) {
      this.dateContainer.empty();
      // input 赋值 显示
      if (datefilter.realDate.initDate) {
        $(this.dateContainer).val(datefilter.realDate.initDate);
      }
      //绑定日期控件
      $(this.dateContainer).datetimepicker(datefilter).on('changeDate', (ev) => {
        // datefilter.realDate.initDate  = formatDate(ev.date, datefilter.format);
      });
    }
  }

  /*
    补零
    */
    appendzero (obj) {
      if (obj < 10) return "0" + "" + obj;
      else return obj;
    }

  //获取密钥
  getPassword(){
    let code = '';
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      const charactersLength = characters.length;
      for (let i = 0; i < 4; i++) {
        code += characters.charAt(Math.floor(Math.random() * charactersLength));
      }
      return code;
  }

  //获取token
  getToken(callback){
    const id = getUrlParameter("id")
    let params = {
      businessId: id,
    };
    const resourcesService = new ResourcesService(this.context);
    resourcesService.getAccessVerifyToken(params,function(result) {
      console.log("token返回结果"+result);
      if(callback){
        callback(result);
      }
    });
  }

  //复制链接
  copyToClipboard() {
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(this.item.shareUrl);
    } else {
      const textArea = document.createElement("textarea");
      textArea.value = this.item.shareUrl;

      textArea.style.position = "absolute";
      textArea.style.left = "-999999px";

      document.body.prepend(textArea);
      textArea.select();

      try {
        document.execCommand("copy");
      } catch (error) {
        console.error(error);
      } finally {
        textArea.remove();
      }
    }
    alert('复制成功');
  }

  getResult(){
    return this.item;
  }

  
}