import TemplTreeDialog from "../TemplTreeDialog";
import { triggerIframe, senderMessage } from "../../utils/ListenerUtil"
import DictService from "../../service/DictService";
import AbstractStyle from "../../AbstractStyle";
export default class TemplDialogStyle extends AbstractStyle {
  constructor(context) {
    super(context);
    // this.iframePath = window.location.origin+"/designer/lsd/preview.html";
    // this.iframePath = "./template.html";
    this.iframePath = "./preview.html";
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // 修改dialog中model-body样式
      chartBody.css({
        'background': 'none',
        'margin': '0px'
      });
      const self = this;
      const nameContainer = $(`<div class="com-row">
      <div class="com-label"><span style="color: #F56C6C;margin-right:4px">*</span>名称</div>
      <div class="com-control com-verify">
        <input type="text" class="com-text" placeholder="请输入名称" ` + modelName + `="name"  />
        <div class="errorHint">名称不能为空</div>
      </div>
      </div>`);
      chartBody.append(nameContainer);
      // const remarkContainer = $(`<div class="com-row">
      // <div class="com-label">备注</div>
      // <div class="com-control">
      //   <textarea type="text" class="com-text" placeholder="备注" ` + modelName + `="remark"  />
      // </div>
      // </div>`);
      // chartBody.append(remarkContainer);
      const dirContainer = $(`<div class="com-row">
      <div class="com-label">目录</div>
      <div class="com-control flex">
        <input type="text" class="com-text" placeholder="请选择目录" ` + modelName + `="dirName" disabled>
        <div class="com-icon fd-font fd-editor"></div>
      </div>
      </div>`);
      chartBody.append(dirContainer);
      const dirTextContainer = $(dirContainer).find(".com-text");
      const dirBtnContainer = $(dirContainer).find(".com-icon");
      $(dirBtnContainer).on("click", function (event) {
        const treeDialog = new TemplTreeDialog(self.context, { type: "1", dirType: item.dirType });
        treeDialog.open(function (list) {
          if (list && list.length) {
            for (let i = 0; i < list.length; i++) {
              if (list[i]["id"]) {
                item["dirId"] = list[i]["id"];
              }
              if (list[i]["name"]) {
                item["dirName"] = list[i]["name"];
              }
              $(dirTextContainer).val(item["dirName"]);
            }
          }
        });
      });
      const tagContainer = $(`<div class="com-row">
      <div class="com-label">标签</div>
      <div class="com-control flex">
        <input type="text" class="com-text" placeholder="请选择标签" ` + modelName + `="tagName" disabled>
        <div class="com-icon fd-font fd-editor"></div>
      </div>
      </div>`);
      chartBody.append(tagContainer);
      const tagTextContainer = $(tagContainer).find(".com-text");
      const tagBtnContainer = $(tagContainer).find(".com-icon");
      $(tagBtnContainer).on("click", function (event) {
        const treeDialog = new TemplTreeDialog(self.context, { type: "2" });
        treeDialog.open(function (list) {
          let tagIds = [];
          let tagName = "";
          if (list && list.length) {
            for (let i = 0; i < list.length; i++) {
              if (list[i]["id"]) {
                tagIds.push(list[i]["id"]);
              }
              if (list[i]["name"]) {
                if (i == list.length - 1) {
                  tagName += list[i]["name"]
                } else {
                  tagName += list[i]["name"] + ",";
                }
              }
            }
          }
          $(tagTextContainer).val(tagName);
          item["tagIds"] = tagIds;
        });
      });

      if (item.type && item.type === "1") {
        const type1Container = $(`<div class="com-row">
        <div class="com-label">类型</div>
        <div class="com-control">
          <select class="com-select" ` + modelName + `="compType" >
            <option value="">请选择</option>
            <option value="line">折线</option>
            <option value="bar">柱状</option>
            <option value="pie">饼图</option>
            <option value="map">地图</option>
          </select>
        </div>
        </div>`);
        chartBody.append(type1Container);
        this.type1select = $(type1Container).find(".com-select");

        const type2Container = $(`<div class="com-row">
        <div class="com-label">子类型</div>
        <div class="com-control">
          <select class="com-select" ` + modelName + `="compTypeChild">
            <option value="">请选择</option>
            <option value="line">折线</option>
            <option value="bar">柱状</option>
            <option value="pie">饼图</option>
            <option value="map">地图</option>
          </select>
        </div>
        </div>`);
        chartBody.append(type2Container);
        this.type2select = $(type2Container).find(".com-select");
        this.refreshData();
      }
      //比例
      const ratioTypeContainer = $(`<div class="com-row">
        <div class="com-label">比例</div>
        <div class="com-control">
          <select class="com-select" ` + modelName + `="ratioType" >
            <option value="">请选择</option>
            <option value="1">16/9</option>
            <option value="2">4/3</option>
          </select>
        </div>
        </div>`);
      chartBody.append(ratioTypeContainer);

      //iframe
      const iframeContainer = $(`<div class="com-row">
        <div class="com-label">预览图</div>
        <iframe class="iframeBody" width="100%" src="` + this.iframePath + `"></iframe>
      </div>`);
      chartBody.append(iframeContainer);
      chartBody.append(iframeContainer);
      const iframeBody = $(iframeContainer).find('.iframeBody');
      //保存图片用
      item.iframeContainer = iframeBody;
      //传递
      this.iframeBody = iframeBody;


      //发送内容
      // const params = { tempLSD: { data: item.screen } };
      // senderMessage(iframeBody, params);
      // 测试用
      // const width = $(iframeBody).width();
      // const innerWidth = $(iframeBody).innerWidth();
      // const outerWidth = $(iframeBody).outerWidth();
      // console.info("width:", width);
      // console.info("innerWidth:", innerWidth);
      // console.info("outerWidth:", outerWidth);
      // var iframe = $('#myframe');
      // var src = iframe.attr('src');
      // iframe.attr('src', ''); // 清空src属性来清空缓存
      // iframe.attr('src', src); // 重新设置src属性以触发重新加载

      // iframeContainer[0].onload = function(){ //width="528" height="396" height="297"
      //   setTimeout(()=> {
      //     let iframeBody =  iframeContainer[0].contentDocument.body;
      //     self.submit.convertBase64Img(iframeBody,function(base64){
      //       // console.log(base64);
      //       item["image"] = base64;
      //     });
      //   }, 1000) // 等待页面echart渲染完成
      // }

      // const imgContainer = $(`<div class="com-row flex">
      // <div class="com-label">缩略图</div>
      // <div class="com-control">
      //   <button class="com-btn text-btn">生成缩略图</button>
      //   <img class="com-img width:"300px" height:"400px"/>
      // </div>
      // </div>`);
      // chartBody.append(imgContainer);
      // const imageContainer = imgContainer.find(".com-img");
      // const imgBtnContainer = imgContainer.find(".com-btn");
      // imgBtnContainer.on("click", function () {
      //   // const canvasElement = iframeContainer[0];
      //   // const canvasElement = iframeContainer[0].contentDocument.getElementById("body-container");
      //   const iframeBody=iframeContainer[0].contentDocument.body;
      //   // const canvasElement=iframeBody;
      //   // const canvasElement = document.getElementById('body-container');
      //   // const previewElement = iframeBody.getElementsByClassName('preview-container');
      //   const previewContainer=$(iframeBody).find(".preview-container");
      //   const canvasElement=previewContainer[0];
      //   self.convertBase64Img(canvasElement, function (base64) {
      //     if (imageContainer) {
      //       $(imageContainer).attr("src", base64);
      //     }
      //   });
      // });

      this.refreshModel(item.screen);
      this.bindModel(item.screen, callback);
    }
  }

  getResult () {
    return this.item;
  }

  refreshEvent (key, value) {
    if (key && key === "compType") {
      if (value) {
        this.refreshData(value);
      }
    }
    if (key && key === "ratioType") {
      if (value && value === "2") {
        this.refreshIframe("2");
      } else {
        this.refreshIframe("1");
      }
    }
  }
  refreshIframe (type) {
    const self = this;
    const iframeBody = this.iframeBody;
    triggerIframe(iframeBody, function () {
      self.handleIframe(iframeBody, type);
    });
  }
  handleIframe (iframeBody, type) {
    const data = this.item.screen;
    if (iframeBody && data) {
      const width = $(iframeBody).width();
      let height;
      if (type && type === "2") {
        height = width * 3 / 4;
      } else {
        height = width * 9 / 16;
      }
      $(iframeBody).height(height);
      const params = { tempLSD: { data: data } };
      senderMessage(iframeBody, params);
    }
  }
  // refreshIframe (type) {
  //   const iframeBody = this.iframeBody;
  //   const iframePath = this.iframePath;
  //   const data = this.item.screen;
  //   if (iframeBody && iframePath && data) {
  //     iframeBody.attr('src', ''); //清空缓存
  //     iframeBody.attr('src', iframePath); // 重新加载
  //     $(iframeBody).on('load', function () {
  //       const width = $(iframeBody).width();
  //       let height;
  //       if (type) {
  //         height = width * 3 / 4;
  //       } else {
  //         height = width * 9 / 16;
  //       }
  //       $(iframeBody).height(height);
  //       data["config"] = { width: width, height: height };
  //       const params = { tempLSD: { data: data } };
  //       senderMessage(iframeBody, params);
  //     });
  //   }
  // }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "templ-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "模板"
  }
  // convertBase64Img (element, callback) {
  //   if (element) {
  //     const rect = element.getBoundingClientRect();
  //     // 生成图片并上传到数据库保存
  //     html2canvas(element, {
  //       scale: 2, // 数值越大生成的图片越清晰
  //       useCORS: true,
  //       allowTaint: true,
  //       y: 0, // 滚动条高度修复
  //       x: 0,
  //       scrollX: 0,
  //       scrollY: rect.top, // 关键代码
  //       height: rect.height, // 加高度，避免截取不全
  //     }).then((canvas) => {
  //       let base64 = canvas.toDataURL("image/jpeg"); // 拿到数据流
  //       if (callback) {
  //         callback(base64);
  //       }
  //     })
  //   }
  // }
  refreshData (parentKey) {
    const self = this;
    const params = { status: 1, nodeKey: "LSD_ASSEMBLY_TYPE", level: "1" };
    if (parentKey) {
      params["parentitemKey"] = parentKey;
      params["level"] = "2";
    }
    const dictService = new DictService(this.context);
    dictService.queryDataByKey(params, function (result) {
      let objs;
      const list = result;
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          const obj = { key: item.itemKey, text: item.itemName };
          if (!objs) {
            objs = [];
          }
          objs.push(obj);
        }
      }
      if (parentKey) {
        if (objs && self.type2select) {
          self.refreshOption(self.type2select, objs);
        }
      } else {
        if (objs && self.type1select) {
          self.refreshOption(self.type1select, objs);
        }
      }
    });
  }
}