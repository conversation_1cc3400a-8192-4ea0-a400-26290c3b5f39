import AbstractStyle from "../../AbstractStyle";
export default class TemplTreeDialogStyle extends AbstractStyle {
  constructor(context, isMulti) {
    super(context);
    this.isMulti = isMulti || false;
  }
  // initModel(container){
  //   if (container) {
  //     container.empty();
  //   }
  // }
  refreshBody(bodyContainer) {
    if (bodyContainer) {
      // 修改dialog中model-body样式
      bodyContainer.css({
        'background': 'none',
        'margin': '0px'
      });
      bodyContainer.empty();
      this.bodyContainer = bodyContainer;
      const list = this.list;
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          this.refreshNode(this.bodyContainer, item);
        }
      }
    }
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "templ-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "模板"
  }

  getResult() {
    let result = [];
    const list = this.list;
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        this.parseResult(result, item);
      }
    }
    return result;
  }
  parseResult(result, item, parent) {
    if (item) {
      if (item["isChecked"]) {
        result.push(item);
      }
      const childs = item["childs"];
      if (childs && childs.length) {
        for (let i = 0; i < childs.length; i++) {
          const child = childs[i];
          this.parseResult(result, child);
        }
      }
    }
  }

  refreshNode(parentContainer, item) {
    if (parentContainer && item) {
      const modelContainer = $(`<div class="comm-wrap com-tree" id="` + item.id + `">
      <div class="comm-head tree-head">
        <div class="head-icon ft-font" id="icon`+ item.id + `"></div>
        <div class="radio-container">
           <div class="checkMark"></div>
        </div>
        <div class="multiple-container">
           <div class="multiple fd-font fd-selected"></div>
        </div>
        <div class="head-title">`+ item.name + `</div>
      </div>
      <div class="comm-body tree-body" id="body`+ item.id + `"></div>
      </div>`);
      parentContainer.append(modelContainer);
      const headContainer = $(modelContainer).find(".tree-head");
      const bodyContainer = $(modelContainer).find(".tree-body");
      const titleContainer = $(modelContainer).find(".tree-head .head-title");
      const radioContainer = $(modelContainer).find(".tree-head .radio-container")
      const multipleContainer = $(modelContainer).find(".tree-head .multiple-container")

      //初始化

      this.refreshOpen(modelContainer, item);
      this.refreshChecked(modelContainer, item);
      //点击
      const self = this;
      $(headContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        item.isOpen = !item.isOpen;
        self.refreshOpen(modelContainer, item);
      });
      // 初始化单选/多选判断
      if (self.isMulti) {
        radioContainer.hide();
        // 多选
        $(multipleContainer).on('click', function (event) {
          event.stopPropagation();
          item.isChecked = !item.isChecked;
          self.refreshChecked(multipleContainer, item);
        })
      } else {
        multipleContainer.hide();
        // 单选
        $(radioContainer).on('click', function (event) {
          event.stopPropagation();
          //单选 清楚所有选中项
          self.cleanCheckedData(self.list);
          self.cleanCheckedStyle(self.bodyContainer);
          item.isChecked = true;
          self.refreshChecked(radioContainer, item);
        })
      }
      //孩子 递归
      const childs = item["childs"];
      if (childs && childs.length) {
        for (let i = 0; i < childs.length; i++) {
          const child = childs[i];
          this.refreshNode(bodyContainer, child);
        }
      }
    }
  }

  /**
   * 设置选中回调
   * @param onCheckedChange
   */
  setOnCheckedChange(onCheckedChange) {
    this.onCheckedChange = onCheckedChange;
  };
  refreshOpen(container, item) {
    if (container && item) {
      //用判断是否有孩子
      const childs = item["childs"];
      if (childs && childs.length) {
        //改用Id 防止向下穿透
        const iconContainer = $(container).find("#icon" + item.id);
        const bodyContainer = $(container).find("#body" + item.id);
        if (item.isOpen) {
          $(bodyContainer).show();
          $(iconContainer).removeClass("icon-xiala");
          $(iconContainer).addClass("icon-shangla");
        } else {
          $(bodyContainer).hide();
          $(iconContainer).removeClass("icon-shangla");
          $(iconContainer).addClass("icon-xiala");
        }
      }
    }
  }
  cleanCheckedData(list) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        item["isChecked"] = false;
        const childs = item["childs"];
        this.cleanCheckedData(childs);
      }
    }
  }
  cleanCheckedStyle(container) {
    if (container) {
      const listContainer = $(container).find(".tree-checked");
      $(listContainer).each(function (index, element) {
        $(element).removeClass("tree-checked");
      });
    }
  }
  refreshChecked(container, item) {
    if (container && item) {
      if (item.isChecked) {
        $(container).addClass("tree-checked");
      } else {
        //多选情况
        $(container).removeClass("tree-checked");
      }
      // 触发回调
      this.onCheckedChange && this.onCheckedChange();
    }
  }

}