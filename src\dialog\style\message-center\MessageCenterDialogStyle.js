import MessageStyle from "./MessageStyle";

export default class MessageCenterDialogStyle {
  constructor(context) {
    this.context = context;
  }
  initPanel (chartBody, list, callback) {
    if (chartBody) {
      this.list = list;
      const addContainer = $(`<div class="dialog-model-add-wrap">
        <div class="add-title">消息详情</div>
        <span class="add-icon ft-font icon-xinzeng1"></span>
      </div>`);
      chartBody.append(addContainer);
      const listContainer = $(`<div class="dialog-model-list-wrap"></div>`);
      chartBody.append(listContainer);
      const addIcon = addContainer.find(".icon-xinzeng1");
      // 添加消息
      addIcon.click(() => {
        if (this.list && Array.isArray(this.list)) {
          let maxId = 0;
          if (this.list.length) {
            maxId = Math.max.apply(null, this.list.map((item) => item.id))
          }
          this.list.push({
            id: maxId + 1,
            name: `消息${maxId + 1}`,
            condition: "",
            isOpen: true,
            links: []
          })
          // 添加完成刷新面板
          this.refreshPanel(listContainer);
        }
      })
      this.callback = callback;
      this.refreshPanel(listContainer);
    }
  }

  refreshPanel (listContainer) {
    if (listContainer && this.list && Array.isArray(this.list)) {
      listContainer.empty();
      for (let i = 0; i < this.list.length; i++) {
        const item = this.list[i];
        const messageStyle = new MessageStyle(this.context)
        messageStyle.initPanel(listContainer, item, this.callback);
        messageStyle.setDeleteCallback((id) => {
          if (this.list && Array.isArray(this.list)) {
            const index = this.list.findIndex((item) => item.id === id);
            if (index > -1) {
              this.list.splice(index, 1);
              // 删除完成刷新面板
              this.refreshPanel(listContainer);
            }
          }
        })
      }
    }
  }
  getModelName () {
    return "message-center-model";
  }
}