import AbstractStyle from "../../../AbstractStyle";

export default class MessageLinkStyle extends AbstractStyle {
  constructor(context) {
    super(context);
  }
  setDeleteCallback (deleteCallback) {
    this.deleteCallback = deleteCallback;
  }

  refreshPanel(chartBody, modelName, item, callback) {
    const linkItemContainer = $(`<div class="link-item-wrap">
            <div class="item-header">
              <div class="item-title">${item.name}</div>
              <div class="flex">
                  <span class="header-icon ft-font icon-close"></span>
                  <span class="header-icon ft-font icon-ArrowDownBold"></span>
              </div>
            </div>
        </div>`);
    chartBody.append(linkItemContainer);

    const paramContainer = $(`<div class="chart-item flex mt10">
        <div class="chart-label">联动参数</div>
          <div class="chart-control">
              <select class="chart-select" ` + modelName + `="param">
              </select>
          </div>
      </div>`);
    linkItemContainer.append(paramContainer);
    this.refreshOption(paramContainer.find('.chart-select'), this.getParamOptions());

    const constContainer = $(`<div class="chart-item flex">
            <div class="chart-label">常量</div>
            <div class="chart-control">
                <input type="text" class="chart-text" placeholder="常量" ` + modelName + `="constValue" />
            </div>
        </div>`);
    linkItemContainer.append(constContainer);

    const headerContainer = linkItemContainer.find('.item-header');
    const iconClose = headerContainer.find('.icon-close');
    const arrowDownIcon = headerContainer.find('.icon-ArrowDownBold');
    headerContainer.click(() => {
      item.isOpen = !item.isOpen;
      arrowDownIcon.attr('switch', item.isOpen ? 'open' : 'close')
      // 点击时切换
      paramContainer.toggle();
      constContainer.toggle();
    })
    // 初始化时切换
    if (item.isOpen) {
      paramContainer.show();
      constContainer.show();
    } else {
      paramContainer.hide();
      constContainer.hide();
    }

    // 删除消息
    iconClose.click((event) => {
      event.stopPropagation()
      this.deleteCallback(item.id)
    })

    this.refreshModel(item);
    this.bindModel(item, callback);
  }

  getParamOptions (isExpr) {
    let options;
    const paramMap = this.context.getParamMap();
    if (paramMap) {
      for (let key in paramMap) {
        const param = paramMap[key];
        const text = param.property.code;
        let expr;
        if (isExpr) {
          expr = "${" + text + "}"
        } else {
          expr = text;
        }
        const option = { key: expr, text: expr };
        if (!options) {
          options = [];
        }
        options.push(option);
      }
    }
    return options;
  }

  refreshEvent(key, value) {

  }

  getModelName() {
    return "message-link-model";
  }
}