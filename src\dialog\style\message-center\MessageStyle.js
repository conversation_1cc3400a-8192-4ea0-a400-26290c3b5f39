import AbstractStyle from "../../../AbstractStyle";
import MessageLinkStyle from "./MessageLinkStyle";

export default class MessageStyle extends AbstractStyle {
  constructor(context) {
    super(context);
  }

  setDeleteCallback(deleteCallback) {
    this.deleteCallback = deleteCallback;
  }

  refreshPanel(chartBody, modelName, item, callback) {
    const listContainer = $(`<div class="message-list-wrap">
            <div class="message-header" mode="view">
                <div class="header-wrap">
                    <span class="header-text">${item.name}</span>
                    <span class="header-icon ft-font icon-bianji ml4"></span>
                </div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="消息名称" ` + modelName + `="name" />
                    <span class="header-icon ft-font icon-tuihui"></span>
                </div>
                <div class="flex">
                    <span class="header-icon ft-font icon-close"></span>
                    <span class="header-icon ft-font icon-ArrowDownBold"></span>
                </div>
            </div>
        </div>`);
    chartBody.append(listContainer);

    const messageBodyContainer = $(`<div class="message-body-wrap">
            <div class="chart-item flex">
                <div class="chart-label">消息名称</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="消息名称" ` + modelName + `="messageName" />
                </div>
            </div>
            <div class="chart-item flex">
                <div class="chart-label">条件</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="条件" ` + modelName + `="condition" />
                </div>
            </div>
        </div>`);
    listContainer.append(messageBodyContainer);

    const linkAddContainer = $(`<div class="link-add-wrap">
            <div class="add-title">联动列表</div>
            <span class="add-icon ft-font icon-xinzeng1"></span>
        </div>`);
    messageBodyContainer.append(linkAddContainer);

    const linkListContainer = $(`<div class="link-list-wrap"></div>`);
    messageBodyContainer.append(linkListContainer);

    if (!item.links) {
      item.links = [];
    }
    this.refreshLinks(linkListContainer, item.links);


    const headerContainer = listContainer.find('.message-header');
    const editIcon = headerContainer.find('.icon-bianji');
    const editOverIcon = headerContainer.find('.icon-tuihui');
    const closeIcon = headerContainer.find('.icon-close');
    const arrowDownIcon = headerContainer.find('.icon-ArrowDownBold');
    headerContainer.click((event) => {
      if (event.target === event.currentTarget || event.target === arrowDownIcon.get(0)) {
        messageBodyContainer.toggle();
        item.isOpen = !item.isOpen;
        arrowDownIcon.attr('switch', item.isOpen ? 'open' : 'close')
      }
    })
    if (item.isOpen) {
      messageBodyContainer.show();
    } else {
      messageBodyContainer.hide();
    }
    editIcon.click(() => {
      // 点击编辑图标显示输入框
      headerContainer.attr('mode', 'edit');
    })
    editOverIcon.click(() => {
      // 编辑结束显示标题，并且赋值
      headerContainer.attr('mode', 'view');
      headerContainer.find('.header-text').text(item.name)
    })
    closeIcon.click(() => {
      this.deleteCallback(item.id);
    })


    const linkAdd = linkAddContainer.find('.icon-xinzeng1');
    linkAdd.click(() => {
      let maxId = 0;
      if (item.links.length) {
        maxId = Math.max.apply(null, item.links.map((item) => item.id))
      }
      item.links.push({
        isOpen: true,
        id: maxId + 1,
        name: `联动${maxId + 1}配置`,
        param: "",
        constValue: "",
      });
      this.refreshLinks(linkListContainer, item.links);
    })

    this.refreshModel(item);
    this.bindModel(item, callback);
  }

  refreshLinks(linkListContainer, links = []) {
    linkListContainer && linkListContainer.empty();
    for (let i = 0; i < links.length; i++) {
      const link = links[i];
      const messageLinkStyle = new MessageLinkStyle(this.context);
      messageLinkStyle.initPanel(linkListContainer, link);
      messageLinkStyle.setDeleteCallback((id) => {
        if (links && Array.isArray(links)) {
          const index = links.findIndex((item) => item.id === id);
          if (index > -1) {
            links.splice(index, 1);
            this.refreshLinks(linkListContainer, links);
          }
        }
      })
    }
  }

  refreshEvent(key, value) {

  }

  getModelName() {
    return "message-list-model";
  }
}