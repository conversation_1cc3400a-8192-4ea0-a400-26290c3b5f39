import "./css/draw.css";
import CanvasPanel from "./panel/CanvasPanel";
import SliderBar from "../assets/element/jquery/slider/Slider";
export default class DrawPanel {
  constructor(context) {
    this.context = context;
  }
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="draw-container"></div>`);
      parentContainer.append(this.container);
    }
  }
  /**
   * 描述:刷新面板
   */
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      //绘画主板容器
      this.mainContainer = $(`<div class="draw-main"></div>`);
      this.container.append(this.mainContainer);
      //设置比例
      // this.context.setScale(this.container);
      // const scale = this.context.getScale();
      //添加画布
      const canvasPanel = new CanvasPanel(this.context);
      canvasPanel.initPanel(this.mainContainer);
      canvasPanel.refreshPanel();
      // canvasPanel.refreshScale(scale);
      this.canvasPanel = canvasPanel;
      //滑动条容器
      const sliderContainer = $(`<div class="slider-bar"></div>`);
      this.container.append(sliderContainer);
      //滑动条
      const sliderBar = new SliderBar(this.context);
      sliderBar.initPanel(sliderContainer);
      sliderBar.refreshPanel();
      sliderBar.refreshEvent({
        slide: (value) => {
          if (canvasPanel && canvasPanel.refreshScale) {
            this.refreshScale(value);
          }
        }
      });
      this.sliderBar = sliderBar;

      //绘画工具容器
      const toolContainer = $(`<div class="draw-tool"></div>`);
      this.container.append(toolContainer);
      //初始化-刷新分辨率/背景
      this.refreshScale();
      this.refreshBackground();
    }
  }
  scrollTo(x = 0, y = 0) {
    if (this.mainContainer && this.mainContainer.length) {
      this.mainContainer.scrollTop(y)
      this.mainContainer.scrollLeft(x)
    }
  }
  /**
   * 获取-画布图层
   */
  getCanvasContainer () {
    if (this.canvasPanel) {
      return this.canvasPanel.getCanvasContainer();
    }
  }
  /**
   * 刷新比例
   */
  refreshScale (value) {
    if (!(value || value === 0)) {
      // 大屏第一次加载完成之后的比例
      this.context.buildScale(this.container);
      value = this.context.getScale();
    }
    this.context.setScale(value);
    if(this.sliderBar){
      this.sliderBar.refreshScale(value);
    }
    if(this.canvasPanel){
      this.canvasPanel.refreshScale(value);
    }
  }
  /**
   * 刷新背景
   */
  refreshBackground(){
    if(this.canvasPanel){
      this.canvasPanel.refreshBackground();
    }
  }
}