.draw-container{
  height: 100%;
  width: 100%;
}
.draw-main{
  height: 100%;
  width: 100%;
  overflow: auto;
}
.canvas-container{
  position: relative;
  height: 100%;
  width: 100%;
}
.canvas-container .scale-mark-x{
  height: 10px;
  margin-left: 10px;
  width: 100%;
}
.canvas-container .scale-mark-y{
  position: absolute;
  width: 10px;
  height: 100%;
}
.canvas-container .scale-canvas{
  width: 100%;
  height: 100%;
  /* transform: scale(0.45); */
  background-color: rgb(43, 51, 64);
  position: relative;
  margin: 0 10px;
  background-size:100% 100%;
  transform-origin:0 0;

  cursor: pointer;
  outline: none;
  /* 这会使光标也被隐藏，如果需要的话可以去掉这行 */
  caret-color: transparent;
  border: none;
  resize: none;
}
.slider-bar{
  position: absolute;
  bottom: 10px;
  left: 10px;
}

.delTag {
  width: 45px;
  height: 22px;
  background: rgba(43, 51, 64, 0.8);
  border-radius: 2px;
  color: #ccc;
  z-index: 2000;
  position: absolute;
  top: 0;
  right: 0;
  text-align: center;
  display: none;
  cursor: pointer
}

.activeMask {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1801
}

.cptDiv {
  position: absolute;
  outline: none
}

.cptDiv:hover .delTag {
  display: block
}

.resizeTag {
  width: 8px;
  height: 8px;
  position: absolute;
  background-color: #B6BFCE;
  z-index: 2000;
  border-radius: 50%;
}

.configBtn:hover {
  cursor: pointer;
  color: #B6BFCE
}

.el-dropdown-link {
  cursor: pointer;
  color: #fff;
}
