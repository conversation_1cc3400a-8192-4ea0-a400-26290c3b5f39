import ScaleMarkX from "../../assets/element/jquery/scaleMark/ScaleMarkX";
import ScaleMarkY from "../../assets/element/jquery/scaleMark/ScaleMarkY";
import { refreshBackground } from "../../utils/StyleUtil";
import {alert, error} from "../../assets/element/jquery/msg/MsgUtil";
import Submit from "../../Submit";
export default class CanvasPanel {
  constructor(context) {
    this.context = context;
    this.designer = context.getDesigner();
  }
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="canvas-container"></div>`);
      parentContainer.append(this.container);
    }
  }
  /**
   * 描述:刷新面板
   */
  refreshPanel (scale) {
    if (this.container) {
      this.container.empty();
      //刻度-x
      const xScaleContainer = $(`<div class="scale-mark-x"></div>`);
      this.container.append(xScaleContainer);
      const scaleMarkX = new ScaleMarkX(this.context);
      scaleMarkX.initPanel(xScaleContainer);
      scaleMarkX.refreshPanel();
      //刻度-y
      const yScaleContainer = $(`<div class="scale-mark-y"></div>`);
      this.container.append(yScaleContainer);
      const scaleMarkY = new ScaleMarkY(this.context);
      scaleMarkY.initPanel(yScaleContainer);
      scaleMarkY.refreshPanel();
      //刻度画布
      this.canvasContainer = $(`<div class="scale-canvas" tabindex="0"></div>`);
      this.container.append(this.canvasContainer);
      this.refreshKeydownEvent();
      //接收拖动(停止拖动)
      this.receiveDrag(this.canvasContainer);
      //刷新点击
      this.refreshClick(this.canvasContainer);
    }
  }
  /**
   * 获取-画布图层
   * @returns 
   */
  getCanvasContainer () {
    if (this.canvasContainer) {
      return this.canvasContainer;
    }
  }
  /**
   * 刷新缩放
   * @param {*} scale 
   */
  refreshScale (scale) {
    if (scale || scale === 0) {
      if (this.context) {
        //宽高
        const width = this.context.getWidth();
        const height = this.context.getHeight();
        if (this.container) {
          $(this.container).css("width", width * scale);
          $(this.container).css("height", height * scale);
        }
        if (this.canvasContainer) {
          $(this.canvasContainer).css("width", width);
          $(this.canvasContainer).css("height", height);
          $(this.canvasContainer).css("transform", "scale(" + scale + ")");
        }
      }
    }
  }
  /**
   * 刷新背景
   */
  refreshBackground () {
    if (this.context && this.canvasContainer) {
      refreshBackground(this.context, this.canvasContainer);
    }
  }
  /**
   * 接收-拖拽
   * @param {*} targetContainer 
   */
  receiveDrag (targetContainer) {
    const self = this;
    if (targetContainer) {
      //dragover
      $(targetContainer).on("dragover", function (event) {
        event.preventDefault(); // 阻止默认行为
      });
      //drop
      $(targetContainer).on("drop", function (event) {
        event.preventDefault();
        const item = self.context.getItem();
        if (item) {
          item["left"] = Math.round(event.offsetX);
          item["top"] = Math.round(event.offsetY);
          //添加图层
          const canvasContainer = self.getCanvasContainer();
          if (self.designer && canvasContainer) {
            self.designer.addLayer(canvasContainer, item);
          }
          self.context.delItem();
        }
      });
      //end
    }
  }

  /**
   * 刷新点击事件
   * @param {*} canvasContainer 
   */
  refreshClick (canvasContainer) {
    const self = this;
    $(canvasContainer).on("mousedown", function (event) {
      // event.preventDefault();
      self.refreshLink();
      
    });
  }
  refreshLink(){
    if(this.designer){
      this.designer.linkByDraw();
    }
  }

  /**
   * 刷新键盘事件
   */
  refreshKeydownEvent() {
    document.addEventListener("keydown", (event) => {
      const key = event.key
      if (!this.context.getIsMousedown() && !this.isEditableElement(event)) {

        const isCopy = event.ctrlKey && event.key === 'c';
        const isPaste = event.ctrlKey && event.key === 'v';
        const isDelete = event.key === 'Delete';
        const isUndo = event.ctrlKey && event.key === 'z';
        const isRedo = event.ctrlKey && event.key === 'y';
        const isAll = event.ctrlKey && event.key === 'a';
        const isSave = event.ctrlKey && event.key === 's';
        const directions = ['ArrowDown', 'ArrowUp', 'ArrowLeft', 'ArrowRight'];

        const shortKeys = [isCopy, isPaste, isDelete, isUndo, isRedo, isAll, isSave];
        // 阻止默认行为
        if (shortKeys.some(k => k) || directions.includes(key)) {
          event.preventDefault();
        } else {
          return;
        }

        // 分发到具体的处理方法
        if (isCopy) {
          this.handleCopy();
        } else if (isPaste) {
          this.handlePaste();
        } else if (isDelete) {
          this.handleDelete();
        } else if (isUndo) {
          this.handleUndo();
        } else if (isRedo) {
          this.handleRedo();
        } else if (isAll) {
          this.handleSelectAll();
        } else if (isSave) {
          this.handleSave();
        } else if (directions.includes(key)) {
          this.handleMove(key);
        }
      }
    })
  }
  /**
   * 刷新位置
   * @param {*} select 
   * @param {*} key 
   */
  refreshPosition(select, key) {
    if (select && key) {
      switch (key) {//方向键移动当前组件
        case 'ArrowDown':
          select.top += 1;
          break;
        case 'ArrowUp':
          select.top -= 1;
          break;
        case 'ArrowLeft':
          select.left -= 1
          break;
        case 'ArrowRight':
          select.left += 1
          break;
      }
    }
  }
  handleSelectAll () {
    this.context.selectAllLayers();
    this.context.refreshSelected();
  }
  // 保存操作
  handleSave () {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    this.debounceTimer = setTimeout(() => {
      const submit = new Submit(this.context);
      submit.save();
    }, 300)
  }
  // 移动操作
  handleMove (key) {
    const selectMap = this.context.getSelectMap();
    const selectFixed = this.context.getSelectFixed();
    const layerMap = this.context.getLayerMap();
    if (selectMap && selectFixed && layerMap) {
      for (let id in selectMap) {
        const select = selectMap[id];
        //刷新位置
        this.refreshPosition(select, key);
        //覆值
        const fixed = selectFixed[id];
        const layer = layerMap[id];
        if (fixed && layer && layer.property) {
          layer.property.top = select.top;
          layer.property.left = select.left;
          fixed.top = select.top;
          fixed.left = select.left;
          layer.refreshLayerCss();
        }
      }
    }
  }
  // 复制操作
  handleCopy() {
    const selectMap = this.context.getSelectMap();
    if (!selectMap) return;

    const mapLength = Object.keys(selectMap).length;
    if (mapLength === 0) return;

    if (mapLength === 1) {
      this.designer.localCopyLayer(this.context.getLayer());
    } else {
      error('只能复制一个图层');
    }
  }
  // 粘贴操作
  handlePaste() {
    this.designer.localPasteLayer(this.context.getLayer());
    alert('粘贴成功')
  }
  // 删除操作
  handleDelete() {
    this.designer.delLayer(this.context.getLayer());
  }
  // 撤销操作
  handleUndo() {
    this.designer.undoLayer();
  }
  // 重做操作
  handleRedo() {
    this.designer.redoLayer();
  }
  isEditableElement(event) {
    const { target } = event;
    const tagName = target.tagName.toLowerCase();
    const isContentEditable = target.isContentEditable;

    // 可编辑元素包括：<input>, <textarea>, 以及具有 contenteditable 属性的元素
    return tagName === 'input' || tagName === 'textarea' || isContentEditable;
  }
}