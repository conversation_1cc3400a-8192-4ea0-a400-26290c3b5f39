import "./css/layer.css";
import { isEmpty, isEmptyObj, isObj, isArray, toList } from "../utils/Util";
import { createProxy } from "../utils/ProxyUtils";
export default class DesignLayer {
  constructor(context) {
    if (context) {
      this.context = context;
      this.designer = context.getDesigner();
    }
    this.property = {};
  }
  /**
   * 图层-设计器
   */
  designLayer () {
    const container = $(`
    <layer class="layer-wrap" tabindex="0">
      <!-- 顶部辅助线 -->
      <div class="v-show" style="position: fixed;border-top: 1px dashed #8A8A8A;width: 100%;left:0"/>
      <!-- 左侧辅助线 -->
      <div class="v-show" style="position: fixed;border-right: 1px dashed #8A8A8A;height:100%;top:0"/>
      <!-- 遮罩层-防止焦点聚焦在iframe内部,添加此蒙版 -->
      <div class="layer-mask v-trigger" tag="move"/>
      <!-- 图层组件 -->
      <div class="layer-panel" style="width: 100%;height: 100%;"></div>
      <!-- 图层工具 -->
      <div class="layer-tool v-show">
        <i class="icon fd-font fd-fuzhi"></i>
        <i class="icon fd-font fd-shanchu" style="margin-left: 4px"></i>
      </div>
      <!-- 调整大小标记点 -->
      <div class="layer-resize-tag v-show v-trigger" tag="lt" style="top: 0;left: 0;cursor: se-resize;transform: translate(-50%, -50%)"/>
      <div class="layer-resize-tag v-show v-trigger" tag="t" style="top: 0;left: 50%;cursor: s-resize;transform: translate(-50%, -50%)"/>
      <div class="layer-resize-tag v-show v-trigger" tag="rt" style="top: 0;right: 0;cursor: ne-resize;transform: translate(50%, -50%)"/>
      <div class="layer-resize-tag v-show v-trigger" tag="r" style="top: 50%;right: 0;cursor: w-resize;transform: translate(50%, -50%)"/>
      <div class="layer-resize-tag v-show v-trigger" tag="rb" style="bottom: 0;right: 0;cursor: se-resize;transform: translate(50%, 50%)"/>
      <div class="layer-resize-tag v-show v-trigger" tag="b" style="bottom: 0;left: 50%;cursor: s-resize;transform: translate(-50%, 50%)"/>
      <div class="layer-resize-tag v-show v-trigger" tag="lb" style="bottom: 0;left: 0;cursor: ne-resize;transform: translate(-50%, 50%)"/>
      <div class="layer-resize-tag v-show v-trigger" tag="l" style="top: 50%;left: 0;cursor: w-resize;transform: translate(-50%, -50%)"/>
    </layer>`);
    return container;
  }
  /**
   * 图层-预览
   */
  // previewLayer () {
  //   const container = $(`
  //   <layer class="layer-wrap">
  //     <div class="layer-panel" style="width: 100%;height: 100%;"></div>
  //   </layer>`);
  //   return container;
  // }
  /**
   * 刷新样式
   */
  // refreshCss () {
  //   this.refreshLayerCss();
  // }
  /**
   * 刷新样式
   */
  refreshLayerCss () {
    if (this.container && this.property) {
      $(this.container).css("top", this.property.top || 0);
      $(this.container).css("left", this.property.left || 0);
      $(this.container).css("width", this.property.width || 0);
      $(this.container).css("height", this.property.height || 0);
      $(this.container).css("z-index", this.property.zIndex || 100);
      //透明度
      const opacity = this.property.opacity;
      $(this.container).css("opacity", (opacity || opacity === 0) ? opacity / 100 : 1);
      $(this.container).css("-webkit-opacity", (opacity || opacity === 0) ? opacity / 100 : 1);
      $(this.container).css("-moz-opacity", (opacity || opacity === 0) ? opacity / 100 : 1);
      $(this.container).css("-o-opacity", (opacity || opacity === 0) ? opacity / 100 : 1);
      $(this.container).css("-ms-opacity", (opacity || opacity === 0) ? opacity / 100 : 1);
      // $(this.container).css("-khtml-opacity", (opacity || opacity === 0) ? opacity / 100 : 1);
      //可见/不可见
      const visibility=this.property.visibility;
      if (visibility && visibility==="hidden") {
        // $(this.container).css("visibility", this.property.visibility || "visible");
        this.property.isHide=true;
      }
      this.refreshHide();
      //是否拖拽 isDrag 控制大小 
      //是否锁定 isLocked
    }
  }
  /**
   * 刷新-触发器
   */
  refreshTrigger () {
    const self = this;
    if (this.container) {
      const list = $(this.container).find(".v-trigger");
      $.each(list, function (index, element) {
        const tag = $(element).attr("tag");
        self.refreshEvent(element, tag);
      });
    }
  }
  /**
   * 刷新-事件
   * @param {*} container 
   * @param {*} tag 
   */
  refreshEvent (container, tag) {
    const self = this;
    if (container) {
      $(container).on("mousedown", function (event) {
        event.preventDefault();// 阻止默认行为
        //设置鼠标点下
        self.context.setIsMousedown(true);
        // //添加缓存
        // self.context.addSelected(self, event);
        // //刷新所有图层选中处理
        // self.context.refreshSelected();
        //刷新联动面板
        self.refreshLink(event);

        //处理
        const scale = self.context.getScale();
        const scaleClientX = event.clientX / scale;
        const scaleClientY = event.clientY / scale;
        const el = event.target;
        const rbX = scaleClientX - el.parentNode.offsetWidth;
        const rbY = scaleClientY - el.parentNode.offsetHeight;
        const ltX = scaleClientX + el.parentNode.offsetWidth;
        const ltY = scaleClientY + el.parentNode.offsetHeight;
        const disX = scaleClientX - el.parentNode.offsetLeft;
        const disY = scaleClientY - el.parentNode.offsetTop;

        let selectMap = self.context.getSelectMap();
        let selectFixed = self.context.getSelectFixed();
        const layerMap = self.context.getLayerMap();
        let cptWidth, cptHeight, cptX, cptY;
        document.onmousemove = function (me) {
          const meScaleClientX = me.clientX / scale;
          const meScaleClientY = me.clientY / scale;
          if (tag === 'move') {
            cptX = meScaleClientX - disX;
            cptY = meScaleClientY - disY;
            // self.property.left = Math.round(cptX);
            // self.property.top = Math.round(cptY);

            if (selectMap) {
              for (let key in selectMap) {
                const left = selectFixed[key].left + Math.round(meScaleClientX - scaleClientX);
                const top = selectFixed[key].top + Math.round(meScaleClientY - scaleClientY);
                selectMap[key].left = left;
                selectMap[key].top = top;
                layerMap[key].property.left = left;
                layerMap[key].property.top = top;
                layerMap[key].refreshLayerCss();
              }
            }
          } else {
            switch (tag) {
              case 'lt':
                cptWidth = ltX - meScaleClientX;
                cptHeight = ltY - meScaleClientY;
                cptX = meScaleClientX - disX;
                cptY = meScaleClientY - disY;
                // that.currentCpt.cptX = Math.round(cptX);
                // that.currentCpt.cptY = Math.round(cptY);
                break;
              case 't':
                cptHeight = ltY - meScaleClientY;
                cptY = meScaleClientY - disY;
                // that.currentCpt.cptY = Math.round(cptY);
                break;
              case 'rt':
                cptWidth = meScaleClientX - rbX;
                cptHeight = ltY - meScaleClientY;
                cptY = meScaleClientY - disY;
                // that.currentCpt.cptY = Math.round(cptY);
                break;
              case 'r':
                cptWidth = meScaleClientX - rbX;
                break;
              case 'rb':
                cptWidth = meScaleClientX - rbX;
                cptHeight = meScaleClientY - rbY;
                break;
              case 'b':
                cptHeight = meScaleClientY - rbY;
                break;
              case 'lb':
                cptWidth = ltX - meScaleClientX;
                cptHeight = meScaleClientY - rbY;
                cptX = meScaleClientX - disX;
                // that.currentCpt.cptX = Math.round(cptX);
                break;
              case 'l':
                cptWidth = ltX - meScaleClientX;
                cptX = meScaleClientX - disX;
                // that.currentCpt.cptX = Math.round(cptX);
                break;
              default:
                console.log("事件处理-不存在类型!");
                break;
            }
            cptWidth = cptWidth < 40 ? 40 : cptWidth;//限制最小缩放
            cptHeight = cptHeight < 20 ? 20 : cptHeight;

            if (!isEmpty(cptX)) {
              self.property.left = Math.round(cptX);
            }
            if (!isEmpty(cptY)) {
              self.property.top = Math.round(cptY);
            }
            if (!isEmpty(cptWidth)) {
              self.property.width = Math.round(cptWidth);
            }
            if (!isEmpty(cptHeight)) {
              self.property.height = Math.round(cptHeight);
            }
            self.refreshCss();
          }
        }
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
          //解决多选移动未松开ctrl键第二次以后拖动定位还原
          self.context.coverSelectFixed();
          //设置鼠标点下
          self.context.setIsMousedown(false);
          //刷新联动面板
          self.refreshLink(event);
        }
        return false;
      });
    }
  }
  /**
   * 
   */
  refreshCopy () {
    if (this.container && this.designer) {
      const self = this;
      const copyContainer = $(this.container).find(".fd-fuzhi");
      if (copyContainer) {
        $(copyContainer).on("mousedown", function (event) {
          event.preventDefault(); // 阻止默认行为
          // console.info("点击:拷贝!");
          self.designer.copyLayer(self);
        });
      }
    }
  }
  refreshDel () {
    if (this.container && this.designer) {
      const self = this;
      const delContainer = $(this.container).find(".fd-shanchu");
      if (delContainer) {
        $(delContainer).on("mousedown", function (event) {
          event.preventDefault(); // 阻止默认行为
          // console.info("点击:删除!");
          self.designer.delLayer(self);
        });
      }
    }
  }
  refreshLink (event) {
    if (this.designer) {
      this.designer.linkByLayer(this, event);
    }
  }

  /**
   * 设置选中
   * @param {*} isChecked 
   */
  setChecked (isChecked) {
    // this.property.isChecked = isChecked || false;
    this.isChecked = isChecked || false;
  }
  /**
   * 刷新隐藏
   */
  refreshHide (isHidden) {
    const isHide = !isEmpty(isHidden)  || this.property.isHide;
    const container = this.container;
    if (container) {
      if (isHide) {
        // property.display="none"
        $(container).hide();
      } else {
        // property.display="block"
        $(container).show();
      }
    }
  }
  /**
   * 刷新锁
   */
  refreshLock () {
    this.refreshChecked();
  }
  /**
   * 刷新-选中项
   */
  refreshChecked () {
    if (this.container && this.property) {
      const container = this.container;
      const isChecked = this.isChecked;
      const isLock = this.isLock;
      //添加焦点
      // $(container).focus();
      //显示层
      const showContainer = $(container).find(".v-show");
      if (showContainer && showContainer.length) {
        if (!isLock && isChecked) {
          $(showContainer).show();
        } else {
          $(showContainer).hide();
        }
      }
      //遮罩层
      const maskContainer = $(container).find(".layer-mask");
      if (maskContainer && maskContainer.length) {

        if (!isLock) {
          $(maskContainer).show();
          if (isChecked) {
            $(maskContainer).addClass("layer-selected");
          } else {
            $(maskContainer).removeClass("layer-selected");
          }
        } else {
          $(maskContainer).hide();

        }

      }
    }
  }
  /**
   * 刷新层级(zIndex)
   * //刷新-层级
   * // if (this.property && this.isChecked) {
   * //   this.property.zIndex = 1800;
   * // } else {
   * //   this.property.zIndex = 100;
   * // }
   */
  refreshZIndex (num) {
    const zIndex = num || 100;
    const container = this.container;
    if (container && zIndex) {
      this.property["zIndex"] = zIndex;
      $(container).css("z-index", zIndex);
      //工具层
      const toolContainer = $(container).find(".layer-tool");
      if (maskContainer && maskContainer.length) {
        $(toolContainer).css("z-index", zIndex + 2);
      }
      //遮罩层
      const maskContainer = $(container).find(".layer-mask");
      if (maskContainer && maskContainer.length) {
        $(maskContainer).css("z-index", zIndex + 1);
      }
      //框架层
      const tagContainer = $(container).find(".layer-resize-tag");
      if (tagContainer && tagContainer.length) {
        $(tagContainer).css("z-index", zIndex + 2);
      }
    }
  }

  refreshProxy () {
    // 代理图层属性对象
    this.property = createProxy(this.property,(oldValue, newValue) => {
      this.context.addUndo({
        id: this.property.id,
        layer: this,
        prevAction: oldValue,
        action: newValue
      }, true)
    })
  }

  /**
   * 获取素材Ids
   */
  getMaterials () {
    const list = [];
    const chart = this.property.chart;
    if (chart) {
      const json = {};
      $.extend(true, json, chart);
      this.parseChart(json, list);
    }
    return list;
  }
  parseChart (json, list) {
    if (json && isObj(json) && !isArray(json) && isArray(list)) {
      for (let key in json) {
        const val = json[key];
        if (isEmptyObj(val) || isEmpty(val)) {
          delete json[key];
          continue;
        }
        if (key === "bg") {
          if (val && val.bgType && val.bgType === "image" && val.image && val.image["id"]) {
            // material = { id: val.image["id"] };
            // list.push(material);
            const material=this.context.getMaterialPropertyById(val.image["id"]);
            if(material){
              list.push(material);
            }
          }
        } else if (key === "border") {
          if (val && val.bdType && val.bdType === "image" && val.image && val.image["id"]) {
            // material = { id: val.image["id"] };
            // list.push(material);
            const material=this.context.getMaterialPropertyById(val.image["id"]);
            if(material){
              list.push(material);
            }
          }
        } else if (key === "geoImage") {
          if (val && val.id) {
            const material=this.context.getMaterialPropertyById(val.id);
            if(material){
              list.push(material);
            }
          }
        } else {
          if (isObj(val)) {
            if (isArray(val)) {
              for (let item of val) {
                this.parseChart(item, list);
              }
            } else {
              this.parseChart(val, list);
            }
          }
        }
      }
    }
  }
  /**
   * 
   */
  // getDataModels () {
  //   let list;
  //   let dataMap;
  //   let mdoelMap;
  //   let item;
  //   let bindId;

  //   // const chartMap = this.property.map;
  //   // if (chartMap) {
  //   //   const chinaBind = chartMap.chinaBind;
  //   //   const provinceBind = chartMap.provinceBind;
  //   //   const cityBind = chartMap.cityBind;

  //   //   if (chinaBind && chinaBind.bindType && chinaBind.bindType === "data") {
  //   //     bindId = bind.id;
  //   //     item = { id: bindId, linkId: bindId, type: "data" };
  //   //     if (!dataMap) {
  //   //       dataMap = {};
  //   //     }
  //   //     dataMap[bindId] = item;
  //   //   }
  //   //   if (provinceBind && provinceBind.bindType && provinceBind.bindType === "data") {
  //   //     bindId = bind.id;
  //   //     item = { id: bindId, linkId: bindId, type: "data" };
  //   //     if (!dataMap) {
  //   //       dataMap = {};
  //   //     }
  //   //     dataMap[bindId] = item;
  //   //   }
  //   //   if (cityBind && cityBind.bindType && cityBind.bindType === "data") {
  //   //     bindId = bind.id;
  //   //     item = { id: bindId, linkId: bindId, type: "data" };
  //   //     if (!dataMap) {
  //   //       dataMap = {};
  //   //     }
  //   //     dataMap[bindId] = item;
  //   //   }
  //   // }
  //   const bind = this.property.bind;
  //   //来源绑定-绑定
  //   // if (bind && bind.bindType && bind.bindType === "data") {
  //   //   bindId = bind.id;
  //   //   item = { id: bindId, linkId: bindId, type: "data" };
  //   //   if (!dataMap) {
  //   //     dataMap = {};
  //   //   }
  //   //   dataMap[bindId] = item;
  //   // }


  //   //来源模型-绑定
  //   // const bind = this.property.bind;
  //   if (bind && bind.bindType && bind.bindType === "model") {
  //     const modelId = bind.modelId;
  //     item = { id: modelId, linkId: modelId, type: "model" };
  //     if (!mdoelMap) {
  //       mdoelMap = {};
  //     }
  //     mdoelMap[modelId] = item;
  //   }
  //   //来源模型-初始化
  //   const inits = this.property.inits;
  //   if (inits && inits.length) {
  //     for (let init of inits) {
  //       if (init.type === "model" && init.linkId) {
  //         const linkId = init.linkId;
  //         item = { id: linkId, linkId: linkId, type: "model" };
  //         if (!mdoelMap) {
  //           mdoelMap = {};
  //         }
  //         mdoelMap[linkId] = item;
  //       }
  //     }
  //   }
  //   if (mdoelMap) {
  //     list = toList(mdoelMap);
  //   }
  //   return list;
  // };
  // getDataDicts () {
  //   let list;
  //   let map;
  //   let item;
  //   //来源模型-绑定
  //   const bind = this.property.bind;
  //   if (bind && bind.bindType && bind.bindType === "dict" && bind.dictId) {
  //     const modelId = bind.dictId;
  //     item = { id: modelId, linkId: modelId, type: "model" };
  //     if (!map) {
  //       map = {};
  //     }
  //     map[modelId] = item;
  //   }
  //   //来源模型-初始化
  //   const inits = this.property.inits;
  //   if (inits && inits.length) {
  //     for (let init of inits) {
  //       if (init.type === "dict" && init.linkId) {
  //         const linkId = init.linkId;
  //         item = { id: linkId, linkId: linkId, type: "model" };
  //         if (!map) {
  //           map = {};
  //         }
  //         map[linkId] = item;
  //       }
  //     }
  //   }
  //   if (map) {
  //     list = toList(map);
  //   }
  //   return list;
  // };
}