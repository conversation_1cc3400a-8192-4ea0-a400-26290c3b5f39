import { uuid, getRandom, isEmpty } from "../utils/Util";
import { computeToResultMap, judge } from "../utils/ConditionUtil";
import { refreshCss, toAnimateStyle, toTransformStyle } from "../utils/StyleUtil";
import { jumpTo } from "../utils/JumpUtil";
import Expression from "../utils/Expression";
import BindQueryHandler from "../service/BindQueryHandler";
import DesignLayer from "./DesignLayer";
export default class Layer extends DesignLayer {
  constructor(context) {
    super(context);
    this.$$isLoading = true;
    this.expression = new Expression(this.context);
    this.bindData = [];
  }
  /**
   * 抽象方法
   */
  getDefaultProperty () { console.info("构建图层-需要实现[getDefaultProperty]!"); };
  /**
   * 
   * @param {*} panelContainer 
   */
  initCompContainer (panelContainer) { console.info("构建图层-需要实现[initCompContainer]"); }
  /**
   * 属性渲染/样式变更
   */
  refreshCompCss () { console.info("构建图层-需要实现[refreshCompCss]"); };
  /**
   * 绑定数据的渲染
   */
  refreshBind () { console.info("构建图层-需要实现[refreshBind]"); };
  /**
   * 初始化-属性
   * @param {*} property 
   */
  initProperty (property) {
    this.property = this.getDefaultProperty();
    if (property) {
      for (let key in property) {
        if (!this.property) {
          this.property = {};
        }
        this.property[key] = property[key];
      }
    }
    //处理新属性
    if (!this.property.id) {
      this.property.id = uuid();
      if (!this.property.name) {
        this.property.name = "图层";
      }
      if(!this.property.label){
        this.property.label = this.property.name + getRandom(3);
      }
    }
  }
  /**
   * 描述:图层(初始化)
   */
  initLayer () {
    this.container = $(`<layer class="layer-wrap">
      <div class="layer-panel" style="width: 100%;height: 100%;"></div>
    </layer>`);
    if (this.context && this.context.getIsDesign()) {
      this.container = this.designLayer();
      this.refreshChecked();
      this.refreshTrigger();
      this.refreshCopy();
      this.refreshDel();
      this.refreshProxy();
    }
    if (this.container) {
      //1.刷新属性
      this.refreshAttr();
      //2.组件Component
      const panelContainer = $(this.container).find(".layer-panel");
      this.initCompContainer(panelContainer);
      this.initCompEvent(panelContainer);
    }
  }
  /**
   * 描述:图层(刷新)
   * 执行refreshLayer之前
   * 需要执行:refreshData
   */
  refreshLayer () {
    //刷新刷新绑定
    this.refreshBind();
    //刷新样式
    this.refreshCss();
  }
  /**
   * 描述:图层(事件)
   * @param {*} layerContainer 
   */
  initCompEvent (layerContainer) {
    if (layerContainer) {
      const self = this;
      $(layerContainer).css("cursor", "pointer");
      $(layerContainer).on("click", function (event) {
        //1.刷新超链接事件
        const data = self.getRowData();
        //超链接
        self.clickJump(data);
        //联动
        self.clickRelation(data);
      });
    }
  };
  /**
   * 点击超链接
   * @param {*} data 
   */
  clickJump (data) {
    const jumpItem = this.property.jump;
    if (jumpItem && jumpItem.isOpen) {
      jumpTo(jumpItem, data);
    }
  }
  /**
   * 
   * @returns 
   */
  getContainer () {
    if (this.container) {
      return this.container;
    }
  }
  /**######################[渲染]######################## */
  /**
   * 刷新-属性
   */
  refreshAttr () {
    if (this.container && this.property) {
      const attrJson = {
        "id": this.property.id,
        "layer-id": this.property.id,
        "layer-name": this.property.name,
        "layer-label": this.property.label,
      };
      $(this.container).attr(attrJson);
    }
  }
  /**
   * 刷新样式
   */
  refreshCss () {
    this.refreshLayerCss();//图层
    this.refreshCompCss();//组件
    this.refreshCondition();//条件
    this.refreshInteractive();//交互
    this.refreshAnimate();//动画
    this.refreshTransform();//转换
  }
  refreshAnimate (thatContainer) {
    const container = thatContainer || this.container;
    if (container && this.property) {
      if (this.property.animate &&
        this.property.animate.isAnimate &&
        this.property.animate.delay &&
        !isNaN(Number(this.property.animate.delay))) {

        container.off("animationstart").on("animationstart", () => {
          this.timer = setTimeout(() => {
            // 动画开始的时候，显示元素
            container.css('visibility', 'visible');
            // 减500毫秒再显示，看起来自然一点
          }, Number(this.property.animate.delay) + Number(this.property.animate.duration) + 500)
        })
        clearTimeout(this.timer);
        // 隐藏元素，但是仍然占据空间
        container.css('visibility', 'hidden');
      }
      let style = toAnimateStyle(this.property.animate);
      if (!style) {
        style = {};
      }
      refreshCss(container, style);
    }
  }
  refreshTransform (thatContainer) {
    // const panelContainer = $(this.container).find(".layer-panel");
    const container = thatContainer || this.container;
    if (container && this.property) {
      const style = toTransformStyle(this.property.transform);
      refreshCss(container, style);
    }
  }
  /**
   * 
   * @param {*} container 
   */
  refreshWH (chart, factor) {
    if (chart && this.property) {
      const width = this.property.width;
      const height = factor ? this.property.height * factor : this.property.height;
      if (this.property.animate && this.property.animate.isAnimate) {
        chart["width"] = 'inherit';
        chart["height"] = 'inherit';
      } else {
        chart["width"] = width;
        chart["height"] = height;
      }
    }
  }
  refreshFS (chart, factor) {
    if (chart && this.property) {
      const width = this.property.width;
      const height = factor ? this.property.height * factor : this.property.height;
      const length = this.getLength();
      const fontSize = this.getFontSize(height, width, length);
      if (this.property.animate && this.property.animate.isAnimate) {
        chart["fontSize"] = 'inherit';
      } else {
        chart["fontSize"] = fontSize;
      }
    }
  }
  getLength () {
    return 2;
  }
  getFontSize (width, height, length) {
    if (width && height && length) {
      return parseInt(2 * (Math.min(width, height) / length));
    }
  };
  // refreshStyle (props, factor) {
  //   if (props && this.property && this.context) {
  //     if (this.property.isDrag && this.context.getIsDesign()) {
  //       const height = factor ? this.property.height * factor : this.property.height;
  //       const width = this.property.width;
  //       const length = this.getLength();
  //       // props["fontSize"] = (Math.min(height, width) * 0.5) < 20 ? 20 : (Math.min(height, width) * 0.5);
  //       // props["fontSize"] = 16 * (width / height) * 5;
  //       props["fontSize"] = this.getFontSize(height, width, length);
  //       props["width"] = width;
  //       props["height"] = height;
  //     }
  //   }
  // }

  /**######################[数据-联动]########################*/

  clickRelation (data) {
    const relation = this.property.relation;
    //是否开启联动
    if (relation && relation.isOpen) {
      if (relation.linkage && "param" === relation.linkage) {
        //1.参数的设置
        const paramLink = relation.paramLink;
        //参数的设置-处理联动参数
        const paramMap = this.getParamMap(paramLink.params, "code", "value", data);
        if (paramMap) {
          for (let key in paramMap) {
            this.context.setParamValue(key, paramMap[key]);
          }
        }
        //2.获取关联图层
        const relationParamLayers = this.getLayersByParamRelation();
        if (relationParamLayers && relationParamLayers.length) {
          // const relationLayerNames = [];
          for (let i = 0; i < relationParamLayers.length; i++) {
            const relationLayer = relationParamLayers[i];
            if (relationLayer === this) {
              console.error("图层[" + this.property.name + "][" + this.property.id + "]参数联动循环调用", relationParamLayers);
            } else {
              relationLayer.refreshData();
              // relationLayerNames.push(relationLayer.property.name);
            }
          }
          // console.info("图层[" + this.property.name + "]触发参数联动,参数：", paramMap, "联动图层：", relationLayerNames);
        }
      } else if (relation.linkage && "layer" === relation.linkage) {
        //2.图层联动
        const layerLink = relation.layerLink;
        //处理图层
        if (layerLink && layerLink.targets && layerLink.targets.length) {
          // 遍历联动图层
          // const relationLayerNames = [];
          for (let j = 0; j < layerLink.targets.length; j++) {
            const layerLinkTarget = layerLink.targets[j];
            if (isEmpty(layerLinkTarget.status) || layerLinkTarget.status !== "1") {
              continue;
            }
            if (isEmpty(layerLinkTarget.layerId)) {
              continue;
            }
            //获取目标 图层 参数
            let relationParam;
            if (layerLinkTarget && layerLinkTarget.params && layerLinkTarget.params.length) {//处理目标图层参数
              relationParam = this.getParamMap(layerLinkTarget.params, "code", "value", data);
            }
            const relationLayer = this.context.getLayerById(layerLinkTarget["layerId"]);
            if (relationLayer) {
              if (relationLayer === this) {
                console.error("图层[" + this.property.name + "][" + this.property.id + "]图层联动循环调用", relationLayer);
              } else {
                if (relationParam) {
                  relationLayer.refreshData("", relationParam);
                } else {
                  relationLayer.refreshData();
                }
              }
              // console.info("图层[" + this.property.name + "]联动[" + relationLayer.property.name + "],参数：", relationParam || "无参数");
            }
          }
        }
      }
    }
  }

  getParamMap (array, key, value, data) {
    const paramMap = {};
    if (array && array.length) {
      for (let i = 0; i < array.length; i++) {
        const item = array[i];
        const itemKey = item[key];
        const itemValue = item[value];
        const itemExpr = item["constant"];
        const itemVal = this.expression.computeExpr(itemValue, data) || itemExpr;
        if (itemKey) {
          if (itemVal) {
            paramMap[itemKey] = itemVal;
          } else {
            paramMap[itemKey] = '';
          }
        }
      }
    }
    return paramMap;
  }
  getLayersByParamRelation () {
    const relation = this.property.relation;
    if (relation && relation.paramLink && relation.paramLink.params) {
      let targets = []
      const params = relation.paramLink.params;
      if (params && params.length) {
        for (let i = 0; i < params.length; i++) {
          const param = params[i];
          if (param && param["code"]) {
            const relationLayers = this.getLayersByParam(param["code"]);
            if (relationLayers && relationLayers.length) {
              for (let j = 0; j < relationLayers.length; j++) {
                const relationLayer = relationLayers[j];
                if (targets && targets.indexOf(relationLayer) === -1) {
                  targets.push(relationLayer);
                }
              }
            }
          }
        }
      }
      if (targets && targets.length) {
        return targets;
      }
    }
  }
  getLayersByParam (paramName) {
    let layers;
    const layerMap = this.context.getLayerMap();
    if (layerMap && paramName) {
      for (let key in layerMap) {
        const layer = layerMap[key];
        if (layer && layer.isHaveParam && layer.isHaveParam(paramName)) {
          if (!layers) {
            layers = [layer];
          } else {
            layers.push(layer);
          }
        }
      }
    }
    return layers;
  }
  /**
     * 描述:是否含有参数
     */
  isHaveParam (paramName) {
    let flag = false;
    //1、获取绑定数据使用到参数的图层
    const bind = this.getBind();
    if (bind && bind.bindType && "data" === bind.bindType && bind.params && bind.params.length) {
      for (let i = 0; i < bind.params.length; i++) {
        let expr = bind.params[i].defValue || bind.params[i].expr;
        if (bind.params[i] && expr && expr.indexOf(paramName) !== -1) {
          flag = true;
        }
      }
    }
    //1、获取条件属性使用到参数的图层
    const conditions = this.property.conditions;
    if (conditions && conditions.length) {
      for (let condition of conditions) {
        if (!condition || !condition["funList"]) {
          continue;
        }
        const funList = condition["funList"];
        if (funList && funList.length) {
          for (let funItem of funList) {
            if (funItem && funItem.type && "param" === funItem.type && paramName === funItem.column) {
              flag = true;
            }
          }
        }
      }
    }
    //IFrameLayer筛选参数
    const type = this.property.type;
    if (type && type === "IframeLayer") {
      const iframe = this.property.chart;
      const link = iframe["link"];
      if (link) {
        const params = link["params"];
        if (params && params.length) {
          for (let i = 0; i < params.length; i++) {
            const param = params[i];
            if (param.value && param.value === paramName) {
              flag = true;
            }
          }
        }
      }
    }
    return flag;
  }

  /**######################[数据-条件]########################*/
  /**
     * 描述:判断是否是分行条件属性
     * @returns {boolean}
     */
  isRowCondition () {
    return false;
  }
  /**
   * 刷新条件
   * @param {*} serieData 
   * @param {*} index 
   */
  refreshCondition (serieData, index) {
    const self = this;
    if (this.property && this.property.conditions) {
      for (let conditionItem of this.property.conditions) {
        let conditionDatas = [];
        if (!this.isRowCondition()) {
          const rowNum = conditionItem.rowNum;
          conditionDatas = [this.getRowData(this.bindData, rowNum)];
        } else {
          conditionDatas = this.bindData;
        }
        if (serieData) {
          conditionDatas = serieData;
        }
        if (conditionDatas && conditionDatas.length) {
          for (let i = 0; i < conditionDatas.length; i++) {
            const rowNum = i;
            const dataItem = conditionDatas[i];
            const paramItem = this.context.getParamPropertyMap();
            const resultMap = computeToResultMap(conditionItem, paramItem, dataItem);
            if (judge(resultMap)) {
              this.refreshConditionStyle(conditionItem, rowNum, dataItem);
            }
          }
        }
      }
    }
  }

  /**
   * 描述:刷新条件样式
   * @param conditionItem
   * @param rowNum
   */
  refreshConditionStyle (conditionItem, rowNum) {
    if (conditionItem) {
      // if (conditionItem.display && "hide" === conditionItem.display) {
      //   this.container.hide();
      // } else {
      //   this.container.show();
      // }
      const display=conditionItem.display;
      if(display==="hide"){
        // this.refreshHide(true);
        this.container.hide();
      }else if(display==="show"){
        // this.refreshHide(false);
        this.container.show();
      }
      this.refreshConditionCss(conditionItem);
    }
  }
  /**
   * 刷新条件css
   * @param {*} conditionItem 
   */
  refreshConditionCss (conditionItem) { };

  /**######################[数据-绑定(联动)]##################*/

  // getBind(){console.error("必须实现获取绑定方法!getBind");}
  getBind () {
    return this.property.bind;
  }
  getBindRowNum () {
    let num = 0;
    const bind = this.property.bind;
    if (bind && bind.rowNum) {
      num = bind.rowNum;
    }
    return num;
  }
  getBindColumns () {
    const bind = this.property.bind;
    if (bind && bind.columns) {
      return bind.columns;
    }
  }
  getBindParams () {
    const bind = this.property.bind;
    if (bind && bind.params) {
      return bind.params;
    }
  }
  // refreshChartColumns(){};
  //选项(输出字段)
  getColOptions (isExpr) {
    let options;
    const columns = this.getBindColumns();
    if (columns && columns.length) {
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        let expr;
        if (isExpr) {
          expr = "${" + column + "}";
        } else {
          expr = column;
        }
        const item = { key: expr, text: expr };
        if (!options) {
          options = [];
        }
        options.push(item);
      }
    }
    return options;
  }
  getParamOptions (isExpr) {
    let options;
    const params = this.getBindParams();
    if (params && params.length) {
      for (let i = 0; i < params.length; i++) {
        const param = params[i];
        const code = param.name;
        let expr;
        if (isExpr) {
          expr = "${" + code + "}";
        } else {
          expr = code;
        }
        const item = { key: expr, text: expr };
        if (!options) {
          options = [];
        }
        options.push(item);
      }
    }
    return options;
  }
  /**
   * 
   * @returns 
   */
  isSingleRow () {
    // const bind = this.property.bind;
    const bind = this.getBind();
    if (bind && bind["isSingleRow"]) {
      return true;
    } else {
      return false;
    }
  }
  /**
   * 
   * @param {*} index 
   * @returns 
   */
  getRowData (list, rowNum) {
    // const bind=this.property.bind;
    const bind = this.getBind();
    const datas = list || this.bindData;
    let index;
    if (bind) {
      index = rowNum || bind.rowNum || 0;
    } else {
      index = rowNum || 0;
    }
    if (datas && datas.length) {
      return datas[index];
    }
  }
  /**
     * 描述:刷新定时器
     * @param bind
     * @returns {boolean}
     */
  refreshInterval () {
    // 旧版绑定
    const bind = this.getBind();
    if (bind && bind["refreshType"] && "1" === bind["refreshType"] && bind["refreshTime"]) {
      const self = this;
      const timeout = bind["refreshTime"];
      this.interval = setInterval(function () {
        self.refreshData();
        console.info("触发图层[" + self.property.name + "]定时[" + timeout + "毫秒]刷新", self);
      }, timeout);
    }
    // 新版数据集
    if (bind && bind.bindType === "dataset") {
      const dataset = this.context.getDataSetById(bind.datasetId);
      if (dataset) {
        dataset.refreshInterval(() => {
          this.refreshData()
        })
      }
    }
  }
  /**
   * 刷新数据
   */
  refreshData (callback, relationParams) {
    // this.bindData;
    //是否加载中
    this.$$isLoading = true;
    // const bind = this.property.bind;
    const bind = this.getBind();
    if (bind && bind.bindType) {
      if (bind.bindType === "data") {
        //查询数据
        const self = this;
        const bindQueryHandler = new BindQueryHandler(this.context, bind);
        bindQueryHandler.queryData(function (result) {
          if (result && result.data) {
            const datas = self.toMappingData(bind.mappings, result.data);
            if (datas) {
              self.bindData = datas;
            }
          }
          //刷新图层
          self.refreshLayer();
          //是否加载中
          self.$$isLoading = false;
          //回调
          if (callback) {
            callback();
          }
        }, relationParams);
      } else if (bind.bindType === "layer") {
        //获取图层
        const layer = this.context.getLayerById(bind.layerId);
        if (layer && layer.bindData && layer.bindData.length) {
          const datas = this.toMappingData(bind.mappings, layer.bindData);
          if (datas && datas.length) {
            this.bindData = datas;
          }
        }
        //刷新图层
        this.refreshLayer();
        //是否加载中
        this.$$isLoading = false;
        //回调
        if (callback) {
          callback();
        }
      } else if (bind.bindType === "model") {//模型
        //获取模型
        const model = this.context.getDataById(bind.modelId);
        if (model) {
          const list = model.getModelDatas();
          this.bindData = this.toMappingData(bind.mappings, list);
        }
        //刷新图层
        this.refreshLayer();
        //是否加载中
        this.$$isLoading = false;
        //回调
        if (callback) {
          callback();
        }
      } else if (bind.bindType === "dict") {//字典
        //获取字典
        const model = this.context.getDictById(bind.dictId);
        if (model) {
          const list = model.getModelDatas(bind.dictLevel);
          this.bindData = this.toMappingData(bind.mappings, list);
        }
        //刷新图层
        this.refreshLayer();
        //是否加载中
        this.$$isLoading = false;
        //回调
        if (callback) {
          callback();
        }
      } else if (bind.bindType === "mock") {
        //模拟数据
        if (bind.mock && bind.mock.length) {
          this.bindData = this.toMappingData(bind.mappings, bind.mock);
        }
        //刷新图层
        this.refreshLayer();
        //是否加载中
        this.$$isLoading = false;
        //回调
        if (callback) {
          callback();
        }
      } else if (bind.bindType === "dataset") {
        const dataset = this.context.getDataSetById(bind.datasetId);
        if (dataset) {
          this.bindData = this.toMappingData(bind.mappings, dataset.filterData(bind));
        }
        //刷新图层
        this.refreshLayer();
        //是否加载中
        this.$$isLoading = false;
        //回调
        if (callback) {
          callback();
        }
      } else {
        //刷新图层
        this.refreshLayer();
        //是否加载中
        this.$$isLoading = false;
        //回调
        if (callback) {
          callback();
        }
      }
    } else {
      //刷新图层
      this.refreshLayer();
      //是否加载中
      this.$$isLoading = false;
      //回调
      if (callback) {
        callback();
      }
    }
  }

  /**
   * 数据映射
   * @param {*} mappings 
   * @param {*} datas 
   * @returns 
   */
  toMappingData (mappings, datas) {
    let list;
    if (datas && datas.length) {
      for (let i = 0; i < datas.length; i++) {
        const item = JSON.parse(JSON.stringify(datas[i]));
        if (!list) {
          list = [];
        }
        list.push(item);
        //处理映射start
        if (mappings && mappings.length) {
          for (let j = 0; j < mappings.length; j++) {
            const mapping = mappings[j];
            const key = mapping["key"];
            const value = this.expression.computeExpr(mapping["value"], item);
            item[key] = value;
          }
        }
        //处理映射end
      }
    }
    return list;
  }

  /**######################[图层交互]##################*/
  refreshInteractive () {
    if (this.compContainer && this.compContainer.length && this.property.interactive) {
      const interactive = this.property.interactive;
      // 交互是否开启，是否有交互事件
      if (interactive.isOpen && interactive.events && interactive.events.length) {
        // 循环事件
        for (let i = 0; i < interactive.events.length; i++) {
          const event = interactive.events[i];
          // 事件属性
          if (event.properties && event.properties.length) {
            // 事件类型
            switch (event.triggerEvent) {
              case "singleClick":
                this.compContainer.off('click').on('click', () => {
                  this.triggerEvent(event)
                })
                break
              case "doubleClick":
                this.compContainer.off('dblclick').on('dblclick', () => {
                  this.triggerEvent(event)
                })
                break
              case "mouseEnter":
                this.compContainer.off('mouseenter').on('mouseenter', (event) => {
                  this.triggerEvent(event)
                })
                break
              case "mouseLeave":
                this.compContainer.off('mouseleave').on('mouseleave', () => {
                  this.triggerEvent(event)
                })
                break
            }
          }
        }
      }
    }
  }
  triggerEvent (event) {
    if (event && event.properties) {
      // 消息体
      const body = {}
      for (let j = 0; j < event.properties.length; j++) {
        const eventProperty = event.properties[j];
        if (eventProperty.key && eventProperty.value) {
          // 数据绑定
          if (event.dataType === "dataBind") {
            // body[eventProperty.key] = this.bindData[this.property.bind.rowNum][eventProperty.value]
            const data = this.getRowData();
            if (data) {
              body[eventProperty.key] = data[eventProperty.value]
            }
          }
          // 参数绑定
          if (event.dataType === "paramBind") {
            body[eventProperty.key] = this.context.getParamValue(eventProperty.value);
          }
        }
      }
      // 组装消息
      const payload = {
        type: event.messageName,
        body,
      };
      console.log('payload', payload)

      const layerMap = this.context.getLayerMap();
      for (const key in layerMap) {
        const layer = layerMap[key];
        const property = layer.property;
        // 给所有iframe发送消息
        if (property && ['Iframe3DLayer', 'IframeLayer'].includes(property.type)) {
          const iframe = layer.compContainer[0];
          if (iframe) {
            const contentWindow = iframe.contentWindow;
            if (contentWindow) {
              contentWindow.postMessage(payload, "*");
            }
          }
        }
      }
    }
  }

}