import { alert, warn, error } from "../assets/element/jquery/msg/MsgUtil";
//基础 base
import FullScreenLayer from "./base/comp/FullScreenLayer";
import GroupLayer from "./base/comp/GroupLayer";
import IframeLayer from "./base/comp/IframeLayer";
import Iframe3DLayer from "./base/comp/Iframe3DLayer";
import NumLayer from "./base/comp/NumLayer";
import TextLayer from "./base/comp/TextLayer";
import ImageLayer from "./base/comp/ImageLayer";
import BorderLayer from "./base/comp/BorderLayer";
import SvgLayer from "./base/comp/SvgLayer";
import SvgAnimationLayer from "./base/comp/SvgAnimationLayer";
import IconLayer from "./base/comp/IconLayer";
import VideoLayer from "./base/comp/VideoLayer";
import XgPlayerLayer from "./base/comp/XgPlayerLayer";
import YsPlayerLayer from "./base/comp/YsPlayerLayer";
import TableLayer from "./base/comp/TableLayer";
import MarqueeLayer from "./base/comp/MarqueeLayer";
import TreeLayer from "./base/comp/TreeLayer";
import GridLayer from "./base/comp/GridLayer";
import DateLayer from "./base/comp/DateLayer";

//形状 shape
import CircularShapeLayer from "./shape/comp/CircularShapeLayer";
import RectangleShapeLayer from "./shape/comp/RectangleShapeLayer";
//过滤 filter
import DateFilterLayer from "./filter/comp/DateFilterLayer";
import RadioFilterLayer from "./filter/comp/RadioFilterLayer";
import SelectFilterLayer from "./filter/comp/SelectFilterLayer";
//图表 chart
import LineChartLayer from "./echarts/chart/comp/LineChartLayer"; //折线图
import LineBarChartLayer from "./echarts/chart/comp/LineBarChartLayer"; //折柱混合
import BarChartLayer from "./echarts/chart/comp/BarChartLayer"; //柱状图
import Bar3DChartLayer from "./echarts/chart/comp/Bar3DChartLayer"; //3d柱状图
import BarYChartLayer from "./echarts/chart/comp/BarYChartLayer"; //条形图
import BarPictorChartLayer from "./echarts/chart/comp/BarPictorChartLayer"; //立体柱状图
import BarPictorYChartLayer from "./echarts/chart/comp/BarPictorYChartLayer"; //水平柱状图
import BarDoughnutChartLayer from "./echarts/chart/comp/BarDoughnutChartLayer"; //环形柱状图

import PieChartLayer from "./echarts/chart/comp/PieChartLayer"; //饼图
import Pie3DChartLayer from "./echarts/chart/comp/Pie3DChartLayer"; //3d饼图
import PieRoseChartLayer from "./echarts/chart/comp/PieRoseChartLayer"; //玫瑰图
import PieDoughnutChartLayer from "./echarts/chart/comp/PieDoughnutChartLayer"; //环形图
import PieDoughnutGapChartLayer from "./echarts/chart/comp/PieDoughnutGapChartLayer"; //环形图断开
import PieNestChartLayer from "./echarts/chart/comp/PieNestChartLayer";
//
import ScatterChartLayer from "./echarts/chart/comp/ScatterChartLayer"; //散点（气泡）图
import RadarChartLayer from "./echarts/chart/comp/RadarChartLayer"; //雷达图
import GaugeChartLayer from "./echarts/chart/comp/GaugeChartLayer"; //仪表盘
import FunnelChartLayer from "./echarts/chart/comp/FunnelChartLayer"; //漏斗图
import CandlestickChartLayer from "./echarts/chart/comp/CandlestickChartLayer"; //K线图
import SankeyChartLayer from "./echarts/chart/comp/SankeyChartLayer"; //桑基图
import GraphChartLayer from "./echarts/chart/comp/GraphChartLayer"; //关系图
import LinesChartLayer from "./echarts/chart/comp/LinesChartLayer"; //路径图
import BoxplotChartLayer from "./echarts/chart/comp/BoxplotChartLayer"; //盒须图
import ParallelChartLayer from "./echarts/chart/comp/ParallelChartLayer"; //平行坐标系
import WordChartLayer from "./echarts/chart/comp/WordChartLayer"; //词云
import LiquidChartLayer from "./echarts/chart/comp/LiquidChartLayer"; //水晶球
//地图 map
import ZgScMapLayer from "./echarts/map/comp/ZgScMapLayer"; //'中国色彩', type: 'ZgScMapLayer'
import ZgRlMapLayer from "./echarts/map/comp/ZgRlMapLayer"; //'中国热力', type: 'ZgRlMapLayer'
import ZgQpMapLayer from "./echarts/map/comp/ZgQpMapLayer"; //'中国气泡', type: 'ZgQpMapLayer'
import ZgFxMapLayer from "./echarts/map/comp/ZgFxMapLayer"; //'中国飞线', type: 'ZgFxMapLayer'
import Zg3DMapLayer from "./echarts/map/comp/Zg3DMapLayer"; //'中国3D', type: 'Zg3DMapLayer'

export function createLayer(context, property) {
  let layer;
  if (context && property && property["type"]) {
    const type = property["type"];
    //图层-创建
    switch (type) {
      //基础 base
      case "FullScreenLayer":
        layer = new FullScreenLayer(context);
        break;
      case "GroupLayer":
        layer = new GroupLayer(context);
        break;
      case "IframeLayer":
        layer = new IframeLayer(context);
        break;
      case "Iframe3DLayer":
        layer = new Iframe3DLayer(context);
        break;
      case "NumLayer":
        layer = new NumLayer(context);
        break;
      case "TextLayer":
        layer = new TextLayer(context);
        break;
      case "ImageLayer":
        layer = new ImageLayer(context);
        break;
      case "BorderLayer":
        layer = new BorderLayer(context);
        break;
      case "SvgLayer":
        layer = new SvgLayer(context);
        break;
      case "SvgAnimationLayer":
        layer = new SvgAnimationLayer(context);
        break;
      case "IconLayer":
        layer = new IconLayer(context);
        break;
      case "VideoLayer":
        layer = new VideoLayer(context);
        break;
      case "XgPlayerLayer":
        layer = new XgPlayerLayer(context);
        break;
      case "YsPlayerLayer":
        layer = new YsPlayerLayer(context);
        break;
      case "TableLayer":
        layer = new TableLayer(context);
        break;
      case "MarqueeLayer":
        layer = new MarqueeLayer(context);
        break;
      case "TreeLayer":
        layer = new TreeLayer(context);
        break;
      case "GridLayer":
        layer = new GridLayer(context);
        break;
      case "DateLayer":
        layer = new DateLayer(context);
        break;
      //形状 shape
      case "CircularShapeLayer":
        layer = new CircularShapeLayer(context);
        break;
      case "RectangleShapeLayer":
        layer = new RectangleShapeLayer(context);
        break;
      //过滤 filter
      case "DateFilterLayer":
        layer = new DateFilterLayer(context);
        break;
      case "RadioFilterLayer":
        layer = new RadioFilterLayer(context);
        break;
      case "SelectFilterLayer":
        layer = new SelectFilterLayer(context);
        break;
      //图表 chart
      case "LineChartLayer":
        layer = new LineChartLayer(context); //折线图
        break;
      case "LineBarChartLayer":
        layer = new LineBarChartLayer(context); //折柱混合
        break;
      case "Bar3DChartLayer":
        layer = new Bar3DChartLayer(context); //柱状图
        break;
      case "BarChartLayer":
        layer = new BarChartLayer(context); //柱状图
        break;
      case "BarYChartLayer":
        layer = new BarYChartLayer(context); //条形图
        break;
      case "BarPictorChartLayer":
        layer = new BarPictorChartLayer(context); //立体柱状图
        break;
      case "BarPictorYChartLayer":
        layer = new BarPictorYChartLayer(context); //水平柱状图
        break;
      case "BarDoughnutChartLayer":
        layer = new BarDoughnutChartLayer(context); //环形柱状
        break;
      case "PieChartLayer":
        layer = new PieChartLayer(context); //饼图
        break;
      case "Pie3DChartLayer":
        layer = new Pie3DChartLayer(context); //3d饼图
        break;
      case "PieRoseChartLayer":
        layer = new PieRoseChartLayer(context); //玫瑰图
        break;
      case "PieDoughnutChartLayer":
        layer = new PieDoughnutChartLayer(context); //环形图
        break;
      case "PieDoughnutGapChartLayer":
        layer = new PieDoughnutGapChartLayer(context); //环形图断开
        break;
      case "PieNestChartLayer":
        layer = new PieNestChartLayer(context); //嵌套饼图
        break;
      case "ScatterChartLayer":
        layer = new ScatterChartLayer(context); //散点/气泡
        break;
      case "RadarChartLayer":
        layer = new RadarChartLayer(context); //雷达图
        break;
      case "GaugeChartLayer":
        layer = new GaugeChartLayer(context); //仪表盘
        break;
      case "FunnelChartLayer":
        layer = new FunnelChartLayer(context); //漏斗
        break;
      case "CandlestickChartLayer":
        layer = new CandlestickChartLayer(context); //K线图
        break;
      case "SankeyChartLayer":
        layer = new SankeyChartLayer(context); //桑基
        break;
      case "GraphChartLayer":
        layer = new GraphChartLayer(context); //关系
        break;
      case "LinesChartLayer":
        layer = new LinesChartLayer(context); //路径
        break;
      case "BoxplotChartLayer":
        layer = new BoxplotChartLayer(context); //盒须
        break;
      case "ParallelChartLayer":
        layer = new ParallelChartLayer(context); //平行坐标系
        break;
      case "WordChartLayer":
        layer = new WordChartLayer(context); //词云
        break;
      case "LiquidChartLayer":
        layer = new LiquidChartLayer(context); //水晶球
        break;
      //地图 map
      case "ZgScMapLayer":
        layer = new ZgScMapLayer(context); //中国色彩
        break;
      case "ZgRlMapLayer":
        layer = new ZgRlMapLayer(context); //中国热力
        break;
      case "ZgQpMapLayer":
        layer = new ZgQpMapLayer(context); //中国气泡
        break;
      case "ZgFxMapLayer":
        layer = new ZgFxMapLayer(context); //中国飞线
        break;
      case "Zg3DMapLayer":
        layer = new Zg3DMapLayer(context); //中国3d;
        break;
      default:
        alert("创建失败![不存在" + type + "类型-图层]");
    }
    //图层-初始化
    if (layer) {
      layer.initProperty(property);
      layer.initLayer();
    }
  }
  return layer;
}

/**
 * 构建图层
 * Builder
 * @param {*} context 上下文
 * @param {*} json Jsons
 * @param {*} isReq 是否查询(请求)
 * @param {*} parentId
 * @param {*} parentContainer
 * @returns
 */
export function buildLayer(context, json, isReq, parentId, parentContainer) {
  if (context && json) {
    const property = JSON.parse(JSON.stringify(json));
    //删除孩子
    if (property && property["childs"]) {
      delete property["childs"];
    }
    //添加父Id
    if (parentId) {
      property["parentId"] = parentId;
    }
    //创建图层
    const layer = createLayer(context, property);
    if (layer) {
      if (isReq) {
        //刷新数据
        layer.refreshData();
        // //刷新图层
        // layer.refreshLayer();
        // //刷新定时
        // layer.refreshInterval();
      } else {
        //临时数据
        if (property["tempData"] && property["tempData"].length) {
          layer.bindData = property["tempData"];
          layer.refreshLayer();
        }
      }
      //添加上下文
      context.addLayer(layer);
      //处理父容器
      if (parentContainer) {
        //图层容器
        const layerContainer = layer.getContainer();
        parentContainer.append(layerContainer);
      }

      //处理递归
      const layerId = layer.property.id;
      let canvasContainer;
      if (layer.getCanvasContainer) {
        canvasContainer = layer.getCanvasContainer();
      }
      //孩子节点
      const childs = json["childs"];
      if (childs && childs.length) {
        for (let i = 0; i < childs.length; i++) {
          const child = childs[i];
          buildLayer(context, child, isReq, layerId, canvasContainer);
        }
      }
    }
    return layer;
  }
}
/**
 *
 */
/**
 * 解析-图层
 * @param {*} isDelId 是否删除Id
 * @param {*} isOffset 是否偏移
 * @param {*} ishold 是否保留数据
 * @param {*} isAlter 是否改变数据(脱敏)
 * @param {*} layer 图层
 * @param {*} parent 父属性
 * @returns
 */
export function parseLayer(isDelId, isOffset, ishold, isAlter, layer, parent) {
  if (layer && layer.property) {
    const property = JSON.parse(JSON.stringify(layer.property));
    //是否删除Id
    if (isDelId && property) {
      if (property.id) {
        delete property.id;
      }
      if (property.parentId) {
        delete property.parentId;
      }
    }
    //是否偏移
    if (isOffset && property) {
      //复制的组件向右下偏移
      property.left = (property.left || 0) + 30;
      property.top = (property.top || 0) + 30;
    }
    //数据
    const datas = layer.bindData;
    if (datas && datas.length) {
      //(是否保留)
      if (ishold) {
        property["tempData"] = datas; //临时数据
      }
      //(是否脱敏)
      if (isAlter) {
        property["bind"]["bindType"] = "mock";
        property["bind"]["mock"] = alterLayerData(datas);
      }
    }

    //保存孩子节点
    if (parent) {
      if (!parent["childs"]) {
        parent["childs"] = [];
      }
      parent["childs"].push(property);
    }
    //孩子
    const childs = layer.childs;
    if (childs && childs.length) {
      for (let i = 0; i < childs.length; i++) {
        const child = childs[i];
        parseLayer(isDelId, isOffset, ishold, isAlter, child, property);
      }
    }
    return property;
  }
}
/**
 * 变更-图层数据
 *
 * Sensitively Data
 * @param {*} datas 敏感数据
 * @returns
 */
export function alterLayerData(datas) {
  let list;
  //中英文正则表达式^[\u4e00-\u9fa5a-zA-Z]+$
  const reg = /^[\u4e00-\u9fa5a-zA-Z]+$/;
  //电话号码正则表达式
  const regTemp = /^1\d{10}$/;
  //金额正则表达式
  // const regexMoney = new RegExp('^\d+(\.\d{1,2})?$');
  var regexMoney = /^[1-9]\d*(\.\d{1,2})?$|^0\.\d{1,2}$/;

  const regPhone = new RegExp("(d{3})d{4}(d{4})");
  const ignoreDesensitize = [
    "dimension",
    "group",
    "source",
    "target",
    "x",
    "y",
    "key",
    "parentKey",
    "name",
    "category",
    "symbolSize",
    "code",
  ];
  if (datas && datas.length) {
    for (let i = 0; i < datas.length; i++) {
      const data = datas[i];
      const item = {};
      //处理数据
      for (let key in data) {
        let value = data[key];
        if (ignoreDesensitize.includes(key)) {
          item[key] = value;
        } else {
          if (reg.test(value)) {
            var encryptedStr = value.substring(0, 1); // 获取第一位字符作为结果的开头部分
            for (var j = 1; j < value.length; j++) {
              encryptedStr += "*"; // 将除了首尾之外的所有字符都用 * 代替
            }
            if (value.length > 2) {
              encryptedStr += value.slice(-1); // 获取最后一位字符作为结果的结尾部分
            }
            value = encryptedStr;
          }
          if (regTemp.test(value)) {
            value = value.replace(regPhone, "$1****$2");
          }
          if (regexMoney.test(value)) {
            value = Math.floor(Math.random() * 100) + 1;
          }
          item[key] = value;
        }
      }
      if (!list) {
        list = [];
      }
      list.push(item);
    }
  }
  return list;
}
