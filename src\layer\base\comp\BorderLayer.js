import { toStyle, refreshCss } from "../../../utils/StyleUtil";
import BaseLayer from "../BaseLayer";
export default class BorderLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "BorderLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {//边框属性(bd)
        border:{
          bdType: "image",//类型 image素材,color边框，
          image: {//图片
            id:"",
            type:"3",
            // imageUrl: "http://localhost:5500/border.png",
            fileUrl: require('../../../assets/border.png'),
          },
          //边框颜色
          borderColor: "#ccc",
          // border: "78px solid transparent",
          //边框样式
          borderStyle: "solid",//solid
          //配置裁剪信息
          borderSlice: "40 24 47 19",
          //配置边框
          borderWidth: "45px 49px 80px 35px",
        }
      },
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-border"></div>`);
      panelContainer.append(this.compContainer);
      // const imageContainer = $(`<img src="`+require('../../../assets/border.png')+`"/>`);
      // this.compContainer.append(imageContainer);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if(chart){
        //宽高/字体
        this.refreshWH(chart);
        this.refreshFS(chart);
        //样式属性
        const sytle = toStyle(this.context,chart);
        refreshCss(this.compContainer, sytle);
      }
    }
  }

  refreshBind () {}
}