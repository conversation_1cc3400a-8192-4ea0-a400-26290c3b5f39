import FullScreen from "../util/FullScreen";
import { toStyle, refreshCss } from "../../../utils/StyleUtil";
import BaseLayer from "../BaseLayer";
export default class FullScreenLayer extends BaseLayer {
  constructor(context) {
    super(context);
    this.isfull = false;
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "FullScreenLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        // font: {},
        // border: {},
      },

    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-full fd-font fd-zhankai" style="cursor:pointer"></div>`);//fd-shouqi
      panelContainer.append(this.compContainer);
      const self = this;
      const fullScreen = new FullScreen(this.context);
      $(this.compContainer).on("click", function (event) {
        fullScreen.refreshOpen(self, function (isFull) {
          self.refreshIcon(isFull);
        });
      });
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if(chart){
        //宽高/字体
        this.refreshWH(chart);
        this.refreshFS(chart);
        //样式属性
        const sytle = toStyle(this.context,chart);
        refreshCss(this.compContainer, sytle);
      }
    
    }
  }
  refreshBind () { };

  refreshIcon (isFull) {
    const container = this.compContainer;
    if (container) {
      if (isFull) {
        $(container).removeClass("fd-zhankai");
        $(container).addClass("fd-shouqi");
      } else {
        $(container).removeClass("fd-shouqi");
        $(container).addClass("fd-zhankai");
      }
    }
  }
}