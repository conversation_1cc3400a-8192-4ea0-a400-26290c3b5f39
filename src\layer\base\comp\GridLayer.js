import JPlayerHandler from "../util/JPlayerHandler";
import { toStyle } from "../../../utils/StyleUtil";
import BaseLayer from "../BaseLayer";
export default class GridLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "栅格",
      type: "GridLayer",
      left: 0, top: 0, height: 300, width: 400,
      tabs: [//type:"default/text/icon"
        { isShow: true, type: "text", text: "4屏", total: 4, col: 2 },
        { isShow: true, type: "text", text: "6屏", total: 6, col: 3 },
        { isShow: true, type: "text", text: "9屏", total: 9, col: 3 },
      ],
      chart: {
        tabStyle: {
          bg: {},
          font: {},
          border: { width: "10px", height: "10px", border: "1px solid rgb(0, 0, 0)", borderRadius: "50%", marginLeft: "10", }
        },
        tabSelectStyle: {
          bg: { background: "#ccc" },
          font: {},
          border: { width: "10px", height: "10px", border: "1px solid rgb(0, 0, 0)", borderRadius: "50%", marginLeft: "10", }
        },
        headStyle: {
          isShow: true,
          height: 50,
          bg: {},
          border: {},
        },
        bodyStyle: {
          height: "calc(100% - 50px);",
          bg: {},
          border: {},
        },
        itemStyle: {
          type: "video",//1.图片(image), 2.视频(video)
          // width: 100,
          // height: 100,
          video: {
            type: "mv4",
            autoplay: false,
            controls: false,
            loop: false,//是否循环
            muted: false,//是否开启声音
            url: "http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4",
          },
          // image: {
          //   width: 300, height: 400,
          //   border: {},
          //   bg: {},
          // },
          textStyle: {
            font: {},
            border: {},
            bg: {},
          },
        },
      },
      bind: {
        bindType: "mock",
        isSingleRow: true,//是否单行数据
        rowNum: 0,//默认第0行数据
        mappings: [
          { key: "name", desc: "名称", value: "${name}" },
          { key: "url", desc: "地址", value: "${url}" }
        ],
        columns: ["name", "url"],
        mock: [
          { name: '北区大门监控', url: 'https://www.w3schools.com/html/movie.mp4' },
          { name: '南区大门监控', url: 'https://www.w3schools.com/html/movie.mp4' },
          { name: '上坪监控', url: 'https://media.w3.org/2010/05/sintel/trailer.mp4' },
          { name: '卫生间广场监控', url: 'https://media.w3.org/2010/05/sintel/trailer.mp4' },
          { name: '1楼过道', url: 'https://media.w3.org/2010/05/sintel/trailer.mp4' },
          { name: '1号影院监控', url: 'https://media.w3.org/2010/05/sintel/trailer.mp4' },
          { name: 'move', url: 'https://media.w3.org/2010/05/sintel/trailer.mp4' },
          { name: '东南角监控', url: 'https://media.w3.org/2010/05/sintel/trailer.mp4' },
          { name: '上沙沟监控', url: 'https://media.w3.org/2010/05/sintel/trailer.mp4' },
          { name: '北区停车场1', url: 'https://media.w3.org/2010/05/sintel/trailer.mp4' },
          { name: '北区停车场出口', url: 'https://media.w3.org/2010/05/sintel/trailer.mp4' },
          { name: '南区地下停车场入口-左侧', url: 'https://media.w3.org/2010/05/sintel/trailer.mp4' }
        ]
      }
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-grid">
          <div class='lsd-tabs-wrap'>
            <div class='lsd-tabs-head'></div>
            <div class="lsd-tabs-body"></div>
          </div>
      </div>`);
      panelContainer.append(this.compContainer);
      this.headContainer = $(this.compContainer).find(".lsd-tabs-head");
      this.bodyContainer = $(this.compContainer).find(".lsd-tabs-body");
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      //宽高
      this.refreshWH(chart);
      //刷新样式
      this.refreshModel();
      this.refreshStyle();
    }
  }

  refreshStyle () {
    const headContainer = this.headContainer;
    const bodyContainer = this.bodyContainer;
    const chart = this.property.chart;

    //刷新head样式
    if(headContainer && chart){
        if(chart.headStyle){
          if(chart.headStyle.isShow === true){
            $(headContainer).show();
          }else if(chart.headStyle.isShow === false){
            $(headContainer).hide();
          }

          const contentListContainer = $(headContainer).find(".head-item");
          if(chart.tabStyle.type === "img"){
            contentListContainer.empty();

            const borderStyle = chart.tabStyle.border;
            $(contentListContainer).css(borderStyle);
          }
        }
    }

    //刷新body样式
    if(bodyContainer && chart){
      if(chart.bodyStyle){
        const bodyStyle = toStyle(this.context,chart.bodyStyle);
        $(bodyContainer).css(bodyStyle);
      }
      if(chart.itemStyle){
        const bodyItemContainer = $(bodyContainer).find(".lsd-grid-item");
        const itemStyle = toStyle(this.context,chart.itemStyle);
        $(bodyItemContainer).css(itemStyle);
      }

    }
  }

  refreshBind () {
    if (this.bodyContainer && this.bindData && this.bindData.length) {
      // this.compContainer.empty();
      // this.refreshModel();
    }
  };
  refreshModel () {
    const headContainer = this.headContainer;
    const chart = this.property.chart;
    const list = this.property.tabs;
    this.refreshHead(headContainer, chart, list);
  }
  refreshHead (headContainer, chart, list) {
    if (headContainer && chart) {
      headContainer.empty();
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          item["index"] = i;
          //渲染
          if (item.isShow) {
            const itemContainer = $(`<div class="head-item">
              <div class="item-content"></div>
            </div>`);
            headContainer.append(itemContainer);
            const contentContainer = $(itemContainer).find(".item-content");
            //内容
            if (item.type && item.type === "text") {//default/icon
              const text = item.text || "标题" + item.index;
              $(contentContainer).text(text);
            } else {
              const style = { width: "10px", height: "10px", border: "1px solid rgb(0, 0, 0)", borderRadius: "50%", marginLeft: "10", };
              $(contentContainer).css(style);
            }
            //样式
            const style = toStyle(this.context, chart.tabStyle);
            $(itemContainer).css(style);

            //第一个
            if (i === 0) {
              item["isChecked"] = true;
            } else {
              item["isChecked"] = false;
            }
            this.refreshHeadChecked(itemContainer, chart, item);
            //事件
            const self = this;
            $(itemContainer).on("click", function (event) {
              self.cleanAllHeadChecked(headContainer, chart, list);
              item["isChecked"] = true;
              self.refreshHeadChecked(itemContainer, chart, item);
            });
          }
        }
      } else {
        this.refreshBody();
      }
    }
  }
  refreshBody (item) {
    const bodyContainer = this.bodyContainer;
    const chart = this.property.chart;
    const datas = this.bindData;
    this.refreshGrid(bodyContainer, chart, datas, item);
  }
  refreshGrid (bodyContainer, chart, datas, item) {
    if (bodyContainer && chart && datas && datas.length) {
      bodyContainer.empty();
      let length = datas.length;
      //刷新布局
      const style = { display: "grid", gridGap: "5px", "grid-template-columns": "auto auto auto auto", }
      if (item) {
        //分屏总数
        if (item.total) {
          length = item.total;
        }
        //分屏列数
        if (item.col === 2) {
          style["grid-template-columns"] = "auto auto";
        } else if (item.col === 3) {
          style["grid-template-columns"] = "auto auto auto";
        } else {
          style["grid-template-columns"] = "auto auto auto auto";
        }
      }
      $(bodyContainer).css(style);
      //数据渲染
      for (let i = 0; i < length; i++) {
        const data = datas[i];
        this.refreshItem(bodyContainer, chart, data);
      }
    }
  }
  refreshItem (listContainer, chart, item) {
    if (listContainer && chart && item) {
      const itemContainer = $(`<div class='lsd-grid-item'>
        <div class="lsd-grid-item-content"></div>
        <div class="lsd-grid-item-title"><span>`+ item.name + `</span></div>
      </div>`);
      listContainer.append(itemContainer);
      const contentContainer = $(itemContainer).find(".lsd-grid-item-content");
      if (chart.itemStyle.type) {
        if (chart.itemStyle.type === "image") {
          //图片
          const imageContainer = $(`<img class='lsd-grid-item-image' src="` + item.url + `" alt="Fullscreen Image"/>`);
          contentContainer.append(imageContainer);

        } else if (chart.itemStyle.type === "video") {
          //视频
          const videoContainer = $(`<div class="lsd-grid-item-video"  role="application" aria-label="media player"></div>`);
          contentContainer.append(videoContainer);
          const video = chart.itemStyle.video;
          video["url"] = item.url;
          const videoHandler = new JPlayerHandler(videoContainer, video);
        }
      }
    }
  }

  cleanAllHeadChecked (headContainer, chart, list) {
    if (headContainer && list && chart) {
      //清除样式高亮
      const itemListContainer = $(headContainer).find(".head-item");
      $(itemListContainer).removeAttr('style');
      const style = toStyle(this.context, chart.tabStyle);
      $(itemListContainer).css(style);
      //清除数据标识
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          item["isChecked"] = false;
        }
      }
    }
  }
  refreshHeadChecked (itemContainer, chart, item) {
    if (itemContainer && item) {
      if (item.isChecked) {
        //高亮
        let style = {};

        const tabStyle = toStyle(this.context, chart.tabStyle);
        $.extend(style, tabStyle);
        const selectStyle = toStyle(this.context, chart.tabSelectStyle);
        $.extend(style, selectStyle);
        $(itemContainer).css(style);
       
        //刷新Body
        this.refreshBody(item);
      }
    }
  }
}
