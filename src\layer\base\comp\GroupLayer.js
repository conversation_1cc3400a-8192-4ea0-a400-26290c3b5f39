import { toStyle, refreshCss } from "../../../utils/StyleUtil";
import BaseLayer from "../BaseLayer";
import GroupMarqueeHandler from "../util/GroupMarqueeHandler";
export default class GroupLayer extends BaseLayer {
  constructor(context) {
    super(context);
    this.childs = [];
  }
  getDefaultProperty() {
    return {
      name: "组合",
      type: "GroupLayer",
      left: 0, top: 0, height: 600, width: 600,
      chart: {//group
        isOverFlow: true,
        isMarquee: false,
        // bg:{},
        // font: {},
        // border: {},
        marquee: {
          loop: -1, //指定内容将滚动多少次。 “ -1”用于无限运动播放
          scrolldelay: 0, //两次移动之间的延迟量（以毫秒为单位）
          scrollamount: 5, //内容移动速度（像素/秒）
          hoverstop: false,//true-该行在鼠标悬停时停止，false-该行不停止
          direction: 'up',//up,down,left,right 上下左右
        },
      },
    }
  };
  initCompContainer(panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-group" style="position: absolute;transform-origin: 0 0;"></div>`);
      panelContainer.append(this.compContainer);
      this.marqueeContainer = $(`<div class="layer-group-marquee" style="position: relative;transform-origin: 0 0;"></div>`);
      panelContainer.append(this.marqueeContainer);

      this.marqueeHandler = new GroupMarqueeHandler(this.marqueeContainer);
    }
  };
  refreshCompCss() {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if (chart) {
        //宽高/字体
        this.refreshWH(chart);
        // this.refreshFS(chart);
        //样式属性
        const style = toStyle(this.context, chart);
        if(chart["isOverFlow"]){
          style["overflow"] = "hidden";
        }
        refreshCss(this.compContainer, style);

        //1处理跑马灯
        if (chart.isMarquee) {
          $(this.marqueeContainer).show();
          $(this.marqueeContainer).empty();
          const marqueeSytle = toStyle(this.context, chart);
          refreshCss(this.marqueeContainer, marqueeSytle);

          const childsContainer = $(this.compContainer).html();
          $(this.marqueeContainer).append(childsContainer);
          if (chart.marquee) {
            this.marqueeHandler && this.marqueeHandler.init(chart.marquee);
          }
          $(this.compContainer).hide();
        } else {
          $(this.compContainer).show();
          $(this.marqueeContainer).hide();

          this.marqueeHandler && this.marqueeHandler.destroy();
        }
      }
    }
  }
  refreshBind() { };

  /**
   * 获取-画布容器
   * @returns 
   */
  getCanvasContainer() {
    if (this.compContainer) {
      return this.compContainer;
    }
  }
  // refreshChild(property){
  //   if(property && this.property && this.property.id){
  //     property["parentId"]=this.property.id;
  //   }
  // }

  refreshConditionCss(conditionItem) {
    if (this.compContainer && conditionItem) {
      const style = toStyle(this.context, conditionItem)
      refreshCss(this.compContainer, style);
    }
  }
}