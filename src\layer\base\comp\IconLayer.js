import { toStyle, refreshCss } from "../../../utils/StyleUtil";
import BaseLayer from "../BaseLayer";
export default class IconLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "IconLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        icon: {
          isClazz: true,
          family: "lsd-font",//家族(序列) fd-font fd-flag  &#xe8e0;
          prefix: "lsd-icon-",
          clazz: "lsd-icon-auto",
          // text:"&#xe6eb;",
        },
        // font: {},
        // border:{},
      },
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-icon fd-font"></div>`);
      panelContainer.append(this.compContainer);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if(chart){
        //宽高/字体
        this.refreshWH(chart);
        this.refreshFS(chart);
         //刷新图标
        this.refreshIcon(this.compContainer, chart.icon);
        //样式
        const style = toStyle(this.context, chart);
        refreshCss(this.compContainer, style);
      }
    }
  }
  refreshBind () { };
  
  refreshConditionCss (conditionItem) {
    if (this.compContainer && conditionItem) {
      //刷新图标
      if(conditionItem.icon){
        const icon = conditionItem.icon;
        icon["family"] = this.property.chart.icon.family;
        icon["isClazz"] = this.property.chart.icon.isClazz;
        icon["prefix"] = this.property.chart.icon.prefix;
      }
      this.refreshIcon(this.compContainer, conditionItem.icon);
      //样式
      const style = toStyle(this.context, conditionItem)
      refreshCss(this.compContainer, style);
    }
  }
  refreshIcon (container, chart) {
    if (container && chart) {
      //渲染
      if (chart["family"]) {
        $(container).removeClass();
        $(container).addClass("layer-icon");
        $(container).addClass(chart["family"]);
      }
      if (chart["isClazz"]) {
        if (chart["clazz"]) {
          $(container).addClass(chart["clazz"]);
        }
      } else {
        if (chart["text"]) {
          $(container).html(chart["text"]);
        }
      }

    }
  }


}