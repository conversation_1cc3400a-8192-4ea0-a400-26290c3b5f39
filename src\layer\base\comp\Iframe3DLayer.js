import { triggerIframe, senderMessage, receiverMessage } from "../../../utils/ListenerUtil";
import IframeLayer from "./IframeLayer";
export default class Iframe3DLayer extends IframeLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "Iframe3DLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        height: "100%",  //-- 框架的宽度
        width: "100%",   //-- 框架的高度
        scrolling: "no", //-- 设置或获取框架是否可被滚动。 auto＝自动调整 yes ＝有滚动条 no ＝无滚动条
        frameborder: 0,   //-- 定义了内容页的边框,取值为(1|0),缺省值为1
        link: {
          isToken: false
        },
      },
      bind: {
        bindType: "mock",//默认数据绑定类型为图层
        isSingleRow: true,//是否单行数据
        rowNum: 0,//默认第0行数据
        mappings: [
          { key: "lat", value: "${lat}", desc: "维度" },
          { key: "lng", value: "${lng}", desc: "经度" },
          { key: "name", value: "${markName}", desc: "标记名称" },
        ],
        columns: ["lat", "lng", "markName"],
        mock: [
          { lat: '31.243045', lng: '121.494724', markName: '豫园' },
          { lat: '31.243453', lng: '121.497204', markName: '外滩' },
          { lat: '31.234801', lng: '121.512115', markName: '财富金融广场' }
        ]
      },
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<iframe class="layer-iframe3d"></iframe>`);
      panelContainer.append(this.compContainer);
      //获取消息
      this.getMessage();
    }
  };

  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if (chart) {
        //宽高/字体
        this.refreshWH(chart);
        this.refreshFS(chart);
        this.refreshIFrame(this.compContainer, chart);
      }
    }
  }

  refreshUrl(iframeContainer, url) {
    if (url) {
      // $(iframeContainer).attr("src", '#');
      $(iframeContainer).on('load', function () {
        $(iframeContainer).attr("src", url);
        $(iframeContainer).show();
      });
    }
  }

  refreshBind () {
    if (this.compContainer && this.bindData && this.bindData.length) {
      this.compContainer.empty();
      const url = this.getUrl();
      const params = { map3D: { list: this.bindData } };
      //发送消息
      this.postMessage(this.compContainer, params, url);
      // window.isLoad = false;
    }
  };

  /**
   * 消息
   * @param {*} iframeContainer 
   * @param {*} params 
   * @param {*} url 
   */
  postMessage (iframeContainer, params, url) {
    // senderMessage(iframeContainer,params,url);
    triggerIframe(iframeContainer, function () {
      senderMessage(iframeContainer, params, url);
    });
  }
  /**
   * 消息
   */
  getMessage () {
    const self = this;
    receiverMessage(function (params) {
      if (params && params.type && params.type === "map3D") {
        self.clickRelation(params.data);
      }
    });
  }
}