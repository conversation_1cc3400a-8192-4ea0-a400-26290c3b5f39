import { getToken } from "../../../utils/StorageUtil";
import BaseLayer from "../BaseLayer";
export default class IframeLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "IframeLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {//iframe
        height: "100%",  //-- 框架的宽度
        width: "100%",   //-- 框架的高度
        scrolling: "no", //-- 设置或获取框架是否可被滚动。 auto＝自动调整 yes ＝有滚动条 no ＝无滚动条
        frameborder: 0,   //-- 定义了内容页的边框,取值为(1|0),缺省值为1
        link: {
          isToken: false
        },
      },
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<iframe class="layer-iframe"></iframe>`);
      panelContainer.append(this.compContainer);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if (chart) {
        //宽高/字体
        // this.refreshWH(chart);
        // this.refreshFS(chart);
        this.refreshIFrame(this.compContainer, chart);
      }
    }
  }

  refreshBind () { };

  /**
   * 
   * @param {*} iframeContainer 
   * @param {*} iframe 
   */
  refreshIFrame (iframeContainer, iframe) {
    if (iframeContainer && iframe) {
      if (iframe["height"]) {
        $(iframeContainer).css("height", iframe["height"]);
      }
      if (iframe["width"]) {
        $(iframeContainer).css("width", iframe["width"]);
      }
      if (iframe["background"]) {
        $(iframeContainer).css("background", iframe["background"]);
      }
      if (iframe["scrolling"]) {
        $(iframeContainer).attr("scrolling", iframe["scrolling"]);
      }
      if (iframe["frameborder"] || iframe["frameborder"] === 0 || iframe["frameborder"] === "0") {
        $(iframeContainer).attr("frameborder", iframe["frameborder"]);
      }
      if (iframe["marginwidth"]) {
        $(iframeContainer).attr("marginwidth", iframe["marginwidth"]);
      }
      if (iframe["marginheight"]) {
        $(iframeContainer).attr("marginheight", iframe["marginheight"]);
      }
      if (iframe["vspace"]) {
        $(iframeContainer).attr("vspace", iframe["vspace"]);
      }
      if (iframe["hspace"]) {
        $(iframeContainer).attr("hspace", iframe["hspace"]);
      }
      //2.处理刷新src属性
      if (iframe["link"]) {
        //地址拼接
        const url = this.formatLinkToUrl(iframe["link"]);
        this.refreshUrl(iframeContainer, url)
      }
    }
  }
  refreshUrl (iframeContainer, url) {
    if (url) {
      $(iframeContainer).attr("src", url);
      $(iframeContainer).hide();
      $(iframeContainer).on('load', function () {
        $(iframeContainer).show();
      });
    }
  }
  /**
   * 
   * @returns 
   */
  getUrl () {
    const iframe = this.property.chart;
    if (iframe && iframe["link"] && iframe["link"]["url"]) {
      const url = iframe["link"]["url"];
      return url;
    }
  }
  /**
   * 根据link对象获取相关属性
   * @returns {*}
   */
  formatLinkToUrl (linkItem) {
    if (linkItem && linkItem.url) {
      let url = linkItem.url;
      let paramBuffer;
      let isFirst = true;
      const params = linkItem["params"];
      if (params && params.length) {
        for (let i = 0; i < params.length; i++) {
          const param = params[i];
          const paramKey = param.code;
          const paramValue = this.context.getParamValue(param.value) || param.constant;
          if (isFirst) {
            paramBuffer = paramKey + "=" + paramValue;
            isFirst = false;
          } else {
            paramBuffer += "&" + paramKey + "=" + paramValue;
          }
        }
      }
      if (linkItem["isToken"]) {
        const token = getToken();
        if (paramBuffer) {
          paramBuffer += "&" + token.key + "=" + token.value;
        } else {
          paramBuffer = token.key + "=" + token.value
        }
      }
      if (paramBuffer) {
        if (url.indexOf("?") != -1) {
          url += "&" + paramBuffer;
        } else {
          url += "?" + paramBuffer;
        }
      }
      return url;
    }
  }
}