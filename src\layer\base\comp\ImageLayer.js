import { toStyle, refreshCss } from "../../../utils/StyleUtil";
import BaseLayer from "../BaseLayer";
export default class ImageLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "ImageLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        bg:{
          bgType: "image",
          image: {//图片
            id: "",
            type: "1",
            // imageUrl: "http://localhost:5500/border.png",
            fileUrl: require('../../../assets/logo.png'),
          },
        },
      },
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-img"></div>`);
      // this.compContainer = $(`<img class="layer-img" src="` + require('../../../assets/logo.png') + `"/>`);
      panelContainer.append(this.compContainer);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if(chart){
        //宽高/字体
        this.refreshWH(chart);
        this.refreshFS(chart);
        const sytle = toStyle(this.context, chart);
        refreshCss(this.compContainer, sytle);
      }
    }
  }
  refreshBind () { };

  refreshConditionCss (conditionItem) {
    if (this.compContainer && conditionItem) {
      //样式
      const style = toStyle(this.context, conditionItem);
      refreshCss(this.compContainer, style);
    }
  }
}