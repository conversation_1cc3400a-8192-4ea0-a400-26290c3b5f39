import { refreshCss, toBorderStyle, toFontStyle } from "../../../utils/StyleUtil"

import BaseLayer from "../BaseLayer";
export default class MarqueeLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "MarqueeLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        // font: {},
        // border:{},
        marquee: {
          loop: -1, //指定内容将滚动多少次。 “ -1”用于无限运动播放
          scrolldelay: 0, //两次移动之间的延迟量（以毫秒为单位）
          scrollamount: 50, //内容移动速度（像素/秒）
          circular: false, //如果为“ true”-字符串是连续的
          drag: false, //如果为“ true”-启用行拖动
          runshort: true, //如果“ true”-短字符串也“运行”，则“ false”-静止不动
          hoverstop: false,//true-该行在鼠标悬停时停止，false-该行不停止
          direction: 'up',//up,down,left,right 上下左右
        },
      },
      bind: {
        bindType: "mock",//默认数据绑定类型为图层
        mappings: [
          { key: "text", value: "${text}" }
        ],
        columns: ["text"],
        mock: [
          { text: "单媒体服务节点可支持600Mbps的媒体数据转发" },
          { text: "可堆叠媒体服务节点以提高数据转发能力，单平台最大可堆叠≥256台媒体服务节点。" },
          { text: "平台可支持多级互联，为大规模的视频资源云化提供有力保证。" },
          { text: "符合行业标准协议和规范，支持Onvif、GB/T28181、SIP、RTP/RTSP等标准协议接入，兼容主流厂商前端设备。" },
          { text: "资源接入数量不受限制，可通过增加节点实现功能与规模的平滑扩展，全网分布式处理，性能随节点无限扩充，可跨网域，不受地理限制，根据需求可将全球任何地点的音视频信号接入视频云平台。" },
          { text: "通过接入服务器将不同厂家的不同协议与格式进行规则化统一，这样可在保持现有系统不做太大变动的情况下，保证不同系统间互联互通，完美解决跨系统使用问题。" },
          { text: "可直接将地方现有资源通过接入系统并入视频云平台，可根据实际需要在全国任何授权单位进行调用，实现灵活部署。" },
          { text: "构建的全国云资源，可根据上级的授权，灵活调权限范围内的所有资源、相关数据，实现首长到哪里，指挥中心就在哪里的需要。" },
        ],

      }
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-marquee lsd-marquee"></div>`);
      panelContainer.append(this.compContainer);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if(chart){
        //宽高/字体
        this.refreshWH(chart);
        this.refreshChart(this.compContainer, chart);
      }
    }
  }
  refreshBind () {
    if (this.compContainer && this.bindData && this.bindData.length) {
      this.compContainer.empty();
      for (let i = 0; i < this.bindData.length; i++) {
        const data = this.bindData[i];
        if (data && data.text) {
          const itemContainer = $(`<div class="lsd-marquee-item">` + data.text + `</div>`);
          this.compContainer.append(itemContainer);
          $(itemContainer).on("click", function (event) {
            console.info("点击跑马灯一行数据，触发相关联动!", data);
          })
        }
      }
      // if (this.property) {
      //   const chart = this.property.chart;
      //   //销毁第一次（不销毁的话无缝滚动会出现密密麻麻重叠）
      //   $(this.compContainer).liMarquee('destroy');
      //   //开启跑马灯
      //   $(this.compContainer).liMarquee(chart.marquee);
      // }
    }
  };
  
  /**
   * liMarquee使用
   * https://gitee.com/pjt12138/jquery_plug_in
   * https://blog.csdn.net/qq_52059326/article/details/133243581
   * https://blog.csdn.net/m0_65730686/article/details/129005094
   */
  refreshChart (container, chart) {
    if (container && chart) {
      //1处理跑马灯
      if (chart.marquee) {
        // $(container).liMarquee('destroy');
        $(container).liMarquee(chart.marquee);
      }
      //2.1处理跑马灯样式配置
      const layerStyle = {};
      if (chart.font) {
        const fontStyle = toFontStyle(chart.font);
        $.extend(layerStyle, fontStyle);
      }
      if (chart.border) {
        const borderStyle = toBorderStyle(this.context,chart.border);
        $.extend(layerStyle, borderStyle);
      }
      //2.2 处理跑马灯每行的样式配置
      const itemContainer = $(container).find(".lsd-marquee-item");
      if (itemContainer && itemContainer.length) {
        refreshCss(itemContainer, layerStyle);
      }
    }
  }
}