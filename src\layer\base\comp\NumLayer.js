import NumQuota from "../util/NumQuota";
import BaseLayer from "../BaseLayer";
export default class NumLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getLength () {
    if (this.length) {
      return this.length;
    }
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "NumLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        isFlip: true,//是否翻牌
        stepTotal: 20, //步长总值
        stepSplitTime: 40, //时间间隔 ms
        amount: '29574876.98',
        // flex:{},//布局
        num: {//数字
          decimalCount: 2, //保留小数位数
          // bg:{},font:{},border:{},
        },
        // thousand:{isShow:false,num:{bg:{},font:{},border:{},}},//千分位
        // unit:{font:{},},//单位

      },//数字翻牌属性
      bind: {
        bindType: "mock",
        isSingleRow: true,//是否单行数据
        rowNum: 0,//默认第0行数据
        mappings: [
          { key: "amount", desc: "数值", value: "${amount}" }
        ],
        columns: ["amount"],
        mock: [
          { amount: "29574876.98" }
        ]
      }
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-num"></div>`);
      panelContainer.append(this.compContainer);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if (chart) {
        if (chart.amount) {
          this.length = chart.amount.toString().length;
        }
        //宽高/字体
        // this.refreshWH(chart);
        // this.refreshFS(chart);
        if (this.numQuota) {
          this.numQuota.refreshPanel(chart);
        }
      }
    }
  }
  refreshBind () {
    if (this.compContainer) {
      if (this.property.bind.bindType !== 'mock' && this.bindData && this.bindData.length) {
        const data = this.getRowData();
        if (this.property.chart && data && (data.amount || data.amount === 0 || data.amount === "0")) {
          this.property.chart.amount = parseFloat(data.amount);
        }
      }
      this.compContainer.empty();
      //重新渲染(重构)
      this.numQuota = new NumQuota(this.context);
      this.numQuota.initPanel(this.compContainer);
    }
  };

  refreshConditionCss (conditionItem) {
    if (this.numQuota && conditionItem) {
      this.numQuota.refreshStyle(conditionItem);
    }
  }
}