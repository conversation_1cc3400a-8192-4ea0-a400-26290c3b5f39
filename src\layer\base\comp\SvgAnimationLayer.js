import { toStyle, refreshCss } from "../../../utils/StyleUtil";
import BaseLayer from "../BaseLayer";
import SvgAnimationHandler from "../util/SvgAnimationHandler";
export default class SvgAnimationLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  // https://github.com/airbnb/lottie-web
  getDefaultProperty () {
    return {
      name: "名称",
      type: "SvgAnimationLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        speed: 1, // 1 is normal speed.
        loop: false, //
        autoplay: true,
      },
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="svg-animation"></div>`);
      panelContainer.append(this.compContainer);
      this.handler = new SvgAnimationHandler(this.compContainer);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      //宽高/字体
      this.refreshWH(this.compContainer);
      this.refreshFS(this.compContainer);
      //样式属性
      const chart = this.property.chart;
      const sytle = toStyle(this.context,chart);
      refreshCss(this.compContainer, sytle, () => {
        if (this.handler) {
          this.handler.refreshProperty(chart)
          this.handler.init();
        }
      });


    }
  }
  refreshBind () { };
}