import { toStyle, refreshCss } from "../../../utils/StyleUtil";
import BaseLayer from "../BaseLayer";
export default class SvgLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "SvgLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        // bg:{},
        // border: {},
      },
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<img src="` + require('../../../assets/logo.png') + `"/>`);
      panelContainer.append(this.compContainer);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      //宽高/字体
      this.refreshWH(this.compContainer);
      this.refreshFS(this.compContainer);
      //样式属性
      const chart = this.property.chart;
      const sytle = toStyle(this.context,chart);
      refreshCss(this.compContainer, sytle);
    }
  }
  refreshBind () { };
}