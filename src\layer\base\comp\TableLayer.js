import TableMarquee from "../util/TableMarqueeNew";
import BaseLayer from "../BaseLayer";
export default class TableLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "TableLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        // height: 300, width: 400,
        columns: [
          { show: true, key: "index", name: "index", type: "text", sort: 1 },
          { show: true, key: "name", name: "name", type: "text", sort: 2 },
          { show: true, key: "status", name: "status", type: "text", sort: 3 },
          { show: true, key: "amount", name: "amount", type: "text", sort: 4 },
          { show: true, key: "percent", name: "percent", type: "text", sort: 5 },
          { show: true, key: "percent1", name: "percent1", type: "text", sort: 6 }
        ],
        theadStyles: {
          // bg:{},
          // font: {},
          // border: {},
          // thStyle:{bg:{},font:{},border:{},}
        },
        rowStyles: {
          // font: {},
          // border: {},
          // series: [
          //   {
          //     bg: {},
          //     font: {},
          //     border: {},
          //     cellStyle: { bg: {},font: {},  border: {} },
          //     textStyle: { bg: {},font: {},  border: {} }
          //   }
          // ]
        },
        columnStyles: [],
        marquee: {
          isOpen: false,
          speed: 2,
          minRow: 10,
        }
      },
      bind: {
        bindType: "mock",
        columns: ["index", "name", "status", "amount", "percent", "percent1"],
        mock: [
          { index: "1", name: "南昌,广东", status: "开启", amount: "865", percent: "42.32%", percent1: "42.32%" },
          { index: "2", name: "九江", status: "开启", amount: "956", percent: "25.18%", percent1: "25.18%" },
          { index: "3", name: "景德镇", status: "开启", amount: "557", percent: "37.45%", percent1: "37.45%" },
          { index: "4", name: "上饶", status: "开启", amount: "336", percent: "59.13%", percent1: "59.13%" },
          { index: "5", name: "鹰潭", status: "开启", amount: "445", percent: "62.55%", percent1: "62.55%" },
          { index: "6", name: "赣州", status: "开启", amount: "415", percent: "52.55%", percent1: "52.55%" },
          { index: "7", name: "宜春,四川", status: "开启", amount: "745", percent: "32.55%", percent1: "32.55%" },
          { index: "8", name: "西湖区", status: "开启", amount: "423", percent: "72.45%", percent1: "72.45%" },
          { index: "9", name: "东湖区", status: "开启", amount: "345", percent: "32.52%", percent1: "32.52%" },
          { index: "10", name: "青云谱", status: "开启", amount: "245", percent: "42.55%", percent1: "42.55%" },
          { index: "11", name: "高新", status: "开启", amount: "345", percent: "62.55%", percent1: "62.55%" },
          { index: "12", name: "朝阳", status: "开启", amount: "754", percent: "32.55%", percent1: "32.55%" },
          { index: "13", name: "湖北", status: "开启", amount: "652", percent: "42.55%", percent1: "42.55%" },
          { index: "14", name: "上海", status: "开启", amount: "254", percent: "32.55%", percent1: "32.55%" },
          { index: "15", name: "北京", status: "开启", amount: "385", percent: "52.55%", percent1: "52.55%" },
          { index: "16", name: "南京", status: "开启", amount: "279", percent: "82.55%", percent1: "82.55%" },
          { index: "17", name: "琴岛", status: "开启", amount: "364", percent: "22.55%", percent1: "22.55%" },
          { index: "18", name: "青海", status: "开启", amount: "158", percent: "36.55%", percent1: "36.55%" }
        ]
      }
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-table"></div>`);
      panelContainer.append(this.compContainer);
    }
  };
  /**
   * [属性]渲染/样式变更
   */
  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if (chart) {
        //宽高/字体
        this.refreshWH(chart);
        this.refreshFS(chart);
      }
      if (this.tableMarquee) {
        this.tableMarquee.refresh();
      }
    }
  }
  /**
   * [绑定数据]的渲染
   */
  refreshBind () {
    if (this.compContainer && this.bindData && this.bindData.length) {
      this.compContainer.empty();
      // this.getTableColumns();
      // this.property.chart.columns = this.refreshChartColumns();
      //刷新字段顺序
      this.refreshSort();
      const config = this.getConfig();
      this.tableMarquee = new TableMarquee(this.context, config);
      this.tableMarquee.initPanel(this.compContainer);
      this.tableMarquee.refreshTable(this.bindData);
      // this.tableMarquee.refresh();
    }
  };

  /**
   * 
   * @returns 
   */
  getConfig () {
    if (this.property && this.property.relation && this.property.relation.isOpen) {
      const self = this;
      //2.判断是否配置了联动 配置联动则需要绑定点击事件
      this.property.chart["events"] = {
        rowClick: function (dataItem) {
          console.info("配置了联动，触发行点击事件", dataItem);
          self.clickRelation(dataItem);
        }
      }
    }
    return this.property.chart
  }
  /**
   * 描述:刷新table属性的fileds属性
   */

  /**
  * 描述:刷新字段排序(冒泡)
  */
  refreshSort () {
    const list = this.property.chart.columns;
    if (list && list.length) {
      //外层循环，控制趟数，每一次找到一个最大值
      for (let i = 0; i < list.length; i++) {
        // 内层循环,控制比较的次数，并且判断两个数的大小  -i 是因为每找到一个最大数 后面就不需要在比较了
        for (let j = 0; j < list.length - 1 - i; j++) {
          if (list[j]["sort"] > list[j + 1]["sort"]) {
            let temp = list[j]
            list[j] = list[j + 1]
            list[j + 1] = temp
          }
        }
      }
    }
  }
  /**
   * 描述:判断是否是分行条件属性
   * @returns {boolean}
   */
  isRowCondition () {
    return true;
  }
  /**
   * 描述:刷新条件样式
   * @param {*} conditionItem 
   * @param {*} rowNum 
   */
  refreshConditionCss (conditionItem) {
    if (conditionItem) {
      if (conditionItem.display && "hide" === conditionItem.display) {
        this.container.hide();
      } else {
        this.container.show();
      }
      if (this.tableMarquee && this.tableMarquee.tableControl && this.tableMarquee.tableControl.refreshCondition) {
        const rowNum = conditionItem.rowNum;
        this.tableMarquee.tableControl.refreshCondition(conditionItem, rowNum);
      }
    }
  }
  refreshChartColumns () {
    let list;
    const columns = this.getBindColumns();
    if (columns && columns.length) {
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        const item = { show: true, key: column, name: column, type: "text", sort: i + 1, };
        if (!list) {
          list = [];
        }
        list.push(item);
      }
    }
    if (list && list.length) {
      this.property.chart.columns = list;
    }
    // return list;
  }
}