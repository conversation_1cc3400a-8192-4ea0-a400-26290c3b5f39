import { toStyle, refreshCss } from "../../../utils/StyleUtil";
import BaseLayer from "../BaseLayer";
export default class TextLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getLength () {
    if (this.length) {
      return this.length;
    }
  }
  getFontSize (width, height, length) {
    let multiplier = 0;
    if (length) {
      multiplier = 1 / length;
    }
    return Math.min(height, width) * multiplier.toFixed(2);
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "TextLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        text:"文本"
        // bg:{},
        // font: {},
        // border:{},
      },
      bind: {
        bindType: "mock",
        isSingleRow: true,//是否单行数据
        rowNum: 0,//默认第0行数据
        mappings: [{ key: "text", desc: "文本", value: "${text}" }],
        columns: ["text"],
        // mock: [
        //   { text: "文本" }
        // ]
      }
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-text"></div>`);
      panelContainer.append(this.compContainer);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      this.compContainer.empty();
      //刷新样式
      const chart = this.property.chart;
      if (chart) {
        //刷新渲染
        if(chart["text"] || chart["text"]===0 || chart["text"]==="0"){
          this.refreshText(this.compContainer, chart);
          this.length =  chart["text"].length;
        }
        //宽高/字体
        this.refreshWH(chart);
        this.refreshFS(chart);
        const sytle = toStyle(this.context, chart);
        refreshCss(this.compContainer, sytle);
      }
    }
  }
  refreshBind () {
    if (this.bindData && this.bindData.length) {
      const data = this.getRowData();
      if (data) {
        this.property.chart["text"] = data.text;
      }
    }
  };

  refreshConditionCss (conditionItem) {
    if (this.compContainer && conditionItem) {
      // this.refreshChart(this.compContainer, conditionItem);
      const chart = conditionItem;
      if (chart["text"]) {
        this.refreshText(this.compContainer, chart);
      }
      const sytle = toStyle(this.context, chart);
      refreshCss(this.compContainer, sytle);
    }
  }

  refreshText (container, chart) {
    if (container) {
      $(container).text(chart.text);
      if (chart.font && chart.font.isEllipsis) {
        $(container).attr({ title: chart.text });
      } else {
        $(container).removeAttr('title');
      }
    }
  }
}