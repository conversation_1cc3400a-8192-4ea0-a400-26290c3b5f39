import { toTree } from "../../../utils/Util";
import { toStyle } from "../../../utils/StyleUtil";
import BaseLayer from "../BaseLayer";
export default class TreeLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty() {
    return {
      name: "树形",
      type: "TreeLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        // iconStyle: {bg:{},font:{},border:{},},
        // textStyle: {bg:{},font:{},border:{},},
        switchStyle: {
          isShow:true,
          swRound: {
            background: "#FFFFFF",
          },
          open: {
            background: "#04D0D9",
          },
          close: {
            background: "#CCCCCC",
          },
        }
      },
      bind: {
        bindType: "mock",
        isSingleRow: false,//是否单行数据
        rowNum: 0,//默认第0行数据
        mappings: [
          { key: "id", desc: "主键", value: "${id}" },
          { key: "text", desc: "文本", value: "${text}" },
          { key: "parentId", desc: "父键", value: "${parentId}" },
          // { key: "status", desc: "开关", value: "${switch}" },
        ],
        columns: ["id", "text", "parentId", "switch"],
        mock: [
          { id: "1", name: "文本1", parentId: "", switch: "2" },
          { id: "2", name: "文本2", parentId: "", switch: "2" },
          { id: "3", name: "文本3", parentId: "1", switch: "2" },
          { id: "4", name: "文本4", parentId: "1", switch: "2" },
          { id: "5", name: "文本5", parentId: "2", switch: "2" },
          { id: "6", name: "文本6", parentId: "2", switch: "2" },
        ]
      }
    }
  };
  initCompContainer(panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-tree" style="overflow-y: auto;"></div>`);
      panelContainer.append(this.compContainer);
    }
  };
  refreshCompCss() {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if (chart) {
        //宽高/字体
        this.refreshWH(chart);
        this.refreshIconStyle(chart.iconStyle);
        this.refreshTextStyle(chart.textStyle);
        this.refreshSwitchStyle(chart.switchStyle);
      }

    }
  }
  refreshBind() {
    if (this.compContainer && this.bindData && this.bindData.length) {
      this.compContainer.empty();
      const data = toTree(this.bindData, "id", "parentId", "childs");
      if (data) {
        this.tree = data.tree;
        this.treeContainer = this.compContainer;
        const list = this.tree;
        if (list && list.length) {
          for (let i = 0; i < list.length; i++) {
            const item = list[i];
            this.refreshNode(this.compContainer, item);
          }
        }
      }
    }
  };
  /**####################刷新渲染#################### */
  refreshNode(parentContainer, item) {
    if (parentContainer && item) {

      const modelContainer = $(`<div class="lsd-tree" id="` + item.id + `">
      <div class="lsd-tree-head">

        <div class="icon fd-font" id="icon`+ item.id + `"></div>
        <div class="title">`+ item.name + `</div>
        <div class="switch">
            <div class="swRound" style="background-color:#FFFFFF"></div>
        </div>

      </div>
      <div class="lsd-tree-body" id="body`+ item.id + `"></div>
      </div>`);
      parentContainer.append(modelContainer);

      const headContainer = $(modelContainer).find(".lsd-tree-head");
      const bodyContainer = $(modelContainer).find(".lsd-tree-body");
      const iconContainer = $(modelContainer).find(".lsd-tree-head .icon");
      const titleContainer = $(modelContainer).find(".lsd-tree-head .title");
      const switchContainer = $(modelContainer).find(".lsd-tree-head .switch");

      //初始化

      this.refreshOpenNode(modelContainer, item);
      this.refreshCheckedNode(titleContainer, item);
      this.refreshSwitchNode(switchContainer, item);
      //点击open
      const self = this;
      $(iconContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        item.isOpen = !item.isOpen;
        self.refreshOpenNode(modelContainer, item);
      });
      //选中
      $(titleContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        // 单选
        self.cleanCheckedData(self.tree);
        self.cleanCheckedStyle(self.treeContainer);
        item.isChecked = true;
        self.refreshCheckedNode(titleContainer, item);
      });

      //开关
      $(switchContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        item.isSwitch = !item.isSwitch;
        self.refreshSwitchNode(switchContainer, item);
      });

      //孩子 递归
      const childs = item["childs"];
      if (childs && childs.length) {
        for (let i = 0; i < childs.length; i++) {
          const child = childs[i];
          this.refreshNode(bodyContainer, child);
        }
      }
    }
  }

  refreshOpenNode(modelContainer, item) {
    if (modelContainer && item) {
      //用判断是否有孩子
      const childs = item["childs"];
      if (childs && childs.length) {
        //改用Id 防止向下穿透
        const iconContainer = $(modelContainer).find("#icon" + item.id);
        const bodyContainer = $(modelContainer).find("#body" + item.id);
        if (item.isOpen) {
          $(bodyContainer).show();
          $(iconContainer).removeClass("fd-add-select");
          $(iconContainer).addClass("fd-sami-select");
        } else {
          $(bodyContainer).hide();
          $(iconContainer).removeClass("fd-sami-select");
          $(iconContainer).addClass("fd-add-select");
        }
      }
    }
  }
  cleanCheckedData(list) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        item["isChecked"] = false;
        const childs = item["childs"];
        this.cleanCheckedData(childs);
      }
    }
  }
  cleanCheckedStyle(container) {
    if (container) {
      const listContainer = $(container).find(".lsd-tree-selected");
      $(listContainer).each(function (index, element) {
        $(element).removeClass("lsd-tree-selected");
      });
    }
  }
  refreshCheckedNode(titleContainer, item) {
    if (titleContainer && item) {
      if (item.isChecked) {
        $(titleContainer).addClass("lsd-tree-selected");
        //刷新右边
        // this.refreshRight(item);
      } else {
        //多选情况
        $(titleContainer).removeClass("lsd-tree-selected");
      }
    }
  }
  refreshSwitchNode(switchContainer, item) {
    if (switchContainer) {
      if (item.isSwitch) {
        $(switchContainer).removeClass("lsd-switch-close");
        $(switchContainer).addClass("lsd-switch-open");
      } else {
        $(switchContainer).removeClass("lsd-switch-open");
        $(switchContainer).addClass("lsd-switch-close");
      }
      this.refreshSwitchStyle();
    }
  }
  /**####################刷新样式#################### */
  refreshIconStyle(iconStyle) {
    const style = iconStyle || this.property.chart.iconStyle;
    if (this.compContainer && style) {
      const iconContainer = $(this.compContainer).find(".lsd-tree-head .icon");
      const iconStyleCss = toStyle(this.context, style);
      $(iconContainer).css(iconStyleCss);
    }
  }
  refreshTextStyle(textStyle) {
    const style = textStyle || this.property.chart.textStyle;
    if (this.compContainer && style) {
      const titleContainer = $(this.compContainer).find(".lsd-tree-head .title");
      const textStyleCss = toStyle(this.context, style);
      $(titleContainer).css(textStyleCss);
    }
  }
  refreshSwitchStyle(switchStyle) {
    const style = switchStyle || this.property.chart.switchStyle;
    if (this.compContainer && style) {
      //刷新开关样式
      const switchContainer = $(this.compContainer).find(".switch");
      $(switchContainer).css(style);
      if(style.isShow){
        $(switchContainer).show();
      }else{
        $(switchContainer).hide();
      }
      //刷新开关圆点样式
      const swRoundContainer = $(this.compContainer).find(".swRound");
      $(swRoundContainer).css(style.swRound);
      //刷新开关打开样式
      const btnOpenContainer = $(this.compContainer).find(".lsd-switch-open");
      $(btnOpenContainer).css(style.open);
      //刷新开关关闭开样式
      const btnCloseContainer = $(this.compContainer).find(".lsd-switch-close");
      $(btnCloseContainer).css(style.close);
    }
  }
}