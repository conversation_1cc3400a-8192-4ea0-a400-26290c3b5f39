import JPlayerHandler from "../util/JPlayerHandler";

import BaseLayer from "../BaseLayer";
export default class VideoLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "VideoLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {//video
        // height: 300, width: 400,
        type: "m4v",
        autoplay: false,
        controls: false,
        loop: false,//是否循环
        muted: false,//是否开启声音
        url:"http://www.jplayer.org/video/m4v/Big_Buck_Bunny_Trailer.m4v",
        // url:"http://**************:8080/file-web/file/image/20d7a79cf21141e989642a561460e7eb.mp4",
        // url: "http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4",
      },
      bind: {
        bindType: "mock",
        isSingleRow: true,//是否单行数据
        rowNum: 0,//默认第0行数据
        mappings: [
          { key: "url", desc: "地址", value: "${url}" }
        ],
        columns: ["url"],
        mock: [
          // { url: "http://localhost:5500/text.mp4" },
          // { url: "http://localhost:5500/designer/lsd-designer/text.mp4" },
        ]
      }
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-video" role="application" aria-label="media player"></div>`);
      panelContainer.append(this.compContainer);
      this.playerHandler=new JPlayerHandler(this.compContainer);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if (chart) {
        //宽高/字体
        this.refreshWH(chart);
        // this.refreshFS(chart);
      }
      //刷新样式
      if(this.playerHandler){
        this.playerHandler.refreshVideo(chart);
      }
    }
  }
  refreshBind () {
    if (this.bindData && this.bindData.length) {
      const data = this.getRowData();
      //数据渲染
      if (data && data.url) {
        this.property.chart.url = data.url;
      }
    }
  };
  refreshConditionCss (conditionItem) {
    if (conditionItem) {
      const video = conditionItem.videoStyle;
      if(video){
        if(this.playerHandler){
          this.playerHandler.refreshVideo(video);
        }
      }
    }
  }
}
