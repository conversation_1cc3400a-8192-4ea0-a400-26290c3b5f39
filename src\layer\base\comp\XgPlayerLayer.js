import XgPlayerHandler from "../util/XgPlayerHandler";
import BaseLayer from "../BaseLayer";
export default class XgPlayerLayer extends BaseLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "名称",
      type: "XgPlayerLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {//video
        width: 300,
        height: 400,
        //url: "https://media.w3.org/2010/05/sintel/trailer.mp4",
        url:"https://sf1-cdn-tos.huoshanstatic.com/obj/media-fe/xgplayer_doc_video/hls/xgplayer-demo.m3u8",
        type:"HLS", //播放器类型 MP4 FLV HLS DASH
        isLive: true, //是否为直播，默认值：false
        autoplay: true,  // 自动播放 默认值: false
        autoplayMuted: false, // 自动播放静音 默认值: false
        videoInit: true,//是否默认初始化video 默认值：true
        //是否启用内联播放模式 该配置项只在移动端生效，当设置了该属性为true的时候
        playsinline: true,//是否在当前页面播放 默认值：true 
        defaultPlaybackRate: 1,//默认播放速度 默认值：1.0 [0.5-2.0]
        volume: 0.6,//音量 默认值：0.6 [0-1]
        loop: false,     // 是否循环 默认值: false
        //poster: "http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4",
        startTime:0,//初始起播时间，仅点播
        //videoAttributes:{},//视频拓展的属性，如：{muted:true}
        //播放器初始显示语言 默认值: document.documentElement.getAttribute('lang') || navigator.language || 'zh-cn'
        lang: 'zh-cn',
        //是否启用流式布局，启用流式布局时根据width、height计算播放器宽高比，若width和height不是
        fluid: false,//是否自适应屏幕 默认值: true
        //尺寸适配 fixed 保持容器宽/高 fixWidth 保持容器宽度，适配高度 fill 拉伸填充 contain 保持宽高比 auto 默认值
        fitVideoSize:"fixed",//是否固定视频尺寸 默认值: fixed
        //seek操作结束之后播放器的状态，如果取值为auto，则维持原播放状态, 默认是seek之后直接播放
        seekedStatus:"play",//是否启用seeked事件 默认值: true
        //播放器进度条故事点信息
        // progressDot:{
        //   id: 0,         // 唯一标识，用于删除的时候索引
        //   time: 10,      // 展示的时间点，例子为在播放到10s钟的时候展示
        //   text: 'Demo',  // hover的时候展示文案，可以为空
        //   duration: 5,   // 展示时间跨度，单位为s
        //   style: {       // 指定样式
        //       backgroundColor: 'red'
        //   }
        // },
        //进度条预览图配置
        // thumbnail:{
        //   urls: [],      // 雪碧图url列表
        //   pic_num: 128,  // 预览图总帧数
        //   row: 10,       // 每张雪碧图包含的预览图行数
        //   col: 10,       // 每张雪碧图包含的预览图列数
        //   height: 160,   // 预览图每一帧的高度（单位：px）
        //   width: 90      // 预览图每一帧的宽度（单位：px）
        // },
        marginControls:false,//是否开启画面和控制栏分离模式 默认值: false 
        //响应的事件类型 touch  default  mouse
        domEventType:"default",
      },
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-video" role="application" aria-label="media player"></div>`);
      panelContainer.append(this.compContainer);
      this.handler = new XgPlayerHandler(this.compContainer, this.property.chart);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if (chart) {
        //宽高/字体
        this.refreshWH(chart);
      }
      //刷新样式
      this.refreshVideo();
    }
  }
  refreshBind () {};
  refreshConditionCss (conditionItem) {
    if (conditionItem) {
      const video=conditionItem.videoStyle;
      // video.autoplay=true;
      this.refreshVideo(video);
    }
  }

  refreshVideo (config) {
    const video = config || this.property.chart;
    if (this.handler) {
      this.handler.refreshVideo(video);
    }
  }

  playNext(config){
    if (this.handler && config) {
      this.handler.playNext(config);
    }
  }

  playVideo () {
    if (this.handler) {
      this.handler.play();
    }
  }

  pauseVideo () {
    if (this.handler) {
      this.handler.stop();
    }
  }
}