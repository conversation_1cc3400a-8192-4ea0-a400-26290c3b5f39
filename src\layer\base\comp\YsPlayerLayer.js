import BaseLayer from "../BaseLayer";
import YsPlayerHandler from "../util/YsPlayerHandler";
import {uuid} from "../../../utils/Util";
export default class YsPlayerLayer extends BaseLayer {
  constructor(context) {
    super(context);
    this.context = context;
  }
  getDefaultProperty () {
    return {
      name: "萤石视频",
      type: "YsPlayerLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        width: 400, // 视频宽度，默认值为容器容器DOM宽度
        height: 300, // 视频高度，默认值为容器容器DOM高度
        url: "ezopen://open.ys7.com/*********/1.live", // 视频ezopen协议播放地址   ezopen://open.ys7.com/*********/7.live
        audio: 1, // 是否开启声音 1开启 0关闭
        template: "simple", // 播放器模板，可以通过选定模板，使用内置的播放器样式，组件 simple：极简版;standard：标准版;security：安防版;
        // header: [], // 视频头部可选UI组件，可选值：capturePicture：截图,save：录像保存,zoom：电子放大
        // footer: [], // 视频底部部可选UI组件，可选值：talk：对讲,broadcast：语音播报,hd：高清标清切换,fullScreen：全屏
        // plugin: [], // 按需加载插件，可选值： talk：对讲
      },
    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div id="layer-video-${uuid()}" class="layer-video-ys" role="application" aria-label="media player">
            <div class="layer-video-ys-gap"></div>
        </div>`);
      panelContainer.append(this.compContainer);
      this.handler = new YsPlayerHandler(this.context, this.compContainer, this.property.chart);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if (chart) {
        //宽高/字体
        this.refreshWH(chart);
      }
      //刷新样式
      this.refreshVideo(chart);
    }
  }
  refreshBind () {};
  refreshConditionCss (conditionItem) {
    if (conditionItem) {
      const video=conditionItem.videoStyle;
      this.refreshVideo(video);
    }
  }

  refreshVideo (config) {
    const video = config || this.property.chart;
    if (this.handler) {
      this.handler.refreshVideo(video);
    }
  }

  playVideo () {
    if (this.handler) {
      this.handler.play();
    }
  }

  stopVideo () {
    if (this.handler) {
      this.handler.stop();
    }
  }
}