/*#################[全屏]#####################*/
.full-screen-mask {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 9999;
  /* background-color: #3a3f51; */
}

/*#################[数字翻牌]#####################*/
.lsd-num-quota {
  display: flex;
}

.lsd-num-quota-item {
  display: inline-block;
  /* float: left; */
}

.lsd-num-quota-bit {
  display: inline-block;
  line-height: 100%;
  padding: 0px 3px;
  /* float: left; */
}

.lsd-num-quota-split {
  display: inline-block;
  /* float: left; */
}

.lsd-num-quota-unit {
  display: inline-block;
}

/*#################[跑马灯]#####################*/
.lsd-marquee {
  overflow: hidden;
  height: 100%;
  width: 100%;
}

.lsd-marquee-item {
  line-height: 30px;
  /* overflow: hidden; */
}

/*#################[树状]#####################*/
.lsd-tree-head {
  box-sizing: border-box;
  width: 100%;
  line-height: 30px;
  /* display: flex; */
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.lsd-tree-body {
  box-sizing: border-box;
  padding-left: 10px;
}

.lsd-tree-head .icon {
  width: 20px;
  text-align: center;
  color: #2aabd2;
}

.lsd-tree-head .title {
  width: 100%;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.lsd-tree-head .switch {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  /* justify-content: start; */
  /* justify-content: end; */
  width: 32px;
  height: 16px;
  border-radius: 32px;
  padding: 0 2px;
  /* background: #CCCCCC; */
  /* background: #04D0D9; */
}
.switch .swRound {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.lsd-switch-open {
  justify-content: end;
  background: #04D0D9;
}
.lsd-switch-close {
  justify-content: start;
  background: #CCCCCC;
}
.lsd-tree-selected {
  font-size: 14px;
  color: #2aabd2;
}

/*#################[栅格]#####################*/
.lsd-tabs-wrap{
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  /* display: flex; */
  /* 垂直排列 */
  /* flex-direction: column;  */
}
.lsd-tabs-head{
  box-sizing: border-box;
  width: 100%;
  height: 50px;
  display: flex;
   /* 垂直居中 */
  align-items: center;
  background-color: chartreuse;
}
.lsd-tabs-body{
  box-sizing: border-box;
  width: 100%;
  height: calc(100% - 50px);
  overflow-y: auto;
  background-color: mediumspringgreen;
}

.lsd-tabs-head .head-item{
  box-sizing: border-box;
  padding: 5px;
  margin-right: 5px;
  font-size: 30px;
}

.lsd-tabs-body .lsd-grid-item{
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  margin: 6px 3px 0px 3px;
 
}

.lsd-grid-item .lsd-grid-item-content{
  width: 100%;
  height: 80%;
  background-color: aliceblue;
}
.lsd-grid-item .lsd-grid-item-title{
  width: 100%;
  height: 20%;
  line-height: 30px;
  font-size: 13px;
  background-color: grey;
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

/* .lsd-grid-item .lsd-grid-item-image{
  height: 100%;
  width: 100%;
}

.lsd-grid-item .lsd-grid-item-video{
    display: inline-block;
    vertical-align: baseline;
} */

.lsd-grid-item-video video {
  width: 100%;
  height: 100%;
  /* 确保视频的宽高比保持不变，并且能够覆盖整个容器 */
  object-fit: cover;
}

.layer-video-ys .layer-video-ys-gap {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  color: #fffdef;
  font-size: 40px;
}

.layer-group-marquee .marquee-scroll,
.layer-group-marquee .marquee-scroll-item
{width:100%;height:100%;position: relative;}


.layer-table .marquee-limit-height-body {
  height: 100%;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
}


.l-table-new {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
}

.l-table-new .l-table-header {
  display: flex;
  font-weight: bold;
}
/*.l-table-header .l-table-cell {*/
/*  border: 1px solid #ccc;*/
/*}*/
/*.l-table-body .l-table-cell {*/
/*  border: 1px solid #ccc;*/
/*}*/

.l-table-new .l-table-body {
  flex: 1;
  overflow-y: auto;
  position: relative;
}

.l-table-new .l-table-row {
  /*display: flex;*/
  /* width: 100%; */
  min-height: 20px;
  white-space: nowrap;
}

.l-table-new .l-table-row .l-table-cell {
  display: inline-block;
  /*flex: 1;*/
  /*border: 1px solid #ccc;*/
}

.l-table-new .l-table-cell {
  /*flex: 1;*/
  /*border: 1px solid #ccc;*/
}
.l-table-new .l-table-cell-text {
  flex: 1;
}