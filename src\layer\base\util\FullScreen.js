import { parseLayer, buildLayer } from "../../LayerUtil";
export default class FullScreen {
  constructor(context) {
    if (context) {
      this.context = context;
    }
    this.initPanel();
    this.addKeydownListener();
  }
  initPanel () {
    const container = $("body").find(".full-screen-mask");
    if (container && container.length) {
      this.container = container;
    } else {
      this.container = $(`<div class="full-screen-mask"></div>`);
      $("body").append(this.container);
    }
    if(this.container){
      $(this.container).hide();
    }
  }
  refreshPanel () {
    if (this.container) {
      $(this.container).empty();
    }
  }
  refreshOpen (property, callback) {
    //是否全屏
    if (!this.isFullScreen()) {
      if (property && property.parentId) {
        const parentLayer = this.context.getLayerById(property.parentId);
        const property = parseLayer(true, false, true, false, parentLayer);
        const layer = buildLayer(this.context, property, false);
        const layerContainer = this.refreshResize(layer);
        if (this.container) {
          $(this.container).show();
          $(this.container).empty();
          $(this.container).append(layerContainer);
        }
      }
      this.handleFullScreen();
      if(callback){
        callback(true);
      }
    } else {
      if (this.container) {
        $(this.container).hide();
        $(this.container).empty();
      }
      this.exitFullscreen();
      if(callback){
        callback(false);
      }
      
    }
  }
  refreshResize (layer) {
    if (layer) {
      const wH = window.screen.height;
      const wW = window.screen.width;
      // const wH=($(window).outerHeight());
      // const wW=($(window).outerWidth());
      const width = layer.property.width;
      const height = layer.property.height;
      let scaleX = (wW / width);
      let scaleY = (wH / height);
      // const style={transform:"scaleX("+scaleX+") scaleY("+scaleY+")"};
      
      const style = { width: "100%", height: "100%", position: "fixed", top: 0, left: 0, margin: "auto" };
      style["display"] = "flex";
      style["justify-content"] = "center";
      style["align-items"] = "center";
      style["transform"] = "scaleX(" + scaleX + ") scaleY(" + scaleY + ") ";
      const container = layer.getContainer();
      refreshCss(container, style);
      return container;
    }
  }

  /**######################################### */
  /**
   * 描述:是否全屏
   * @returns 
   */
  isFullScreen () {
    return !!(
      document.fullscreen ||
      document.mozFullScreen ||
      document.webkitIsFullScreen ||
      document.webkitFullScreen ||
      document.msFullScreen
    );
  }
  /**
   * 描述:句柄全屏
   * launch
   * handle
   * @param {*} container 
   */
  // handleFullscreen (container) {
  //   let element = document.documentElement;
  //   if (container) {
  //     element = $(container).get(0);
  //   }
  //   if (element.requestFullscreen) {
  //     element.requestFullscreen();//W3C
  //   } else if (element.mozRequestFullScreen) {
  //     element.mozRequestFullScreen();//兼容Firefox
  //   } else if (element.webkitRequestFullScreen) {
  //     element.webkitRequestFullScreen();//兼容Chrome
  //   } else if (element.msRequestFullscreen) {
  //     element.msRequestFullscreen();//兼容IE
  //   }
  // }
  /**
   * 描述:全屏处理(句柄)
   * 调用各个浏览器提供的全屏方法
   */
  handleFullScreen () {
    const doc = document.documentElement;
    if (doc.requestFullscreen) {
      doc.requestFullscreen();
    } else if (doc.mozRequestFullScreen) {
      doc.mozRequestFullScreen();
    } else if (doc.webkitRequestFullScreen) {
      doc.webkitRequestFullScreen();
    } else if (doc.msRequestFullscreen) {
      doc.msRequestFullscreen();
    }
  }
  /**
   * 描述:退出全屏处理
   * 调用各个浏览器提供的退出全屏方法
   */
  exitFullscreen () {
    if (document.exitFullScreen) {
      document.exitFullScreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();//兼容Firefox
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();//兼容Chrome, Safari and Opera等
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();//兼容IE/Edge
    }
  }
  /**
   * 添加键盘监听
   * F11
   */
  addKeydownListener () {
    window.addEventListener("keydown", (event) => {
      const ev = event || window.event
      // console.log(ev.keyCode);
      if (ev.keyCode === 27 || ev.keyCode === 122) {
        return false;
      }
    });
    // $(document).on("keydown", function (event) {//(keyup/keydown)
    //   const ev = event || window.event
    //   // console.log(ev.keyCode);
    //   if (ev.keyCode === 27 || ev.keyCode === 122) {
    //     return false;
    //   }
    // })
  }
}