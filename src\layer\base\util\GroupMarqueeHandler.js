/**
 * 组合组件跑马灯插件
 */
export default class GroupMarqueeHandler {
  constructor(container) {
    this.timer = null;
    this.container = container;
  }

  init(option = {}) {
    if (!this.container) {
      return;
    }
    // 滚动次数
    const loop = option.loop || -1;
    // 滚动延迟
    const scrolldelay = option.scrolldelay || 0;
    // 滚动像素 px/ms
    const scrollamount = option.scrollamount || 5;
    // 滚动方向
    const direction= option.direction || "up";
    // 鼠标进入是否停止
    const hoverstop = Boolean(option.hoverstop);

    const isHorizontal = direction === 'left' || direction === 'right';

    const html = this.container.html();
    this.container.empty();
    const inner = $(`<div class="marquee-scroll overflow-transparent"><div class="marquee-scroll-head marquee-scroll-item">${html}</div><div class="marquee-scroll-tail marquee-scroll-item"></div></div>`);
    this.container.append(inner);
    const scroll = this.container.find('.marquee-scroll');
    const scrollItem = this.container.find('.marquee-scroll-item');
    if (isHorizontal) {
      scroll.css({
        'white-space': 'nowrap',
      })
      scrollItem.css({
        'display': 'inline-block',
      })
    }
    const scrollDom = scroll.get(0)
    const scrollMain = this.container.find(".marquee-scroll-head").get(0);
    const scrollTail = this.container.find(".marquee-scroll-tail").get(0);

    scrollTail.innerHTML = scrollMain.innerHTML;

    let loopCount = 0;

    function marquee(){
      switch(direction){
        case "up":
          if(scrollTail.offsetHeight - scrollDom.scrollTop <= 0){
            scrollDom.scrollTop -= scrollTail.offsetHeight;
            // 滚动次数
            if (loop > -1) {
              if (++loopCount >= loop) {
                clearInterval(this.timer);
              }
            }
          }
          else{
            scrollDom.scrollTop+=scrollamount;
          }
          scroll.css('overflow-y', 'scroll')
          scroll.css('overflow-x', 'hidden')
          break;
        case "down":
          if(scrollDom.scrollTop <= 0){
            scrollDom.scrollTop += scrollTail.offsetHeight;
            if (loop > -1) {
              if (++loopCount >= loop) {
                clearInterval(this.timer);
              }
            }
          }
          else{
            scrollDom.scrollTop-=scrollamount;
          }
          scroll.css('overflow-y', 'scroll')
          scroll.css('overflow-x', 'hidden')
          break;
        case "right":
          if(scrollDom.scrollLeft <= 0){
            scrollDom.scrollLeft += scrollTail.offsetWidth;
            if (loop > -1) {
              if (++loopCount >= loop) {
                clearInterval(this.timer);
              }
            }
          }
          else{
            scrollDom.scrollLeft-=scrollamount;
          }
          scroll.css('overflow-y', 'hidden')
          scroll.css('overflow-x', 'scroll')
          break;
        case "left":
          if(scrollTail.offsetWidth - scrollDom.scrollLeft <= 0){
            scrollDom.scrollLeft -= scrollTail.offsetWidth;
            if (loop > -1) {
              if (++loopCount >= loop) {
                clearInterval(this.timer);
              }
            }
          }
          else{
            scrollDom.scrollLeft+=scrollamount;
          }
          scroll.css('overflow-y', 'hidden')
          scroll.css('overflow-x', 'scroll')
          break;
      }
    }

    this.timer = setInterval(marquee,scrolldelay);
    if (hoverstop) {
      scrollDom.onmouseover = () => {
        clearInterval(this.timer);
      };
      scrollDom.onmouseout = () => {
        this.timer = setInterval(marquee,scrolldelay);
      };
    }
  }

  destroy() {
    if (this.container) {
      const scroll = this.container.find('.marquee-scroll');
      if (scroll && scroll.get(0)) {
        scroll.get(0).onmouseover = null;
        scroll.get(0).onmouseout = null;
      }
      clearInterval(this.timer)
      this.container.empty();
    }

  }
}