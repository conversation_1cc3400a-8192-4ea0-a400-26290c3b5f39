/**
 * 媒体类:视频播放工具
 * 基于jplayer开发的视频播放软件
 * http://www.jplayer.org/
 * https://blog.csdn.net/intergameover/article/details/9665397
 * https://blog.csdn.net/yan8910/article/details/37568019
 * https://www.ancii.com/akknn07u/
 * 
 */
export default class JPlayerHandler {
  constructor(container) {
    this.container = container;
    this.isReady = false;
    // this.initVideo();
  }
  // initVideo () {
  //   const container = this.container;
  //   if (container) {
  //     //构建
  //     const self = this;
  //     this.buildJPlayer(container,function(){
  //       self.isReady=true;
  //     });
  //   }
  // }
  refreshVideo (video) {
    const container = this.container;
    if (container) {
      if (this.isReady) {
        this.refreshJPlayer(container, video);
      } else {
        const self = this;
        this.buildJPlayer(container, function () {
          self.isReady = true;
          self.refreshJPlayer(container, video);
        });
      }
    }
  }



  /**
   * 构建视频
   * @param {*} videoContainer 
   * @param {*} callback 
   */
  buildJPlayer (videoContainer, callback) {
    const container = videoContainer;
    if (container) {
      $(container).jPlayer({
        ready: function () {
          if (callback) {
            callback();
          }
        },
        // play: function() { // 避免多个jPlayer一起玩.
        //   $(this).jPlayer("pauseOthers");
        // },
        // pause: function () {
        //   $(this).jPlayer("clearMedia");
        // },
        swfPath: "./dist/jplayer/jquery.jplayer.swf",//当不支持html5的时候启用swf flash
        supplied: "flv, webmv, ogv, m4v", //包含的文件格式
        size: {
          width: "100%",
          height: "100%",
        },
        useStateClassSkin: true,
        autoBlur: false,
        smoothPlayBar: true,
        keyEnabled: true,
        remainingDuration: true,
        toggleDuration: true,
        fullScreen: true,
      })
    }
  }
  refreshJPlayer (videoContainer, video) {
    const container = videoContainer;
    if (container && video) {
      //视频元素
      const videoElement = $(container).find('video');
      if (videoElement) {
        //是否显示播放控件
        if (video.controls) {
          $(videoElement).attr("controls", true);
        } else {
          $(videoElement).attr("controls", false);
        }
        //是否循环播放
        if (video.loop) {
          $(videoElement).attr("loop", true);
        } else {
          $(videoElement).attr("loop", false);
        }
        //设置大小
        this.setSize(videoElement, video);
      }
      const media = this.buildMedia(video);
      if (media) {
        //设置媒体
        $(container).jPlayer("setMedia", media);
        //是否自动播放
        if (video.autoplay) {
          this.play(container);
        }
        //是否静音
        if (video.muted) {
          this.mute(container);
        } else {
          this.unmute(container);
        }
      }
      //重置大小
      // this.resize(container);
    }
  }
  buildMedia (video) {
    let media;
    if (video && video.url) {
      if (video.type === "flv") {
        media = { flv: video.url };
      } else if (video.type === "webmv") {
        media = { webmv: video.url };
      } else if (video.type === "ogv") {
        media = { ogv: video.url };
      } else {
        media = { m4v: video.url };
      }
    }
    return media;
  }

  //播放
  play (container, time) {
    if (container) {
      if (time) {
        $(container).jPlayer("play", time);
      } else {
        $(container).jPlayer("play");
      }
    }
  }
  //停止
  stop (container) {
    if (container) {
      $(container).jPlayer("stop");
    }
  }
  //暂停
  pause (container) {
    if (container) {
      $(container).jPlayer("pause");
    }
  }
  //销毁
  destroy (container) {
    if (container) {
      $(container).jPlayer("destroy");
    }
  }
  //循环
  repeat (container) {
    if (container) {
      $(container).jPlayer("repeat");
    }
  }
  //静音
  mute (container) {
    if (container) {
      $(container).jPlayer("mute");
    }
  }
  //取消静音
  unmute (container) {
    if (container) {
      $(container).jPlayer("unmute");
    }
  }
  //重置大小
  resize (container) {
    if (container) {
      $(container).jPlayer("resize");
    }
  }
  //设置大小
  setSize (container, video) {
    // if (container && video) {
    //   $(container).jPlayer({ size: { width: video.width, height: video.height } });
    // }
    if (container) {
      $(container).css({
        width: video.width, // 例如："500px"
        height: video.height, // 例如："300px"
      });
    }
  }
}
