import { refreshCss, toStyle, toFontStyle, toFlexStyle } from "../../../utils/StyleUtil";
export default class NumQuota {
  constructor(context) {
    if (context) {
      this.context = context;
    }
    this.refreshProperty();
  }
  getDefaultProperty () {
    return {
      isFlip: true,//是否翻牌
      stepTotal: 20, //步长总值
      stepSplitTime: 40, //时间间隔 ms
      // flex:{},//布局
      num: {//数字
        decimalCount: 2, //保留小数位数
        // font:{},border:{},bg:{}
      },
      // thousand:{isShow:false,num:{font:{},border:{},bg:{}}},//千分位
      // unit:{font:{},},//单位
    }
  }
  /**
   * 初始化-属性
   * @param {*} property 
   */
  refreshProperty (property) {
    this.property = this.getDefaultProperty();
    // if(property){
    //   this.property=property;
    // }
    if (property) {
      for (let key in property) {
        if (!this.property) {
          this.property = {};
        }
        this.property[key] = property[key];
      }
    }
  }
  initPanel (parentContainer) {
    if (parentContainer) {
      this.layerContainer = $(`<div class="lsd-num-quota" style="white-space: nowrap;"></div>`);
      parentContainer.append(this.layerContainer);
    }
  }

  refreshPanel (property) {
    this.refreshProperty(property)
    this.refreshStyle()
    if (this.property && (this.property.amount || this.property.amount === '0' || this.property.amount === 0)) {
      if (this.property["isFlip"]) {
        const stepGrow = this.computeStepGrow(this.property.amount);
        this.startGrow(this.property.amount, 0, stepGrow, 0);
      } else {
        this.refreshNum(this.property.amount);
      }
    }
  }
  /**
   * 描述:刷新滚动数字动画
   * @param endNum
   */
  refreshQuota (amountStr) {
    if (this.layerContainer) {
      this.layerContainer.empty();
      if (amountStr) {
        for (let i = 0; i < amountStr.length; i++) {
          const key = amountStr.charAt(i);
          if (key === ",") {
            const numQuotaItem = $(`<div class="lsd-num-quota-bit">` + key + `</div>`);
            this.layerContainer.append(numQuotaItem);
          } else if (key === ".") {
            const numQuotaItem = $(`<div class="lsd-num-quota-split">` + key + `</div>`);
            this.layerContainer.append(numQuotaItem);
          } else {
            const numQuotaItem = $(`<div class="lsd-num-quota-item">` + key + `</div>`);
            this.layerContainer.append(numQuotaItem);
          }
        }
      } else if ("0" === amountStr || 0 === amountStr) {
        const numQuotaItem = $(`<div class="lsd-num-quota-item">0</div>`);
        this.layerContainer.append(numQuotaItem);
      }
    }
  }
  refreshUnit (property) {
    const unitText = this.getUnitText(property);
    if (this.layerContainer && unitText) {
      const unitContainer = $(`<div class="lsd-num-quota-unit">` + unitText + `</div>`);
      this.layerContainer.append(unitContainer);
    }
  }
  /**
     * 描述:刷新样式
     */
  refreshStyle (property) {
    const propertyStyle = property || this.property;
    if (this.layerContainer && propertyStyle) {
      //刷新宽高字体
      const sytle = toStyle(this.context, propertyStyle);
      refreshCss(this.layerContainer, sytle);
      //2.1 处理布局样式
      if (propertyStyle.flex) {
        this.flexStyle = toFlexStyle(propertyStyle.flex);
        refreshCss(this.layerContainer, this.flexStyle);
      }
      //2.2.1 处理数字样式
      if (propertyStyle.num) {
        const numItemsContainer = this.layerContainer.find(".lsd-num-quota-item");
        const numStyle = toStyle(this.context, propertyStyle.num);
        refreshCss(numItemsContainer, numStyle);
        //处理小数点的样式
        if (propertyStyle.num.font) {
          const numItemSplitContainer = this.layerContainer.find(".lsd-num-quota-split");
          const fontStyle = toFontStyle(propertyStyle.num.font);
          refreshCss(numItemSplitContainer, fontStyle);
        }
      }
      //2.2.2 处理千分位
      if (propertyStyle.thousand && propertyStyle.thousand.num) {
        const numBitsContainer = this.layerContainer.find(".lsd-num-quota-bit");
        const thousandNumStyle = toStyle(this.context, propertyStyle.thousand.num);
        refreshCss(numBitsContainer, thousandNumStyle);
      }
      //2.3 处理单位样式
      if (propertyStyle.unit) {
        const unitItemContainer = this.layerContainer.find(".lsd-num-quota-unit");
        const unitStyle = toStyle(this.context, propertyStyle.unit);
        refreshCss(unitItemContainer, unitStyle);
      }
    }
  }
  /**
   * 刷新数字
   * @param {*} num 
   */
  refreshNum (num) {
    const propertyStyle = this.property;
    const amountStr = this.formatAmount(num, propertyStyle);
    this.refreshQuota(amountStr);
    if (propertyStyle) {
      if (propertyStyle["unit"] && propertyStyle["unit"]["show"]) {
        this.refreshUnit(propertyStyle);
      }
      this.refreshStyle(propertyStyle);
    }
  }



  /**
   * 描述:刷新步曾
   * @param endNum 最终值
   * @param currentNum 当前值
   * @param stepGrow  步长每次增长数字
   * @param stepTotal 步长运行总次数
   * @param stepCount 步长运行次数统计
   */
  startGrow (endNum, currentNum, stepGrow, stepCount) {
    const self = this;
    //步长运行总次数
    const stepTotal = this.getStepTotal();
    //步长间隔时间
    const stepSplitTime = this.getStepSplitTime();

    if ((!stepCount && stepCount !== 0) || !stepTotal) {
      self.refreshNum(endNum);
      return;
    }

    if (stepCount < stepTotal) {
      stepCount++;
      self.refreshNum(currentNum);
      setTimeout(function () {
        currentNum += stepGrow;
        self.startGrow(endNum, currentNum, stepGrow, stepCount);
      }, stepSplitTime);
    } else {
      self.refreshNum(endNum);
    }
  }
  /**
   * 描述:计算步长增量值
   * @param endNum
   */
  computeStepGrow (endNum) {
    let stepTotal = this.getStepTotal();
    if (endNum) {
      const stepGrow = (endNum / stepTotal);
      return stepGrow;
    }
  }
  /**
   * 描述:获取步骤总值
   * @param total
   */
  getStepSplitTime () {
    if (this.property && this.property.stepSplitTime) {
      return this.property.stepSplitTime;
    }
    return 40;
  }
  /**
   * 描述:获取步骤总值
   * @param total
   */
  getStepTotal () {
    if (this.property && this.property.stepTotal) {
      return this.property.stepTotal;
    }
    return 20;
  }

  /**########################单位########################### */
  /**
     * 描述:获取单位文本
     * @returns {string}
     */
  getUnitText (property) {
    const propertyStyle = property || this.property;
    if (propertyStyle.unit && propertyStyle.unit.show) {
      let unitText;
      switch (propertyStyle.unit.type) {
        case "1":
          unitText = "%";
          break;
        case "2":
          unitText = "万";
          break;
        case "3":
          unitText = "十万";
          break;
        case "4":
          unitText = "百万";
          break;
        case "5":
          unitText = "千万";
          break;
        case "6":
          unitText = "亿";
          break;
        case "7":
          unitText = propertyStyle.unit.text;
          break;
        default:
          unitText = "元";
          break;
      }
      return unitText;
    }
  }
  /**
   * 描述:格式化金额
   * @param amount
   */
  formatAmount (amount, property) {
    if (!amount || amount === 0 || "0" === amount) {
      return amount || 0;
    }
    const propertyStyle = property || this.property;
    let formateAmount = parseFloat(amount);
    if (propertyStyle.unit && propertyStyle.unit["type"]) {
      //处理单位
      switch (propertyStyle.unit["type"]) {
        case "1":
          formateAmount = parseFloat(formateAmount * 100);
          break;
        case "2":
          formateAmount = parseFloat(formateAmount / 10000);
          break;
        case "3":
          formateAmount = parseFloat(formateAmount / 100000);
          break;
        case "4":
          formateAmount = parseFloat(formateAmount / 1000000);
          break;
        case "5":
          formateAmount = parseFloat(formateAmount / 10000000);
          break;
        case "6":
          formateAmount = parseFloat(formateAmount / 100000000);
          break;
        default:
          break;
      }
    }
    //处理保留小数
    if (this.property.num && (this.property.num["decimalCount"] || 0 === this.property.num["decimalCount"])) {
      formateAmount = formateAmount.toFixed(this.property.num["decimalCount"]);
    }
    //处理千分位
    if (this.property["thousand"] && this.property["thousand"]["isShow"]) {
      formateAmount = this.thousandthFormat(formateAmount);
    }
    return formateAmount + "";
  }
  /**
   * @description: 用于将数字金额，处理成千分位格式
   * @param {Number} num 需要处理的数字
   * @param {String} mark 用于分隔的字符
   * @return:{String} 千分位格式的金额
   */
  thousandthFormat (number, mark) {
    const separator = mark || ',';
    // 将数字根据小数点'.'分割成数组
    const arrNumber = number && number.toString().split('.');
    if (arrNumber && arrNumber.length) {
      arrNumber[0] = this.formatCash(arrNumber[0], separator);
      // 将小数点前面的数字，添加分隔符
      // arrNumber[0] = arrNumber[0].replace(/B(?=(d{3})+(?!d))/g, separator);
    }
    // 将数组处理为字符串返回
    return arrNumber && arrNumber.join('.');
  }

  /**
   * 整数千分位
   * @param str
   * @param separator
   * @returns {string}
   */
  formatCash (str, separator) {
    const arr = [];
    let counter = 0;
    str = (str || 0).toString().split("");
    for (let i = str.length - 1; i >= 0; i--) {
      counter++;
      arr.unshift(str[i]);
      if (!(counter % 3) && i !== 0) {
        arr.unshift(separator || ",");
      }
    }
    return arr.join("");
  }
}