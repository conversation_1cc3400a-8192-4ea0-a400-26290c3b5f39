import Lottie from 'lottie-web'

export default class SvgAnimationHandler {
  constructor(container) {
    this.container = container;
    this.property = this.getDefaultProperty();
  }

  getDefaultProperty() {
    return {
      animationData: require('../../../assets/svg/msg.json'),
      speed: 1, // 1 is normal speed.
      loop: false, //
      autoplay: true,
    }
  }

  refreshProperty (property) {
    if (property) {
      for (let key in property) {
        if (!this.property) {
          this.property = {};
        }
        this.property[key] = property[key];
      }
    }
  }

  init() {
    if (this.lottie) {
      this.destroy();
    }

    if (this.container && this.container.length) {
      const lottieConfig = {
        container: this.container.get(0),
        renderer: 'svg',
        loop: false,
        autoplay: false,
      }
      const config = Object.assign(lottieConfig, this.property);
      if (config.hasOwnProperty('path') &&
        config.hasOwnProperty('animationData') &&
        config.path.startsWith('http') &&
        config.path.endsWith('json')) {
        delete config.animationData
      }
      this.lottie = Lottie.loadAnimation(config)
    }
  }

  play() {
    if (this.lottie) {
      this.lottie.play()
    }
  }

  pause() {
    if (this.lottie) {
      this.lottie.pause()
    }
  }

  stop() {
    if (this.lottie) {
      this.lottie.stop()
    }
  }

  destroy() {
    if (this.lottie) {
      this.lottie.destroy()
    }
  }

}