import { refreshCss, toStyle } from "../../../utils/StyleUtil";
import Expression from "../../../utils/Expression";
import { jumpTo } from "../../../utils/JumpUtil";
/**
 * 描述:表格面板组件
 *  selector:{ //选择中的数据
 *      $$selected:true
 *  },
 *  config {
 *      data:[{
 *
 *      }],
 *      rows:{
 *
 *      },
 *      style:{
 *          thead:{},
 *          tbody:{},
 *          th:{},
 *          td:{},
 *      },
 *      tbody:{
 *          height: width padding margin
 *          bg,font,border
 *          tr{
 *              height: width padding margin
 *              bg,font,border
 *          }
 *      },
 *      rowStyles:{
 *          height: width padding margin,
 *          font
 *          series:[{
 *              bg,font,border
 *          }]
 *      }
 *      tools:["add","edit","del"],
 *      columns:[{
 *          key:"id",
 *          name:"编码",
 *          type:"INPUT,TEXT,SELECT,CHECKBOX,RADIO,INDEX",
 *      }],
 *      columnStyles:[{
 *          cellStyle:{}, //单元格样式
 *          jump:{},    //
 *          progress:{},//
 *          textStyle:{},
 *          key:"id",
 *          type:"",
 *      }],
 *      events:{
 *          change:function(item,key,value){} //修改变量
 *          rowClick:function(item){}; //行点击方法
 *          rowDbClick:function(item){}; //行双击方法
 *          columnClick:function(item,column){};//字段点击
 *          columnDbClick:function(item,column){};//字段双击
 *      }
 *  }
 */
export default class TableHelper {
  constructor(context, config) {
    this.context = context;
    this.initPanel();
    this.config = config;
    this.datas = [];
  }

  refreshCondition (conditionItem, rowNum) {
    const tbodyContainer = this.tbodyContainer;
    if (tbodyContainer && conditionItem) {
      //满足条件 处理字段
      const conditionColumns = conditionItem["columnStyles"];
      if (conditionColumns && conditionColumns.length) {
        for (let i = 0; i < conditionColumns.length; i++) {
          const conditionColumn = conditionColumns[i];
          if (conditionColumn["key"]) {
            const column = this.getColumnByKey(conditionColumn["key"]);
            conditionColumn["name"] = column["name"];
            conditionColumn["show"] = column["show"];
            conditionColumn["type"] = column["type"];
            conditionColumn["split"] = column["split"];
            conditionColumn["decimalCount"] = column["decimalCount"];
            this.refreshColumnStyle(conditionColumn, rowNum);
          }
        }
      }
    }
  }
  refreshJump (tBodyContainer) {
    const config = this.config;
    const datas = this.datas;
    if (tBodyContainer && config) {
      const styles = config.columnStyles;
      if (styles && styles.length) {
        for (let i = 0; i < styles.length; i++) {
          const style = styles[i];

          if (style.jump && style.jump.isOpen && datas && datas.length) {
            const tdDivContainer = tBodyContainer.find("[column='" + style.key + "']");
            tdDivContainer.each(function (index, element) {
              // let jumpData = {};
              // if(self.data && self.data.length && self.data[index]){
              //     jumpData=self.data[index-1];
              // }
              $(element).css("cursor", "pointer");
              $(element).on("click", function (event) {
                if (style && style["jump"]) {
                  jumpTo(style["jump"], datas[index - 1]);
                }
              })
            });
          }
        }
      }
    }
  }
  /**
   * 描述:初始化面板
   */
  initPanel () {
    this.container = $(`<div class="lsd-ptab-wrap" style="width: 100%;height: 100%">
        <table class="l-table" style="width: 100%;border-collapse: collapse;">
            <thead class="l-table-thead"></thead>
            <tbody class="l-table-tbody"></tbody>
        </table>
    </div>`);
    this.theadContainer = $(this.container).find(".l-table-thead");
    this.tbodyContainer = $(this.container).find(".l-table-tbody");
  }
  /**
   * 描述:刷新面板
   */
  refreshPanel (datas) {
    const config = this.config;
    if (config) {
      this.datas = datas;
      const columns = config.columns;
      this.refreshThead(columns);
      this.refreshTbody(columns, datas, config);
    }
  }
  refreshStyle () {
    const config = this.config;
    if (config) {
      const columns = config.columns;
      const datas = this.datas;
      this.refreshTheadStyle(columns, config);
      this.refreshTbodyStyle(columns, config, datas);
    }
  }

  refreshThead (columns) {
    const theadContainer = this.theadContainer;
    if (theadContainer) {
      theadContainer.empty();
      const trContainer = $(`<tr class="l-table-tr" ></tr>`);
      theadContainer.append(trContainer);

      if (columns && columns.length) {
        for (let i = 0; i < columns.length; i++) {
          const column = columns[i];
          //渲染
          const thContainer = $(`<th class="l-table-th" style="max-width:50px;"></th>`);
          trContainer.append(thContainer);
          $(thContainer).attr("column", column.key);
          $(thContainer).text(column.name);
        }
      }
    }
  }

  refreshTbody (columns, datas, config) {
    const tbodyContainer = this.tbodyContainer;
    if (tbodyContainer) {
      tbodyContainer.empty();
      if (datas && datas.length) {
        for (let i = 0; i < datas.length; i++) {
          const data = datas[i];
          //添加行
          const trContainer = $(`<tr class="l-table-tr"></tr>`);
          tbodyContainer.append(trContainer);
          $(trContainer).attr("row-num", i);
          //添加列
          this.refreshTd(columns, trContainer, data);
          //点击事件
          $(trContainer).on("click", function (event) {
            if (config && config.events && config.events.rowClick) {
              config.events.rowClick();
            }
          });
        }
      } else {
        //无数据
        if (config) {
          const noData = config.noData;
          let rowContainer;
          if (noData && noData.isShow && noData.html && noData.html != "") {
            rowContainer = $(noData.html);
          } else {
            rowContainer = $(`<tr><td><div class="l-table-noData">无数据</div></td></tr>`);
          }
          tbodyContainer.append(rowContainer);
        }
      }
    }
  }
  refreshTd (columns, trContainer, data) {
    if (trContainer && data) {
      if (columns && columns.length) {
        for (let i = 0; i < columns.length; i++) {
          const column = columns[i];
          //获取值
          const key = column.key;
          let value = data[column.key];
          if (typeof (value) === "undefined" || value === null || value === "" || value === "undefined") {
            value = "";
          }
          //保留小数
          if (column.decimalCount || 0 === column.decimalCount) {
            //判断数值是否为数字类型
            if (!isNaN(parseFloat(value))) {
              if (value.indexOf("%") > 0) {
                value = parseFloat(value).toFixed(column.decimalCount) + "%";
              } else {
                value = parseFloat(value).toFixed(column.decimalCount);
              }
            }
          }
          //渲染
          const tdContainer = $(`<td class="l-table-td"></td>`);
          trContainer.append(tdContainer);
          $(tdContainer).attr("column", column.key);
          // $(tdContainer).text(value);
          this.refreshText(tdContainer, column, value);
        }
      }
    }
  }
  refreshText (tdContainer, column, value) {
    if (tdContainer) {
      if (column && column.split) {
        const listContainer = $(`<div class="l-table-td-list"  style="display: flex"  ></div>`);
        tdContainer.append(listContainer);
        let list;
        if (value.indexOf(",") !== -1) {
          list = value.split(",");
        } else {
          list = [value];
        }
        if (list && list.length) {
          for (let i = 0; i < list.length; i++) {
            const item = list[i];
            const textContainer = $(`<div class="l-table-td-text" >` + item + `</div>`);
            tdContainer.append(textContainer);
          }
        }
      } else {
        const textContainer = $(`<div class="l-table-td-text" >` + value + `</div>`);
        tdContainer.append(textContainer);
      }
    }
  }

  refreshTheadStyle (columns, config) {
    const theadContainer = this.theadContainer;
    if (theadContainer && config) {
      const theadConfig = config.theadStyles;
      //处理theadStyle
      const theadStyle = toStyle(this.context, theadConfig);
      refreshCss(theadContainer, theadStyle);
      //thead.isShow
      if (theadConfig.isShow) {
        theadContainer.show();
      } else {
        theadContainer.hide();
      }
      //style.thStyle
      const thConfig = theadConfig.thStyle || {};
      if (columns && columns.length) {
        for (let i = 0; i < columns.length; i++) {
          const column = columns[i];
          const colContainer = $(theadContainer).find("[column='" + column.key + "']");
          //显示隐藏
          if (column.show) {
            colContainer.show();
          } else {
            colContainer.hide();
          }
          const thStyle = toStyle(this.context, thConfig);
          //处理width
          if (column.key) {
            const columnStyle = this.getColumnStyleByKey(config, column.key);
            if (columnStyle && columnStyle.width) {
              thStyle["width"] = columnStyle.width;
            }
            refreshCss(colContainer, thStyle);
          }
        }
      }
    }
  }
  refreshTbodyStyle (columns, config, datas) {
    const tbodyContainer = this.tbodyContainer;
    if (tbodyContainer && config) {

      //行样式
      const rowConfig = config.rowStyles;
      const rowStyle = toStyle(this.context, rowConfig);

      const serieStyles = [];
      //处理rowStyle.series
      const series = rowConfig.series;
      if (series && series.length) {
        for (let i = 0; i < series.length; i++) {
          const serie = series[i];
          const serieStyle = toStyle(this.context, serie);

          if (serie.rowNum) {
            const rowNum = (serie.rowNum - 1)

            let trStyle = {};
            $.extend(trStyle, rowStyle);
            $.extend(trStyle, serieStyle);
            const trContainer = tbodyContainer.find("[row-num='" + rowNum + "']");
            refreshCss(trContainer, trStyle);

            const tdContainer = trContainer.find(".l-table-td");
            const tdTextContainer = trContainer.find(".l-table-td-text");
            refreshCss(tdContainer, toStyle(this.context, serie.cellStyle));
            refreshCss(tdTextContainer, toStyle(this.context, serie.textStyle));

          } else {
            serieStyles.push(serieStyle);
          }
        }
      }
      tbodyContainer.find(".l-table-tr").each(function (index, element) {
        let style = {};
        $.extend(style, rowStyle);
        //配置行系列样式
        if (serieStyles && serieStyles.length) {
          const rowSeriesIndex = (index % serieStyles.length);
          if (serieStyles[rowSeriesIndex]) {
            $.extend(style, serieStyles[rowSeriesIndex]);
          }
        }
        refreshCss($(element), style);
      });

      //处理columns属性
      if (columns && columns.length) {
        for (let column of columns) {
          const colContainer = $(tbodyContainer).find("[column='" + column.key + "']");
          //显示隐藏
          if (column.show) {
            colContainer.show();
          } else {
            colContainer.hide();
          }
          //
          if (column.key) {
            let tempColumn = {};
            $.extend(tempColumn, column);
            const columnStyle = this.getColumnStyleByKey(config, column.key);
            if (columnStyle) {
              $.extend(tempColumn, columnStyle);
            }
            //刷新字段配置样式属性
            this.refreshColumnStyle(tempColumn);
          }
        }
      }
    }
  }

  refreshColumnStyle (column, rowNum) {
    const datas = this.datas;
    if (column) {
      //是否显示
      //保留两位小数
      //行号
      if (rowNum || rowNum === "0" || rowNum === 0) {
        this.refreshColumnItemStyle(datas, column, rowNum);
      } else {
        //分行刷新字段信息
        for (let i = 0; i < datas.length; i++) {
          this.refreshColumnItemStyle(datas, column, i);
        }
      }
    }
  }
  refreshColumnItemStyle (datas, column, index) {
    const tbodyContainer = this.tbodyContainer;
    if (tbodyContainer && datas && column) {
      //
      const columnTrSelector = tbodyContainer.find("[row-num='" + index + "']");
      const columnTdSelector = columnTrSelector.find("[column='" + column["key"] + "']");
      const columnTdTextSelector = columnTdSelector.find(".l-table-td-text");
      //样式处理-单元格
      if (column.cellStyle) {
        const cellStyle = toStyle(this.context, column.cellStyle);
        refreshCss(columnTdSelector, cellStyle);
      }
      //样式处理-文本样式
      if (column.textStyle) {
        const textStyle = toStyle(this.context, column.textStyle);
        refreshCss(columnTdTextSelector, textStyle);
      }
      //刷新标题
      this.refreshAttrTitle(columnTdTextSelector, column);
      //刷新进度条
      this.refreshProgress(columnTdTextSelector, column, datas[index]);
    }
  }

  /**
   * 描述:刷新字段属性
   */
  refreshAttrTitle (columnTdDivSelector, column) {
    columnTdDivSelector.each(function (index, element) {
      $(element).removeAttr("title");
      if (column.title) {
        const text = $(element).text();
        $(element).attr("title", text);
      }
    })
  }

  /**
   * 描述:刷新进度条信息
   * @param columnTdDivSelector
   * @param column
   * @param columnData
   * @returns {boolean}
   */
  refreshProgress (columnTdDivSelector, column, columnData) {
    if (column && column.progress && column.progress.isShow) {
      if (!columnData) {
        console.error("进度条渲染失败!columnData为空");
        return false;
      }
      let percent = 0;
      if (column["key"] && columnData[column["key"]]) {
        percent = parseFloat(columnData[column["key"]]);
      }
      columnTdDivSelector.each(function (index, element) {
        //2.清除内容
        $(element).empty();
        if (column.progress.isText) {
          $(element).text(percent + "%");
        }
        //刷新样式
        if (!column.progress.height) {
          console.error("进度条需要配置高度");
        }
        const progressStyle = toStyle(this.context, column.progress);
        if (column.progress.backgroundColor) {
          progressStyle["background-color"] = column.progress.backgroundColor;
        }
        progressStyle["width"] = parseFloat(percent) + "%";  //234.0
        refreshCss($(element), progressStyle);
      });
    }
  }
  //获取字段
  getColumnByKey (key) {
    let columnMap;
    const columns = this.columns;
    if (columns && columns.length) {
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        if (column["key"]) {
          if (!columnMap) {
            columnMap = {};
          }
          columnMap[column["key"]] = column;
        }
      }
    }
    if (columnMap && columnMap[key]) {
      return columnMap[key];
    }
  }
  //获取字段样式
  getColumnStyleByKey (config, key) {
    if (config && key) {
      const styles = config.columnStyles;
      if (styles && styles.length) {
        for (let i = 0; i < styles.length; i++) {
          const style = styles[i];
          if (style && style["key"] && key === style["key"]) {
            return style;
          }
        }
      }
    }
  }

}