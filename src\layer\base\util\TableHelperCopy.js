import {refreshCss,toStyle} from "../../../utils/StyleUtil";
import Expression from "../../../utils/Expression";
import {jumpTo} from "../../../utils/JumpUtil";
/**
 * 描述:表格面板组件
 *  selector:{ //选择中的数据
 *      $$selected:true
 *  },
 *  config {
 *      data:[{
 *
 *      }],
 *      rows:{
 *
 *      },
 *      style:{
 *          thead:{},
 *          tbody:{},
 *          th:{},
 *          td:{},
 *      },
 *      tbody:{
 *          height: width padding margin
 *          bg,font,border
 *          tr{
 *              height: width padding margin
 *              bg,font,border
 *          }
 *      },
 *      rowStyles:{
 *          height: width padding margin,
 *          font
 *          series:[{
 *              bg,font,border
 *          }]
 *      }
 *      tools:["add","edit","del"],
 *      columns:[{
 *          key:"id",
 *          name:"编码",
 *          type:"INPUT,TEXT,SELECT,CHECKBOX,RADIO,INDEX",
 *      }],
 *      columnStyles:[{
 *          cellStyle:{}, //单元格样式
 *          jump:{},    //
 *          progress:{},//
 *          textStyle:{},
 *          key:"id",
 *          type:"",
 *      }],
 *      events:{
 *          change:function(item,key,value){} //修改变量
 *          rowClick:function(item){}; //行点击方法
 *          rowDbClick:function(item){}; //行双击方法
 *          columnClick:function(item,column){};//字段点击
 *          columnDbClick:function(item,column){};//字段双击
 *      }
 *  }
 */
export default class TableHelperCopy {
    constructor(context,config) {
        this.context=context;
        this.config = config;
        this.data = [];
        this.init();
        this.expression = new Expression();
    }

    /**
     * 描述:刷新表格
     */
    init() {
        this.container = $(`<div class="lsd-ptab-wrap" style="width: 100%;height: 100%"></div>`);
        //2.初始化表格
        this.tableContainer = this.createTableElement();
        this.container.append(this.tableContainer);
        this.theadContainer = this.createTheadElement();
        this.tbodyContainer = this.createTbodyElement();
        this.tableContainer.append(this.theadContainer).append(this.tbodyContainer);
        this.initThead(this.theadContainer);
    }

    /**
     * 描述:获取字段信息
     * @param code
     * @returns {*}
     */
    getColumnByKey(key){
        if(this.config.columns && this.config.columns.length){
            for(let columnItem of this.config.columns){
                if(!this.columnMaps){
                    this.columnMaps={};
                }
                if(columnItem["key"]){
                    this.columnMaps[columnItem["key"]]=columnItem;
                }
            }
            if(this.columnMaps && this.columnMaps[key]){
                return this.columnMaps[key];
            }
        }
    }
    /**
     * 描述:刷新表格样式
     */
    refreshStyle() {
        this.refreshHeaderStyle();
        this.refreshBodyStyle();
    }
    /**
     * 描述:刷新条件样式
     * @param conditionItem
     * @param rowNum
     */
    refreshConditionStyle(conditionItem, rowNum) {
        if (this.tbodyContainer && conditionItem) {
            //满足条件 处理字段
            const conditionColumns = conditionItem["columnStyles"];
            if (conditionColumns && conditionColumns.length) {
                for (let i = 0; i < conditionColumns.length; i++) {
                    const conditionColumn = conditionColumns[i];
                    if(conditionColumn["key"]){
                        const column = this.getColumnByKey(conditionColumn["key"]);
                        conditionColumn["name"]=column["name"];
                        conditionColumn["show"]=column["show"];
                        conditionColumn["type"]=column["type"];
                        conditionColumn["split"]=column["split"];
                        conditionColumn["decimalCount"]=column["decimalCount"];
                        this.refreshColumnStyle(conditionColumn,rowNum);
                    }
                }
            }
        }
    }


    /**
     * 描述:刷新表头样式
     */
    refreshHeaderStyle() {
        //1.刷新表头样式
        if (this.theadContainer && this.config.theadStyles) {
            const theadStyles = this.config.theadStyles;
            //1.刷新头行样式
            const theadStyle = toStyle(this.context,theadStyles);
            refreshCss(this.theadContainer, theadStyle);
            if (theadStyles.isShow) {
                this.theadContainer.show();
            } else {
                this.theadContainer.hide();
            }
            //2.根据字段样式设置单元格宽度
            if (this.config.columns && this.config.columns.length) {
                for (let i = 0; i < this.config.columns.length; i++) {
                    const columnItem = this.config.columns[i];
                    const columnThSelector = this.theadContainer.find("[column='" + columnItem.key + "']");
                    let thStyle;
                    if(theadStyles.thStyle){
                        thStyle=toStyle(this.context,theadStyles.thStyle);
                    }else{
                        thStyle={};
                    }
                    if (!columnItem.show) {
                        thStyle["display"] = "none";
                    }
                    if(columnItem.key){
                        const columnStyle=this.getColumnStyleByKey(columnItem.key);
                        if(columnStyle && columnStyle.width){
                            thStyle["width"]=columnStyle.width;
                        }
                        refreshCss(columnThSelector, thStyle);
                    }
                    const columnName = columnItem.name || "";
                    columnThSelector.text(columnName);
                }
            }
        }
    }
    /**
     * 描述:刷新表头样式
     */
    refreshBodyStyle() {
        //1.配置行公共样式
        if(!this.config || !this.tbodyContainer){
            console.error("刷新行样式异常!没有配置rowStyles 或者 tbodyContainer");
        }else{
            const rowStyles = this.config.rowStyles;
            this.refreshRowStyles(rowStyles);
        }
        //2.刷新字段 + 字段样式组成新的column去刷新字段
        if(!this.config.columns){
            console.warn("刷新字段样式异常!没有配置columns");
        }
        for(let column of this.config.columns){
            if(column["key"]){
                let columnItem={}
                //拷贝字段的属性
                $.extend(columnItem,column);
                //拷贝字段配置样式属性
                let columnStyle = this.getColumnStyleByKey(column["key"]);
                if(columnStyle){
                    $.extend(columnItem,columnStyle);
                }
                //刷新字段配置样式属性
                this.refreshColumnStyle(columnItem);
            }
        }
    }

    /**
     * 描述:返回字段样式信息
     */
    getColumnStyleByKey(key){
        if(key && this.config.columnStyles && this.config.columnStyles.length){
            for(let columnStyle of this.config.columnStyles){
                if(columnStyle && columnStyle["key"] && key===columnStyle["key"]){
                    return columnStyle;
                }
            }
        }
    }
    /**
     * 描述:刷新行样式
     */
    refreshRowStyles(rowStyles){
        if(!rowStyles || !this.tbodyContainer){
            console.warn("刷新行样式异常!没有配置rowStyles 或者 tbodyContainer");
            return;
        }
        //1.配置行公共样式
        const self = this;
        this.rowCommonStyle = toStyle(this.context,rowStyles);
        //2.获取刷新行样式
        // if(rowStyles.series && rowStyles.series.length){
        //   this.rowSeriesStyles=[];
        //   for(let seriesItem of rowStyles.series){
        //       const rowSeriesStyle=toStyle(seriesItem);
        //       this.rowSeriesStyles.push(rowSeriesStyle);
        //   }
        // }
        if(rowStyles.series && rowStyles.series.length){
            this.rowSeriesStyles=[];
            for(let seriesItem of rowStyles.series){
                const rowSeriesStyle=toStyle(this.context,seriesItem);
                // this.rowSeriesStyles.push(rowSeriesStyle);
                if(seriesItem.rowNum){
                  const rowNum=(seriesItem.rowNum-1)
                  const rowContainer = this.tbodyContainer.find("[row-num='" + rowNum + "']");
                  let rowStyle={};
                  $.extend(rowStyle,this.rowCommonStyle);
                  $.extend(rowStyle,rowSeriesStyle);
                  const tdContainer = rowContainer.find(".l-table-td");
                  const tdTextContainer = rowContainer.find(".l-table-td-text");
                  refreshCss(tdContainer,toStyle(this.context,seriesItem.cellStyle));
                  refreshCss(tdTextContainer,toStyle(this.context,seriesItem.textStyle));
                  refreshCss(rowContainer,rowStyle);
                }else{
                  this.rowSeriesStyles.push(rowSeriesStyle);
                }
            }
        }
        this.tbodyContainer.find(".l-table-tr").each(function(index,element){
            let rowStyle={};
            $.extend(rowStyle,self.rowCommonStyle);
            //配置行系列样式
            if(self.rowSeriesStyles && self.rowSeriesStyles.length){
                const rowSeriesIndex = (index % self.rowSeriesStyles.length);
                if(self.rowSeriesStyles[rowSeriesIndex]){
                    $.extend(rowStyle,self.rowSeriesStyles[rowSeriesIndex]);
                }
            }
            refreshCss($(element),rowStyle);
        });
    }
    /**
     * 描述:刷新字段
     *  const tbodyTrSelector = this.tbodyContainer.find(".l-table-tr");
     const tbodyTdSelector = tbodyTrSelector.find(".l-table-td");
     const tbodyTdDivSelector = tbodyTdSelector.find(".l-table-column");
     */
    refreshColumnStyle(column,rowNum) {
        if(!column || !column["key"]|| !this.tbodyContainer){
            console.warn("刷新字段异常!没有配置column或者key值");
        }else{
            //1.单元格样式-显示影厂
            if(!this.data || !this.data.length){
                console.info("列表数据为空不需要刷新行样式信息");
                return false;
            }

            let columnCellStyle={};
            let columnTextStyle={};
            if (!column.show) {
                columnCellStyle["display"] = "none";
            }
            //1.单元格
            if(column.cellStyle){
                const cellTempStyle=toStyle(this.context,column.cellStyle);
                $.extend(columnCellStyle,cellTempStyle);
            }
            //2.文本样式
            if(column.textStyle){
                columnTextStyle=toStyle(this.context,column.textStyle);
            }
            //3.数据格式化
            if(column.decimalCount || 0 === column.decimalCount){
                let newData = [];
                for(let i=0;  i<this.data.length;i++){
                    const dataItem = this.data[i];
                    //判断数值是否为数字类型
                    if(!isNaN(parseFloat(dataItem[column["key"]]))){
                        if(dataItem[column["key"]].indexOf("%") > 0){
                            let numShow = parseFloat(dataItem[column["key"]]);
                            dataItem[column["key"]] = numShow.toFixed(column.decimalCount)+"%";
                        }else{
                            let numShow = parseFloat(dataItem[column["key"]]);
                            dataItem[column["key"]] = numShow.toFixed(column.decimalCount);
                        }
                        
                    }
                    newData.push(dataItem);
                }
                this.data = newData;
            }
            if(rowNum || rowNum==="0" || rowNum===0){
                const columnTrSelector = this.tbodyContainer.find("[row-num='" + rowNum + "']");
                const columnTdSelector = columnTrSelector.find("[column='" + column["key"] + "']");
                refreshCss(columnTdSelector,columnCellStyle);
                const columnTdTextSelector = columnTdSelector.find(".l-table-td-text");
                refreshCss(columnTdTextSelector,columnTextStyle);
                this.refreshAttrTitle(columnTdTextSelector, column);
                //刷新进度条
                this.refreshProgress(columnTdTextSelector, column,this.data[rowNum]);
            }else{
                //分行刷新字段信息
                for(let rowIndex=0;rowIndex<this.data.length;rowIndex++){
                    const columnTrSelector = this.tbodyContainer.find("[row-num='" + rowIndex + "']");
                    const columnTdSelector = columnTrSelector.find("[column='" + column["key"] + "']");
                    refreshCss(columnTdSelector,columnCellStyle);
                    const columnTdTextSelector = columnTdSelector.find(".l-table-td-text");
                    refreshCss(columnTdTextSelector,columnTextStyle);
                    this.refreshAttrTitle(columnTdTextSelector, column);
                    //刷新进度条
                    this.refreshProgress(columnTdTextSelector, column,this.data[rowIndex]);
                }
            }
        }
    }

    /**
     * 描述:刷新进度条信息
     * @param columnTdDivSelector
     * @param column
     * @param columnData
     * @returns {boolean}
     */
    refreshProgress(columnTdDivSelector, column, columnData) {
        if(column && column.progress && column.progress.isShow){
            if(!columnData){
                console.error("进度条渲染失败!columnData为空");
                return false;
            }
            let percent=0;
            if(column["key"] && columnData[column["key"]]){
                percent=parseFloat(columnData[column["key"]]);
            }
            columnTdDivSelector.each(function (index, element) {
                //2.清除内容
                $(element).empty();
                if (column.progress.isText) {
                    $(element).text(percent+"%");
                }
                //刷新样式
                if (!column.progress.height) {
                    console.error("进度条需要配置高度");
                }
                const progressStyle = toStyle(this.context,column.progress);
                if (column.progress.backgroundColor) {
                    progressStyle["background-color"] = column.progress.backgroundColor;
                }
                progressStyle["width"] = parseFloat(percent) + "%";  //234.0
                refreshCss($(element), progressStyle);
            });
        }
    }

    /**
     * 描述:刷新字段属性
     */
    refreshAttrTitle(columnTdDivSelector, columnItem) {
        columnTdDivSelector.each(function (index, element) {
            $(element).removeAttr("title");
            if (columnItem.title) {
                const text = $(element).text();
                $(element).attr("title", text);
            }
        })
    }

    /**
     * 描述: 跳转信息
     * @param tBodyContainer
     */
    refreshColumnJump(tBodyContainer) {
        const self = this;
        if (this.config.columnStyles && this.config.columnStyles.length) {
            for (let i = 0; i < this.config.columnStyles.length; i++) {
                //console.info("========初始化地址跳转===========",this.config);
                const columnStyle = this.config.columnStyles[i];
                if (tBodyContainer && columnStyle.jump && columnStyle.jump.isOpen && self.data && self.data.length) {
                    const tdDivContainer = tBodyContainer.find("[column='" + columnStyle.key + "']");
                    tdDivContainer.each(function (index, element) {
                        // let jumpData = {};
                        // if(self.data && self.data.length && self.data[index]){
                        //     jumpData=self.data[index-1];
                        // }
                        $(element).css("cursor", "pointer");
                        $(element).on("click", function (event) {
                            if(columnStyle && columnStyle["jump"]){
                                jumpTo(columnStyle["jump"],self.data[index-1]);
                            }
                        })
                    });
                }
            }
        }
    }

    /**
     * 初始化表头信息
     * @param theadContainer
     */
    initThead(theadContainer) {
        if (theadContainer && this.config && this.config.columns && this.config.columns.length) {
            const trContainer = this.createTrElement();
            theadContainer.append(trContainer);
            for (let i = 0; i < this.config.columns.length; i++) {
                const columnItem = this.config.columns[i];
                const thContainer = this.createThElement();
                $(thContainer).attr("column", columnItem.key);
                $(thContainer).text(columnItem.name);
                trContainer.append(thContainer);
                if (columnItem.style) {
                    refreshCss(thContainer, columnItem.style);
                }
            }
        } else {
            console.error("未配置表头信息");
        }
    }

    /**
     * 描述:添加一行数据
     * @param item
     */
    add(itemData, index) {
        if (!this.config || !this.config.columns || !this.config.columns.length) {
            console.error("表格字段信息没有定义！请查看config.columns属性!");
            return false;
        }
        //console.info("添加一行数据", itemData);
        const self = this;
        if (!itemData) {
            itemData = {};
        }
        if (!index && 0 !== index) {
            index = this.data.length;
        }
        if (this.tbodyContainer && itemData && this.data) {
            this.data.push(itemData);
            const rowContainer = this.createTrElement();
            $(rowContainer).attr("row-num", index);
            this.tbodyContainer.append(rowContainer);
            for (let i = 0; i < this.config.columns.length; i++) {
                const columnItem = this.config.columns[i];
                const colContainer = this.createTd(columnItem, itemData);
                rowContainer.append(colContainer);
            }
            rowContainer.on("click", function () {
                console.info("触发点击行事件!", rowContainer);
                self.clickRow(rowContainer, itemData);
                if (self.config.events && self.config.events.rowClick) {
                    self.config.events.rowClick(itemData);
                }
            });

            if (self.config.events && self.config.events.rowDbClick) {
                rowContainer.on("dblclick", function () {
                    self.config.events.rowDbClick(itemData);
                });
            }
        }
    }

    /**
     * @param column
     * @param item
     */
    createTd(column, item) {
        if (column && column.type && column.key) {
            let columnValue;
            if (typeof (item[column.key]) === "undefined" || item[column.key] === null || item[column.key] ==="" || item[column.key] ==="undefined") {
                columnValue = "";
            }else{
                columnValue = item[column.key];
            }
            // if(item && item[column.key]){
            //     columnValue = item[column.key];
            // }else{
            //     columnValue = "";
            // }

            const self = this;
            let colContainer = this.createTdElement();
            $(colContainer).attr("column", column.key);
            if (self.config.events && self.config.events.columnClick) {
                colContainer.on("click", function () {
                    self.config.events.columnClick(item, column);
                });
            }
            if (self.config.events && self.config.events.columnDbClick) {
                colContainer.on("dblclick", function () {
                    self.config.events.columnDbClick(item, column);
                });
            }
            //let columnContainer=$(`<div class="l-table-td-text"  column="` + column.key + `  t-model="` + column.key + `">`+columnValue+`</div>`);
            if (column.split && columnValue) {
                const columnContainer=$(`<div class="l-table-td-list"  style="display: flex"  ></div>`);
                colContainer.append(columnContainer);
                let columnItems;
                if(columnValue.indexOf(",") !== -1){
                    columnItems = columnValue.split(",");
                }else{
                    columnItems=[columnValue];
                }
                if(columnItems && columnItems.length){
                    for(let k=0;k<columnItems.length;k++){
                        const columnItemContainer=$(`<div class="l-table-td-text" >` + columnItems[k] + `</div>`);
                        columnContainer.append(columnItemContainer);
                    }
                }
            } else {
                const columnContainer = $(`<div class="l-table-td-text" >` + columnValue + `</div>`);
                colContainer.append(columnContainer);
            }
            return colContainer;
        } else {
            console.error("字段定义属性必填 key name type!", column);
            return false;
        }
    }


    /**
     * 描述:选中行信息
     */
    clickRow(rowContainer, item) {
        if (this.tbodyContainer) {
            this.tbodyContainer.find(".l-table-tr").each(function (index, element) {
                $(element).selected = false;
                $(element).removeClass("selected");
            });
            if (rowContainer) {
                $(rowContainer).addClass("selected");
            }
            this.selector = item;
            this.selectorRow = rowContainer;
        }
    }

    /**
     * 删除一行数据
     * @param index
     */
    del() {
        if (this.selector && this.selectorRow) {
            if (this.data && this.data.length && this.selector) {
                for (let i = 0; i < this.data.length; i++) {
                    if (this.data[i] && this.selector && this.data[i] === this.selector) {
                        this.data.splice(i, 1);
                    }
                }
                this.selectorRow.remove();
            } else {
                console.error("数组为空或者越界!", this.data, index);
            }
        }
    }

    /**
     * 描述:刷新表格
     * @param data
     */
    refresh(data) {
        if (this.tbodyContainer) {
            this.tbodyContainer.empty();
        }
        if (data && data.length) {
            for (let i = 0; i < data.length; i++) {
                this.add(data[i], i);
            }
        } else {
            let rowContainer
            if(this.config && this.config.noData && this.config.noData.isShow && this.config.noData.html && this.config.noData.html!=""){
                const html=this.config.noData.html;
                rowContainer = $(html);
            }else{
                rowContainer = $(`<tr><td><div class="l-table-noData">无数据</div></td></tr>`);
            }
            this.tbodyContainer.append(rowContainer);
        }
        this.refreshStyle();
    }

    createTdElement() {
        return $(`<td class="l-table-td" ></td>`);
    }

    createThElement() {
        return $(`<th class="l-table-th" style="max-width:50px;"></th>`);
    }

    createTrElement() {
        return $(`<tr class="l-table-tr" ></tr>`);
    }

    createTableElement() {
        return $(`<table class="l-table" style="width: 100%;border-collapse: collapse;"></table>`);
    }

    createTheadElement() {
        return $(`<thead class="l-table-thead"></thead>`);
    }

    createTbodyElement() {
        return $(`<tbody class="l-table-tbody"></tbody>`);
    }
}