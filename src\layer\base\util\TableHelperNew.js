import { refreshCss, toStyle } from "../../../utils/StyleUtil";
import { jumpTo } from "../../../utils/JumpUtil";

export default class TableNewHelper {
  constructor(context, config) {
    this.context = context;
    this.initPanel();
    this.config = config;
    this.datas = [];
  }
  /**
   * 描述:初始化面板
   */
  initPanel() {
    this.table = $(`<div class="l-table-new"></div>`);
    this.table.append(`<div class="l-table-header"></div>`);
    this.table.append(`<div class="l-table-body"></div>`);
  }
  /**
   * 描述:刷新面板
   */
  refreshPanel(datas) {
    const config = this.config;
    if (config) {
      this.datas = datas;
      const columns = config.columns;
      this.refreshThead(columns);
      this.refreshTbody(columns, datas, config);
    }
  }
  refreshStyle() {
    const config = this.config;
    if (config) {
      const columns = config.columns;
      const datas = this.datas;
      this.refreshTheadStyle(columns, config);
      this.refreshTbodyStyle(columns, config, datas);
    }
  }

  refreshCondition(conditionItem, rowNum) {
    const tbodyContainer = this.tbodyContainer;
    if (tbodyContainer && conditionItem) {
      //满足条件 处理字段
      const conditionColumns = conditionItem["columnStyles"];
      if (conditionColumns && conditionColumns.length) {
        for (let i = 0; i < conditionColumns.length; i++) {
          const conditionColumn = conditionColumns[i];
          if (conditionColumn["key"]) {
            const column = this.getColumnByKey(conditionColumn["key"]);
            conditionColumn["name"] = column["name"];
            conditionColumn["show"] = column["show"];
            conditionColumn["type"] = column["type"];
            conditionColumn["split"] = column["split"];
            conditionColumn["decimalCount"] = column["decimalCount"];
            this.refreshColumnStyle(conditionColumn, rowNum);
          }
        }
      }
    }
  }
  refreshJump(tBodyContainer) {
    const config = this.config;
    const datas = this.datas;
    if (tBodyContainer && config) {
      const styles = config.columnStyles;
      if (styles && styles.length) {
        for (let i = 0; i < styles.length; i++) {
          const style = styles[i];

          if (style.jump && style.jump.isOpen && datas && datas.length) {
            const tdDivContainer = tBodyContainer.find("[column='" + style.key + "']");
            tdDivContainer.each(function (index, element) {
              // let jumpData = {};
              // if(self.data && self.data.length && self.data[index]){
              //     jumpData=self.data[index-1];
              // }
              $(element).css("cursor", "pointer");
              $(element).on("click", function (event) {
                if (style && style["jump"]) {
                  jumpTo(style["jump"], datas[index - 1]);
                }
              })
            });
          }
        }
      }
    }
  }

  refreshThead(columns) {
    this.theadContainer = $(this.table).find(".l-table-header");
    if (this.theadContainer) {
      this.theadContainer.empty();
      if (columns && columns.length) {
        for (let i = 0; i < columns.length; i++) {
          const column = columns[i];
          //渲染
          // const thContainer = $(`<th class="l-table-th" style="max-width:50px;"></th>`);
          // trContainer.append(thContainer);
          // $(thContainer).attr("column", column.key);
          // $(thContainer).text(column.name);
          const cellContainer = $(`<div class="l-table-cell"></div>`);
          this.theadContainer.append(cellContainer);
          cellContainer.attr('column', column.key)
          cellContainer.text(column.name);
        }
      }
    }
  }

  refreshTbody(columns, datas, config) {
    this.tbodyContainer = $(this.table).find(".l-table-body");
    if (this.tbodyContainer) {
      this.tbodyContainer.empty();
      if (datas && datas.length) {
        for (let i = 0; i < datas.length; i++) {
          const data = datas[i];
          //添加行
          const trContainer = $(` <div class="l-table-row"></div>`);
          this.tbodyContainer.append(trContainer);
          $(trContainer).attr("row-num", i);
          //添加列
          this.refreshTd(columns, trContainer, data);
          //点击事件
          $(trContainer).on("click", function (event) {
            if (config && config.events && config.events.rowClick) {
              config.events.rowClick();
            }
          });
        }
      } else {
        //无数据
        if (config) {
          const noData = config.noData;
          let rowContainer;
          if (noData && noData.isShow && noData.html && noData.html != "") {
            rowContainer = $(noData.html);
          } else {
            rowContainer = $(`<div class="l-table-noData">无数据</div>`);
          }
          this.tbodyContainer.append(rowContainer);
        }
      }
    }
  }
  refreshTd(columns, trContainer, data) {
    if (trContainer && data) {
      if (columns && columns.length) {
        for (let i = 0; i < columns.length; i++) {
          const column = columns[i];
          let value = data[column.key];
          if (typeof (value) === "undefined" || value === null || value === "" || value === "undefined") {
            value = "";
          }
          //保留小数
          if (column.decimalCount || 0 === column.decimalCount) {
            //判断数值是否为数字类型
            if (!isNaN(parseFloat(value))) {
              if (value.indexOf("%") > 0) {
                value = parseFloat(value).toFixed(column.decimalCount) + "%";
              } else {
                value = parseFloat(value).toFixed(column.decimalCount);
              }
            }
          }
          //渲染
          const tdContainer = $(`<div class="l-table-cell"></div>`);
          trContainer.append(tdContainer);
          $(tdContainer).attr("column", column.key);
          this.refreshText(tdContainer, column, value);
        }
      }
    }
  }
  refreshText(tdContainer, column, value) {
    if (tdContainer) {
      if (column && column.split) {
        const listContainer = $(`<div class="l-table-cell-list"  style="display: flex"  ></div>`);
        tdContainer.append(listContainer);
        let list;
        if (value.indexOf(",") !== -1) {
          list = value.split(",");
        } else {
          list = [value];
        }
        if (list && list.length) {
          for (let i = 0; i < list.length; i++) {
            const item = list[i];
            const textContainer = $(`<div class="l-table-cell-text" >` + item + `</div>`);
            tdContainer.append(textContainer);
          }
        }
      } else {
        const textContainer = $(`<div class="l-table-cell-text" >` + value + `</div>`);
        tdContainer.append(textContainer);
      }
    }
  }

  refreshTheadStyle(columns, config) {
    if (this.theadContainer && config && columns && columns.length) {
      const theadConfig = config.theadStyles;
      if (theadConfig.isShow) {
        this.theadContainer.show();

        //处理theadStyle
        const theadStyle = toStyle(this.context, theadConfig);
        refreshCss(this.theadContainer, theadStyle);
      } else {
        this.theadContainer.hide();
      }
      //style.thStyle
      const thConfig = theadConfig.thStyle || {};
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        const colContainer = $(this.theadContainer).find("[column='" + column.key + "']");
        //显示隐藏
        if (column.show) {
          colContainer.show();
          const thStyle = toStyle(this.context, thConfig);
          if (!thStyle["width"]) {
            thStyle["width"] = `${100/columns.length}%`
          }
          refreshCss(colContainer, thStyle);

          // 处理字段样式
          if (column.key) {
            const columnStyle = this.getColumnStyleByKey(config, column.key);
            if (columnStyle) {
              if (columnStyle.textStyle) {
                const style = toStyle(this.context, columnStyle.textStyle);
                refreshCss(colContainer, style);
              }
              if (columnStyle.cellStyle) {
                const style = toStyle(this.context, columnStyle.cellStyle);
                refreshCss(colContainer, style);
              }
            }
          }
        } else {
          colContainer.hide();
        }
      }
    }
  }
  refreshTbodyStyle(columns, config, datas) {
    if (this.tbodyContainer && config && columns && columns.length) {
      const rowConfig = config.rowStyles;
      const rowStyle = toStyle(this.context, rowConfig);

      // 表头样式
      const theadConfig = config.theadStyles || {};
      const thConfig = theadConfig.thStyle || {};
      const thStyle = toStyle(this.context, thConfig);

      // // 处理行样式最外层
      const rows = this.tbodyContainer.find(".l-table-row");
      rows.each((index, element) => {
        // 每一行应用最外层样式
        refreshCss(element, rowStyle);
        // 初始化每个单元格宽度
        $(element).find(".l-table-cell").each((index, td) => {
          // 与表头同宽
          if (thStyle["width"]) {
            $(td).css("width", thStyle["width"])
          } else {
            $(td).css("width", `${100 / columns.length}%`)
          }
        })
      });

      // 处理系列
      const series = rowConfig.series;
      if (series && series.length) {
        for (let i = 0; i < series.length; i++) {
          const serie = series[i];
          if (serie.rowNum) {
            const rowNum = (serie.rowNum - 1)
            const trContainer = this.tbodyContainer.find("[row-num='" + rowNum + "']");
            refreshCss(trContainer, toStyle(this.context, serie));

            const tdContainer = trContainer.find(".l-table-cell");
            const tdTextContainer = trContainer.find(".l-table-cell-text");
            refreshCss(tdContainer, toStyle(this.context, serie.cellStyle));
            refreshCss(tdTextContainer, toStyle(this.context, serie.textStyle));
          }
        }
      }

      // 处理columns字段属性
      for (let column of columns) {
        const colContainer = $(this.tbodyContainer).find("[column='" + column.key + "']");
        //显示隐藏
        if (column.show) {
          colContainer.show();
        } else {
          colContainer.hide();
        }
        //
        if (column.key) {
          let tempColumn = {};
          $.extend(tempColumn, column);
          const columnStyle = this.getColumnStyleByKey(config, column.key);
          if (columnStyle) {
            $.extend(tempColumn, columnStyle);
          }
          //刷新字段配置样式属性
          this.refreshColumnStyle(tempColumn);
        }
      }
    }
  }

  refreshColumnStyle(column, rowNum) {
    const datas = this.datas;
    if (column) {
      //是否显示
      //保留两位小数
      //行号
      if (rowNum || rowNum === "0" || rowNum === 0) {
        this.refreshColumnItemStyle(datas, column, rowNum);
      } else {
        //分行刷新字段信息
        for (let i = 0; i < datas.length; i++) {
          this.refreshColumnItemStyle(datas, column, i);
        }
      }
    }
  }
  refreshColumnItemStyle(datas, column, index) {
    const tbodyContainer = this.tbodyContainer;
    if (tbodyContainer && datas && column) {
      //
      const columnTrSelector = tbodyContainer.find("[row-num='" + index + "']");
      const columnTdSelector = columnTrSelector.find("[column='" + column["key"] + "']");
      const columnTdTextSelector = columnTdSelector.find(".l-table-cell-text");
      //样式处理-单元格
      if (column.cellStyle) {
        const cellStyle = toStyle(this.context, column.cellStyle);
        refreshCss(columnTdSelector, cellStyle);
      }
      //样式处理-文本样式
      if (column.textStyle) {
        const textStyle = toStyle(this.context, column.textStyle);
        refreshCss(columnTdTextSelector, textStyle);
      }
      //刷新标题
      this.refreshAttrTitle(columnTdTextSelector, column);
      //刷新进度条
      this.refreshProgress(columnTdTextSelector, column, datas[index]);
    }
  }

  /**
   * 描述:刷新字段属性
   */
  refreshAttrTitle(columnTdDivSelector, column) {
    columnTdDivSelector.each(function (index, element) {
      $(element).removeAttr("title");
      if (column.title) {
        const text = $(element).text();
        $(element).attr("title", text);
      }
    })
  }

  /**
   * 描述:刷新进度条信息
   * @param columnTdDivSelector
   * @param column
   * @param columnData
   * @returns {boolean}
   */
  refreshProgress(columnTdDivSelector, column, columnData) {
    if (column && column.progress && column.progress.isShow) {
      if (!columnData) {
        console.error("进度条渲染失败!columnData为空");
        return false;
      }
      let percent = 0;
      if (column["key"] && columnData[column["key"]]) {
        percent = parseFloat(columnData[column["key"]]);
      }
      columnTdDivSelector.each(function (index, element) {
        //2.清除内容
        $(element).empty();
        if (column.progress.isText) {
          $(element).text(percent + "%");
        }
        //刷新样式
        if (!column.progress.height) {
          console.error("进度条需要配置高度");
        }
        const progressStyle = toStyle(this.context, column.progress);
        if (column.progress.backgroundColor) {
          progressStyle["background-color"] = column.progress.backgroundColor;
        }
        progressStyle["width"] = parseFloat(percent) + "%";  //234.0
        refreshCss($(element), progressStyle);
      });
    }
  }
  //获取字段
  getColumnByKey(key) {
    let columnMap;
    const columns = this.columns;
    if (columns && columns.length) {
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        if (column["key"]) {
          if (!columnMap) {
            columnMap = {};
          }
          columnMap[column["key"]] = column;
        }
      }
    }
    if (columnMap && columnMap[key]) {
      return columnMap[key];
    }
  }
  //获取字段样式
  getColumnStyleByKey(config, key) {
    if (config && key) {
      const styles = config.columnStyles;
      if (styles && styles.length) {
        for (let i = 0; i < styles.length; i++) {
          const style = styles[i];
          if (style && style["key"] && key === style["key"]) {
            return style;
          }
        }
      }
    }
  }

}