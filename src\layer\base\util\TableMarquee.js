import TableHelper from "./TableHelper";
import { refreshCss } from "../../../utils/StyleUtil";

export default class TableMarquee {
  constructor(context, config) {
    this.context = context;
    this.config = config;
    this.tableControl = new TableHelper(context, config);

  }
  /**
   * 描述: 初始化面板
   * @param parentContainer
   */
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="marquee-wrap" style="position: relative;width:100%;height:100%;overflow:hidden;"></div>`);
      parentContainer.append(this.container);
    }
  }
  /**
   * 描述:刷新tableHelper对象
   * @param bindData
   */
  refreshTable (bindData) {
    if (this.tableControl) {
      this.tableControl.refreshPanel(bindData);
    }
  }
  /**
   * 描述:刷新绑定数据
   */
  refresh () {
    const self = this;
    const config = this.config;
    const tableHandler = this.tableControl;
    const container = this.container;
    if (container && tableHandler && config) {
      tableHandler.refreshStyle();

      this.container.empty();
      this.theadContainer = $(`<div class="marquee-thead"></div>`);
      this.tbodyContainer = $(`<div class="marquee-tbody"></div>`);
      // 限制滚动高度的容器
      this.limitHeightBody = $(`<div class="marquee-limit-height-body"></div>`)
      this.container.append(this.theadContainer);
      this.container.append(this.limitHeightBody);
      this.limitHeightBody.append(this.tbodyContainer)
      //拷贝
      const tableHtml = tableHandler.container.html();
      this.theadContainer.append(tableHtml);
      this.tbodyContainer.append(tableHtml);
      tableHandler.refreshJump(this.tbodyContainer);
      //透明
      const opacityStyle = this.getOpacityStyle();

      const headerStyle = { position: "absolute", left: "0px", top: "-1px", width: "100%", "z-index": "90", "margin-top": "-1px", overflow: "hidden" };
      refreshCss(this.theadContainer, headerStyle);
      refreshCss(this.theadContainer.find(".l-table-tbody"), opacityStyle);

      //滚动条
      let overflow = 'hidden';
      if (config.overflow) {
        //是否开启isOpen
        if (config.overflow.isOpen) {
          overflow = 'auto'
          refreshCss(this.container, { overflow: overflow });
        } else {
          overflow = 'hidden'
          refreshCss(this.container, { overflow: overflow });
        }
        //是否显示isShow
        if (config.overflow.isShow) {
          if ($(this.container).hasClass('overflow-transparent')) {
            $(this.container).removeClass("overflow-transparent");
          }
          if ($(this.limitHeightBody).hasClass('overflow-transparent')) {
            $(this.limitHeightBody).removeClass("overflow-transparent");
          }
        } else {
          if (!$(this.container).hasClass('overflow-transparent')) {
            $(this.container).addClass("overflow-transparent");
          }
          if (!$(this.limitHeightBody).hasClass('overflow-transparent')) {
            $(this.limitHeightBody).addClass("overflow-transparent");
          }
        }
      }

      // 开启显示表头后，表体的垂直偏移量
      let top = 0;
      if (config.theadStyles && config.theadStyles.isShow) {
        // 表头高度
        const headerHeight = this.getTHeaderHeight();
        // 偏移表头的距离
        top = -headerHeight;
        refreshCss(this.limitHeightBody, { height: `calc(100% - ${headerHeight}px)`, overflow: overflow });
      } else {
        refreshCss(this.limitHeightBody, { height: `100%`, overflow: overflow });
      }

      // 刷新样式
      const bodyStyle = { position: "absolute", top: top, width: "100%" };
      refreshCss(this.tbodyContainer, bodyStyle);
      refreshCss(this.tbodyContainer.find(".l-table-thead"), opacityStyle);

      //跑马灯
      const datas = tableHandler.datas;
      const marquee = config.marquee;
      if (marquee) {
        
        // if (marquee.headerHeight) {
        //   refreshCss(this.theadContainer, { height: marquee.headerHeight });
        // }
        
        if (!marquee.minRow) {
          marquee.minRow = 10;
        }
        if (marquee.isOpen) {
          this.tbodyContainer.find(".l-table-tbody").mouseenter(() => {
            if (!this.isContentUpdated) {
              this.isContentUpdated = true;
              // 鼠标移入，移除滚动体，重新添加一次表格
              this.tbodyContainer.css("top", top+'px')
              const tBodyHtml = tableHandler.tbodyContainer.html();
              this.tbodyContainer.find(".l-table-tbody").empty().append(tBodyHtml);
              tableHandler.refreshJump(tBodyHtml);
              clearInterval(this.marquee);
            }
          })
          this.tbodyContainer.find(".l-table-tbody").mouseleave(() => {
            this.isContentUpdated = false;
            // 鼠标移出，再添加一次表体，开启定时器
            const tBodyHtml = tableHandler.tbodyContainer.html();
            this.tbodyContainer.find(".l-table-tbody").append(tBodyHtml);
            this.refreshEvent(datas);
            tableHandler.refreshJump(tBodyHtml);

            this.refreshMarquee(marquee.speed);
          })
          if (datas && datas.length && (datas.length > marquee.minRow)) {
            //处理数据列
            const tBodyHtml = tableHandler.tbodyContainer.html();
            this.tbodyContainer.find(".l-table-tbody").append(tBodyHtml);
            this.refreshMarquee(marquee.speed);
            this.refreshEvent(datas);
            tableHandler.refreshJump(tBodyHtml);
          }
        } else {
          // 关闭跑马灯把主体清空，再把第一个容器显示出来
          this.tbodyContainer.empty();
          this.theadContainer.find('.l-table-tbody').css('opacity', '1')
          if (this.marquee) {
            clearInterval(this.marquee);
          }
        }
      }

    }
  }
  /**
   * 描述:刷新绑定事件信息
   */
  refreshEvent (bindData) {
    if (this.tbodyContainer && this.config && this.config.events) {
      const self = this;
      const tBodySelector = this.tbodyContainer.find(".l-table-tbody");
      tBodySelector.find(".l-table-tr").each(function (index, rowElement) {
        let dataItem;
        const bindDataIndex = $(rowElement).attr("row-num");
        if (bindData && bindData[bindDataIndex]) {
          dataItem = bindData[bindDataIndex];
        }
        if (self.config.events.rowClick) {
          $(rowElement).click(function () {
            self.config.events.rowClick(dataItem);
          })
        }
        if (self.config.events.rowDbClick) {
          $(rowElement).dblclick(function () {
            self.config.events.rowDbClick(dataItem);
          })
        }
      });
    }
  }

  /**
   * 描述：开启上滚
   */
  refreshMarquee (dept) {
    let top = 0;
    const speed = 100;
    const self = this;
    let outerHeight;
    if (!dept) {
      dept = 5;
    }
    const topScoll = function () {
      const theadHeight = self.getTHeaderHeight();
      refreshCss(self.theadContainer, { height: theadHeight });
      outerHeight = (self.getTBodyHeight() - (dept * 2));
      // if(!outerHeight){
      // const theadHeight = self.getTHeaderHeight();
      // refreshCss(self.theadContainer,{height:theadHeight,overflow:"hidden"});
      // outerHeight=(self.getTBodyHeight()-(dept*2));//(Math.round(self.getTBodyHeight()/2)-2);
      /*console.info("标题高度为："+theadHeight,"数据高度:"+outerHeight);*/
      // }
      if (top <= -outerHeight) {
        if (self.config.theadStyles && self.config.theadStyles.isShow) {
          top = -theadHeight;
        } else {
          top = 0;
        }
      } else {
        top -= dept;
      }
      refreshCss(self.tbodyContainer, { "top": top });
    }
    // this.tbodyContainer.find(".l-table-tbody").hover(function (){
    //     clearInterval(self.marquee);
    // },function (){
    //     clearInterval(self.marquee);
    //     self.marquee = setInterval(topScoll,speed);
    // })
    if (this.marquee) {
      clearInterval(this.marquee);
    }
    this.marquee = setInterval(topScoll, speed);
  }

  /**
   * 描述:获取tbody的高度
   */
  getTHeaderHeight () {
    if (this.theadContainer) {
      let height = 0;
      const tableThListContainer = this.theadContainer.find(".l-table-thead .l-table-tr");
      if (tableThListContainer && tableThListContainer.length) {
        for (let i = 0; i < tableThListContainer.length; i++) {
          const tableThContainer = tableThListContainer[i];
          height += tableThContainer.offsetHeight;
        }
      }
      return height || (this.config.theadStyles && this.config.theadStyles.height) || 40;
    }
  }
  /**
   * 描述:获取tbody的高度
   */
  getTBodyHeight () {
    if (this.tbodyContainer) {
      let height = 0;
      const tableTrListContainer = this.tbodyContainer.find(".l-table-tbody .l-table-tr");
      if (tableTrListContainer && tableTrListContainer.length) {
        for (let i = 0; i < (tableTrListContainer.length / 2); i++) {
          const tableTrContainer = tableTrListContainer[i];
          height += tableTrContainer.offsetHeight;
        }
      }
      return height;
    }
  }
  /**
   * 获取透明样式
   * @returns {{}}
   */
  getOpacityStyle () {
    return {
      "opacity": 0,
      "-ms-opacity": 0,
      "-moz-opacity": 0,
      "-webkit-opacity": 0,
      "-o-opacity": 0
    }
  }
}