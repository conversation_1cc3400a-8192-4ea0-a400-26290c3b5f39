import TableNewHelper from "./TableHelperNew";

export default class TableMarqueeNew {
  constructor(context, config) {
    this.context = context;
    this.config = config;
    this.tableHelper = new TableNewHelper(context, config);
    this.timer = null;

  }
  /**
   * 描述: 初始化面板
   * @param parentContainer
   */
  initPanel(parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="marquee-wrap" style="position: relative;width:100%;height:100%;overflow:hidden;"></div>`);
      parentContainer.append(this.container);
      this.container.append(this.tableHelper.table)
    }
  }

  /**
   * 刷新绑定数据
   * @param bindData 绑定数据
   */
  refreshTable(bindData) {
    if (this.tableHelper) {
      this.tableHelper.refreshPanel(bindData)
    }
  }

  /**
   * 刷新配置
   */
  refresh() {
    if (this.tableHelper) {
      this.tableHelper.refreshStyle()
      this.marquee(this.config)
    }
  }

  /**
   * 执行跑马灯
   * @param config 表格配置
   */
  marquee(config) {
    if (config) {
      const table = this.tableHelper.table;

      // 是否开启跑马灯
      const isOpenMarquee = config.marquee && config.marquee.isOpen;
      // 是否开启滚动
      const isOpenScroll = config.overflow && config.overflow.isOpen;

      // 获取表格主体
      const tableBody = table.find('.l-table-body');

      // 先停止定时器
      this.stop();
      tableBody.off('mouseenter');
      tableBody.off('mouseleave');

      if (isOpenMarquee && isOpenScroll) {

        // 鼠标移入
        tableBody.on('mouseenter', () => {
          this.resetTableBody(tableBody);
          this.stop();
        })
        // 鼠标移出
        tableBody.on('mouseleave', () => {
          this.appendTableBody(tableBody);
          this.start(config, tableBody);
        })

        // 复制表格
        this.appendTableBody(tableBody)
        // 开启定时器
        this.start(config, tableBody);

      } else {
        // 重置表格表体
        this.resetTableBody(tableBody);
      }
      // 刷新表格样式
      this.refreshStyle(table, config);
    }
  }

  /**
   * 复制表格表体
   * @param tableBody 元素
   */
  appendTableBody(tableBody) {
    if (tableBody &&
      tableBody.length &&
      Math.ceil(tableBody.children().length / 3) < this.tableHelper.datas.length)
    {
      const html = tableBody.html();
      // 复制两份，防止表格内容高度小于滚动主体高度时，滚动条无法滚动
      tableBody.append(html).append(html)
    }
  }

  /**
   * 重置表格表体
   * @param tableBody 元素
   */
  resetTableBody(tableBody) {
    if (tableBody && tableBody.length) {
      // 清除复制的表格主体
      for (let i = tableBody.children().length; i > this.tableHelper.datas.length - 1; i--) {
        tableBody.children().eq(i).remove();
      }
    }
  }

  start(config, tableBody) {
    if (tableBody && tableBody.length) {
      this.timer = setInterval(() => {
        // 滚动主体
        const el = tableBody.get(0);
        // 滚动主体高度
        const elScrollHeight = el.scrollHeight;
        // 滚动主体高度 - 滚动条高度 <= 1/3 * 滚动主体高度
        if (elScrollHeight - el.scrollTop <= Math.abs(Math.ceil(elScrollHeight / 3))) {
          // 滚动条置顶
          el.scrollTop = 0;
        } else {
          // 滚动条向滚动
          el.scrollTop += 2;
        }
      }, config.marquee.speed || 10)
    }
  }

  stop() {
    clearInterval(this.timer);
  }

  refreshStyle(table, config) {
    if (table) {
      const body = table.find('.l-table-body')

      // 是否开启滚动
      const isOpenScroll = config.overflow && config.overflow.isOpen;
      // 是否显示表头
      const isShowHeader = config.theadStyles && config.theadStyles.isShow;
      // 是否显示滚动条
      const isOverflowVisible = config.overflow && config.overflow.isShow;

      if (isOpenScroll) {
        body.css('overflow', 'auto');
      } else {
        body.css('overflow', 'unset');
      }

      if (isShowHeader) {
        table.find('.l-table-header').show();
      } else {
        table.find('.l-table-header').hide();
      }

      const toggleScrollBar = (element, className, isVisible) => {
        if (isVisible && !element.hasClass(className)) {
          element.addClass(className);
        } else if (!isVisible && element.hasClass(className)) {
          element.removeClass(className);
        }
      };
      toggleScrollBar(body, 'overflow-transparent', !isOverflowVisible);
    }
  }
}