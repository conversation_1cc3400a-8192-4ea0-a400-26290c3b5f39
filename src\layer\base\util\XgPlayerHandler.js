// import Player from 'xgplayer';
// import HlsPlayer from "xgplayer-hls";
// import FlvPlayer from "xgplayer-flv";
// import DashPlayer from "xgplayer-dash";
// import 'xgplayer/dist/index.min.css';
/**
 * 媒体类:视频播放工具
 * 基于jplayer开发的视频播放软件
 */
export default class XgPlayerHandler {
  constructor(container, property, readyCallback) {
    this.initVideo(container, property, readyCallback);
  }
  /**
   * 描述:初始化
   * https://blog.csdn.net/fuyujiaof/article/details/70312619
   * https://blog.csdn.net/fuyujiaof/article/details/70312619
   * https://blog.csdn.net/m0_37346206/article/details/123051848
   * https://blog.csdn.net/flqxing/article/details/78221585
   * http://www.360doc.com/content/16/1126/15/33651124_609683052.shtml
   */
  initVideo (container, property,readyCallback) {
    const self = this;
    this.container = container;
    this.property = property;
    if(!container || !container[0])
      console.error("初始化失败，容器未找到");
    $(document).ready(function (events) {
    
      const config = self.buildConfig({el:container[0]})
      self.player = new Player(config);
      self.config = config;
    });
  }

  /**
   * 描述:构建配置
   * @param {*} newConfig 
   */
  buildConfig(newConfig) {
    let config = {};
    if (this.property) 
      for (let key in this.property) 
        config[key] = this.property[key];

    if(newConfig)
      for (let key in newConfig) 
        config[key] = newConfig[key];

    // if(this.property.type && this.property.type == "HLS")
    //   config.plugins = [HlsPlayer]
    // if(this.property.type && this.property.type == "FLV")
    //   config.plugins = [FlvPlayer]
    // if(this.property.type && this.property.type == "DASH")
    //   config.plugins = [DashPlayer]

    if(this.property.type && this.property.type == "HLS")
      config.plugins = [window.HlsPlayer]
    if(this.property.type && this.property.type == "FLV")
      config.plugins = [window.FlvPlayer]
    if(this.property.type && this.property.type == "DASH")
      config.plugins = [window.DashPlayer]
    
    console.info("直播视频配置信息:",config);
    return config;
  }

  /**
   * 描述:刷新
   */
  refreshVideo (newConfig) {
    //刷新属性
    const config = this.buildConfig(newConfig);
    if(this.player && this.player.setConfig){
      this.player.setConfig(config);
      this.config=config;
      this.player.reload();
    }else{
      console.error("刷新失败，播放器未初始化");
    }
  }
  /**
   * 
   */
  pause () {
    if(!this.player || !this.player.pause)
      console.error("暂停失败，播放器未初始化");
    this.player.pause().then(() => {
      // 播放成功
    }).catch(() => {
      // 播放失败，一般发生于未经用户交互时的自动播放
    })
  }
  /**
   * 
   */
  play () {
    if(!this.player || !this.player.play)
      console.error("暂停失败，播放器未初始化");
    this.player.play().then(() => {
      // 播放成功
    }).catch(() => {
      // 播放失败，一般发生于未经用户交互时的自动播放
    })
  }
}