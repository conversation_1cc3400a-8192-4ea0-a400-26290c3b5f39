import {getYsToken, setYsToken} from "../../../utils/StorageUtil";
import YsTokenService from "../../../service/YsTokenService";

/**
 * 萤石云播放器
 */
export default class YsPlayerHandler {
  constructor(context, container, property, readyCallback) {
    this.service = new YsTokenService(context);
    this.tryCount = 0;
    this.tryMaxCount = 10;
    this.timer = null;
    this.initVideo(container, property, readyCallback);
  }
  /**
   * 描述:初始化
   */
  initVideo (container, property, readyCallback) {
    this.container = container;
    this.property = property;
    if(!container || !container[0]) {
      console.error("初始化失败，容器未找到");
    } else {
      this.syncYsToken(() => {
        this.refreshVideo(property);
        readyCallback && readyCallback();
      })
    }
  }

  /**
   * 描述:构建配置
   * @param {*} newConfig 
   */
  buildConfig(newConfig) {
    let config = {};
    if (this.property) 
      for (let key in this.property) 
        config[key] = this.property[key];

    if(newConfig)
      for (let key in newConfig) 
        config[key] = newConfig[key];

    return config;
  }

  /**
   * 描述:刷新
   */
  refreshVideo (newConfig) {
    const config = this.buildConfig(newConfig);
    const accessToken = getYsToken();
    // boolean转数字
    const audio = Number(Boolean(config.audio));

    // pathname = 当前网站 /designer/lsd-designer/designer.html?id=6538c0e8-8e08-43f6-b0a9-5ef10f03cf13
    // staticPath = 取得 /designer/lsd-designer/ + /dist/ezuikit/ezuikit_static
    // 这里的静态资源如果不放在服务器，就会去萤石云公网服务器取
    const pathname = window.location.pathname;
    const staticPath = pathname.substring(0, pathname.lastIndexOf("/") + 1) + "dist/ezuikit/ezuikit_static";

    const conf = Object.assign(config, {
      id: this.container.attr('id'),
      accessToken,
      audio,
      staticPath,
      handleSuccess: () => {
        console.log("播放成功")
      },
      handleError: (e) => {
        console.log(`播放失败：${JSON.stringify(e)}`)
      } });
    clearTimeout(this.timer)
    this.timer = setTimeout(() => {
      this.player = new EZUIKit.EZUIKitPlayer(conf);
    }, 2000)
  }
  play () {
    if(!this.player || !this.player.play || !this.player.stop) {
      console.error("播放失败，播放器未初始化");
    }
    this.player.stop().then(() => {
      this.player.play()
    })
  }
  stop () {
    if(!this.player || !this.player.stop)
      console.error("暂停失败，播放器未初始化");
    this.player.stop().then(() => {
      // 播放成功
    }).catch(() => {
      // 播放失败，一般发生于未经用户交互时的自动播放
    })
  }

  /**
   * 同步萤石云token
   * @param callback 完成的回调
   */
  syncYsToken(callback) {
    const token = getYsToken();
    if (token) {
      callback()
    } else {
      this.service.getToken((data, status) => {
        if (data.code === 0) {
          setYsToken(data.accessToken)
          callback()
        } else {
          if (++this.tryCount >= this.tryMaxCount) {
            console.error(`获取萤石云token异常:${data.msg}`)
          } else {
            setTimeout(this.syncYsToken.bind(this, callback), 3000);
          }
        }
      }, (err) => {
        if (++this.tryCount >= this.tryMaxCount) {
          console.error(`获取萤石云token异常:${JSON.stringify(err)}`)
        } else {
          setTimeout(this.syncYsToken.bind(this, callback), 3000);
        }
      })
    }
  }
}