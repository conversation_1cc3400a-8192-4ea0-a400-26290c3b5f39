.layer-wrap {
  position: absolute;
  outline: none;
}

/* .layer-wrap:hover .layer-tool {
  display: block
} */

.layer-tool {
  width: 60px;
  height: 30px;
  background: rgba(43, 51, 64, 0.8);
  border-radius: 2px;
  color: #ccc;
  z-index: 2000;
  position: absolute;
  top: 0;
  right: 0;
  text-align: center;
  /* display: none; */
  cursor: pointer
}

.layer-tool .icon {
  line-height: 30px;
  font-size: 25px;
}

.layer-mask {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1801
}

.layer-resize-tag {
  width: 8px;
  height: 8px;
  position: absolute;
  background-color: #B6BFCE;
  z-index: 2000;
  border-radius: 50%;
}

.layer-selected {
  border: 1px solid #B6BFCE
}