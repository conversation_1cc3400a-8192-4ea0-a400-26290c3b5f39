import Layer from '../Layer';
import { refreshOption } from "./OptionUtil";
export default class EchartsLayer extends Layer {
  constructor(context) {
    super(context);
    this.echarts = context.echarts;
    // this.echarts = require("echarts");
    // require("echarts-gl");
    // require("echarts-wordcloud");
    // require("echarts-liquidfill");
  }
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.echartsContainer = $(`<div class="layer-echarts"></div>`);
      panelContainer.append(this.echartsContainer);
      if (this.echarts) {
        this.myChart = this.echarts.init(this.echartsContainer[0], null, { renderer: 'svg' });
      }
    }
  };
  refreshCompCss () {
    if (this.echartsContainer) {
      const style = {};
      this.refreshWH(style);
      $(this.echartsContainer).css(style);
    }
    if (this.myChart) {
      //清理
      this.myChart.clear();
      //刷新Option
      if (this.option) {
        //刷新Option属性
        if (this.property && this.property["chart"]) {
          // if(this.property.type==="GaugeChartLayer"){
          //   this.property["chart"]={};
          // }
          refreshOption(this.context, this.option, this.property["chart"]);
        }
        //清理
        if (this.option.color || this.option.backgroundColor) {
          const clearOption = { color: 'transparent', backgroundColor: 'transparent', };
          this.myChart.setOption(clearOption);// 其他配置...
        }
        // 开启动画效果
        // option.animation = true;
      }
      //自适应大小
      this.myChart.resize();
      // 是否地图
      if (this.option.geo) {
        // 地图是否注册成功
        if (this.isRegister) {
          //设置option
          this.myChart.setOption(this.option);
        }
      } else {
        //设置option
        this.myChart.setOption(this.option);
      }

    }
  };
  refreshBind () {
    //注册地图
    if (this.echarts) {
      this.registerMap(this.echarts);
    }
    //刷新option[数据]-得到option
    this.option = this.refreshOption(this.bindData);
  };
  registerMap (echarts) {
    // console.error("地图必须实现registerMap");
  }
  initOption () {
    console.error("图层必须实现initOption");
  }
  refreshOption (datas) {
    console.error("图层必须实现parseOption");
  }
  /**
   * 描述:初始化事件
   * @param {*} callback 
   * @param {*} dblclick 
   */
  initCompEvent () {
    if (this.myChart) {
      // this.myChart.off('click');
      // this.myChart.off('dblclick');
      let timeFun;
      const self = this;
      // 绑定单击事件
      this.myChart.on('click', function (params) {
        //清除延迟
        if (timeFun) {
          clearTimeout(timeFun);
        }
        //由于单击事件和双击事件冲突，故单击的响应事件延迟250毫秒执行
        timeFun = setTimeout(function () {
          self.clickEvent(params);
        }, 250);
      });
      // 绑定双击事件
      this.myChart.on('dblclick', function (params) {
        //当双击事件发生时，清除单击事件，仅响应双击事件
        //清除延迟
        if (timeFun) {
          clearTimeout(timeFun);
        }
        self.dblclickEvent(params);
      });
    }
  }
  clickEvent (params) {
    // console.info("单击:", params);
  }
  dblclickEvent (params) {
    // console.info("双击:", params);
  }
  /**
   * 
   * @param {*} datas 
   * @param {*} columns 
   * @returns 
   */
  parseGroupMap (datas, columns) {
    let groupMap;
    if (datas && datas.length && columns && columns.length) {
      for (let i = 0; i < datas.length; i++) {
        const data = datas[i];
        const key = this.getGroupKey(columns, data);
        if (!groupMap) {
          groupMap = {};
        }
        if (!groupMap[key]) {
          groupMap[key] = [];
        }
        groupMap[key].push(data);
      }
    }
    return groupMap;
  }
  getGroupKey (columns, data) {
    let key = "";
    let isFirst = true;
    if (columns && columns.length) {
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        if (data && data[column]) {
          if (isFirst) {
            isFirst = false;
            key = data[column];
          } else {
            key = key + "_" + data[column];
          }

        } else {
          if (isFirst) {
            isFirst = false;
            key = column;
          } else {
            key = key + "_" + column;
          }
        }
      }
    }
    return key;
  }
}