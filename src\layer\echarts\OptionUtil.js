import { isArray, isEmpty, isEmptyObj, isFun, isNum, isObj, isStr, listToArray, } from '../../utils/Util';

/**
 * 解析
 * loads: ["title", "toolbox", "tooltip", "legend", "textStyle", "grid", "xAxis", "yAxis", "animate"],
 * @param context
 * @param option
 * @param {*} chart
 */
export function refreshOption (context, option, chart) {
  if (option && chart) {
    const json = {};
    $.extend(true, json, chart);
    parseJson(json, option);

    for (let key in json) {
      if (key === 'color') {//颜色
        option[key] = json[key];
      }
      if (key === 'backgroundColor') {//背景
        option[key] = json[key];
      }
      if (key === 'xAxisList') {

        const styles = json[key];
        const list = option['xAxis'];
        refreshList(list, styles);

      } else if (key === 'yAxisList') {

        const styles = json[key];
        const list = option['yAxis'];
        refreshList(list, styles);

      } else if (key === 'lineVal') {//自定义-折柱混合(设置类型)

        const vals = chart[key];
        const list = option['series'];
        const array = listToArray(vals, 'value');
        refreshLineBar(list, array);

      } else if (key === 'barPictorSeries') {//自定义序列-柱状象形混合

        const styles = json[key];
        const list = option['series'];
        refreshList(list, styles, 'barPictor');

      } else if (key === 'serie') {//序列相同(单)

        const style = json[key];
        const item = option['series'][0];
        refreshItem(item, style);

      } else if (key === 'series') {//序列相同

        const styles = json[key];
        const list = option['series'];
        refreshList(list, styles);

      } else if (key === 'barSeries') {//序列不同 柱状

        const styles = json[key];
        const list = option['series'];
        refreshList(list, styles);

      } else if (key === 'lineSeries') {//序列不同 折线

        const styles = json[key];
        const list = option['series'];
        refreshList(list, styles);

      } else if (key === 'pictorialBarSeries') {//序列不同 象形柱

        const styles = json[key];
        const list = option['series'];
        refreshList(list, styles);

      } else if (key === 'mapSeries') {//序列不同 地图

        const styles = json[key];
        const list = option['series'];
        refreshList(list, styles);

      } else if (key === 'heatmapSeries') {//序列不同 热力

        const styles = json[key];
        const list = option['series'];
        refreshList(list, styles);

      } else if (key === 'scatterSeries') {//序列不同 散点

        const styles = json[key];
        const list = option['series'];
        refreshList(list, styles);

      } else if (key === 'linesSeries') {//序列不同 路径

        const styles = json[key];
        const list = option['series'];
        refreshList(list, styles);

      } else if (key === 'effectScatterSeries') {//序列不同 涟漪散点

        const styles = json[key];
        const list = option['series'];
        refreshList(list, styles);
      } else if (key === 'geo') {//序列不同 地图

        const geo = json[key];
        if (geo.itemStyle) {
          // 处理背景
          if (geo.itemStyle.type === 'image' && geo.itemStyle.geoImage.id) {
            const image = context.getMaterialById(geo.itemStyle.geoImage.id);
            geo.itemStyle.color = {
              image: image.getUrl()
            }
          } else if(geo.itemStyle.type === 'color' && geo.itemStyle.geoColor) {
            geo.itemStyle.color = geo.itemStyle.geoColor;
          }
        }
        refreshItem(option[key], json[key]);
      } else {
        //映射
        if (!option[key]) {
          option[key] = {};
        }
        refreshItem(option[key], json[key]);
      }
    }
  }
}

export function parseJson (json, option) {
  if (json && isObj(json) && !isArray(json)) {
    for (let key in json) {
      const val = json[key];
      //转小写
      let lowerKey = key.toLowerCase();
      //判断是否是颜色属性
      if (lowerKey.indexOf('color') != -1) {
        // json[key]=parseColor(val);
        // continue;
        const exprColor = parseColor(val);
        if (exprColor) {
          json[key] = exprColor;
          continue;
        }
      }
      //如果为空
      if (isEmpty(val)) {
        delete json[key];
        continue;
      }
      //字符串
      if (isStr(val)) {
        //数字
        if (isNum(val)) {
          json[key] = Number(val);
        }
        //函数
        if (isFun(val)) {
          // json[key] = eval(val) || new Function(val);
          if (lowerKey.indexOf('formatter') != -1) {
            json[key] = parseformatterFun(val, option);
          } else {
            json[key] = parseFunction(val);
          }
        }
        //逗号
        if (val.indexOf(',') != -1 && lowerKey.indexOf('color') == -1) {
          let list = [];
          const array = val.split(',');
          if (array && array.length) {
            for (let i = 0; i < array.length; i++) {
              const arr = array[i];
              let value;
              if (isNum(arr)) {
                value = Number(arr);
              } else {
                value = arr;
              }
              list.push(value);
            }
          }
          json[key] = list;
        }
        continue;
      }
      //对象
      if (isObj(val)) {
        if (!isEmptyObj(val)) {
          //不为空
          if (isArray(val)) {
            //数组
            const array = [];
            const list = val;
            for (let i = 0; i < list.length; i++) {
              const item = list[i];
              if (isObj(item)) {
                parseJson(item, option);
                if (!isEmptyObj(item)) {
                  array.push(item);
                }
              } else {
                array.push(item);
              }
            }
            if (!isEmptyObj(array)) {
              json[key] = array;
            } else {
              delete json[key];
            }

          } else {
            parseJson(val, option);
          }

        } else {
          //为空
          delete json[key];
        }

      }
    }
  }
}
function parseFunction (fnStr) {
  let fn;
  try {
    fn = eval("(false || " + fnStr + ")");
  } catch (err) {
    // console.error("解析函数失败![" + fnStr + "]");
  }
  return fn;
}
function parseformatterFun (fnStr, option) {
  //函数
  const formatterFun = parseFunction(fnStr);
  return function (params) {
    // console.info("params:", params);
    if (formatterFun) {
      const data = parseSeriesData(option);
      return formatterFun(params, data);
    }
  }
}

function parseSeriesData (option) {
  let list;
  if (option) {
    const series = option.series;
    if (series && series.length) {
      for (let i = 0; i < series.length; i++) {
        const serie = series[i];
        let namekey = "名称";
        if (serie.name) {
          namekey = serie.name;
        }
        const item = { name: namekey, data: serie };
        if (!list) {
          list = [];
        }
        list.push(item);
      }
    }
  }
  return list;
}
function parseColor (style) {
  let color;
  if (style && style.type) {
    if (style.type === "color") {
      color = style.color;
    }
    if (style.type === "list") {
      const list = style.list;
      color = listToArray(list, 'color');
    }
    if (style.type === "gradient") {
      const grad = style.gradient;
      if (grad.type && grad.type === "linear") {
        color = {
          type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [{
            offset: 0, color: grad.color00 // 0% 处的颜色
          }, {
            offset: 1, color: grad.color01 // 100% 处的颜色
          }],
          global: false // 缺省为 false
        }
      }
      if (grad.type && grad.type === "radial") {
        color = {
          type: 'radial', x: 0.5, y: 0.5, r: 0.5,
          colorStops: [{
            offset: 0, color: 'red' // 0% 处的颜色
          }, {
            offset: 1, color: 'blue' // 100% 处的颜色
          }],
          global: false // 缺省为 false
        }
      }
    }
    if (style.type === "linear") {
      color = style.linear;
    }
    if (style.type === "radial") {
      color = style.radial;
    }
  } else {
    if (isStr(style)) {
      color = style;
    }
  }
  return color;
};
export function refreshLineBar (list, array) {
  if (list && list.length) {
    for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if (item['name'] && array.indexOf(item['name']) !== -1) {
        item['type'] = 'line';
        item['yAxisIndex'] = 1;
      } else {
        item['type'] = 'bar';
        item['yAxisIndex'] = 0;
      }
    }
  }
}

export function refreshList (list, styles, type) {
  let index = 0;
  let style;
  if (list && list.length && styles && styles.length) {
    for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if (type) {
        style = styles[i];
        refreshItem(item, style);
      } else {
        style = getStyle(styles, index);
        if (item['type'] && style['type']) {
          if (item['type'] === style['type']) {
            refreshItem(item, style);
            index++;
          }
        } else {
          refreshItem(item, style);
          index++;
        }
      }
    }
  }
}
export function refreshItem (item, style) {
  if (item && style) {
    $.extend(true, item, style);
    //处理data
    const dataList = item['data'];
    const dataStyles = style['data'];
    refreshDatas(dataList, dataStyles);
  }
}
export function refreshDatas (list, styles) {
  if (list && list.length && styles && styles.length) {
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        let style = getStyle(styles, i);
        if (!isObj(item) && isStr(item) && style) {
          list[i] = { value: item };
        }
        $.extend(true, list[i], style);
      }
    }
  }
}
export function getStyle (styles, index) {
  if (!index) {
    index = 0;
  }
  if (styles && styles.length) {
    for (let i = 0; i < styles.length; i++) {
      if (index % styles.length === i) {
        return styles[i];
      }
    }
  }
}