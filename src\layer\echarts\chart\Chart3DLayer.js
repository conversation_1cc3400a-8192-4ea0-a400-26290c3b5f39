import ChartLayer from "./ChartLayer";

export default class Chart3DLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.echartsContainer = $(`<div class="layer-echarts"></div>`);
      panelContainer.append(this.echartsContainer);
      if (this.echarts) {
        this.myChart = this.echarts.init(this.echartsContainer[0], null, { renderer: 'canvas' });
      }
    }
  };
}
