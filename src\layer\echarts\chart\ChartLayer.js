import { isEmpty, isStr, isNum } from '../../../utils/Util';
import EchartsLayer from '../EchartsLayer';
export default class ChartLayer extends EchartsLayer {
  constructor(context) {
    super(context);
  }
  registerMap (echarts) { };

  clickEvent (params) {
    //超链接
    this.clickJump(params.data);
    //联动
    this.clickRelation(params.data);
  }
  mockData () {
    return [
      { name: "小明", month: "Mon", mark: 12, year: 2001, chinaese: 50, lowest: 80, highest: 130, max: 200 },
      { name: "小明", month: "Tue", mark: 20, year: 2001, chinaese: 67, lowest: 80, highest: 130, max: 200 },
      { name: "小明", month: "Wed", mark: 15, year: 2001, chinaese: 34, lowest: 80, highest: 130, max: 200 },
      { name: "小明", month: "Thu", mark: 80, year: 2001, chinaese: 58, lowest: 80, highest: 130, max: 200 },
      { name: "小明", month: "Fri", mark: 70, year: 2001, chinaese: 36, lowest: 80, highest: 130, max: 200 },
      { name: "小明", month: "Sat", mark: 11, year: 2001, chinaese: 25, lowest: 80, highest: 130, max: 200 },
      { name: "小明", month: "Sun", mark: 13, year: 2001, chinaese: 15, lowest: 80, highest: 130, max: 200 },

      { name: "小明", month: "Mon", mark: 12, year: 2002, chinaese: 130, lowest: 80, highest: 130, max: 200 },
      { name: "小明", month: "Tue", mark: 20, year: 2002, chinaese: 130, lowest: 80, highest: 130, max: 200 },
      { name: "小明", month: "Wed", mark: 15, year: 2002, chinaese: 130, lowest: 80, highest: 130, max: 200 },
      { name: "小明", month: "Thu", mark: 80, year: 2002, chinaese: 130, lowest: 80, highest: 130, max: 200 },
      { name: "小明", month: "Fri", mark: 70, year: 2002, chinaese: 130, lowest: 80, highest: 130, max: 200 },
      { name: "小明", month: "Sat", mark: 10, year: 2002, chinaese: 130, lowest: 80, highest: 130, max: 200 },
      { name: "小明", month: "Sun", mark: 13, year: 2002, chinaese: 130, lowest: 80, highest: 130, max: 200 },

      // { name: "小明", month: "Mon", mark: 120, year: 2003 },
      // { name: "小明", month: "Tue", mark: 200, year: 2003 },
      // { name: "小明", month: "Wed", mark: 150, year: 2003 },
      // { name: "小明", month: "Thu", mark: 80, year: 2003 },
      // { name: "小明", month: "Fri", mark: 70, year: 2003 },
      // { name: "小明", month: "Sat", mark: 110, year: 2003 },
      // { name: "小明", month: "Sun", mark: 130, year: 2003 },

      { name: "小红", month: "Mon", mark: 3, year: 2001, chinaese: 50 },
      { name: "小红", month: "Tue", mark: 5, year: 2001, chinaese: 67 },
      { name: "小红", month: "Wed", mark: 7, year: 2001, chinaese: 34 },
      { name: "小红", month: "Thu", mark: 8, year: 2001, chinaese: 58 },
      { name: "小红", month: "Fri", mark: 7, year: 2001, chinaese: 36 },
      { name: "小红", month: "Sat", mark: 11, year: 2001, chinaese: 25 },
      { name: "小红", month: "Sun", mark: 13, year: 2001, chinaese: 15 },
    ];
  }

  lines () {
    const resultlist = this.loadJson("./geo/hangzhou-tracks.json");
    const data = resultlist.map(function (track) {
      return {
        coords: track.map(function (seg, idx) {
          return seg.coord;
        })
      };
    });
    return data;
  }
  
  /**
   * 描述:获取json数据
   * @param url
   */
  loadJson (url) {
    let result;
    $.ajaxSettings.async = false;
    $.getJSON(url, function (data) {
      if (data) {
        result = data;
      }
    });
    $.ajaxSettings.async = true;
    return result;
  }

  /**
   * 解析-获取分组
   * @param {*} datas 数据list
   * @param {*} columns ["group","dimension"]
   * @returns {"key_小明_Mon":[
   * { name: "小明", month: "Mon", mark: 120, year: 2001 },
   * { name: "小明", month: "Mon", mark: 120, year: 2002 },
   * { name: "小明", month: "Mon", mark: 120, year: 2003 },
   * ]}
   */
  // parseGroupMap (datas, columns) {
  //   let groupMap;
  //   if (datas && datas.length && columns && columns.length) {
  //     for (let i = 0; i < datas.length; i++) {
  //       const data = datas[i];
  //       const key = this.getGroupKey(columns, data);
  //       if (!groupMap) {
  //         groupMap = {};
  //       }
  //       if (!groupMap[key]) {
  //         groupMap[key] = [];
  //       }
  //       groupMap[key].push(data);
  //     }
  //   }
  //   return groupMap;
  // }
  // getGroupKey (columns, data) {
  //   let key = "";
  //   let isFirst = true;
  //   if (columns && columns.length) {
  //     for (let i = 0; i < columns.length; i++) {
  //       const column = columns[i];
  //       if (data && data[column]) {
  //         if (isFirst) {
  //           isFirst = false;
  //           key = data[column];
  //         }else{
  //           key = key + "_" + data[column];
  //         }

  //       } else {
  //         if (isFirst) {
  //           isFirst = false;
  //           key = column;
  //         }else{
  //           key = key + "_" + column;
  //         }
  //       }
  //     }
  //   }
  //   return key;
  // }
  /**
   * 根据字段获取 数据值
   * @param {*} list 
   * @param {*} column 
   * @param {*} isObj 返回的是否是对象 true(对象) false (值)
   * @returns 
   */
  getDataValue (list, column) {
    let value;
    if (column) {
      value = 0;
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          if (column && (item[column] || item[column] === "0" || item[column] === 0)) {
            if (isNum(item[column])) {
              value = value + Number(item[column])
            } else {
              value = value + "," + item[column];
            }
          }
        }
      }
    } else {
      value = {};
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          for (let key in item) {
            if (item[key] || item[key] === "0" || item[key] === 0) {

              if (isNum(item[key])) {
                if (!value[key]) {
                  value[key] = Number(item[key]);
                } else {
                  if (key == "year" || key == "month" || key == "day") {
                    value[key] = value[key] + "," + item[key];
                  } else {
                    value[key] = value[key] + Number(item[key]);
                  }
                }
              } else if (isStr(item[key])) {
                if (!value[key]) {
                  value[key] = item[key];
                } else {
                  if (value[key].indexOf(item[key]) === -1) {
                    value[key] = value[key] + "," + item[key];
                  }
                }
              }

            }
          }

        }
      }
    }
    return value;
  }
  getValue (data, column) {
    if (column && (data[column] || data[column] === 0 || data[column] === "0")) {
      // data["value"]=data[column];
      return data[column];
    }
  }
}
