import Chart3DLayer from "../Chart3DLayer";

export default class Bar3<PERSON>hartLayer extends Chart3DLayer {
  constructor(context) {
    super(context);
  }

  getDefaultProperty() {
    return {
      name: "Echarts图表",
      type: "Bar3DChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "grid3D", "visualMap", "xAxis3D", "yAxis3D", "zAxis3D", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          {key: "group", value: "${name}", desc: "分组"},//序列
          {key: "dimensionX", value: "${month}", desc: "维度x"},//x轴数据
          {key: "dimensionY", value: "${year}", desc: "维度y"},//x轴数据
          {key: "value", value: "${mark}", desc: "数值"},//序列数据
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };

  initOption() {
    const option = {
      xAxis3D: {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      },
      yAxis3D: {
        type: 'category',
        data: [0,1,2,3,4,5,6,7,8,9,10,11,12]
      },
      zAxis3D: {
        type: 'value',
      },
      visualMap: {
        show: false,
        inRange: {
          color: [
            // {color:'#5470c6'}, {color:'#91cc75'}, {color:'#fac858'},  {color:'#ee6666'}
            '#313695',
            '#4575b4',
            '#74add1',
            '#abd9e9',
            '#e0f3f8',
            '#ffffbf',
            '#fee090',
            '#fdae61',
            '#f46d43',
            '#d73027',
            '#a50026'
          ]
        }
      },
      grid3D: {
        show: false,
        boxWidth: 200,
        boxDepth: 80,
        viewControl: {
          projection: 'perspective',
          // 默认视角距离主体的距离，对于 globe 来说是距离地球表面的距离，
          // 对于 grid3D 和 geo3D 等其它组件来说是距离中心原点的距离。
          // 在 projection 为'perspective'的时候有效。
          distance: 250,
        },
      },
      series: [
        {
          type: 'bar3D',
          name: '系列1',
          data: [{value: [0,1,1]},{value: [1,4,10]},{value: [2,3,8]},{value: [3,5,20]},{value: [4,2,15]},{value: [5,8,14]},{value: [6,11,25]}],
          shading: 'lambert',
        },
      ],
    };
    return option;
  }

  refreshOption(datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //分组-数据
      const dataMap = this.parseGroupMap(datas, ["group", "dimensionX", "dimensionY"]);
      //分组-分组
      const groupMap = this.parseGroupMap(datas, ["group"]);
      //分组-维度x
      const dimXMap = this.parseGroupMap(datas, ["dimensionX"]);
      //分组-维度y
      const dimYMap = this.parseGroupMap(datas, ["dimensionY"]);
      //序列
      let _series;
      const _yAxis3D = {type: 'category', data: []};
      const _xAxis3D = {type: 'category', data: []};
      if (dimXMap) {
        for (let dimName in dimXMap) {
          _xAxis3D.data.push(dimName);
        }
      }
      if (dimYMap) {
        for (let dimName in dimYMap) {
          _yAxis3D.data.push(dimName);
        }
      }
      //处理
      if (groupMap && dimXMap && dimYMap) {
        for (let groupName in groupMap) {
          const _serie = {name: groupName, type: "bar3D", data: [], shading: 'lambert'};
          if (!_series) {
            _series = [];
          }
          _series.push(_serie);

          for (let dimNameX in dimXMap) {
            for (let dimNameY in dimYMap) {
              const key = this.getGroupKey([groupName, dimNameX, dimNameY]);
              const list = dataMap[key];
              const dataVal = list ? this.getDataValue(list, "value") : 0;
              // [x, y, z]
              _serie.data.push([dimNameX, dimNameY, dataVal]);
            }
          }
        }
      }
      //x轴
      if (_xAxis3D) {
        option["xAxis3D"] = _xAxis3D;
      }
      if (_yAxis3D) {
        option["yAxis3D"] = _yAxis3D;
      }
      //序列
      if (_series) {
        option["series"] = _series;
      }
    }
    return option;
  }
}