import { isNum, isInterval } from "../../../../utils/Util";
import ChartLayer from "../ChartLayer";
export default class BarDoughnutChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "环形柱状图",
      type: "BarDoughnutChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "polar", "angleAxis", "radiusAxis", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          // { key: "group", value: "${name}", desc: "分组" },//序列
          // { key: "dimension", value: "${month}", desc: "维度" },//x轴数据
          { key: "value", value: "${mark}", desc: "数值" },//序列数据
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      polar: {
        radius: ['44%', '60%'],
        center: ['50%', '50%'],
      },
      angleAxis: {
        max: 100,
        show: false,
      },
      radiusAxis: {
        type: 'category',
        show: false,
      },
      series: [
        {
          name: '小明',
          type: 'bar',
          barWidth: 200,
          data: [60],
          roundCap: true,//圆角
          coordinateSystem: 'polar',
          showBackground: true,
        },
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //序列
      const dataVal = this.getDataValue(datas);
      if (dataVal && dataVal["value"]) {
        let value = dataVal["value"];
        if (isNum(value)) {
          if (isInterval(value, 0, 10)) {
            value = parseFloat((value / 10).toFixed(2));
          } else if (isInterval(value, 10, 100)) {
            value = parseFloat((value / 100).toFixed(2));
          } else if (isInterval(value, 100, 1000)) {
            value = parseFloat((value / 1000).toFixed(2));
          } else if (isInterval(value, 1000, 10000)) {
            value = parseFloat((value / 10000).toFixed(2));
          } else if (isInterval(value, 10000, 100000)) {
            value = parseFloat((value / 100000).toFixed(2));
          }
          if (isInterval(value, 0, 1)) {
            dataVal["value"] = value * 100;
          }
        }
      }
      const data = [dataVal];
      option["series"][0]["data"] = data;
    }
    return option;
  }


}