import ChartLayer from "../ChartLayer";
export default class BarPictorYChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "Echarts图表",
      type: "EchartsLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "xAxis", "yAxis", "grid", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          // { key: "group", value: "${name}", desc: "分组" },//序列
          { key: "dimension", value: "${month}", desc: "维度" },//轴数据
          { key: "value", value: "${mark}", desc: "数值" },//序列数据
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      xAxis: {
        type: 'value',
        max: 10000,
        splitLine: { show: false },
        axisLine: { show: false },
        axisLabel: { show: false },
        axisTick: { show: false }
      },
      yAxis: {
        type: "category",
        data: ["管控", "集中式", "纳管", "纳管", "纳管"],
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: true }
      },
      series: [
        {
          name: "内容",
          type: "bar",
          barWidth: 18,
          data: [
            { name: "管控", value: 2500 },
            { name: "集中式", value: 8000 },
            { name: "纳管", value: 3000 },
            { name: "纳管", value: 3000 },
            { name: "纳管", value: 3000 },
          ],
        }, {
          name: "外框",
          type: "bar",
          barWidth: 25,
          barGap: "-120%", // 设置外框粗细
          data: [10000, 10000, 10000, 10000, 10000],
          itemStyle: {
            normal: {
              color: "transparent", // 填充色
              barBorderColor: "#1C4B8E", // 边框色
              barBorderWidth: 1, // 边框宽度
              // barBorderRadius: 0, //圆角半径
              label: {
                // 标签显示位置
                show: false,
                position: "top" // insideTop 或者横向的 insideLeft
              }
            }
          },

        }
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      let length=0;
      let maxValue = 0;
      const ydata=[];
      const sdata=[];
      //分组-维度
      const dimMap = this.parseGroupMap(datas, ["dimension"]);
      if (dimMap) {
        for (let dimName in dimMap) {
          length++;
          //值
          const list = dimMap[dimName];
          //最大值
          const value = this.getDataValue(list, "value");
          if (maxValue <= value) {
            maxValue = value;
          }
          //data-序列
          const dataVal = this.getDataValue(list);
          ydata.push(dimName);
          sdata.push(dataVal);
         
        }
        //data-轴
        option["xAxis"].max=maxValue;
        //data-轴
        option["yAxis"].data=ydata;
        option["series"][0].data=sdata;
        if(length){
          let list=[];
          for(let i=0;i<length;i++){
            list.push(maxValue);
          }
          option["series"][1].data=list;
        }
      }

    }
    return option;
  }
}