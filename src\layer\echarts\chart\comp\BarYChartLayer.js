import ChartLayer from "../ChartLayer";
export default class BarYChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "Echarts图表",
      type: "EchartsLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads:["title","tooltip","toolbox","legend","textStyle","color","xAxis","yAxis","grid","series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "group", value: "${name}", desc: "分组" },//序列
          { key: "dimension", value: "${month}", desc: "维度" },//y轴数据
          { key: "value", value: "${mark}", desc: "数值" },//序列数据
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption(){
    const option = {
      xAxis: {
        type: 'value',
      },
      yAxis: {
        type: 'category',
        data: ['Brazil', 'Indonesia', 'USA', 'India', 'China', 'World']
      },
      series: [
        {
          name: '2011',
          type: 'bar',
          data: [18203, 23489, 29034, 104970, 131744, 630230]
        },
        {
          name: '2012',
          type: 'bar',
          data: [19325, 23438, 31000, 121594, 134141, 681807]
        }
      ]
    };
    return option;
  }
  refreshOption(datas){
    const option = this.initOption();
    if (datas && datas.length) {
      //分组-数据
      const dataMap = this.parseGroupMap(datas, ["group", "dimension"]);
      //分组-分组
      const groupMap = this.parseGroupMap(datas, ["group"]);
      //分组-维度
      const dimMap = this.parseGroupMap(datas, ["dimension"]);
      //序列
      let _series;
      //y轴
      const _yAxis = { type: 'category', data: [] };
      if (dimMap) {
        for(let dimName in dimMap){
          _yAxis.data.push(dimName);
        }
      }
      //处理
      if (groupMap && dimMap) {
        for (let groupName in groupMap) {
          const _serie = {name:groupName, type: "bar", data: [] };
          if (!_series) {
            _series = [];
          }
          _series.push(_serie);
          //处理Data
          for (let dimName in dimMap) {
            //序列
            const key = this.getGroupKey([groupName, dimName]);
            if (dataMap && dataMap[key]) {
              const list = dataMap[key];
              const dataVal = this.getDataValue(list);
              //data-序列
              _serie.data.push(dataVal);
            }
          }
        }
      }
      //y轴
      if (_yAxis) {
        option["yAxis"] = _yAxis;
      }
      //序列
      if (_series) {
        option["series"] = _series;
      }
    }
    return option;
  }
}