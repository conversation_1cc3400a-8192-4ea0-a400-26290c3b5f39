
import ChartLayer from "../ChartLayer";
export default class BoxplotChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "盒须图",
      type: "BoxplotChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "xAxis", "yAxis", "grid", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          // { key: "group", value: "${name}", desc: "分组" },
          { key: "dimension", value: "${month}", desc: "维度" },//维度  
          { key: "min", value: "${mark}", desc: "最小" },//数值
          { key: "Q1", value: "${lowest}", desc: "数值" },//数值
          { key: "Q2", value: "${chinaese}", desc: "中间" },//数值
          { key: "Q3", value: "${highest}", desc: "数值" },//数值
          { key: "max", value: "${max}", desc: "最大" },//数值
        ],
        columns: ["name", "year", "month", "mark", "chinaese", "lowest", "highest", "max"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      xAxis: {
        type: 'category',
        data: ['2017-10-24', '2017-10-25', '2017-10-26', '2017-10-27']
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          type: 'boxplot',
          data: [
            [20, 34, 35, 38, 40], [30, 34, 37, 47, 50], [25, 34, 35, 38, 50], [20, 34, 35, 50, 60]
          ]
        }
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //分组-数据
      const dataMap = this.parseGroupMap(datas, ["group", "dimension"]);
      //分组-分组
      const groupMap = this.parseGroupMap(datas, ["group"]);
      //分组-维度
      const dimMap = this.parseGroupMap(datas, ["dimension"]);
      let axisData;
      let serieData;
      let series;
      if (dimMap) {
        for (let dimKey in dimMap) {
          if (!axisData) {
            axisData = [];
          }
          axisData.push(dimKey);
          //单序列
          const list = dimMap[dimKey];
          const dataVal = this.getDataValue(list);
          const min = this.getValue(dataVal, "min");
          const Q1 = this.getValue(dataVal, "Q1");
          const Q2 = this.getValue(dataVal, "Q2");
          const Q3 = this.getValue(dataVal, "Q3");
          const max = this.getValue(dataVal, "max");
          dataVal["value"] = [min, Q1, Q2, Q3, max];
          if (!serieData) {
            serieData = [];
          }
          serieData.push(dataVal);
        }
      }
      // if (groupMap && dimMap) {
      //   for (let groupKey in groupMap) {
      //     const serie = { type: 'boxplot', data: [] };
      //     if (!series) {
      //       series = [];
      //     }
      //     series.push(serie);
      //     //序列data;
      //     for (let dimKey in dimMap) {
      //       const key = this.getGroupKey([groupKey, dimKey]);
      //       if (dataMap && dataMap[key]) {
      //         const list = dataMap[key];
      //         const dataVal = this.getDataValue(list);
      //         const min = this.getValue(dataVal, "min");
      //         const Q1 = this.getValue(dataVal, "Q1");
      //         const Q2 = this.getValue(dataVal, "Q2");
      //         const Q3 = this.getValue(dataVal, "Q3");
      //         const max = this.getValue(dataVal, "max");
      //         dataVal["value"] = [min, Q1, Q2, Q3, max];
      //         serie.data.push(dataVal);
      //       }
      //     }
      //   }
      // }
      if (axisData && axisData.length) {
        option.xAxis.data = axisData;
      }
      if (serieData && serieData.length) {
        option.series[0].data = serieData;
      }
      // if (series && series.length) {
      //   option.series = series;
      // }
    }
    return option;
  }
}