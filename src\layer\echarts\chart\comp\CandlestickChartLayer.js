
import ChartLayer from "../ChartLayer";
export default class CandlestickChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "K线图",
      type: "CandlestickChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "xAxis", "yAxis", "grid", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          // { key: "group", value: "${name}", desc: "分组" },//序列
          { key: "dimension", value: "${month}", desc: "维度" },//x轴数据
          // { key: "value", value: "${mark}", desc: "数值" },//序列数据
          //[open, close, lowest, highest]
          { key: "open", value: "${mark}", desc: "开盘值" },
          { key: "close", value: "${chinaese}", desc: "收盘值" },
          { key: "lowest", value: "${lowest}", desc: "最低值" },
          { key: "highest", value: "${highest}", desc: "最高值" },
        ],
        columns: ["name", "year", "month", "mark", "chinaese", "lowest", "highest"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      xAxis: {
        data: ['2017-10-24', '2017-10-25', '2017-10-26', '2017-10-27']
      },
      yAxis: {},
      series: [
        {
          type: 'candlestick',
          data: [
            [20, 34, 10, 38],
            [40, 35, 30, 50],
            [31, 38, 33, 44],
            [38, 15, 5, 42]
          ]
        }
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      let axisData;
      let serieData;
      //分组-维度
      const dimMap = this.parseGroupMap(datas, ["dimension"]);
      if (dimMap) {
        for (let dimKey in dimMap) {
          //轴
          if (!axisData) {
            axisData = [];
          }
          axisData.push(dimKey);
          //解析
          const list = dimMap[dimKey];
          const open = this.getDataValue(list, "open");
          const close = this.getDataValue(list, "close");
          const lowest = this.getDataValue(list, "lowest");
          const highest = this.getDataValue(list, "highest");
          const value = [open, close, lowest, highest];
          //序列数据
          const dim = { name: dimKey, value: value };
          if(!serieData){
            serieData=[];
          }
          serieData.push(dim);
        }
      }
      if(axisData && axisData.length){
        option.xAxis.data = axisData;
      }
      if (serieData && serieData.length) {
        option.series[0].data = serieData;
      }
    }
    return option;
  }

}