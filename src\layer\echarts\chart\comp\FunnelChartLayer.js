
import ChartLayer from "../ChartLayer";
export default class Funnel<PERSON>hart<PERSON>ayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "漏斗图",
      type: "FunnelChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          // { key: "group", value: "${name}", desc: "分组" },//序列
          { key: "dimension", value: "${month}", desc: "维度" },//名称
          { key: "value", value: "${mark}", desc: "数值" },
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      series: [
        {
          // name: 'Funnel',
          type: 'funnel',
          data: [
            { value: 60, name: 'Visit' },
            { value: 40, name: 'Inquiry' },
            { value: 20, name: 'Order' },
            { value: 80, name: 'Click' },
            { value: 100, name: 'Show' }
          ]
        }
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //处理数据
      let dimData;
      //分组-维度
      const dimMap = this.parseGroupMap(datas, ["dimension"]);
      if (dimMap) {
        for (let dimKey in dimMap) {
          const dimList = dimMap[dimKey];
          const dimVal = this.getDataValue(dimList);
          if (dimVal) {
            dimVal["name"] = dimKey;
            if (!dimData) {
              dimData = [];
            }
            dimData.push(dimVal);
          }
        }
      }
      if (dimData && dimData.length) {
        option.series[0].data = dimData;
      }
    }
    return option;
  }
}