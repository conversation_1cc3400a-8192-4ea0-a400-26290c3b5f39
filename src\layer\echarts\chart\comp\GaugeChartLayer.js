import { isNum, isInterval } from "../../../../utils/Util";
import ChartLayer from "../ChartLayer";
export default class GaugeChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "仪表盘",
      type: "GaugeChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "value", value: "${mark}", desc: "数值" },
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      tooltip: {
        formatter: '{a} <br/>{b} : {c}%'
      },
      series: [
        {
          name: 'Pressure',
          type: 'gauge',
          detail: {
            formatter: '{value}'
          },
          data: [
            {
              value: 50,
              name: 'SCORE'
            }
          ]
        }
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //序列
      const dataVal = this.getDataValue(datas);
      if (dataVal && dataVal["value"]) {
        let value = dataVal["value"];
        if (isNum(value)) {
          if (isInterval(value, 0, 10)) {
            value = parseFloat((value / 10).toFixed(2));
          } else if (isInterval(value, 10, 100)) {
            value = parseFloat((value / 100).toFixed(2));
          } else if (isInterval(value, 100, 1000)) {
            value = parseFloat((value / 1000).toFixed(2));
          } else if (isInterval(value, 1000, 10000)) {
            value = parseFloat((value / 10000).toFixed(2));
          } else if (isInterval(value, 10000, 100000)) {
            value = parseFloat((value / 100000).toFixed(2));
          }
          if (isInterval(value, 0, 1)) {
            dataVal["value"] = value * 100;
          }
        }
      }
      const data = [dataVal];
      option["series"][0]["data"] = data;
    }
    return option;
  }
}