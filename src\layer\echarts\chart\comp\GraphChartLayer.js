
import ChartLayer from "../ChartLayer";
export default class GraphChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "关系图",
      type: "GraphChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "xAxis", "yAxis", "grid", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          // { key: "group", value: "${name}", desc: "分组" },//分类
          { key: "source", value: "${year}", desc: "来源" },
          { key: "target", value: "${month}", desc: "目标" },
          { key: "value", value: "${mark}", desc: "数值" },
          { key: "category", value: "${name}", desc: "类别" },
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      series: [
        {
          // name: 'Les Miserables',
          type: 'graph',
          layout: 'force',
          roam: true,
          symbolSize: 20,
          force: {
            repulsion: 300
          },
          // lineStyle: {
          //   color: '#2f4554'
          // },
          data: [
            { id: 0, name: "Myriel", value: 28, symbolSize: 30, category: 3, label: { show: true } },
            { id: 1, name: "Napole", value: 4, symbolSize: 10, category: 1, },
            { id: 2, name: "MlleBa", value: 9, symbolSize: 20, category: 2, },
            { id: 3, name: "MmeMag", value: 9, symbolSize: 20, category: 2, },
            { id: 4, name: "Counte", value: 4, symbolSize: 10, category: 1, },
            { id: 5, name: "Gebora", value: 4, symbolSize: 10, category: 1, },
          ],
          links: [
            { source: "Myriel", target: "Myriel" },
            { source: "Napole", target: "Myriel" },
            { source: "MlleBa", target: "Myriel" },
            { source: "MmeMag", target: "Myriel" },
            { source: "Counte", target: "Myriel" },
            { source: "Gebora", target: "Myriel" },
          ],
          categories: [
            { name: "A" },
            { name: "B" },
            { name: "C" },
            { name: "D" },
            { name: "E" },
            { name: "F" },
            { name: "G" },
            { name: "H" },
            { name: "I" }
          ]
        }
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //分组-来源
      const sourceMap = this.parseGroupMap(datas, ["source"]);
      //分组-目标
      const targetMap = this.parseGroupMap(datas, ["target"]);
      //分组-分类
      const categoryMap = this.parseGroupMap(datas, ["category"]);

      const sourceDataMap = this.parseGroupMap(datas, ["source", "category"]);
      const targetDataMap = this.parseGroupMap(datas, ["target", "category"]);
      let tempData;
      let linkData;
      let cateData;
      //序列data
      if (categoryMap && sourceMap && targetMap) {
        for (let categoryKey in categoryMap) {
          //来源
          for (let sourceKey in sourceMap) {
            const key = this.getGroupKey([sourceKey,categoryKey]);
            if (sourceDataMap && sourceDataMap[key]) {
              const list = sourceDataMap[key];
              const dataVal = this.getDataValue(list);
              dataVal["id"] = key;
              dataVal["name"] = sourceKey;
              if (!tempData) {
                tempData = [];
              }
              tempData.push(dataVal);
            }
          }
          //目标
          for (let targetKey in targetMap) {
            const key = this.getGroupKey([targetKey,categoryKey]);
            if (targetDataMap && targetDataMap[key]) {
              const list = targetDataMap[key];
              const dataVal = this.getDataValue(list);
              dataVal["id"] = key;
              dataVal["name"] = targetKey;
              if (!tempData) {
                tempData = [];
              }
              tempData.push(dataVal);
            }
          }
        }
      }
      //序列links
      if (sourceDataMap && targetDataMap) {
        for (let sourceKey in sourceDataMap) {
          for (let targetKey in targetDataMap) {
            const link = { source: sourceKey, target: targetKey };
            if (!linkData) {
              linkData = [];
            }
            linkData.push(link);
          }
        }
      }
      //序列categories
      if (categoryMap) {
        for (let categoryKey in categoryMap) {
          const category = { name: categoryKey };
          if (!cateData) {
            cateData = [];
          }
          cateData.push(category);
        }
      }
      if (tempData && tempData.length) {
        option.series[0].data = tempData;
      }
      if (linkData && linkData.length) {
        option.series[0].links = linkData;
      }
      if (cateData && cateData.length) {
        option.series[0].categories = cateData;
      }

      // //分组-类别
      // const groupMap = this.parseGroupMap(datas, ["categories"]);
      // //分组-数据
      // const dataMap = this.parseGroupMap(datas, ["source", "target"]);
      // //分组-来源
      // const sourceMap = this.parseGroupMap(datas, ["source"]);
      // //分组-维度
      // const dimMap = this.parseGroupMap(datas, ["target"]);
      // //序列
      // let _series = [];
      // let _serie = {
      //   name: 'Les Miserables', type: 'graph', data: [], links: [], categories: [], layout: 'force',
      //   roam: true,
      //   symbolSize: 10,
      //   force: {
      //     repulsion: 100
      //   }, lineStyle: {
      //     color: '#2f4554'
      //   }
      // };
      // _series.push(_serie);
      // //处理categories
      // if (groupMap) {
      //   for (let groupName in groupMap) {
      //     const categorieMap = {};
      //     categorieMap["name"] = groupName;
      //     _serie.categories.push(categorieMap);
      //   }
      // }
      // //处理data
      // if (sourceMap && dimMap) {
      //   for (let sourceName in sourceMap) {
      //     const sourceList = sourceMap[sourceName];
      //     // const sourceValue = this.sumGroupValue(sourceList, "value");
      //     const sourceValue = this.getDataValue(sourceList);
      //     sourceValue["id"] = sourceName;
      //     // const nodesMap = {};
      //     // nodesMap["value"] = sourceValue;
      //     _serie.data.push(sourceValue);
      //   }
      //   for (let dimName in dimMap) {
      //     const targetList = dimMap[dimName];
      //     // const targetValue = this.sumGroupValue(targetList, "value");
      //     // const targetValue = this.getDataValue(targetList);
      //     const targetValue = this.getDataValue(targetList);
      //     // const nodesMap = {};
      //     targetValue["id"] = dimName;
      //     // nodesMap["value"] = targetValue;
      //     _serie.data.push(targetValue);
      //   }
      // }
      // //处理links
      // if (sourceMap && dimMap) {
      //   for (let sourceName in sourceMap) {
      //     for (let dimName in dimMap) {
      //       const key = this.getGroupKey([sourceName, dimName]);
      //       if (dataMap && dataMap[key]) {
      //         const linksMap = {};
      //         linksMap["source"] = sourceName;
      //         linksMap["target"] = dimName;
      //         _serie.links.push(linksMap);
      //       }
      //     }
      //   }
      // }
      // //序列
      // if (_series) {
      //   option["series"] = _series;
      // }
    }
    return option;
  }
}