import ChartLayer from "../ChartLayer";
export default class LineBarChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty() {
    return {
      name: "Echarts图表",
      type: "EchartsLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "xAxis", "yAxisList", "grid", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "group", value: "${name}", desc: "分组" },//序列
          { key: "dimension", value: "${month}", desc: "维度" },//y轴数据
          { key: "value", value: "${mark}", desc: "数值" },//序列数据
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption() {
    const option = {
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      },
      yAxis: [{type: 'value'},{type: 'value'}],
      series: [
        {
          name: 'Evaporation',
          type: 'bar',
          data: [
            2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
          ]
        },
        {
          name: 'Precipitation',
          type: 'bar',
          data: [
            2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
          ]
        },
        {
          name: 'Temperature',
          type: 'line',
          yAxisIndex: 1,
          data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2]
        }
      ]
    };
    return option;
  }
  refreshOption(datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //分组-数据
      const dataMap = this.parseGroupMap(datas, ["group", "dimension"]);
      //分组-分组
      const groupMap = this.parseGroupMap(datas, ["group"]);
      //分组-维度
      const dimMap = this.parseGroupMap(datas, ["dimension"]);
      this.options = [];
      //序列
      let _series;
      const _xAxis = { type: 'category', data: [] };
      if (dimMap) {
        for (let dimName in dimMap) {
          _xAxis.data.push(dimName);
        }
      }
      //处理
      if (groupMap && dimMap) {
        for (let groupName in groupMap) {
          const _serie = { name: groupName, type: "bar", data: [] };
          if (!_series) {
            _series = [];
          }
          _series.push(_serie);
          //折线选择值
          const option ={key:groupName,text:groupName}; 
          this.options.push(option);
          //处理Data
          for (let dimName in dimMap) {
            //序列
            const key = this.getGroupKey([groupName, dimName]);
            if (dataMap && dataMap[key]) {
              const list = dataMap[key];
              // const value = this.sumGroupValue(list, "value");
              const dataVal = this.getDataValue(list);
              //data-序列
              _serie.data.push(dataVal);
            }
          }
        }
      }
      //序列
      if (_xAxis) {
        option["xAxis"] = _xAxis;
      }
      //序列
      if (_series) {
        option["series"] = _series;
      }
    }
    return option;
  }

  getOptions() {
    let list;
    if(this.options && this.options.length){
      list = this.options;
    }
    return list;
  }
}