
import ChartLayer from "../ChartLayer";
export default class LinesChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "Echarts图表",
      type: "EchartsLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads:["title","tooltip","toolbox","legend","textStyle","color","geo","series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "group", value: "${name}", desc: "分组" },//序列
          { key: "dimension", value: "${month}", desc: "维度" },//y轴数据
          { key: "value", value: "${mark}", desc: "数值" },//序列数据
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption(){
    const option = {
      bmap: {
        center: [120.13066322374, 30.240018034923],
        zoom: 14,
        roam: true,
      },
      series: [
        {
          type: 'lines',
          coordinateSystem: 'bmap',
          data: this.lines(),
          polyline: true,
          lineStyle: {
            color: 'purple',
            opacity: 0.6,
            width: 1
          }
        }
      ]
    }
    return option;
  }
  refreshOption(datas){
    const option=this.initOption();
    return option;
  }
}