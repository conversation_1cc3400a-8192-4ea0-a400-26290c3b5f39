import ChartLayer from "../ChartLayer";

export default class LiquidChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "水晶球",
      type: "LiquidChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "color", "xAxis", "yAxis", "grid", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "value", value: "${mark}", desc: "数值" },
        ],
        rowNum: 0,
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      series: [
        {
          type: 'liquidFill',
          radius: '45%',
          center: ['50%', '50%'],
          data: [0.5, 0.5, 0.5], // data个数代表波浪数
          backgroundStyle: {
            borderWidth: 1,
            color: 'rgb(255,0,255,0.1)',
          },
          outline: {
            show: false,
          },
        },
      ],
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      const rowData = this.getRowData()
      let dataVal = 0;
      if (rowData) {
         dataVal = Number(rowData["value"])
      }
      option["series"][0]["data"] = [dataVal, dataVal, dataVal];
    }
    return option;
  }
}