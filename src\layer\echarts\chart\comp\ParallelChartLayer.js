
import ChartLayer from "../ChartLayer";
export default class ParallelChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "平行坐标系",
      type: "ParallelChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "parallelAxis", "parallel", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "group", value: "${name}", desc: "分组" },//类别
          { key: "dimension", value: "${month}", desc: "维度" },//轴数据
          { key: "value", value: "${mark}", desc: "数值" },//序列数据
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      parallelAxis: [
        { dim: 0, name: 'Price' },
        { dim: 1, name: 'Net Weight' },
        { dim: 2, name: 'Amount' },
        {
          dim: 3,
          name: 'Score',
          type: 'category',
          data: ['Excellent', 'Good', 'OK', 'Bad']
        }
      ],
      series: [
        {
          type: 'parallel',
          lineStyle: {
            width: 4
          },
          data: [
            [12.99, 100, 82, 'Good'],
            [9.99, 80, 77, 'OK'],
            [20, 120, 60, 'Excellent']
          ]
        }
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //分组-数据
      const dataMap = this.parseGroupMap(datas, ["group", "dimension"]);
      //分组-分组
      const groupMap = this.parseGroupMap(datas, ["group"]);
      //分组-维度
      const dimMap = this.parseGroupMap(datas, ["dimension"]);
      let axisData;
      let serieData;
      let index = 0;
      //parallelAxis
      if (groupMap && dimMap) {
        for (let dimKey in dimMap) {
          const axis = { dim: index, name: dimKey };
          if (!axisData) {
            axisData = [];
          }
          axisData.push(axis);
          index++;
        }
        index++;
        const group = { dim: index, name: '类别', type: 'category', data: [] };
        if (!axisData) {
          axisData = [];
        }
        axisData.push(group);
        for (let groupKey in groupMap) {
          group.data.push(groupKey);
        }
      }
      //series
      if (groupMap && dimMap) {
        for (let groupKey in groupMap) {
          const groupData = { name: groupKey, value: [] };
          if (!serieData) {
            serieData = [];
          }
          serieData.push(groupData);
          for (let dimKey in dimMap) {
            const key = this.getGroupKey([groupKey, dimKey]);
            if (dataMap && dataMap[key]) {
              const list = dataMap[key];
              const dataVal = this.getDataValue(list, "value");
              groupData.value.push(dataVal);
            }
          }
          groupData.value.push(groupKey);
        }
      }
      if (axisData && axisData.length) {
        option.parallelAxis = axisData;
      }
      if (serieData && serieData.length) {
        option.series[0].data = serieData;
      }
    }
    return option;
  }
}