import Chart3DLayer from "../Chart3DLayer";

export default class Pie3<PERSON><PERSON>Layer extends Chart3DLayer {
  constructor(context) {
    super(context);
  }

  getDefaultProperty() {
    return {
      name: "Echarts图表",
      type: "Pie3DChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "grid3D", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          {key: "dimension", value: "${month}", desc: "维度"},
          {key: "value", value: "${mark}", desc: "数值"},
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };

  initOption() {
    // const option = {
    //   series: [
    //     {
    //       name: 'Access From',
    //       type: 'pie',
    //       radius: '50%',
    //       data: [
    //         {value: 1048, name: 'Search Engine'},
    //         {value: 735, name: 'Direct'},
    //         {value: 580, name: 'Email'},
    //         {value: 484, name: 'Union Ads'},
    //         {value: 300, name: 'Video Ads'}
    //       ],
    //     }
    //   ]
    // };
    // return option;
    return this.getPie3D([
      {value: 3, name: 'Search Engine'},
      {value: 2, name: 'Direct'},
      {value: 1, name: 'Email'},
      {value: 2, name: 'Union Ads'},
      {value: 1, name: 'Video Ads'}
    ], 0)
  }

  refreshOption(datas) {
    let option = this.initOption();
    if (datas && datas.length) {
      const data = [];
      //分组-分组
      const dimMap = this.parseGroupMap(datas, ["dimension"]);
      if (dimMap) {
        for (let dimName in dimMap) {
          //值
          const list = dimMap[dimName];
          const dataVal = this.getDataValue(list);
          dataVal["name"] = dimName;
          data.push(dataVal);
        }
      }
      option = this.getPie3D(data, 0);
      // option = this.getPie3D([{name: '已消除', value: 4,},{name: '未消除', value: 2,},{name: '消除', value: 1,},], 0);
    }
    return option;
  }

  /**
   * https://www.makeapie.cn/echarts_content/x4rwZPDJck.html
   * 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
   */
  getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, height) {

    // 计算
    let midRatio = (startRatio + endRatio) / 2;

    let startRadian = startRatio * Math.PI * 2;
    let endRadian = endRatio * Math.PI * 2;
    let midRadian = midRatio * Math.PI * 2;

    // 如果只有一个扇形，则不实现选中效果。
    if (startRatio === 0 && endRatio === 1) {
      isSelected = false;
    }

    // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
    k = typeof k !== 'undefined' ? k : 1 / 3;

    // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
    let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
    let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

    // 计算高亮效果的放大比例（未高亮，则比例为 1）
    let hoverRate = isHovered ? 1.05 : 1;

    // 返回曲面参数方程
    return {

      u: {
        min: -Math.PI,
        max: Math.PI * 3,
        step: Math.PI / 32
      },

      v: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20
      },

      x: function (u, v) {
        if (u < startRadian) {
          return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
        }
        if (u > endRadian) {
          return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
        }
        return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
      },

      y: function (u, v) {
        if (u < startRadian) {
          return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
        }
        if (u > endRadian) {
          return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
        }
        return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
      },

      z: function (u, v) {
        if (u < -Math.PI * 0.5) {
          return Math.sin(u);
        }
        if (u > Math.PI * 2.5) {
          return Math.sin(u);
        }
        return Math.sin(v) > 0 ? 1 * height : -1;
      }
    };
  }

  /**
   * 生成模拟 3D 饼图的配置项
   * @param pieData
   * @param internalDiameterRatio
   * @returns {{yAxis3D: {min: number, max: number}, zAxis3D: {min: number, max: (number|number)}, legend: {data: *[], textStyle: {color: string, fontSize: number}}, series: *[], tooltip: {formatter: ((function(*): (string|undefined))|*)}, xAxis3D: {min: number, max: number}, labelLine: {show: boolean}, label: {show: boolean}, grid3D: {bottom: string, show: boolean, boxHeight: number, viewControl: {distance: number, alpha: number, beta: number}}}}
   */
  getPie3D(pieData, internalDiameterRatio) {

    let series = [];
    let sumValue = 0;
    let startValue = 0;
    let endValue = 0;
    let legendData = [];
    let k = typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3;

    // 为每一个饼图数据，生成一个 series-surface 配置
    for (let i = 0; i < pieData.length; i++) {

      sumValue += pieData[i].value;

      let seriesItem = {
        name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
        type: 'surface',

        parametric: true,
        wireframe: {
          show: false
        },
        pieData: pieData[i],
        pieStatus: {
          selected: false,
          hovered: false,
          k: k
        },
        labelLine: {
          show: true
        },
        label: {
          show: true
        }
      };

      if (typeof pieData[i].itemStyle != 'undefined') {

        let itemStyle = {};

        typeof pieData[i].itemStyle.color != 'undefined' ? itemStyle.color = pieData[i].itemStyle.color : null;
        typeof pieData[i].itemStyle.opacity != 'undefined' ? itemStyle.opacity = pieData[i].itemStyle.opacity : null;

        seriesItem.itemStyle = itemStyle;
      }
      series.push(seriesItem);
    }

    // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
    // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
    for (let i = 0; i < series.length; i++) {
      endValue = startValue + series[i].pieData.value;
      series[i].pieData.startRatio = startValue / sumValue;
      series[i].pieData.endRatio = endValue / sumValue;
      series[i].parametricEquation = this.getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio, false, false, k, series[i].pieData.value);

      startValue = endValue;

      legendData.push(series[i].name);
    }


    const max = pieData && pieData.length ? Math.max.apply(null, pieData.map(p => p.value)) : 1;
    // 准备待返回的配置项，把准备好的 legendData、series 传入。
    let option = {
      tooltip: {
        formatter: params => {
          if (params.seriesName !== 'mouseoutSeries') {
            return `${params.seriesName}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>${option.series[params.seriesIndex].pieData.value}`;
          }
        }
      },
      labelLine: {
        show: true,
      },
      label: {
        show: true,
      },
      legend: {
        data: legendData,
        textStyle: {
          color: '#fff',
          fontSize: 14
        }
      },
      xAxis3D: {
        min: -1,
        max: 1
      },
      yAxis3D: {
        min: -1,
        max: 1
      },
      zAxis3D: {
        min: -1,
        max: max
      },
      grid3D: {
        show: false,
        boxHeight: 20,
        //top: '30%',
        bottom: '50%',
        // environment: '#021041',
        viewControl: {
          distance: 220,
          alpha: 25,
          beta: 130,
        },

      },
      series: series
    };
    return option;
  }
}