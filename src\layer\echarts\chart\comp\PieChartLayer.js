import ChartLayer from "../ChartLayer";
export default class Pie<PERSON>hart<PERSON>ayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "Echarts图表",
      type: "EchartsLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          // { key: "group", value: "${name}", desc: "分组" },
          { key: "dimension", value: "${month}", desc: "维度" },
          { key: "value", value: "${mark}", desc: "数值" },
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      series: [
        {
          name: 'Access From',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 1048, name: 'Search Engine' },
            { value: 735, name: 'Direct' },
            { value: 580, name: 'Email' },
            { value: 484, name: 'Union Ads' },
            { value: 300, name: 'Video Ads' }
          ],
        }
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      const sdata=[];
      //分组-分组
      const dimMap = this.parseGroupMap(datas, ["dimension"]);
      if (dimMap) {
        for (let dimName in dimMap) {
          //值
          const list = dimMap[dimName];
          const dataVal = this.getDataValue(list);
          dataVal["name"]=dimName;
          sdata.push(dataVal);
        }
      }
      option.series[0].data=sdata;
    }
    return option;
  }
}