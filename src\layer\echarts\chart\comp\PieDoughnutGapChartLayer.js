import PieChartLayer from "./PieChartLayer";
export default class <PERSON><PERSON><PERSON>nutGapChartLayer extends PieChartLayer {
  constructor(context) {
    super(context);
  }
  initOption(){
    const option = {
      series: [
        {
          name: 'Access From',
          type: 'pie',
          radius: ['40%', '70%'],
          padAngle: 5,
          data: [
            { value: 1048, name: 'Search Engine' },
            { value: 735, name: 'Direct' },
            { value: 580, name: 'Email' },
            { value: 484, name: 'Union Ads' },
            { value: 300, name: 'Video Ads' }
          ]
        }
      ]
    };
    return option;
  }
  
}