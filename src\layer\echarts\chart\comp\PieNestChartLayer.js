import ChartLayer from "../ChartLayer";
export default class PieNestChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "Echarts图表",
      type: "EchartsLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "group", value: "${name}", desc: "分组" },//序列
          { key: "dimension", value: "${month}", desc: "维度" },//x轴数据
          { key: "value", value: "${mark}", desc: "数值" },//序列数据
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      series: [
        {
          type: 'pie',
          selectedMode: 'single',
          radius: [0, '30%'],
          label: {
            position: 'inner',
            fontSize: 14
          },
          data: [
            { value: 1548, name: 'Search Engine' },
            { value: 775, name: 'Direct' },
            { value: 679, name: 'Marketing', selected: true }
          ]
        },
        {
          type: 'pie',
          radius: ['45%', '60%'],
          label: {
            backgroundColor: '#F6F8FC',
            borderColor: '#8C8D8E',
          },
          data: [
            { value: 1048, name: 'Baidu' },
            { value: 335, name: 'Direct' },
            { value: 310, name: 'Email' },
            { value: 251, name: 'Google' },
            { value: 234, name: 'Union Ads' },
            { value: 147, name: 'Bing' },
            { value: 135, name: 'Video Ads' },
            { value: 102, name: 'Others' }
          ]
        }
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //分组-数据
      const dataMap = this.parseGroupMap(datas, ["group", "dimension"]);
      //分组-分组
      const groupMap = this.parseGroupMap(datas, ["group"]);
      //分组-维度
      const dimMap = this.parseGroupMap(datas, ["dimension"]);

      //处理数据
      const dimData = [];
      const groupData = [];
      if (groupMap) {
        for (let groupName in groupMap) {
          //值
          const groupList = groupMap[groupName];
          const groupVal = this.getDataValue(groupList);
          groupVal["name"] = groupName;
          groupData.push(groupVal);

          if (dimMap) {
            for (let dimName in dimMap) {
              //序列
              const key = this.getGroupKey([groupName, dimName]);
              if (dataMap && dataMap[key]) {
                const dimList = dataMap[key];
                const dimVal = this.getDataValue(dimList);
                dimVal["name"] = dimName;
                dimData.push(dimVal);
              }
            }
          }
        }
      }
      option.series[0].data = groupData;
      option.series[1].data = dimData;
    }
    return option;
  }
}