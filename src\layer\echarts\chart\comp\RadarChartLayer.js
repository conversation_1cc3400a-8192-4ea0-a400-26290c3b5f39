
import ChartLayer from "../ChartLayer";
export default class RadarChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "雷达图",
      type: "RadarChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "radar", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "group", value: "${name}", desc: "分组" },//序列
          { key: "dimension", value: "${month}", desc: "维度" },//y轴数据
          { key: "value", value: "${mark}", desc: "数值" },//序列数据
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      radar: {
        // shape: 'circle',
        indicator: [
          { name: 'Sales', max: 6500 },
          { name: 'Administration', max: 16000 },
          { name: 'Information Technology', max: 30000 },
          { name: 'Customer Support', max: 38000 },
          { name: 'Development', max: 52000 },
          { name: 'Marketing', max: 25000 }
        ]
      },
      series: [
        {
          // name: 'Budget vs spending',
          type: 'radar',
          data: [
            {
              value: [4200, 3000, 20000, 35000, 50000, 18000],
              name: 'Allocated Budget'
            },
            {
              value: [5000, 14000, 28000, 26000, 42000, 21000],
              name: 'Actual Spending'
            }
          ]
        }
      ]
      // series: [
      //   {
      //     name: 'Allocated Budget',
      //     type: 'radar',
      //     data: [[4200, 3000, 20000, 35000, 50000, 18000]]
      //   },{
      //     name: 'Actual Spending',
      //     type: 'radar',
      //     data: [[5000, 14000, 28000, 26000, 42000, 21000]],
      //   },
      // ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //分组-数据
      const dataMap = this.parseGroupMap(datas, ["group", "dimension"]);
      //分组-分组
      const groupMap = this.parseGroupMap(datas, ["group"]);
      //分组-维度
      const dimMap = this.parseGroupMap(datas, ["dimension"]);

      let indMap;
      let groups;
      //处理
      if (groupMap && dimMap) {
        for (let groupName in groupMap) {
          const group = {name:groupName,value:[]};
          if (!groups) {
            groups = [];
          }
          groups.push(group);
          //处理Data
          for (let dimName in dimMap) {
            //序列
            const key = this.getGroupKey([groupName, dimName]);
            if (dataMap && dataMap[key]) {
              const list = dataMap[key];
              const value = this.getDataValue(list, "value");
              //分组
              group["value"].push(value);
              //最大值
              if (!indMap) {
                indMap = {};
              }
              if (value && indMap[dimName]) {
                const oldValue = indMap[dimName];
                if (oldValue < value) {
                  indMap[dimName] = value;
                }
              } else {
                indMap[dimName] = value;
              }
            }
          }
        }
      }
      if (indMap) {
        let indicator=[];
        for(let indKey in indMap){
          const ind={name:indKey,value:indMap[indKey]};
          indicator.push(ind);
        }
        option.radar.indicator = indicator;
      }
      if (groups && groups.length) {
        option.series[0].data = groups;
      }
    }
    return option;
  }
}