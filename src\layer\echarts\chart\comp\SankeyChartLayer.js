
import ChartLayer from "../ChartLayer";
export default class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "桑基图",
      type: "SankeyChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          // { key: "group", value: "${name}", desc: "分组" },//序列
          { key: "source", value: "${name}", desc: "来源" },
          { key: "target", value: "${month}", desc: "目标" },
          { key: "value", value: "${mark}", desc: "数值" },
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      series: [
        {
          type: 'sankey',
          // layout: 'none',
          // emphasis: {
          //   focus: 'adjacency'
          // },
          data: [
            { name: 'a' },
            { name: 'b' },
            { name: 'a1' },
            { name: 'a2' },
            { name: 'b1' },
            { name: 'c' }
          ],
          links: [
            { source: 'a', target: 'a1', value: 5 },
            { source: 'a', target: 'a2', value: 3 },
            { source: 'b', target: 'b1', value: 8 },
            { source: 'a', target: 'b1', value: 3 },
            { source: 'b1', target: 'a1', value: 1 },
            { source: 'b1', target: 'c', value: 2 }
          ]
        }
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      const dataMap = this.parseGroupMap(datas, ["source", "target"]);
      //分组-来源
      const sourceMap = this.parseGroupMap(datas, ["source"]);
      //分组-目标
      const targetMap = this.parseGroupMap(datas, ["target"]);

      let tempData;
      let linkData;
      if (sourceMap && targetMap) {
        ////处理Data 合并分组与维度的对象
        let tempMap = Object.assign(sourceMap, targetMap);
        for (let tempKey in tempMap) {
          const temp={name:tempKey};
          if(!tempData){
            tempData=[];
          }
          tempData.push(temp);
        }
        //处理links
        for (let sourceKey in sourceMap) {
          for (let targetKey in targetMap) {
            const key = this.getGroupKey([sourceKey, targetKey]);
            if (dataMap && dataMap[key]) {
              const list = dataMap[key];
              const dataVal = this.getDataValue(list);
              dataVal["source"] = sourceKey;
              dataVal["target"] = targetKey;
              if(!linkData){
                linkData=[];
              }
              linkData.push(dataVal);
            }
          }
        }
      }
      if (tempData && tempData.length) {
        option.series[0].data = tempData;
      }
      if (linkData && linkData.length) {
        option.series[0].links = linkData;
      }
    }
    return option;
  }
}