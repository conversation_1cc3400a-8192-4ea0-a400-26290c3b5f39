
import ChartLayer from "../ChartLayer";
export default class ScatterChartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "Echarts图表",
      type: "EchartsLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      //GitHub 打卡气泡图有径向轴以及角度轴
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "xAxis", "yAxis", "grid", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          // { key: "group", value: "${name}", desc: "分组" },//序列
          // { key: "dimension", value: "${month}", desc: "维度" },//x轴数据
          // { key: "value", value: "${mark}", desc: "数值" },//序列数据
          { key: "xValue", value: "${mark}", desc: "维度X值" },
          { key: "yValue", value: "${chinaese}", desc: "维度Y值" },
        ],
        columns: ["name", "year", "month", "mark", "chinaese"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      xAxis: {
        // type: 'category',
        // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      },
      yAxis: {
        // type: 'category',
      },
      series: [
        {
          type: 'scatter',
          symbolSize: 20,
          data: [
            // {value:10},
            // {value:[10.0, 8.04]},
            [10.0, 8.04],
            [8.07, 6.95],
            [13.0, 7.58],
            [9.05, 8.81],
            [11.0, 8.33],
            [14.0, 7.66],
            [13.4, 6.81],
            [10.0, 6.33],
            [14.0, 8.96],
            [12.5, 6.82],
            [9.15, 7.2],
            [11.5, 7.2],
            [3.03, 4.23],
            [12.2, 7.83],
            [2.02, 4.47],
            [1.05, 3.33],
            [4.05, 4.96],
            [6.03, 7.24],
            [12.0, 6.26],
            [12.0, 8.84],
            [7.08, 5.82],
            [5.02, 5.68]
          ],
        }
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      for(let i=0;i<datas.length;i++){
        const data=datas[i];
        const xValue = data["xValue"];
        const yValue = data["yValue"];
        data["value"]=[xValue, yValue];
      }
      option.series[0].data = datas;
      // //处理数据
      // let dimData;
      // //分组-维度
      // const dimMap = this.parseGroupMap(datas, ["dimension"]);
      // if (dimMap) {
      //   for (let key in dimMap) {
      //     const dimList = dimMap[key];
      //     const xValue = this.getDataValue(dimList, "xValue");
      //     const yValue = this.getDataValue(dimList, "yValue");
      //     //对象
      //     const dimVal = this.getDataValue(dimList);
      //     dimVal["value"] = [xValue, yValue];
      //     if (!dimData) {
      //       dimData = [];
      //     }
      //     dimData.push(dimVal);
      //   }
      // }
      // if (dimData && dimData.length) {
      //   option.series[0].data = dimData;
      // }

    }
    return option;
  }
}