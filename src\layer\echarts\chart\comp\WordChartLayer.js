import ChartLayer from "../ChartLayer";
export default class Word<PERSON>hartLayer extends ChartLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "词云",
      type: "WordChartLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "xAxis", "yAxis", "grid", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "dimension", value: "${month}", desc: "维度" },//名称
          { key: "value", value: "${mark}", desc: "数值" },//数据
        ],
        columns: ["name", "year", "month", "mark"],
        mock: this.mockData(),
      },
    }
  };
  initOption () {
    const option = {
      series: [
        {
          type: 'wordCloud',
          shape: 'circle',
          data: [
            { name: '雨伞', value: 30 },
            { name: '晴天', value: 28 },
            { name: '电话', value: 24 },
            { name: '手机', value: 23 },
            { name: '下雨', value: 22 },
            { name: '暴雨', value: 21 },
            { name: '多云', value: 20 },
            { name: '雨衣', value: 29 },
            { name: '屋檐', value: 28 },
            { name: '大风', value: 27 },
            { name: '台风', value: 26 },
            { name: '下雪', value: 25 },
            { name: '打雷', value: 24 },
            { name: '小雨', value: 30 },
            { name: '中雨', value: 18 },
            { name: '大雨', value: 14 },
            { name: '雷阵雨', value: 13 },
            { name: '下雪', value: 12 },
            { name: '小雪', value: 11 },
            { name: '中雪', value: 10 },
            { name: '大雪', value: 9 },
            { name: '暴雪', value: 8 },
            { name: '东风', value: 7 },
            { name: '南风', value: 6 },
            { name: '西北风', value: 5 },
            { name: '北风', value: 4 },
            { name: '闪电', value: 3 }],
        }],
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //处理数据
      let dimData;
      //分组-维度
      const dimMap = this.parseGroupMap(datas, ["dimension"]);
      if (dimMap) {
        for (let key in dimMap) {
          const dimList = dimMap[key];
          const dimVal = this.getDataValue(dimList);
          if (dimVal) {
            dimVal["name"] = key;
            if (!dimData) {
              dimData = [];
            }
            dimData.push(dimVal);
          }
        }
      }
      if (dimData && dimData.length) {
        option.series[0].data = dimData;
      }
    }
    return option;
  }
}