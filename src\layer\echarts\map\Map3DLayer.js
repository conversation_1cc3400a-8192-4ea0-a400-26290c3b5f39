import MapParser from './util/MapParser';
import EchartsLayer from '../EchartsLayer';
import MapLayer from "./MapLayer";
export default class Map3DLayer extends MapLayer {
  constructor(context) {
    super(context);
  }
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.echartsContainer = $(`<div class="layer-echarts"></div>`);
      panelContainer.append(this.echartsContainer);
      if (this.echarts) {
        this.myChart = this.echarts.init(this.echartsContainer[0], null, { renderer: 'canvas' });
      }
    }
  };
  refresh3DMapOption (option) {
    if (option) {
      const mapProp = this.property.map;
      if (mapProp) {
        const code = mapProp.id;
        const name = mapProp.name;
        if (option["geo3D"]) {
          option["geo3D"]["map"] = code
        }
        if (option["series"] && option["series"].length) {
          const series = option["series"];
          for (let i = 0; i < series.length; i++) {
            const serie = series[i];
            if (serie.type === "map3D") {
              serie["name"] = name;
              serie["map"] = code;
            }
          }
        }
      }
    }
  }
}