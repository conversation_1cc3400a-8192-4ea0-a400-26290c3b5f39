import MapParser from './util/MapParser';
import EchartsLayer from '../EchartsLayer';
export default class MapLayer extends EchartsLayer {
  constructor(context) {
    super(context);
    // 地图是否注册成功
    this.isRegister = false;
  }
  mockData () {
    const data = [
      { code: '11', name: '北京' },
      { code: '12', name: '天津' },
      { code: '31', name: '上海' },
      { code: '50', name: '重庆' },
      { code: '13', name: '河北' },
      { code: '41', name: '河南' },
      { code: '53', name: '云南' },
      { code: '21', name: '辽宁' },
      { code: '23', name: '黑龙江' },
      { code: '43', name: '湖南' },
      { code: '34', name: '安徽' },
      { code: '37', name: '山东' },
      { code: '65', name: '新疆' },
      { code: '32', name: '江苏' },
      { code: '33', name: '浙江' },
      { code: '36', name: '江西' },
      { code: '42', name: '湖北' },
      { code: '45', name: '广西' },
      { code: '62', name: '甘肃' },
      { code: '14', name: '山西' },
      { code: '15', name: '内蒙古' },
      { code: '61', name: '陕西' },
      { code: '22', name: '吉林' },
      { code: '35', name: '福建' },
      { code: '52', name: '贵州' },
      { code: '44', name: '广东' },
      { code: '63', name: '青海' },
      { code: '54', name: '西藏' },
      { code: '51', name: '四川' },
      { code: '64', name: '宁夏' },
      { code: '46', name: '海南' },
      { code: '71', name: '台湾' },
      { code: '81', name: '香港' },
      { code: '82', name: '澳门' }
    ];
    for (var i = 0; i < data.length; i++) {
      data[i].value = Math.round(Math.random() * 100);
      data[i].endCode = '11';
    }
    return data;
  }
  initMap () {
    return {
      id: "china",
      code: "china",
      name: "中国",
      level: "china",
      parentId: "",
      initCode: "china",
      isProvinceDrill: true,//是否省份下钻
      isCityDrill: true,//是否市县下钻

      // isDrill: true,//是否下钻
      // id: "china",
      // name: "中国",
      // level: "china",
      // isHasChild: true,
      // isProvinceDrill: false,//是否省份下钻
      // isCityDrill: false,//是否市县下钻
      // chinaBind: {
      //   name: "中国地图模拟数据",
      //   // mock: getMapScData(),
      //   mappings: [
      //     { key: "code", value: "${code}", desc: "省份编码" },
      //     { key: "name", value: "${name}", desc: "省份名称" },
      //     { key: "value", value: "${value}", desc: "数值" },
      //   ]
      // },
      // provinceBind: {
      //   name: "省级地图模拟数据",
      //   // mock: getMapScData(),
      //   mappings: [
      //     { key: "code", value: "${code}", desc: "市/县编码" },
      //     { key: "name", value: "${name}", desc: "市/县名称" },
      //     { key: "value", value: "${value}", desc: "数值" },
      //   ]
      // },
      // cityBind: {
      //   name: "市级地图模拟数据",
      //   // mock: getMapScData(),
      //   mappings: [
      //     { key: "code", value: "${code}", desc: "区编码" },
      //     { key: "name", value: "${name}", desc: "区名称" },
      //     { key: "value", value: "${value}", desc: "数值" },
      //   ]
      // }
    }
  }
  initChart () {
    const chart = {
      visualMap: {
        type: 'continuous',
        text: ['', ''],
        showLabel: true,
        left: '50',
        min: 0,
        max: 100,
        inRange: {
          color: ['#edfbfb', '#b7d6f3', '#40a9ed', '#3598c1', '#215096',],
        },
        splitNumber: 0
      },
      // geo: {
      //   map: 'china',
      // },
      // series: [
      //   {
      //     name: '地图',
      //     type: 'map',
      //     mapType: 'china',// map: 'china',
      //     data: this.mockData(),
      //   }
      // ]
    };
    return chart;
  }
  getOptions (isCustomMap) {
    const mapParser = MapParser.getInstance(this.context);
    // 是否自定义地图
    if (isCustomMap) {
      return mapParser.getCustomAreaMap();
    } else {
      return mapParser.getAreaMap();
    }
  }
  /**#############################获取数据绑定############################# */
  getBind () {
    let bind;
    const map = this.property.map;
    if (map) {
      if (map.level === "china") {
        //全国
        bind = map["chinaBind"];
      } else if (map.level === "province") {
        //省份
        bind = map["provinceBind"];
      } else if (map.level === "city") {
        //市区
        bind = map["cityBind"];
      }
    }
    if (!bind) {
      bind = this.property.bind;
    }
    return bind;
  }
  /**##########################注册[注册地图]############################ */
  registerMap (echarts) {
    const mapId = this.property.map.id;//china
    const mapParser = MapParser.getInstance(this.context);
    const geoMap = mapParser.getGeoMapById(mapId, this.property.map.isCustom);
    this.isRegister = !!geoMap;
    if (echarts && geoMap) {
      echarts.registerMap(mapId, geoMap);
    }
  };
  /**############################事件[点击事件]############################ */
  clickEvent (params) {
    // console.info("单击:", params);
    const propMap = this.property.map;
    let flag = false;//是否钻取
    if (propMap && propMap.isProvinceDrill && propMap["level"] === "china") {
      flag = true;
    }
    if (propMap && propMap.isCityDrill && propMap["level"] === "province") {
      flag = true;
    }
    if (flag) {
      this.isDrilled = true;//是否钻取过
      if (params && params.data) {
        const paramMap = params.data;
        this.refreshPropMap(paramMap);
        this.refreshData();
      }
    } else {
      //超链接
      this.clickJump(params.data);
      //联动
      this.clickRelation(params.data);
    }
  }
  dblclickEvent (params) {
    // console.info("双击:", params);
    const propMap = this.property.map;
    if (propMap && propMap.parentId && this.isDrilled) {
      const mapId = propMap.parentId;
      const mapParser = MapParser.getInstance(this.context);
      const areaMap = mapParser.getAreaById(mapId);
      this.refreshPropMap(areaMap);
      this.refreshData();
      //处理回到初始地图
      if (propMap.initCode === areaMap["id"]) {
        this.isDrilled = false;
      }

    }
  }
  /**#########################刷新[刷新属性]############################ */
  refreshPropMap (param) {
    const propMap = this.property.map;
    if (propMap && param) {
      propMap["id"] = param["id"];
      propMap["code"] = param["code"];
      propMap["name"] = param["name"];
      propMap["level"] = param["level"];
      propMap["parentId"] = param["parentId"];
    }
  }
  /**
   * 描述：刷新OptionMap
   * @param {*} option 
   */
  refreshMapOption (option) {
    if (option) {
      const mapProp = this.property.map;
      if (mapProp) {
        const code = mapProp.id;
        const name = mapProp.name;
        if (option["geo"]) {
          option["geo"]["map"] = code
        }
        if (option["series"] && option["series"].length) {
          const series = option["series"];
          for (let i = 0; i < series.length; i++) {
            const serie = series[i];
            if (serie.type === "map") {
              serie["name"] = name;
              serie["map"] = code;
            }
          }
        }
      }
    }
  }
  refresh3DMapOption (option) {}
  /**##########################数据[数据解析]########################## */
  parseData (datas, isMask) {
    let list;
    if (datas && datas.length) {
      const mapId = this.property.map.id;
      let area;
      const mapParser = MapParser.getInstance(this.context);
      // 是否自定义地图
      if (this.property.map.isCustom) {
        area = mapParser.getCustomAreaById(mapId);
      } else {
        area = mapParser.getAreaById(mapId);
      }
      const childMap = area["childs"];
      if (childMap) {
        for (let key in childMap) {
          const child = childMap[key];
          // const item = { id: child.id, code: child.id, name: child.name, value: [0, 0, 0] };//coord: child.lng + "," + child.lat
          const item = { id: child.id, code: child.id, name: child.name, value: 0, parentId: child.parentId, level: child.level };
          if (isMask) {
            item.value = [child.lng, child.lat, 0];
          }
          for (let j = 0; j < datas.length; j++) {
            const data = datas[j];
            if (data.code == key) {
              item.value = data.value;
              if (isMask) {
                item.value = [child.lng, child.lat, data.value];
              }
            }
          }

          if (!list) {
            list = [];
          }
          list.push(item);
        }
      }

    }
    return list;
  }
  refreshCoord (datas) {
    const mapId = this.property.map.id;
    const mapParser = MapParser.getInstance(this.context);
    const area = mapParser.getAreaById(mapId);
    const childMap = area["childs"];
    if (datas && datas.length) {
      for (let i = 0; i < datas.length; i++) {
        const data = datas[i];
        data["coord"] = this.getCoord(childMap, data.code);

        if (!data["startCoord"]) {
          if (data["startCode"]) {
            data["startCoord"] = this.getCoord(childMap, data.startCode);
          } else {
            data["startCoord"] = data["coord"];
          }
        }

        if (!data["endCoord"]) {
          if (data["endCode"]) {
            data["endCoord"] = this.getCoord(childMap, data.endCode);
          } else {
            data["endCoord"] = data["coord"];
          }
        }
      }
    }
  }

  getCoord (dataMap, key) {
    let coord = 0;
    if (key && dataMap && dataMap[key]) {
      const data = dataMap[key];
      coord = data.lng + "," + data.lat;
    }
    return coord;
  }

  // parseData (datas) {
  //   let list;
  //   const mapId = this.property.map.id;
  //   const mapParser = MapParser.getInstance();
  //   const geoMap = mapParser.getGeoMapById(mapId);
  //   const features = geoMap.features;
  //   for (let i = 0; i < features.length; i++) {
  //     const properties = features[i].properties;

  //     const item = { id: properties.id, code: properties.id, name: properties.name, value: [0, 0, 0] };

  //     for (let j = 0; j < datas.length; j++) {
  //       const data = datas[j];
  //       if (data.code == properties.id) {
  //         item.value = [properties.cp[0], properties.cp[1], data.value]
  //       }
  //     }

  //     if (!list) {
  //       list = [];
  //     }
  //     list.push(item);
  //   }
  //   return list
  // }
}