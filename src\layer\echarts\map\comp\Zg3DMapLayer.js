import Map3DLayer from "../Map3DLayer";

export default class Zg3DMapLayer extends Map3DLayer {
  constructor(context) {
    super(context);
  }

  getDefaultProperty() {
    return {
      name: "Echarts地图",
      type: "Zg3DMapLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["tooltip", "visualMap", "geo3D", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "code", value: "${code}", desc: "区编码" },
          { key: "name", value: "${name}", desc: "区名称" },
          { key: "value", value: "${value}", desc: "数值" },
        ],
        columns: ["code", "name", "value"],
        mock: this.mockData(),
      },
      map: this.initMap(),
    }
  };

  initOption() {
    return {
      series: [
        {
          name: '地图',
          type: 'map3D',
          // boxWidth: 80,  // 控制地图在容器中的宽度
          // boxHeight: 10,  // 控制地图的高度
          // boxDepth: 100,   // 控制地图深度
          map: 'china',//mapType: 'china',// map: 'china',
          data: this.mockData(),
          // 3D地图只需要设置序列样式
          // label: {
          //   show: true, // 确保标签显示
          //   textStyle: {
          //     fontSize: 30, // 调整字体大小
          //     color: '#f00', // 设置字体颜色
          //   },
          //   formatter: '{b}' // 显示区域名称
          // },
          // viewControl: {
          //   distance: 100, // 调整视角距离
          //   minDistance: 10,
          //   maxDistance: 200,
          // },
          // emphasis: {
          //   label: {
          //     show: true, // 显示鼠标悬浮时的标签
          //     textStyle: {
          //       fontSize: 40, // 调整字体大小
          //       color: '#000', // 设置字体颜色
          //     },
          //   }
          // },
          // itemStyle: {
          //   areaColor: '#f00',
          //   borderColor: '#111',
          //   borderWidth: 1,
          //   opacity: 100
          // },
          geoIndex: 0,
        },
      ]
    };
  }

  refreshOption(datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //刷新Option
      this.refresh3DMapOption(option);
      //地图
      const mapList = this.parseData(datas);
      if (mapList && mapList.length) {
        option["series"][0]["data"] = mapList;
      }
    }
    return option;
  }
}