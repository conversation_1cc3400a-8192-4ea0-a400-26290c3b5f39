// import { map } from 'echarts/types/src/export/api/util.js';
import MapLayer from '../MapLayer';
export default class ZgFxMapLayer extends MapLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "Echarts地图",
      type: "MapLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["tooltip", "visualMap", "geo","polar", "series"],
      //loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "xAxis", "yAxis", "grid", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "group", value: "${group}", desc: "分组" },
          { key: "code", value: "${code}", desc: "编码" },//起点编码
          { key: "name", value: "${name}", desc: "名称" },//起点坐标
          { key: "value", value: "${value}", desc: "数值" },
          { key: "coord", value: "${coord}", desc: "坐标" },
          { key: "startCode", value: "${startCode}", desc: "起点编码" },
          { key: "startCoord", value: "${startCoord}", desc: "起点坐标" },
          { key: "endCode", value: "${endCode}", desc: "终点编码" },
          { key: "endCoord", value: "${endCoord}", desc: "终点坐标" },
        ],
        columns: ["group", "code", "name", "value", "coord", "startCode", "startCoord", "endCode", "endCoord"],
        mock: this.mockData(),
      },
      map: this.initMap(),
    }
  };
  initOption () {
    const option = {
      visualMap: {
        type: 'continuous',
        text: ['', ''],
        showLabel: true,
        left: '50',
        min: 0,
        max: 100,
        inRange: {
          color: ['#edfbfb', '#b7d6f3', '#40a9ed', '#3598c1', '#215096',],
        },
        splitNumber: 0
      },
      geo: {
        map: 'china',
      },
      series: [
        {
          name: '地图',
          type: 'map',
          map: 'china',//mapType: 'china',// map: 'china',
          data: this.mockData(),
          geoIndex: 0,
          // coordinateSystem: "geo",
        },
        {
          type: 'lines',
          coordinateSystem: 'geo',
          zlevel: 1,
          data: this.mockData(),
          lines:[{}]
        },
        {
          type: "effectScatter",
          coordinateSystem: 'geo',
          zlevel: 2,
          data: this.mockData(),
          // label: {
          //   show: true,
          //   position: "right",
          //   formatter: "{b}",
          //   color: "#fff",
          //   fontSize: 10
          // },
        },
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //刷新Option
      this.refreshMapOption(option);
      //地图
      const mapList = this.parseData(datas);
      if (mapList && mapList.length) {
        option["series"][0]["data"] = mapList;
      }
      //遮罩(飞线)
      this.refreshCoord(datas);//刷新数据(坐标)
      const groupMap = this.parseGroupMap(datas, ["group"]);//分组
      if (groupMap) {
        //  飞线
        let linesList = [];
        //  涟漪散点
        let scatterList = [];
        for (let key in groupMap) {
          let list = groupMap[key];

          for (let i = 0; i < list.length; i++) {
            let item = list[i];
            // 线条起始坐标
            let startCoord = item.startCoord.split(",");
            // 线条终点坐标
            let endCoord = item.endCoord.split(",")
            // 飞线（组装数据和样式）
            let lines = [
              { coord: startCoord, name: item.name, value: item.value },
              { coord: endCoord }
            ]
            linesList.push(lines);
            // 涟漪散点（组装数据和样式）
            let scatter = { name: item.name, value: [...startCoord, item.value] }
            scatterList.push(scatter);
          }
        }
        option["series"][1]["data"] = linesList;
        option["series"][2]["data"] = scatterList;
      }
    }

    return option;
  }
}