import MapLayer from '../MapLayer';
export default class ZgQpMapLayer extends MapLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty() {
    return {
      name: "Echarts地图",
      type: "MapLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["tooltip", "visualMap", "geo", "series"],
      //loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "xAxis", "yAxis", "grid", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "code", value: "${code}", desc: "区编码" },
          { key: "name", value: "${name}", desc: "区名称" },
          { key: "value", value: "${value}", desc: "数值" },
        ],
        columns: ["code", "name", "value"],
        mock: this.mockData(),
      },
      map: this.initMap(),
    }
  };

  initOption() {
    const option = {
      visualMap: {
        type: 'continuous',
        text: ['', ''],
        showLabel: true,
        left: '50',
        min: 0,
        max: 100,
        inRange: {
          color: ['#edfbfb', '#b7d6f3', '#40a9ed', '#3598c1', '#215096',],
        },
        splitNumber: 0
      },
      geo: {
        map: 'china',
      },
      series: [
        {
          name: '地图',
          type: 'map',
          map: 'china',//mapType: 'china',// map: 'china',
          data: this.mockData(),
          geoIndex: 0,
          // coordinateSystem: "geo",
        },
        {
          name: '散点',
          type: 'scatter',
          coordinateSystem: 'geo',
          zlevel: 1,
          data: this.mockData(),
          // hoverAnimation: true,
          // symbolSize: 30,
          // rippleEffect: {
          //   brushType: 'stroke'
          // },
          // label: {
          //   show: true,
          //   formatter: "{b}",
          //   textStyle: {
          //     color: '#fff',
          //     fontSize: 9,
          //   }
          // },
          // itemStyle: {
          //   color: '#F62157', //标志颜色
          // },

        },
      ]
    };
    return option;
  }
  refreshOption(datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //刷新Option
      this.refreshMapOption(option);
      //地图
      const mapList = this.parseData(datas);
      if (mapList && mapList.length) {
        option["series"][0]["data"] = mapList;
      }
      //遮罩(气泡)
      const dataList = this.parseData(datas, true);
      if (dataList && dataList.length) {
        const list = dataList.map((item) => { return item.value[2] });
        // 获取当前数据中的最小值
        const minValues = list.reduce((a, b) => { return parseFloat(b) < parseFloat(a) ? parseFloat(b) : parseFloat(a) })
        // 获取当前数据中的最大值
        const maxValues = list.reduce((a, b) => { return parseFloat(b) > parseFloat(a) ? parseFloat(b) : parseFloat(a) })
        // 区间最小范围
        const minRegion = 30;
        // 区间最大范围
        const maxRegion = 60;
        // 计算缩放比例
        const scale = (maxRegion - minRegion) / (maxValues - minValues);

        option["series"][1]["data"] = dataList;
        option["series"][1]["symbolSize"] = function (dataItem) {
          return Math.ceil(minRegion + scale * (parseFloat(dataItem[2]) - minValues));
        };
      }
    }
    return option;
  }
}