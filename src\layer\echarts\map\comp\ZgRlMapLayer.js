import MapLayer from '../MapLayer';
export default class ZgRlMapLayer extends MapLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "Echarts地图",
      type: "MapLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["tooltip", "visualMap", "geo", "series"],
      //loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "xAxis", "yAxis", "grid", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "code", value: "${code}", desc: "区编码" },
          { key: "name", value: "${name}", desc: "区名称" },
          { key: "value", value: "${value}", desc: "数值" },
        ],
        columns: ["code", "name", "value"],
        mock: this.mockData(),
      },
      map: this.initMap(),
    }
  };
  initOption () {
    const option = {
      // xAxis:{
      //   type: 'category',
      // },
      // yAxis:{
      //   type: 'category',
      // },
      visualMap: {
        type: 'continuous',
        text: ['', ''],
        showLabel: true,
        left: '50',
        min: 0,
        max: 100,
        inRange: {
          color: ['#edfbfb', '#b7d6f3', '#40a9ed', '#3598c1', '#215096',],
        },
        splitNumber: 0
      },
      geo: {
        map: 'china',
      },
      series: [
        {
          name: '地图',
          type: 'map',
          map: 'china',//mapType: 'china',// map: 'china',
          data: this.mockData(),
          geoIndex: 0,
          // coordinateSystem: "geo",
        },
        {
          name: '热力',
          type: 'heatmap',
          coordinateSystem: 'geo',
          zlevel: 1,
          data: this.mockData(),
        }
      ]
    };
    return option;
  }
  refreshOption (datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //刷新Option
      this.refreshMapOption(option);
      // //地图
      const mapList = this.parseData(datas);
      if (mapList && mapList.length) {
        option["series"][0]["data"] = mapList;
      }
      //遮罩(热力)
      const dataList = this.parseData(datas, true);
      if (dataList && dataList.length) {
        option["series"][1]["data"] = dataList;
      }
    }
    return option;
  }
}