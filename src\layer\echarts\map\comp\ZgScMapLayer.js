import MapLayer from '../MapLayer';
export default class ZgScMapLayer extends MapLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty() {
    return {
      name: "Echarts地图",
      type: "MapLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {},
      loads: ["tooltip", "visualMap", "geo", "series"],
      //loads: ["title", "tooltip", "toolbox", "legend", "textStyle", "color", "xAxis", "yAxis", "grid", "series"],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "code", value: "${code}", desc: "区编码" },
          { key: "name", value: "${name}", desc: "区名称" },
          { key: "value", value: "${value}", desc: "数值" },
        ],
        columns: ["code", "name", "value"],
        mock: this.mockData(),
      },
      map: this.initMap(),
    }
  };
  initOption() {
    const option = {
      visualMap: {
        type: 'continuous',
        text: ['', ''],
        showLabel: true,
        left: '50',
        min: 0,
        max: 100,
        inRange: {
          color: ['#edfbfb', '#b7d6f3', '#40a9ed', '#3598c1', '#215096',],
        },
        splitNumber: 0
      },
      geo: {
        map: 'china',
      },
      series: [
        {
          name: '地图',
          type: 'map',
          map: 'china',//mapType: 'china',// map: 'china',
          data: this.mockData(),
          geoIndex: 0,
          // coordinateSystem: "geo",
        }
      ]
    };
    return option;
  }
  refreshOption(datas) {
    const option = this.initOption();
    if (datas && datas.length) {
      //刷新Option
      this.refreshMapOption(option);
      //获取最大最小
      const list = datas.map(item => { return item.value });
      const mins = list.reduce((a, b) => { return parseFloat(b) < parseFloat(a) ? parseFloat(b) : parseFloat(a) })
      const maxs = list.reduce((a, b) => { return parseFloat(b) > parseFloat(a) ? parseFloat(b) : parseFloat(a) })
      option["visualMap"].min = mins;
      option["visualMap"].max = maxs;
      //地图
      const mapList = this.parseData(datas);
      if (mapList && mapList.length) {
        option["series"][0]["data"] = mapList;
      }
    }
    return option;
  }
}