import MapJson from "./MapJson";
import CustomMapService from "../../../../service/CustomMapService";
import {isObject} from "../../../../utils/Util";
import {error} from "../../../../assets/element/jquery/msg/MsgUtil";
export default class MapParser {
  constructor(context) {
    this.property = {};
    this.chinaPath = "./geo/china.json";
    this.provinceDir = "./geo/province/";
    this.cityDir = "./geo/city/";
    this.areaMap = {
      china: { id: "china", name: "全国", level: "china", lng: 0, lat: 0, parentId: "", childs: MapJson.getJson(), },
    };
    this.context = context;
    this.refreshtArea(this.areaMap);
  }
  /**
   * 描述:单例
   * @returns {MapParser}
   */
  static getInstance (context) {
    if (!this.instance) {
      this.instance = new MapParser(context);
    }
    return this.instance;
  }
  /**
   * 描述:获取json数据
   * @param url
   */
  loadJson (url) {
    let result;
    $.ajaxSettings.async = false;
    $.getJSON(url, function (data) {
      if (data) {
        result = data;
      }
    });
    $.ajaxSettings.async = true;
    return result;
  }
  /**
   * 描述:刷新数据
   * 填充地址
   * @param {*} areaMap 
   */
  refreshtArea (areaMap) {
    if (areaMap) {
      for (let key in areaMap) {
        const area = areaMap[key];
        if (area["level"]) {
          if (area["level"] === "china") {
            area["path"] = this.chinaPath;
          }
          if (area["level"] === "province") {
            area["path"] = this.provinceDir + key + ".json";
          }
          if (area["level"] === "city") {
            //直辖市
            if(area["parentId"]==="china"){
              area["path"] = this.provinceDir + key + ".json";
            }else{
              area["path"] = this.cityDir + key + "00.json";
            }
          }
        }
        if (area["childs"]) {
          this.refreshtArea(area["childs"]);
        }
      }
    }
  }
  searchArea (areaMap, id) {
    let data;
    if (areaMap && id) {
      for (let key in areaMap) {
        const area = areaMap[key];
        if (key === id) {
          return area;
          // break;
          // continue;
        }
        if (area["childs"]) {
          data=this.searchArea(area["childs"], id);
          if(data){
            return data;
          }
        }
      }
    }
    return data;
  }
  getAreaMap(){
    if(this.areaMap){
      return this.areaMap;
    }
  }
  getCustomAreaMap(){
    return this.context.getCustomMap()
  }

  /**
   * 根据id获取自定义地图区域
   * @param customAreaMap
   * @param id
   * @returns {*}
   */
  getCustomAreaById (id) {
    if (id) {
      const customAreaMap = this.getCustomAreaMap();
      return this.searchArea(customAreaMap, id);
    }
  }
  getAreaById (id) {
    if (this.areaMap && id) {
      return this.searchArea(this.areaMap, id);
    }
  }
  getGeoMapById (id, isCustom) {
    if (id) {
      if (!this.geoMap) {
        this.geoMap = {};
      }
      if (this.geoMap[id]) {
        //存在
        return this.geoMap[id];
      } else {
        //不存在
        let area;
        let result;
        if (isCustom) {
          area = this.getCustomAreaById(id);
          const customMapService = new CustomMapService(this.context)
          // 获取区域
          const areaResult = customMapService.queryById({id: area.id})
          if (areaResult && areaResult.fileUrl) {
            // 获取区域的json
            const jsonResult = this.loadJson(areaResult.fileUrl);
            if(jsonResult){
              this.geoMap[area.id] = jsonResult;
            }
            result = jsonResult
          }
        } else {
          area = this.getAreaById(id);
          if (area && area["path"]) {
            const jsonResult = this.loadJson(area["path"]);
            if(jsonResult){
              this.geoMap[id] = jsonResult;
            }
            result = jsonResult;
          }
        }
        if (this.checkJson(result)) {
          return result
        } else {
          error('地图格式不正确')
        }
      }
    }
  }

  /**
   * 检查json格式是否正确
   * @param json
   * @returns {boolean}
   */
  checkJson (json) {
    if (json && isObject(json)) {
      const types = ["Feature", "FeatureCollection", "GeometryCollection", "LineString", "MultiLineString", "MultiPoint", "MultiPolygon", "Point", "Polygon"];
      return Object.hasOwn(json, "type") && types.includes(json.type);
    }
  }
}