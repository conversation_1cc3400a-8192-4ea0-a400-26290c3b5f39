import "./css/comp.css";
import Layer from "../Layer";
export default class FilterLayer extends Layer {
  constructor(context) {
    super(context);
  }
  initCompEvent () { }

  toInitData (list) {
    let dataMap;
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const key = item["key"];
        let value;
        if (item.type) {
          if (item.type === "model") {
            if (item.linkId) {
              const model = this.context.getModelById(item.linkId);
              if (model) {
                const datas = model.getModelDatas();
                const data = this.getRowData(datas);
                if (data && item.code) {
                  value = data[item.code];
                }
              }
            }

          } else if (item.type === "dict") {
            if (item.linkId) {
              const model = this.context.getDictById(item.linkId);
              if (model) {
                const datas = model.getModelDatas();
                const data = this.getRowData(datas);
                if (data && item.code) {
                  value = data[item.code];
                }
              }
            }
          } else if (item.type === 'dataset') {
            if (item.datasetId) {
              const dataset = this.context.getDataSetById(item.datasetId);
              if (dataset) {
                // 数据集数据
                const datas = dataset.getDatas();
                if (datas && datas.length) {
                  const property = dataset.property;
                  if (property.type === "model") {
                    const datas = dataset.getDatas();
                    const data = this.getRowData(datas);
                    if (data && item.code) {
                      value = data[item.code];
                    }
                  } else if (property.type === "file") {
                    const file = property.file;
                    if (file) {
                      if (item.datasetSheetId && (file.fileType === "xlsx" || file.fileType === "csv")) {
                        // sheet数据
                        const sheetDatas = datas.find(d => d.id === item.datasetSheetId);
                        if (sheetDatas) {
                          // 行数据
                          const data = this.getRowData(sheetDatas.datas);
                          if (data && item.code) {
                            // 单字段数据
                            value = data[item.code];
                          }
                        }
                      } else if (file.fileType === 'json') {
                        // json数据
                        const data = this.getRowData(datas);
                        if (data && item.code) {
                          value = data[item.code];
                        }
                      }
                    }
                  }
                }
              }
            }
          } else if (item.type === "param") {
            if (item.code) {
              value = this.context.getParamValue(item.code);
            }
          } else {
            if (item.value) {
              value = item.value;
            }
          }
        } else {
          if (item.value) {
            value = item.value;
          }
        }
        if (!dataMap) {
          dataMap = {};
        }
        dataMap[key] = value || "";
      }
    }
    return dataMap;
  }
}