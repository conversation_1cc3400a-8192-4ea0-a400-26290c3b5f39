import { formatDate, formatDateToStr } from "../../../utils/Util";
import { toStyle, refreshCss } from "../../../utils/StyleUtil";
import FilterLayer from "../FilterLayer";
export default class DateFilterLayer extends FilterLayer {
  constructor(context) {
    super(context);
  }
  getLength () {
    if (this.compContainer) {
      const dateContainer = $(this.compContainer).find(".lsd-date-filter");
      const text = $(dateContainer).val();
      return text.length;
    }
  }
  getDefaultProperty () {
    return {
      name: "日期",
      type: "DateFilterLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        // bg: {},
        // font: {},
        // border: {},
      },
      inits: [
        { key: "max", text: "时间选择最大值", type: "param", expr: "${max}" },
        { key: "min", text: "时间选择最小值", type: "param", expr: "${min}" },
        { key: "value", text: "时间当前值", type: "param", expr: "${value}" }
      ],
      datefilter: {
        startDate: "",
        endDate: "",
        format: 'yyyy-mm-dd',//日期的格式 yyyy-mm-dd hh:ii:ss
        language: 'zh-CN', //语言
        // initialDate: new Date(), //初始化时间
        // startDate: new Date(),//Date. 默认值：开始时间
        // endDate: new Date(),//Date. 默认值：结束时间
        // startView: 3,
        // maxView: 3,
        // minView: 0,//可以选择的最小数字 'month',
        // weekStart: 0, //一周从哪一天开始
        autoclose: true, //设置好时间之后，默认自动关闭日历
        // todayBtn: true, //是否显示'今天'按钮,默认为false
        // todayHighlight: false,//是否高亮日期
        // keyboardNavigation: false,//是否允许通过方向键改变日期
        // forceParse: false,//强制解析
        realDate: {
          futureDay: false,//禁用结束日期
          initDate: "2021-07-25",//兼容
          inputFormat: 'yyyy-mm-dd',//兼容
          outputFormat: 'yyyy-MM-dd',//兼容
        },
      },
      bind: {
        bindType: "mock",
        // isSingleRow: true, //是否单行数据
        // rowNum: 0, //默认第0行数据
        // mappings: [
        //   { key: "datetime", desc: "时间", value: "${datetime}" },
        //   { key: "maxDate", desc: "最大时间", value: "${maxDate}" },
        //   { key: "minDate", desc: "最小时间", value: "${minDate}" },
        //   { key: "year", desc: "年", value: "${year}" },
        //   { key: "month", desc: "月", value: "${month}" },
        //   { key: "date", desc: "日", value: "${date}" },
        //   { key: "day", desc: "星期", value: "${day}" },
        //   { key: "hours", desc: "小时", value: "${hours}" },
        //   { key: "minutes", desc: "分钟", value: "${minutes}" },
        //   { key: "seconds", desc: "秒", value: "${seconds}" },
        // ],
        columns: ["datetime", "year", "month", "date", "day", "hours", "minutes", "seconds", "yearMonth"],
        // mock: [
        //   { year: "2021", month: "07", day: "25" }
        // ]
      },

    }
  };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-date"></div>`);
      panelContainer.append(this.compContainer);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {
      //渲染
      if (this.property.datefilter) {
        this.refreshInitData(this.data);
        this.refreshPicker(this.compContainer, this.property.datefilter);
      }
      //样式
      const chart = this.property.chart;
      if (chart) {
        //宽高/字体
        this.refreshWH(chart, 0.5);
        this.refreshFS(chart);
        //刷新属性样式
        const sytle = toStyle(this.context, chart);
        const dateContainer = $(this.compContainer).find(".lsd-date-filter");
        if (dateContainer) {
          refreshCss(dateContainer, sytle);
        }
      }
    }
  }
  refreshBind () {
    // if (this.bindData && this.bindData.length) {
    //   this.data = this.getRowData();
    // }
    if (this.property.inits) {
      this.data = this.toInitData(this.property.inits);
    }
  };
  /**
     * 描述:时间控件修改初始化时间
     * @param dataMap
     */
  refreshInitData (dataMap) {
    if (this.property && dataMap) {
      const datefilter = this.property.datefilter;
      if (datefilter) {
        //是否禁止选择未来时间
        if (datefilter.realDate.futureDay) {
          let dateTime = new Date();
          dateTime = dateTime.setDate(dateTime.getDate());
          dateTime = new Date(dateTime)
          datefilter.endDate = formatDate(dateTime, datefilter.format);
        }
        //当前时间
        if (dataMap['value']) {
          // const dateStr = formatDateToStr(datefilter.realDate.inputFormat, String(dataMap['value']));
          const dateStr = formatDateToStr(dataMap['value'], datefilter.inputFormat);
          datefilter.realDate.initDate = formatDate(new Date(dateStr), datefilter.format);
        }
        //最大时间
        if (dataMap['max']) {
          // const maxDateStr = formatDateToStr(datefilter.realDate.inputFormat, String(dataMap['max']));
          const maxDateStr = formatDateToStr(dataMap['max'], datefilter.inputFormat);
          datefilter.endDate = formatDate(new Date(maxDateStr), datefilter.format);
        }
        //最小时间
        if (dataMap['min']) {
          // const minDateStr = formatDateToStr(datefilter.realDate.inputFormat, String(dataMap['min']));
          const minDateStr = formatDateToStr(dataMap['min'], datefilter.inputFormat);
          datefilter.startDate = formatDate(new Date(minDateStr), datefilter.format);
        }
      }
    }
  }
  /**
   * https://www.cnblogs.com/yangxiansen/p/7860019.html
   * https://blog.51cto.com/u_16021118/7470757
   * https://gitee.com/qinshenxue/datepicker
   * https://gitee.com/felixui/bootstrap-datepicker
   * https://blog.csdn.net/weixin_30501857/article/details/95204623
   */
  refreshPicker (container, datefilter) {
    if (container && datefilter) {
      container.empty();
      const dateContainer = $(`<input type="text" class="lsd-date-filter" readonly='readonly'>`);
      container.append(dateContainer);

      const filter=datefilter.realDate;
      // input 赋值 显示
      if (filter && filter.initDate) {
        const initDate=formatDate(filter.initDate,datefilter.format);
        $(dateContainer).val(initDate);
      }
      //绑定日期控件
      const self = this;
      $(dateContainer).datetimepicker(datefilter).on('changeDate', function (ev) {
        datefilter.realDate.initDate = formatDate(ev.date, datefilter.format);
        const outdate = formatDate(ev.date, filter.outputFormat);
        const year = ev.date.getFullYear();
        const month = self.appendzero(ev.date.getMonth() + 1);
        const date = ev.date.getDate();
        const day = ev.date.getDay();
        const hours = ev.date.getHours();
        const minutes = ev.date.getMinutes();
        const seconds = ev.date.getSeconds();
        const relationJson = {
          datetime: String(outdate),
          year: String(year),
          month: String(month),
          date: String(date),
          day: String(day),
          hours: String(hours),
          minutes: String(minutes),
          seconds: String(seconds),
          yearMonth: String(year) + String(month)
        };
        //超链接
        self.clickJump(relationJson);
        //联动
        self.clickRelation(relationJson);
      });
    }
  }
  /*
    补零
    */
  appendzero (obj) {
    if (obj < 10) return "0" + "" + obj;
    else return obj;
  }
}