import { toStyle, refreshCss } from "../../../utils/StyleUtil";
import Expression from "../../../utils/Expression";
import FilterLayer from "../FilterLayer";
export default class RadioFilterLayer extends FilterLayer {
  constructor(context) {
    super(context);
    this.expression = new Expression();
  }
  getLength() {
    if (this.length) {
      return this.length;
    }
  }
  getFontSize(width, height, length) {
    let multiplier = 0;
    if (length) {
      multiplier = 1 / length;
    }
    return Math.min(height, width) * multiplier.toFixed(2);
  }
  getDefaultProperty() {
    return {
      name: "单选",
      type: "RadioFilterLayer",//SingleTableFilterLayer
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        // bg: {},
        // font: {},
        // border: {},
        // cell: {bg:{},font:{},border:{},},
      },
      inits: [{ key: "key", text: "单选初始化值", type: "param", expr: "${key}" }],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "key", value: "${key}", desc: "编码" },
          { key: "text", value: "${text}", desc: "名称" }
        ],
        columns: ["key", "text"],
        mock: [
          { key: "36", text: "江西" },
          { key: "3601", text: "南昌" }
        ],
      }
    }
  };
  initCompContainer(panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-radio lsd-single-table-filter"></div>`);
      panelContainer.append(this.compContainer);
    }
  };
  refreshCompCss() {
    if (this.compContainer && this.property) {
      const chart = this.property.chart;
      if (chart) {
        //宽高/字体
        this.refreshWH(chart);
        this.refreshFS(chart);
        //刷新样式
        this.refreshChart(this.compContainer, chart);
      }
    }
  }
  refreshBind() {
    if (this.compContainer && this.bindData && this.bindData.length) {
      this.compContainer.empty();
      this.refreshOption(this.compContainer, this.bindData);
    }
    if (this.property.inits) {
      const data = this.toInitData(this.property.inits);
      this.refreshInitData(data);
    }
  };
  refreshOption(selectContainer, list) {
    if (selectContainer && list && list.length) {
      this.length = 0;
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const optionContainer = $(`<div class="lsd-single-table-item" >` + item.text + `</div>`);
        selectContainer.append(optionContainer);
        //文本长度
        this.length = this.length + item.text.length;

        const self = this;
        $(optionContainer).on("click", function (ui, event) {
          if (item["key"] || 0 === item["key"]) {
            self.refreshValue(item["key"]);
          }
          //超链接
          self.clickJump(item);
          //联动
          self.clickRelation(item);
        });
      }
    }
  }
  /**
   * 描述:刷新选中值
   * @param value
   */
  refreshValue(selectValue) {
    if (selectValue) {
      this.$$selectValue = selectValue;
    }
    if ((this.$$selectValue || 0 == this.$$selectValue) && this.bindData && this.bindData.length) {
      for (let selectIndex = 0; selectIndex < this.bindData.length; selectIndex++) {
        const bindDataItem = this.bindData[selectIndex];
        if (bindDataItem && bindDataItem["key"] === this.$$selectValue) {
          this.$$selectIndex = selectIndex;
        }
      }
    }
    if ((this.$$selectIndex || 0 === this.$$selectIndex) && this.refreshChart) {
      const chart = this.property.chart;
      this.refreshChart(this.compContainer, chart);
    }
  }
  /**
     * 描述:刷新表格样式
     */
  refreshChart(container, chart) {
    if (container && chart) {
      //
      const style = toStyle(this.context, chart);
      refreshCss(container, style);
      //刷新选择样式
      const filterItemSelector = container.find(".lsd-single-table-item");
      if (filterItemSelector && chart && chart.cell) {
        const self = this;
        var filterStyle = toStyle(self.context, chart.cell);
        var filterSelectStyle = {};
        if (chart.cell.selected) {
          filterSelectStyle = toStyle(self.context, chart.cell.selected);
        }
        filterItemSelector.each(function (index, element) {
          $(element).removeAttr("style");
          const itemStyle = {};
          if (filterStyle) {
            $.extend(true, itemStyle, filterStyle);
          }
          if (self.$$selectIndex === index || (0 === self.$$selectIndex && 0 === index) && filterSelectStyle) {
            $.extend(true, itemStyle, filterSelectStyle);
          }
          refreshCss(element, itemStyle);
        });
      }
    }
  }

  /**
     * 描述:初始化属性
     * @param initMap
     */
  refreshInitData(initMap) {
    if (initMap && initMap["key"]) {
      this.refreshValue(initMap["key"]);
    }
  }

}