import { toStyle, refreshCss } from "../../../utils/StyleUtil";
import Expression from "../../../utils/Expression";
import FilterLayer from "../FilterLayer";
export default class SelectFilterLayer extends FilterLayer {
  constructor(context) {
    super(context);
    this.expression = new Expression();
    this.timer = null;
  }
  getLength() {
    if (this.length) {
      return this.length;
    }
  }
  getFontSize(width, height, length) {
    let multiplier = 0;
    if (length) {
      multiplier = 1 / length;
    }
    return Math.min(height, width) * multiplier.toFixed(2);
  }
  getDefaultProperty() {
    return {
      name: "选择",
      type: "SelectFilterLayer", //SingleSelectFilterLayer
      left: 0,
      top: 0,
      height: 300,
      width: 400,
      chart: {
        // bg: {},
        // font: {},
        // border: {},
      },
      inits: [
        { key: "key", text: "单选初始化值", type: "param", expr: "${key}" },
      ],
      bind: {
        bindType: "mock",
        mappings: [
          { key: "key", value: "${key}", desc: "编码" },
          { key: "text", value: "${text}", desc: "名称" },
        ],
        columns: ["key", "text"],
        mock: [
          { key: "36", text: "江西" },
          { key: "3601", text: "南昌" },
        ],
      },
    };
  }
  initCompContainer(panelContainer) {
    if (panelContainer) {
      this.selectContainer = $(`<select class="layer-select"></select>`);
      panelContainer.append(this.selectContainer);
      //联动触发
      const self = this;
      console.log(this);
      $(this.selectContainer).on("change", function (event) {
        console.log(self);
        self.$$selectValue = $(this).val();
        const dataItem = self.getSelectData(self.$$selectValue);
        //超链接
        self.clickJump(dataItem);
        //联动
        self.clickRelation(dataItem);
      });

      // 写一个循环的定时器，如果已经设置了联动，就触发一次clickRelation
      if (self.timer) {
        clearInterval(self.timer);
      }
      self.timer = setInterval(() => {
        if (self.checkIsLink()) {
          this.startRelation();
          clearInterval(self.timer);
          self.timer = null;
        }
      }, 1000);

      // 预览直接触发一次
      setTimeout(() => {
        this.startRelation();
      }, 2000);

      // 检查事件是否冒泡
      // document.addEventListener('click', function (event) {
      //   // 检查事件对象的stopPropagation方法是否被调用过
      //   if (event.stopPropagation) {
      //     console.log('stopPropagation方法已被调用。');
      //   } else if (event.cancelBubble) {
      //     console.log('cancelBubble属性已被设置为true。');
      //   } else {
      //     console.log('事件冒泡正常。');
      //   }
      // }, true); // 注册为捕获阶段的监听器
    }
  }

  startRelation() {
    let self = this;
    const selectContainer = $(this.selectContainer);
    self.$$selectValue = selectContainer.val();

    const dataItem = self.getSelectData(self.$$selectValue);
    console.log({ dataItem });
    //超链接
    self.clickJump(dataItem);
    //联动
    self.clickRelation(dataItem);
  }

  checkIsLink() {
    let isLink = false;
    if (
      this.property &&
      this.property.relation &&
      this.property.relation.paramLink &&
      this.property.relation.paramLink.params &&
      this.property.relation.paramLink.params.length > 0 &&
      this.property.relation.isOpen
    ) {
      const firstParam = this.property.relation.paramLink.params[0];
      if (firstParam && firstParam.code && firstParam.value) {
        isLink = true;
        console.log("已经设置联动");
      }
    }

    return isLink;
  }

  refreshCompCss() {
    if (this.selectContainer) {
      const chart = this.property.chart;
      if (chart) {
        //宽高/字体
        this.refreshWH(chart, 0.5);
        this.refreshFS(chart, 0.5);
        //刷新样式
        this.refreshChart(this.selectContainer, chart);
      }
    }
  }

  refreshBind() {
    if (this.selectContainer && this.bindData && this.bindData.length) {
      this.selectContainer.empty();
      this.refreshOption(this.selectContainer, this.bindData);
    }
    if (this.property.inits) {
      const data = this.toInitData(this.property.inits);
      this.refreshInitData(data);
    }
  }
  refreshOption(selectContainer, list) {
    if (selectContainer && list && list.length) {
      this.length = 0;
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const optionContainer = $(
          `<option style=" background-color: transparent !important;" class="lsd-select-table-filter-item" value="` +
            item.key +
            `">` +
            item.text +
            `</option>`
        );
        selectContainer.append(optionContainer);
        //文本长度
        if (this.length < item.text.length) {
          this.length = item.text.length;
        }
      }
    }
  }
  refreshChart(container, chart) {
    if (container && chart) {
      const style = toStyle(this.context, chart);
      refreshCss(container, style);
      //刷新下拉列表中字体大小
      const optionContainer = container.find(".lsd-select-table-filter-item");
      if (optionContainer && chart && chart.font && chart.font.optionFontSize) {
        const optionStyle = { "font-size": chart.font.optionFontSize };
        refreshCss(optionContainer, optionStyle);
      }
    }
  }
  refreshInitData(initMap) {
    if (initMap && initMap["key"]) {
      this.refreshValue(initMap["key"]);
    }
  }
  /**
   * 描述:刷新选中值
   * @param value
   */
  refreshValue(selectValue) {
    if (selectValue) {
      this.$$selectValue = selectValue;
    }
    if (this.selectContainer && this.$$selectValue) {
      $(this.selectContainer).val(this.$$selectValue);
    }
  }
  /**
   * 获取
   * @param {} selectValue
   * @returns
   */
  getSelectData(selectValue) {
    if (this.bindData && this.bindData.length) {
      for (let index = 0; index < this.bindData.length; index++) {
        const bindDataItem = this.bindData[index];
        if (bindDataItem && bindDataItem["key"] === selectValue) {
          return bindDataItem;
        }
      }
    }
  }
}
