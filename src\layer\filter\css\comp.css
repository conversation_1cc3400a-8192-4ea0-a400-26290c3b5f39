/*单选列表工具*/
.lsd-single-table {
  display: inline-flex;
}

.lsd-single-table-item {
  float: left;
  padding: 10px;
  user-select: none;
}

/*.lsd-single-table-item:hover,*/
/*.lsd-single-table-item:active {*/
/*  color: #0A246A;*/
/*}*/

/*日期控件*/
.dropdown-menu {
  box-sizing: border-box;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgb(0 0 0 / 18%);
  box-shadow: 0 6px 12px rgb(0 0 0 / 18%);
}

/* 
* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.dropdown-menu table {
  background-color: transparent;
  border-collapse: collapse;
  border-spacing: 0;
}

.dropdown-menu thead {
  display: table-header-group;
  vertical-align: middle;
  border-color: inherit;
}

.dropdown-menu tbody {
  display: table-row-group;
  vertical-align: middle;
  border-color: inherit;
}

.dropdown-menu tfoot {
  display: table-footer-group;
  vertical-align: middle;
  border-color: inherit;
} 

*/

.table-condensed>thead>tr>th,
.table-condensed>tbody>tr>th,
.table-condensed>tfoot>tr>th,
.table-condensed>thead>tr>td,
.table-condensed>tbody>tr>td,
.table-condensed>tfoot>tr>td {
  padding: 5px;
}
/* 
th {
  text-align: left;
}

td,
th {
  padding: 0;
}

.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: "Glyphicons Halflings";
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-left {
  font-family: "fd-font" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-right {
  font-family: "fd-font" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-left:before {
  content: "\E749";
} 
*/