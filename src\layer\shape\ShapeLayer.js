import { toStyle, refreshCss } from "../../utils/StyleUtil";
import "./css/comp.css";
import Layer from "../Layer";
export default class ShapeLayer extends Layer {
  constructor(context) {
    super(context);
  }
  // getDefaultProperty () {
  //   return {
  //     name: "形状",
  //     type: "ShapeLayer",
  //     left: 0, top: 0, height: 300, width: 400,
  //   }
  // };
  initCompContainer (panelContainer) {
    if (panelContainer) {
      this.compContainer = $(`<div class="layer-shape"></div>`);
      panelContainer.append(this.compContainer);
    }
  };
  refreshCompCss () {
    if (this.compContainer && this.property) {

      const chart = this.property.chart;
      if(chart){
        //宽高/字体
        this.refreshWH(chart);
        this.refreshFS(chart);
        //样式属性
        if (chart) {
          const style = toStyle(this.context, chart)
          refreshCss(this.compContainer, style);
        }
      }
    }
  };

  refreshBind () { };

  refreshConditionCss (conditionItem) {
    if (this.compContainer && conditionItem) {
      const style = toStyle(this.context, conditionItem)
      refreshCss(this.compContainer, style);
    }
  }
}