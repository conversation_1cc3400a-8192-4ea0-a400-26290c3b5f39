import ShapeLayer from "../ShapeLayer";
export default class CircularShapeLayer extends ShapeLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "圆形",
      type: "CircularShapeLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        bg:{bgType:"color",color:{type:"color",color:"#CCC"}},
        border:{
          borderRadius: "50%"
        }
      },
    }
  };
}