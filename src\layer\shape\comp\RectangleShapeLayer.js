import ShapeLayer from "../ShapeLayer";
export default class RectangleShapeLayer extends ShapeLayer {
  constructor(context) {
    super(context);
  }
  getDefaultProperty () {
    return {
      name: "矩形",
      type: "RectangleShapeLayer",
      left: 0, top: 0, height: 300, width: 400,
      chart: {
        bg:{bgType:"color",color:{type:"color",color:"#CCC"}},
        // border:{},
      },
    }
  };
}