export function getMenuComponent() {
  const base = {
    name: "基础",
    isOpen: true,
    child: [
      { name: "全屏", type: "FullScreenLayer", icon: "&#xe018;" },
      { name: "组合", type: "GroupLayer", icon: "&#xe63c;" },
      { name: "链接", type: "IframeLayer", icon: "&#xe742;" },
      { name: "3D链接", type: "Iframe3DLayer", icon: "&#xe742;" },
      { name: "数字", type: "NumLayer", icon: "&#xe8c4;" }, //Flip,Percent
      // { name: '百分比', type: 'PercentLayer', icon: '&#xe682;' },
      { name: "文本", type: "TextLayer", icon: "&#xe015;" },
      { name: "图片", type: "ImageLayer", icon: "&#xe67b;" },
      { name: "边框", type: "BorderLayer", icon: "&#xe6ff;" },
      { name: "矢量图", type: "SvgLayer", icon: "&#xe67b;" },
      { name: "SVG动画", type: "SvgAnimationLayer", icon: "&#xe67b;" },
      { name: "图标", type: "IconLayer", icon: "&#xe7ae;" },
      { name: "视频", type: "VideoLayer", icon: "&#xe677;" },
      { name: "直播", type: "XgPlayerLayer", icon: "&#xe677;" },
      { name: "萤石流", type: "YsPlayerLayer", icon: "&#xe677;" },
      { name: "表格", type: "TableLayer", icon: "&#xe60e;" }, //SimpleTableLayer
      { name: "跑马灯", type: "MarqueeLayer", icon: "&#xe6a0;" },

      { name: "树形", type: "TreeLayer", icon: "&#xe60d;" },
      { name: "栅格", type: "GridLayer", icon: "&#xe60e;" },
      { name: "日期", type: "DateLayer", icon: "&#xe60e;" },
    ],
  };
  const shape = {
    name: "形状",
    isOpen: false,
    child: [
      { name: "圆形", type: "CircularShapeLayer", icon: "&#xe016;" },
      // { name: '椭圆', type: 'OvalShapeLayer', icon: '&#xe791;' },
      { name: "矩形", type: "RectangleShapeLayer", icon: "&#xe790;" },
    ],
  };
  const filter = {
    name: "过滤",
    isOpen: false,
    child: [
      { name: "日期选择", type: "DateFilterLayer", icon: "&#xe660;" },
      { name: "单项选择", type: "RadioFilterLayer", icon: "&#xe024;" },
      { name: "下拉选择", type: "SelectFilterLayer", icon: "&#xe027;" },
    ],
  };
  const chart = {
    name: "图表",
    isOpen: true,
    child: [
      { name: "折线图", type: "LineChartLayer", icon: "&#xe618;" },
      { name: "折柱混合", type: "LineBarChartLayer", icon: "&#xe607;" },
      { name: "柱状图", type: "BarChartLayer", icon: "&#xe617;" },
      { name: "3D柱状图", type: "Bar3DChartLayer", icon: "&#xe617;" },
      { name: "条形图", type: "BarYChartLayer", icon: "&#xe600;" },
      { name: "柱状象形图", type: "BarPictorChartLayer", icon: "&#xe601;" },
      { name: "条形象形图", type: "BarPictorYChartLayer", icon: "&#xe639;" },
      { name: "环形柱状图", type: "BarDoughnutChartLayer", icon: "&#xe603;" },
      { name: "饼图", type: "PieChartLayer", icon: "&#xe60f;" },
      { name: "3D饼图", type: "Pie3DChartLayer", icon: "&#xe60f;" },
      { name: "玫瑰图", type: "PieRoseChartLayer", icon: "&#xe621;" },
      { name: "环形图", type: "PieDoughnutChartLayer", icon: "&#xe603;" },
      {
        name: "环形图断开",
        type: "PieDoughnutGapChartLayer",
        icon: "&#xe603;",
      },
      { name: "嵌套环形图", type: "PieNestChartLayer", icon: "&#xe60b;" },

      { name: "散点/气泡", type: "ScatterChartLayer", icon: "&#xe61a;" },
      { name: "雷达图", type: "RadarChartLayer", icon: "&#xe60a;" },
      { name: "仪表盘", type: "GaugeChartLayer", icon: "&#xe610;" },
      { name: "漏斗图", type: "FunnelChartLayer", icon: "&#xe609;" },
      { name: "K线图", type: "CandlestickChartLayer", icon: "&#xe608;" },
      { name: "桑基图", type: "SankeyChartLayer", icon: "&#xe61d;" },
      { name: "关系图", type: "GraphChartLayer", icon: "&#xe624;" },
      // { name: '路径图', type: 'LinesChartLayer', icon: '&#xe624;' },
      { name: "盒须图", type: "BoxplotChartLayer", icon: "&#xe61f;" },
      { name: "平行坐标系", type: "ParallelChartLayer", icon: "&#xe63a;" },
      { name: "词云", type: "WordChartLayer", icon: "&#xe616;" },
      { name: "水晶球", type: "LiquidChartLayer", icon: "&#xe637;" },
    ],
  };
  const map = {
    name: "地图",
    isOpen: true,
    child: [
      { name: "中国色彩", type: "ZgScMapLayer", icon: "&#xe613;" },
      { name: "中国热力", type: "ZgRlMapLayer", icon: "&#xe614;" },
      { name: "中国气泡", type: "ZgQpMapLayer", icon: "&#xe615;" },
      { name: "中国飞线", type: "ZgFxMapLayer", icon: "&#xe636;" },
      { name: "中国3D", type: "Zg3DMapLayer", icon: "&#xe613;" },
    ],
  };
  return [base, shape, filter, chart, map];
}
