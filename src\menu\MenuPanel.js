import { uuid } from "../utils/Util";
import CompPanel from "./panel/CompPanel";
import LayerPanel from "./panel/LayerPanel";
import ParamSetPanel from "./panel/ParamSetPanel";
import DataSetPanel from "./panel/DataSetPanel";
import DataSetPanelNew from "./panel/dataset/DataSetPanel";
import DictSetPanel from "./panel/DictSetPanel";
import CompSetPanel from "./panel/CompSetPanel";
import AnimationPanel from "./panel/animation/AnimationPanel";
import "./css/menu.css";
export default class MenuPanel {
  constructor(context) {
    this.context = context;
  }
  initPanel(parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="menu-container"></div>`);
      parentContainer.append(this.container);
    }
  }
  /**
   * 描述:刷新面板
   */
  refreshPanel() {
    if (this.container) {
      this.container.empty();
      //获取数据
      const menuTools = this.getMenuTools(this.context);
      //添加tool面板
      this.toolContainer = $(`<div class="menu-tool"></div>`);
      this.container.append(this.toolContainer);
      //添加main面板
      this.mainContainer = $(`<div class="menu-main"></div>`);
      this.container.append(this.mainContainer);
      //添加漂浮面板
      this.floatContainer = $(`<div class="menu-float"></div>`);
      this.container.append(this.floatContainer);
      $(this.floatContainer).addClass("menu-drawer");
      //刷新数据
      if (menuTools && menuTools.length) {
        for (let i = 0; i < menuTools.length; i++) {
          const toolItem = menuTools[i];
          toolItem.id = uuid();
          const toolItemContainer = $(`
          <div class="menu-tool-icon ${toolItem.className}" id="` + toolItem.id + `"  title="` + toolItem.name + `">
              <i class="ft-font ${toolItem.className}">` + toolItem.icon + ` </i>
          </div>`);
          this.toolContainer.append(toolItemContainer);
          const self = this;
          $(toolItemContainer).on("click", function (event) {
            toolItem.isOpen = !toolItem.isOpen
            self.refreshChecked(menuTools[i]);
          });
        }
        this.refreshChecked(menuTools[0]);
      }
    }
  }
  refreshChecked(item) {
    if (this.toolContainer) {
      this.toolContainer.find(".menu-tool-icon").removeClass("selected");
      if (item && item.id) {
        this.toolContainer.find("#" + item.id).addClass("selected");
      }
    }
    //面板类型
    if (item && item.type && item.type === "panel") {
      if (this.mainContainer) {
        this.mainContainer.empty();
        this.toolItem = item;
        // 测试
        // if(item.panel instanceof LayerPanel){
        //   console.info("LayerPanel");
        // }
        if (item && item.panel) {
          item.panel.initPanel(this.mainContainer);
          item.panel.refreshPanel();
        }
      }
    }
    //悬浮类型
    if (item && item.type && item.type === "float") {
      if (this.floatContainer) {
        this.floatContainer.empty();
        // item.style.display = item.style.display === 'none' ? 'block' : 'none';
        // $(this.floatContainer).css("display",item.style.display);
        if (item.float) {
          //位置
          if (item.float.isBottom) {
            $(this.floatContainer).css("bottom", "0px");
          }
          //大小(高度)
          if (item.float.height) {
            $(this.floatContainer).css("height", item.float.height);
          }
          //大小(宽度)
          if (item.float && item.float.width) {
            $(this.floatContainer).css("width", item.float.width);
          } else {
            $(this.floatContainer).css("width", "200px");
          }
        }
        if (item.panel) {
          item.panel.initPanel(this.floatContainer, function () {
            item.isOpen = false;
            self.refreshOpen(item);
          });
        }
        this.refreshOpen(item);
        // 点击其它任何区域关闭悬浮
        const self = this;
        $(document).mousedown((event) => {
          var target = $(event.target);
          if (!target.closest('.drawer-open').length) {
            // 防止点击组件集连续触发
            if (target[0].className === 'ft-font signIcon' || target[0].className === 'menu-tool-icon signIcon selected') { }
            else {
              item.isOpen = false;
            }
            self.refreshOpen(item);
          }
        })

      }
    }
  }
  refreshOpen(item) {
    if (item) {
      if (item.isOpen) {
        $(this.floatContainer).addClass("drawer-open");
        if (item.panel) {
          item.panel.refreshPanel();
        }
      } else {
        $(this.floatContainer).removeClass("drawer-open");
        $(this.floatContainer).css("width", "0px");
      }
    }
  }
  refreshLinkPanel() {
    if (this.toolItem && this.toolItem.panel && (this.toolItem.panel instanceof LayerPanel)) {
      this.toolItem.panel.refreshPanel();
    }
  }

  getMenuTools(context) {
    return [
      { name: "组件", type: "panel", icon: "&#xe6d6;", panel: new CompPanel(context) },
      { name: "图层列表", type: "panel", icon: "&#xe77f;", panel: new LayerPanel(context) },
      { name: "参数集", type: "panel", icon: "&#xe6a3;", panel: new ParamSetPanel(context) },
      { name: "数据集", type: "panel", icon: "&#xe6ab;", panel: new DataSetPanelNew(context) },
      // { name: "数据集", type: "panel", icon: "&#xe6ab;", panel: new DataSetPanel(context) },
      // { name: "字典集", type: "panel", icon: "&#xe6ae;", panel: new DictSetPanel(context) },//&#xe7fc; &#xe7de;
      //isFollow 是否跟随 
      //isDrawer 是否抽屉
      //drawer: { isBottom: true, width: "400", height: "400" } 
      { name: "组件集", type: "float", icon: "&#xe6a5;", panel: CompSetPanel.getInstance(context), float: { isBottom: false, width: "820", height: "724" }, className: "signIcon" },
      { name: "自定义动画", type: "panel", icon: "&#xe6b4;", panel: new AnimationPanel(context) },
    ]
  }
}