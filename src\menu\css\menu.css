/*===========================菜单样式======================*/
.menu-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    background: #191919;
    color: #fff;
    z-index: 9;
}

/****抽象数据集公共样式****/
.menu-set-card-wrap .menu-set-card-head {
    height: 32px;
    line-height: 32px;
    border-bottom: 1px solid #414243;
    padding: 6px 16px;
    background: #262727;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.menu-set-card-head .menu-set-head-title {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #CFD3DC;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.menu-set-card-head .menu-set-head-preview {
    padding-right: 10px;
    font-weight: 400;
    font-size: 12px;
    color: #0952ff;
    line-height: 30px;
    white-space: nowrap;
}

.menu-set-card-wrap .menu-set-card-body {
    padding: 10px 16px 0 16px;
    background: #262727;
    display: flex;
    flex-direction: column;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

/*############左侧菜单[内容/编辑]###########*/
.menu-chart-item {
    width: 100%;
    line-height: 20px;
    box-sizing: border-box;
    display: flex;
    margin-bottom: 10px;
}

.menu-chart-item .menu-chart-label {
    min-width: 50px;
    margin-right: 5px;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
    font-weight: 400;
    font-size: 12px;
    color: #A3A6AD;
}

.menu-chart-item .menu-chart-control {
    flex: 1;
    height: 20px;
    display: flex;
    /* 垂直居中 */
    align-items: center;
    /* 水平居中 */
    justify-content: center;
    background: #1D1D1D;
    position: relative;
}

/****************自定义[select]的样式 ****************/
.menu-chart-control select {
    line-height: 20px;
    width: 100%;
    /* 去掉焦点时的轮廓 */
    outline: none;
    /* 移除iOS上的内置样式 */
    -webkit-appearance: none;
    /* 移除Firefox上的内置样式 */
    -moz-appearance: none;
    /* 将来浏览器标准化后使用 */
    appearance: none;
    /* 设置背景色为透明 */
    background-color: transparent;

    height: 20px;
    font-weight: 400;
    font-size: 12px;
    color: #CFD3DC;

    border-radius: 2px 2px 2px 2px;
    border: 1px solid #4C4D4F;
    padding: 0 8px;
}

.menu-chart-control select option {
    outline: none;
    line-height: 20px;
    width: 100%;
    /* 去掉焦点时的轮廓 */
    /* 移除iOS上的内置样式 */
    -webkit-appearance: none;
    /* 移除Firefox上的内置样式 */
    -moz-appearance: none;
    /* 将来浏览器标准化后使用 */
    appearance: none;
    /* 去除边框 */
    border: none;
    /* 设置背景色为透明 */
    background-color: #191919;

    height: 20px;
    font-weight: 400;
    font-size: 12px;
    color: #CFD3DC;
}

.menu-chart-control:has(.chart-select):after {
    content: "\e790";
    font-family: "ft-font";
    font-size: 8px;
    transform: rotateZ(-90deg);
    color: #56585b;
    position: absolute;
    z-index: 1;
    display: block;
    top: 0;
    right: 10px;
}

/****************自定义[input]的样式 ****************/
.menu-chart-control input[type="text"],
.menu-chart-control input[type="number"] {
    width: 100%;
    /* 去除点击输入框时产生的默认边框 */
    outline: none;
    /* 移除iOS上的内置样式 */
    -webkit-appearance: none;
    /* 移除Firefox上的内置样式 */
    -moz-appearance: none;
    /* 将来浏览器标准化后使用 */
    appearance: none;
    /* 设置背景色为透明 */
    background-color: transparent;

    height: 20px;
    font-weight: 400;
    font-size: 12px;
    color: #CFD3DC;

    border-radius: 2px 2px 2px 2px;
    border: 1px solid #4C4D4F;
    padding: 2px 8px;
}

.menu-chart-control input[type="text"]::-webkit-input-placeholder,
.menu-chart-control input[type="number"]::-webkit-input-placeholder {
    color: #8D9095;
}

.menu-chart-control input[type="text"]::-moz-placeholder,
.menu-chart-control input[type="number"]::-moz-placeholder {
    color: #8D9095;
}

.menu-chart-control input[type="text"]::-ms-input-placeholder,
.menu-chart-control input[type="number"]::-ms-input-placeholder {
    color: #8D9095;
}

.menu-chart-control select:focus,
.menu-chart-control input:focus {
    border: 1px solid #409EFF;
}

.menu-chart-control input[disabled] {
    background: #262727;
}

/*menu-tool*/
.menu-tool {
    width: 44px;
    height: 100%;
    border-right: 1px solid #0e0e0e;
    border-top: 1px solid #0e0e0e;
    padding-top: 5px; /* margin-top: 5px; */
}

.menu-comp-container {
    width: 220px
}

/*menu-main*/
.menu-main {
    width: 220px;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto
}

/* 除数据集以外的menu面板标题 */
.menu-main .menu-head-title {
    width: 220px;
    height: 50px;
    padding: 14px 20px;
    background: #191919;
    border-top: 1px solid #0e0e0e;
    border-bottom: 1px solid #0e0e0e;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    color: #E5EAF3;
}

/*ment-float*/
.menu-float {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    /* 内容超出时可以滚动 */
    overflow-y: auto;
    /* position: absolute; */
    /* display: none; */
}

.menu-drawer {
    /* 抽屉固定在页面窗口上 */
    position: fixed;
    left: 276px;
    top: 64px;
    background: #191919;
    /* background-color: #353f50; */
    /* box-shadow: 5px -5px 10px #1c2129cc; */
    /* 抽屉展开/关闭的动画效果 */
    /*transition: width 0.3s ease-out;*/
    /* 初始状态下抽屉处于收起状态 */
    width: 0;
}

/* 
.drawer-open {
  width: 200px;
}
.drawer-close{
  width: 0px;
} */
/****************menu-mouse*******************/
.menu-mouse-wrap {
    /* position: absolute(绝对);relative(相对);*/
    position: absolute;
    padding: 10px 0;
    width: 150px;
    list-style: none;
    border-radius: 2px;
    color: #ccc;
    z-index: 2000;
    cursor: pointer;
    background: rgba(43, 51, 64, 0.8);
    box-shadow: 5px 5px 10px #1c2129cc;

}

.menu-mouse-item {
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
}

.menu-mouse-item .icon-font,
.menu-mouse-item .fd-font {
    /* margin-right: 8px; */
    display: inline-block;
    /* width: 20px;
    height: 20px; */
}

.menu-mouse-item:hover {
    background: rgb(63, 75, 95);
}

.menu-mouse-active {
    display: block
}

/* 数据集标题 */
.menu-set-container .menu-set-head-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    padding: 11px 20px;
    background: #191919;
    border-top: 1px solid #0e0e0e;
    border-bottom: 1px solid #0e0e0e;
}

.menu-set-container .menu-set-head-wrap .menu-set-head-title {
    width: auto;
    font-weight: 400;
    font-size: 16px;
    color: #E5EAF3;
}

/* 单图标默认样式 */
.menu-set-card-head .menu-set-head-icon {
    font-size: 8px;
    text-align: center;
    color: #84868b;
}

/* 折叠图标 */
.menu-set-card-head .head-icon-fold {
    font-size: 12px;
    text-align: center;
    color: #84868b;
    margin-right: 6px;
}

.menu-set-card-head[switch='close'] .head-icon-fold {
    transform: rotateZ(-90deg);
}

.menu-set-card-head[switch='open'] .head-icon-fold {
    transform: rotateZ(0);
}

.menu-set-container .menu-set-head-wrap .menu-set-head-add {
    font-weight: 600;
    font-size: 12px;
    color: #409EFF;
    line-height: 17px;
    text-align: center;
}

/*############左侧菜单[属性-块/背景]###########*/
.menu-set-body .menu-chart-wrap {
    box-sizing: border-box;
    border-radius: 3px;
    margin: 8px;
}

/* 图层图标组样式 */
.layer-head-wrap .head-layer-icon {
    text-align: center;
    color: #ffffff;
    font-size: 13px;
}

.layer-head-wrap .head-layer-icon[switch='close'] {
    transform: rotateZ(-90deg);
}

.layer-head-wrap .head-layer-icon[switch='open'] {
    transform: rotateZ(0deg);
}

.layer-head-wrap .head-layer-icon i {
    font-size: 14px;
}

.layer-head-wrap .head-layer-icon[visibility] {
    visibility: hidden;
}

.layer-head-wrap:hover .head-layer-icon[visibility] {
    visibility: visible;
}

/*****数据集 字典集  项目标题*****/
.set-item {
    min-height: 20px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 0 8px;
    background: #262727;
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #414243;
    font-weight: 400;
    font-size: 12px;
    color: #6C6E72;
}

.set-item:hover {
    background: #3a3b3c;
}

.set-item:last-child {
    margin-bottom: 0;
}

/*###############[菜单tool面板]###############*/
.menu-tool .menu-tool-icon {
    width: 36px;
    height: 36px;
    font-size: 25px;
    text-align: center;
    line-height: 36px;
    border-radius: 4px 4px 4px 4px;
    margin-bottom: 16px;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
}

.menu-tool-icon i {
    font-size: 20px;
}

.menu-tool-icon:hover {
    color: #ffffff;
    background: #409EFF;
}

.menu-tool .selected {
    color: #ffffff;
    background: #409EFF;
}

/*###############[菜单main面板]###############*/

/****************menu-box(盒子)*******************/
.menu-box {
    box-sizing: border-box;
    margin-left: -1px;
    margin-right: -1px;
}

.menu-box .box-item {
    box-sizing: border-box;
    width: 73px;
    height: 77px;
    float: left;
    padding-left: 1px;
    padding-right: 1px;
    padding-top: 2px;
}

.box-item .item-block:hover {
    background: #1D3043;
    cursor: pointer;
}

.box-item .item-block {
    /* margin-top: 2px; */
    width: 100%;
    height: 100%;
    text-align: center;
    background: #262727;
}

.box-item .item-icon {
    width: 100%;
    line-height: 40px;
    font-size: 20px;
}

.box-item .item-title {
    font-size: 12px;
}

/*###############[组件]###############*/

.menu-comp-wrap .menu-comp-head {
    line-height: 40px;
    background: #303030;
    font-weight: 400;
    font-size: 14px;
    color: #E5EAF3;
    border-bottom: 1px solid #0e0e0e;
}

.menu-comp-wrap .menu-comp-head-title {
    width: 100%;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.menu-comp-head .menu-comp-icon {
    width: 16px;
    text-align: center;
    color: #CFD3DC;
    margin-left: 16px;
    margin-right: 6px;
}

.menu-comp-head .menu-comp-icon i {
    font-size: 12px;
}

.menu-comp-head .menu-comp-icon[switch='close'] {
    transform: rotateZ(-90deg);
}

.menu-comp-head .menu-comp-icon[switch='open'] {
    transform: rotateZ(0deg);
}


/*###############[组件模板]###############*/
.drawer-main {
    box-sizing: border-box;
    height: 100%;
    width: 100%;
    padding: 20px;
}

/* .drawer-wrap .drawer-left {
  box-sizing: border-box;
  height: 100%;
  width: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  border-right: 1px solid rgba(42, 171, 210, 0.80);
}

.drawer-wrap .drawer-right {
  box-sizing: border-box;
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
} */

.queryModel {
    display: flex;
    position: relative;
}

/* 图标 */
.queryModel .searchIcon {
    position: absolute;
    left: 10px;
    top: 7px;
    color: #CFD3DC;
    font-size: 13px;
    height: 18px;
    padding-top: 3px;
}

.queryModel .closeIcon {
    height: 32px;
    line-height: 32px;
    color: #A3A6AD;
    font-size: 20px;
    cursor: pointer;
}

/* 输入框 */
.queryModel input {
    width: 100%;
    height: 32px;
    border-radius: 4px 4px 4px 4px;
    outline: none;
    border: 1px solid #4C4D4F;
    padding-left: 35px;
    background: none;
    margin-right: 20px;
    color: #E5EAF3;
}

.queryModel input div {
    font-weight: 400;
    font-size: 14px;
    color: #8D9095;
}

.queryModel input:focus {
    outline: none;
    border: 1px solid #409EFF;
}

/* 按钮 */
.queryModel button {
    background-color: #409EFF;
    border-radius: 4px 4px 4px 4px;
    padding: 6px 16px;
    font-weight: 500;
    font-size: 14px;
    width: 71px;
    height: 32px;
    line-height: 20px;
    color: #FFFFFF;
    margin-right: 16px;
    border: none;
    cursor: pointer;
}

/*#####[组件-栏目]#####*/
.drawer-main .drawer-middle {
    margin-top: 14px;
}

.drawer-main .drawer-middle .tabList-main {
    display: flex;
    border-bottom: 1px solid #2B2B2C;
}

.drawer-main .drawer-middle .tabList-main .tabItem {
    width: 71px;
    height: 44px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.drawer-main .drawer-middle .tabList-main .tabItem .line {
    width: 40px;
    height: 3px;
    border-radius: 3px;
    position: absolute;
    bottom: 0px;
}

.drawer-main .drawer-middle .tabList-main .tabItem .line.activeLine {
    background: #1989FA;
}

.drawer-main .drawer-middle .tabList-main .tabItem.active .tabName {
    color: #FFFFFF;
}

.drawer-main .tabItem .tabIcon {
    font-weight: 400;
    font-size: 16px;
    color: #A3A6AD;
    margin-right: 5px;
}

.drawer-main .tabItem .tabName {
    font-weight: 400;
    font-size: 14px;
    color: #A3A6AD;
}

.preview-main .preview-tabList {
    display: flex;
    flex-wrap: wrap;
    margin-top: 8px;
    margin-bottom: 24px;
    gap: 11px;
}

.preview-main .preview-tabList .preview-item {
    padding: 7px 12px;
    font-weight: 400;
    font-size: 12px;
    color: #909399;
    height: 32px;
    background: #202121;
    border-radius: 999px 999px 999px 999px;
    border: 1px solid #2D2D2F;
    cursor: pointer;
}

.preview-main .preview-tabList .preview-item.active {
    background: #18222C;
    border: 1px solid #1D3043;
    color: #409EFF;
}

.preview-imgList {
    display: flex;
    flex-wrap: wrap;
    align-items: start;
    align-content: flex-start;
    gap: 16px;
    height: 500px;
    overflow: auto;
    scrollbar-width: none;
}

.preview-imgList .preview-item {
    margin-bottom: 24px;
}

.preview-imgList .preview-item .imgContent {
    width: 180px;
    height: 120px;
    background: #262727;
    padding: 20px;
}

.preview-imgList .preview-item .imgContent:hover {
    border: 1px solid #409EFF;
    cursor: pointer;
}

.preview-imgList .preview-item .imgName {
    width: 100%;
    height: 24px;
    font-weight: 400;
    font-size: 14px;
    color: #CFD3DC;
    margin-top: 8px;
    text-align: center;
    margin-top: 8px;
}

.preview-imgList .preview-item .imgContent img {
    width: 100%;
    height: 100%;
    background: #000;
}

/*#####[自定义动画]#####*/
.menu-ani-property-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.layer-head-wrap .head-title {
    font-size: 14px;
    color: #CFD3DC;
    margin-left: 16px;
}

.layer-head-wrap .head-animation-icon {
    text-align: center;
    color: #e5eaf3;
    font-size: 14px;
}

.layer-head-wrap .head-animation-icon[visibility] {
    visibility: hidden;
}

.layer-head-wrap:hover .head-animation-icon[visibility] {
    visibility: visible;
}

.menu-ani-property-panel .property-title-wrap {
    display: flex;
    justify-content: space-between;
    height: 50px;
    background: #262727;
    border-bottom: 1px solid #000000;
}

.property-title-wrap .property-title {
    flex: 3;
    padding: 14px 0 14px 20px;
    font-weight: 400;
    font-size: 16px;
    color: #FFFFFF;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.property-title-wrap .property-title-gap {
    margin: 16px 12px;
    border: 1px solid #4C4D4F;
}

.property-title-wrap .property-btn-group {
    flex: 4;
    display: flex;
    align-items: center;
}

.property-btn-group .property-btn {
    padding: 6px 12px;
    margin: 11px 12px 11px 0;
    font-weight: 600;
    font-size: 12px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    border-radius: 4px 4px 4px 4px;
}

.property-btn-group .icon-close {
    font-size: 14px;
    cursor: pointer;
}

.property-btn-group .property-btn-publish {
    background: #409EFF;
    color: #FFFFFF;
}

.property-btn-group .property-btn-add {
    color: #409EFF;
    border: 1px solid #409EFF;
}
.property-btn-group .property-btn-add[disabled] {
    cursor: not-allowed;
    border-color: #d9d9d9;
    color: #d9d9d9;
    background-color: #7b7b7b;
}

/****帧列表****/
.menu-ani-property-panel .frame-list-wrap {
    width: 100%;
    border-bottom: 1px solid #000000;
}

.frame-list-wrap .frame-list-scroll-wrap {
    overflow-x: scroll;
    display: flex;
}

.frame-list-scroll-wrap .frame-list-scroll-item {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 400;
    font-size: 14px;
    color: #A3A6AD;
    padding: 10px 20px;
    word-break: keep-all;
    border-right: 1px solid #000000;
}

/*帧激活*/
.frame-list-scroll-wrap .frame-list-scroll-item[active=true] {
    color: #409EFF;
    background: #262727;
}

/*帧激活*/
.frame-list-scroll-wrap .frame-list-scroll-item[active=true] .icon-guanbi {
    display: block;
}

/*帧鼠标经过*/
.frame-list-scroll-wrap .frame-list-scroll-item:hover {
    color: #409EFF;
}

.frame-list-scroll-item .frame-item {
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
}
.frame-list-scroll-item .icon-guanbi {
    font-size: 8px;
    display: none;
}

/****自定义帧列表滚动条样式****/
.frame-list-scroll-wrap::-webkit-scrollbar-track-piece {
    -webkit-border-radius: 999;
    /*滚动条的圆角宽度*/
}

.frame-list-scroll-wrap::-webkit-scrollbar-track {
    background: transparent;
    /* 滚动条轨道背景颜色 */
}

.frame-list-scroll-wrap::-webkit-scrollbar {
    width: 2px;
    /*滚动条的宽度*/
    height: 4px;
    /*滚动条的高度*/
}

.menu-ani-property-panel .frame-wrap {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    margin: 16px 20px;
    padding: 12px 14px;
    background: #262727;
}

.frame-base-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
}

.frame-base-head .head-title {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #CFD3DC;
    line-height: 20px;
}

.frame-base .base-btn {
    padding: 2px 12px;
    font-weight: 600;
    font-size: 12px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    border-radius: 4px 4px 4px 4px;
    color: #409EFF;
    border: 1px solid #409EFF;
}

.frame-base .chart-item {
    width: 100%;
    line-height: 20px;
    box-sizing: border-box;
    display: flex;
    margin-top: 8px;
}

/**********覆盖属性面板抽象基类的样式***********/
.frame-panel .model-body {
    padding: unset;
    margin: unset;
}

/**********动画卡片样式***********/
.ani-card-wrap {
    padding: 12px 14px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #414243;
}

.ani-card-wrap .ani-card-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ani-card-wrap .ani-card-body {
    margin-top: 12px;
}

.ani-card-head .head-title {
    font-weight: 400;
    font-size: 14px;
    color: #CFD3DC;
    line-height: 20px;
    text-align: left;
}

.ani-card-head .head-icon {
    font-size: 14px;
    color: #A3A6AD;
}

.ani-card-head .head-icon[switch='close'] {
    transform: rotateZ(90deg);
}

.ani-card-head .head-icon[switch='open'] {
    transform: rotateZ(0deg);
}

.ani-card-body .chart-item .chart-label {
    min-width: 48px;
    margin-right: 5px;
    text-align: right;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
    font-weight: 400;
    font-size: 12px;
    color: #A3A6AD;
    animation: aaa;
}

.ani-card-body .chart-item .chart-control {
    flex: 1;
    min-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1D1D1D;
    position: relative;
}

.ani-card-body .chart-item .chart-control .chart-number {
    border-right: 1px solid #424344;
}

.ani-card-body .chart-item .chart-control .chart-number:last-child {
    border-right: none;
}
