/**
 * 字典集面板
 */
export default class AbsSetPanel {
  constructor(context) {
    if (context) {
      this.context = context;
      // this.designer = context.getDesigner();
    }
  }
  /**
   * 描述:初始化面板
   * @param {*} parentContainer
   */
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="menu-set-container"></div>`);
      parentContainer.append(this.container);
      this.initModel(this.container);
    }
  }
  initModel (container) {
    if (container) {
      const modelContainer = $(`<div class="comm-wrap menu-wrap">
        <div class="comm-head menu-set-head-wrap">
          <span class="menu-set-head-title">`+ this.getTitle() + `</span>
          <div class="menu-set-head-btn menu-set-head-add">添加</div>
        </div>
        <div class="comm-body menu-set-body"></div>
      </div>`);
      container.append(modelContainer);
      const btnContainer = $(modelContainer).find(".menu-set-head-wrap .menu-set-head-btn");
      this.bodyContainer = $(modelContainer).find(".menu-set-body");
      //事件
      const self = this;
      $(btnContainer).on("click", function (event) {
        self.refreshClick();
      });
    }
  }
  refreshPanel(){
    return console.error("数据集中点击方法[refreshClick]，必须实现");
  }
  refreshClick(){
    return console.error("数据集中点击方法[refreshClick]，必须实现");
  }
}