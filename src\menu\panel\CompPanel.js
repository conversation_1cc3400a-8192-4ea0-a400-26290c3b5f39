import { uuid } from "../../utils/Util";
import { getMenuComponent } from '../CompUtil';
/**
 * ComponentPanel
 */
export default class CompPanel {
  constructor(context) {
    this.context = context;
    this.designer = context.getDesigner();
    this.isOpen = true;
  }
  /**
   * 描述:初始化面板
   * @param {*} parentContainer
   */
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class='menu-comp-container'></div>`);
      parentContainer.append(this.container);
    }
  }

  /**
   * 描述:刷新面板
   */
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      this.container.append(`<div class="comm-wrap menu-head-title">组件</div>`)
      const cptList = getMenuComponent();
      for (let i = 0; i < cptList.length; i++) {
        const cptItem = cptList[i];
        this.refreshModel(this.container, cptItem);
      }
    }
  }
  refreshModel (parentContainer, model) {
    if (parentContainer && model) {
      model.id = uuid();
      const modelContainer = $(`<div class='comm-wrap menu-comp-wrap' id="` + model.id + `">
      <div class="comm-head menu-comp-head">
        <div class="menu-comp-icon" switch="close"><i class="ft-font icon-shangla"></i></div>
        <div class="menu-comp-head-title">`+ model.name + `</div>
      </div>
      <div class="menu-box"></div>
      </div>`);
      parentContainer.append(modelContainer);
      const headContainer = $(modelContainer).find(".menu-comp-head");
      const bodyContainer = $(modelContainer).find(".menu-box");

      //孩子节点
      if (model && model.child) {
        for (let i = 0; i < model.child.length; i++) {
          const item = model.child[i];
          this.refreshItem(bodyContainer, item);
        }
      }
      //点击
      const self = this;
      $(headContainer).on("click", function (event) {
        model.isOpen = !model.isOpen;
        self.refreshOpen(model);
      });
      this.refreshOpen(model);

    }
  }
  refreshOpen (model) {
    if (this.container && model && model.id) {
      const modelContainer = $(this.container).find("#" + model.id);
      const iconContainer = $(modelContainer).find(".menu-comp-icon");
      const bodyContainer = $(modelContainer).find(".menu-box");
      if (model.isOpen) {
        $(bodyContainer).show();
        $(iconContainer).attr({ switch: 'open' })
        // $(iconContainer).removeClass("fd-arrow-right");
        // $(iconContainer).addClass("fd-arrow-down");
      } else {
        $(bodyContainer).hide();
        $(iconContainer).attr({ switch: 'close' })
        // $(iconContainer).removeClass("fd-arrow-down");
        // $(iconContainer).addClass("fd-arrow-right");
      }
    }
  }
  refreshItem (parentContainer, item) {
    if (parentContainer && item) {
      const itemContainer = $(`
      <div class='box-item'>
        <div class='item-block'>
          <div class='item-icon'><i class="icon-font" >` + item.icon + `</i></div>
          <div class='item-title'>` + item.name + `</div>
        </div>
      </div>`);
      parentContainer.append(itemContainer);
      const itemBlockContainer = $(itemContainer).find(".item-block");
      this.refreshDraggable(itemBlockContainer, item);
      this.refreshClick(itemBlockContainer, item);
    }
  }
  /**
   * 拖拽
   * draggable="true" @dragstart="dragStart"
   */
  refreshDraggable (container, item) {
    const self = this;
    if (container) {
      $(container).attr("draggable", true);
      $(container).on("dragstart", function (event) {
        //拷贝 阻止数据 赋值后属性串联
        const copy = JSON.parse(JSON.stringify(item));
        self.context.addItem(copy);
      });
    }
  }
  /**
   * 点击
   * @param {*} container 
   * @param {*} item 
   */
  refreshClick (container, item) {
    const self = this;
    if (container) {
      $(container).on("click", function (event) {
        const layer = self.context.getLayer();
        if (layer && layer.property && layer.property.type && layer.property.type === "GroupLayer") {
          //拷贝 阻止数据 赋值后属性串联
          const copy = JSON.parse(JSON.stringify(item));
          //添加父Id
          copy["parentId"] = layer.property.id;
          //添加图层
          const canvasContainer = layer.getCanvasContainer();
          if (self.designer && canvasContainer) {
            self.designer.addLayer(canvasContainer, copy);
          }
        }
      });
    }
  }
}