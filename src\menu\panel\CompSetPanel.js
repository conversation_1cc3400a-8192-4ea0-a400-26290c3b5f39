import ScreenService from "../../service/ScreenService";
import DictService from "../../service/DictService";
import Starter from "../../Starter";

/**
 * 组件集
 * ComponentSetPanel
 tabs = [
 { id: "1", key: "All", name: "全部", icon: "&#xe6d7;", childs: [] },
 {
        id: "2", key: "Chart", name: "图表", icon: "&#xe709;", childs: [
          { id: "2-1", key: "All", name: "全部" },
          { id: "2-2", key: "ColumnChart", name: "柱状图" },
          { id: "2-3", key: "BarChart", name: "条形图" },
        ]
      }
 ]
 datas = [
 { id: "1", name: "组件名称1", image: require('../../assets/logo.png') },
 { id: "2", name: "组件名称2", image: require('../../assets/logo.png') },
 ]
 */
export default class CompSetPanel {
  constructor(context) {
    if (context) {
      this.context = context;
      this.designer = context.getDesigner();
    }
    //查询组件参数
    this.params = { name: "", tabType: "", labelType: [] };
    this.tabs = [];
    //全部的组件集
    this.datas = [];
  }

  static getInstance (context) {
    if (!this.instance) {
      this.instance = new CompSetPanel(context);
    }
    return this.instance;
  }

  /**
   * 描述:初始化面板
   * @param {*} parentContainer
   */
  initPanel (parentContainer,callback) {
    if (parentContainer) {
      this.container = $(`<div class="compset-container">
                <div class="drawer-main">
                  <div class="drawer-top">
                      <div class="queryModel">
                          <div class="ft-font searchIcon icon-shurukuangsousuo"></div>
                          <input type="text" class="search-text" name="assembly" placeholder="搜索组件" autocomplete="off"/>
                          <button class="search-btn">搜索</button>
                          <div class="fd-font closeIcon">&#xe75e</div>
                      </div>
                  </div>
                  <div class="drawer-middle">
                      <div class="tabList-main"></div>
                  </div>
                  <div class="drawer-preview">
                      <div class="preview-main">
                          <div class="preview-tabList"></div>
                          <div class="preview-imgList"></div>
                      </div>
                  <div>
                </div>
              </div>`);
      parentContainer.append(this.container);
      this.topContainer = $(this.container).find(".drawer-top");
      this.middleContainer = $(this.container).find(".drawer-middle");
      this.previewContainer = $(this.container).find(".drawer-preview");

      this.tabsContainer = $(this.container).find(".tabList-main");
      this.labelsContainer = $(this.container).find(".preview-tabList");
      this.previewImgList = $(this.container).find(".preview-imgList");

      //关闭按钮
      const closeBtn = $(this.container).find(".closeIcon");
      //搜索框
      const searchText = $(this.container).find(".search-text");
      const searchBtn = $(this.container).find(".search-btn");
      //初始化名称值
      if (this.params && this.params.name) {
        $(searchText).val(this.params.name);
      }
      $(searchText).on('keydown', event => {
        if (event.keyCode === 13) {
          this.refreshData(searchText);
        }
      });

      $(searchBtn).on("click", () => this.refreshData(searchText));

      if(closeBtn) {
        $(closeBtn).on("click", function (event) {
          if (callback) {
            callback();
          }
        });
      }
    }
  }

  /**
   * 刷新面板数据
   * @param container
   */
  refreshData(container) {
    const value = $(container).val();
    if (this.params) {
      this.params["name"] = value;
    }
    const data = this.filteDatas();
    this.refreshImages(data);
  }

  refreshPanel () {
    const self = this;
    //初始化过滤参数()
    this.params = { name: "", tabType: "", labelType: [] };
    //刷新数据
    if (this.tabs && this.tabs.length) {
      const tabs = this.tabs;
      this.refreshTabs(tabs);
    } else {
      this.queryImage(function (datas) {
        self.datas = datas;
        //刷新Tabs
        self.queryDict(function (tabs) {
          self.tabs = tabs;
          self.refreshTabs(tabs);
        });
      });
    }
  }

  /**
   * 过滤数据
   * @returns
   */
  filteDatas () {
    let list = [];
    const params = this.params;
    const datas = this.datas;
    if (datas && datas.length) {
      if (params) {
        const name = params["name"];
        const tabType = params["tabType"];
        const labelType = params["labelType"];
        if (name || tabType || labelType) {
          datas.forEach(function (data) {
            //是否是满足条件的数据(默认不满足)
            let flag = false;
            //类型
            if (tabType) {
              if (tabType === data["compType"] || tabType === "All") {
                //标签
                if (labelType && labelType.length) {
                  labelType.forEach(function (label) {
                    if (label === data["compTypeChild"] || label === "All") {
                      flag = true;
                    }
                  });
                } else {
                  flag = true;
                }
              }
            }


            if (flag) {
              //名称
              if (name) {
                if (data["name"].indexOf(name) != -1) {
                  list.push(data);
                }
              } else {
                list.push(data);
              }
            }
          });
        } else {
          list = datas;
        }
      } else {
        list = datas;
      }
    }
    return list;
  }

  queryDict (callback, parentKey) {
    const params = { status: 1, nodeKey: "LSD_ASSEMBLY_TYPE", level: "1" };
    //判断是否需要查询
    let isQuery = true;
    if (parentKey) {
      if (parentKey === "All") {
        isQuery = false;
      } else {
        isQuery = true;
        params["parentitemKey"] = parentKey;
        params["level"] = "2";
      }
    }
    const datas = [{ key: "All", name: "全部", icon: "&#xe6b3;" },];
    if (isQuery) {
      const dictService = new DictService(this.context);
      dictService.queryDataByKey(params, function (result) {
        const list = result;
        if (list && list.length) {
          for (let i = 0; i < list.length; i++) {
            const item = list[i];
            item["key"] = item.itemKey;
            item["name"] = item.itemName;
            item["icon"] = item.iconUrl;
            datas.push(item);
          }
          if (callback) {
            callback(datas);
          }
        }
      });
    } else {
      if (callback) {
        callback(datas);
      }
    }
  }

  queryImage (callback) {
    const params = { type: "1", status: "1", bizStatus: "2" };
    const screenService = new ScreenService(this.context);
    screenService.queryList(params, function (result) {
      const list = result;
      if (callback) {
        callback(list);
      }
    });
  }


  /*##############################[Tabs]################################# */
  refreshTabs (list) {
    const tabsContainer = this.tabsContainer;
    if (tabsContainer) {
      $(tabsContainer).empty();
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          this.refreshTab(tabsContainer, item);
        }
      }
      this.refreshTabFirst(tabsContainer);
    }
  }

  refreshTab (parentContainer, item) {
    if (parentContainer && item) {
      const itemContainer = $(`<div class="tabItem">
        <div class="ft-font tabIcon">` + item.icon + `</div>
        <div class="tabName">` + item.name + `</div>
        <div class="line"></div>
      </div>`);
      parentContainer.append(itemContainer);
      const self = this;
      $(itemContainer).on("click", (event) => {
        this.refreshTabChecked(itemContainer, item);
      });
    }
  }

  //刷新第一次点击
  refreshTabFirst (parentContainer) {
    //单击选项卡
    $(parentContainer).find(".tabItem:first").trigger('click');
  }

  //刷新选中样式
  refreshTabCheckedCss (itemNode) {
    // 移除所有已有的 active 类
    $('.tabItem').removeClass('active');
    $('.line').removeClass('activeLine');
    // 给被点击的元素添加 active 类
    itemNode.addClass('active');
    itemNode.find(".line").addClass('activeLine');
  }

  //选中
  refreshTabChecked (itemContainer, item) {
    const self = this;
    if (itemContainer && item) {
      //参数赋值
      if (self.params) {
        self.params.tabType = item.key;
      }
      //刷新选中样式
      this.refreshTabCheckedCss(itemContainer);
      //刷新选中数据
      const childs = item["childs"];
      if (childs && childs.length) {
        self.refreshLabels(childs);
      } else {
        self.queryDict(function (datas) {
          if (datas && datas.length) {
            item["childs"] = datas;
            self.refreshLabels(datas);
          }
        }, item.key);
      }
    }
  }

  /*##############################[Label]################################# */
  refreshLabels (list) {
    const labelsContainer = this.labelsContainer;
    if (labelsContainer) {
      $(labelsContainer).empty();
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          this.refreshLabel(labelsContainer, item);
        }
      }
      this.refreshLabelFirst(labelsContainer);
    }
  }

  refreshLabel (parentContainer, item) {
    if (parentContainer && item) {
      const itemContainer = $(`<div class="preview-item">
        <div class="itemName">` + item.name + `</div>
      </div>`);
      parentContainer.append(itemContainer);
      const self = this;
      $(itemContainer).on("click", (event) => {
        self.refreshLabelChecked(itemContainer, item);
      });
    }
  }

  refreshLabelFirst (parentContainer) {
    //单击选项卡
    $(parentContainer).find(".preview-item:first").trigger('click');
  }

  refreshLabelCheckedCss (itemNode) {
    // 移除所有已有的 active 类
    $('.preview-item').removeClass('active');
    // 给被点击的元素添加 active 类
    itemNode.addClass('active');
  }

  //选中
  refreshLabelChecked (itemContainer, item) {
    const self = this;
    if (itemContainer && item) {
      //刷新选中样式
      self.refreshLabelCheckedCss(itemContainer);

      //参数赋值
      if (self.params) {
        self.params.labelType = [];
        self.params.labelType.push(item.key);
      }
      //触发
      const datas = self.filteDatas();
      self.refreshImages(datas);
    }
  }

  /*##############################[Iamge]################################# */

  refreshImages (list) {
    const imgsContainer = this.previewImgList;
    if (imgsContainer) {
      $(imgsContainer).empty();
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          this.refreshImage(imgsContainer, item);
        }
      }
    }
  }

  refreshImage (parentContainer, item) {
    if (parentContainer && item) {
      const itemContainer = $(`<div class="preview-item">
        <div class="imgContent">
          <img src="` + item.image + `" />
        </div>
        <div class="imgName">` + item.name + `</div>
      </div>`);
      parentContainer.append(itemContainer);
      const self = this;
      $(itemContainer).on("click", (event) => {
        if (item && self.designer) {
          // //dataModels数据模型
          // if (item["dataModels"]) {
          //   starter.parseDataModel(item["dataModels"]);
          // }
          // //dataDicts数据字典
          // if (item["dataDicts"]) {
          //   starter.parseDataDict(item["dataDicts"]);
          // }
          if (item["id"]) {
            const screenService = new ScreenService(this.context);
            // 查组件的素材列表
            screenService.queryScreen({ id: item["id"] }, (result) => {
              const starter = new Starter(this.context);

              if (result["materials"]) {
                starter.parseMaterial(result["materials"]);
              }
              if (item["jsons"]) {
                const json = starter.parseJsons(item["jsons"]);
                if(json && json["layers"] && json["layers"].length){
                  const property=json["layers"][0];
                  if(property["id"]){
                    delete property["id"];
                  }
                  if(property["parentId"]){
                    delete property["parentId"];
                  }

                  const width =  self.context.getWidth();
                  const height = self.context.getHeight();
                  const layerWidth=property["width"];
                  const layerHeight=property["height"];
                  if(width && height){
                    property["width"] =Math.round(layerWidth);
                    property["height"] =Math.round(layerHeight);
                    property["left"] = Math.round(Math.abs((width-layerWidth)/2));
                    property["top"] = Math.round(Math.abs((height-layerHeight)/2));
                  }
                  const canvasContainer = self.designer.getCanvasContainer();
                  if (canvasContainer) {
                    self.designer.addLayer(canvasContainer, property);
                  }
                }
              }
            },null);
          }
        }
      });
    }
  }
}