
import DataModel from "../../model/DataModel";
import DataSetStyle from "./style/DataSetStyle";
import DataModelDialog from "../../dialog/DataModelDialog";

import AbsSetPanel from "./AbsSetPanel";
/**
 * 数据集面板
 */
export default class DataSetPanel extends AbsSetPanel{
  constructor(context) {
    super(context);
  }
  /**
   * 描述:初始化面板
   * @param {*} parentContainer
   */
  // initPanel (parentContainer) {
  //   if (parentContainer) {
  //     this.container = $(`<div class="menu-data-container"></div>`);
  //     parentContainer.append(this.container);
  //     this.initModel(this.container);
  //   }
  // }
  // initModel (container) {
  //   if (container) {
  //     const modelContainer = $(`<div class="comm-wrap menu-wrap">
  //       <div class="comm-head menu-head">
  //         <div class="head-title menu-title">`+ this.getTitle() + `</div>
  //         <div class="head-btn head-add">添加</div>
  //       </div>
  //       <div class="comm-body menu-body"></div>
  //     </div>`);
  //     container.append(modelContainer);
  //     const btnContainer = $(modelContainer).find(".menu-head .head-btn");
  //     this.bodyContainer = $(modelContainer).find(".menu-body");
  //     const self = this;
    //   $(btnContainer).on("click", function (event) {
    //     const dataModelDialog = new DataModelDialog(self.context);
    //     dataModelDialog.open(function (item) {
    //       self.add(item);
    //       self.refreshPanel();
    //     });
    //   });
    // }
  // }
  refreshClick(){
    const self=this;
    const dataModelDialog = new DataModelDialog(this.context);
    dataModelDialog.open(function (item) {
      self.add(item, () => {
        self.refreshPanel();
      });
    });
  }
  /**
   * 描述:刷新面板
   */
  refreshPanel () {
    const self = this;
    if (this.bodyContainer) {
      this.bodyContainer.empty();
      const modelMap = this.context.getDataMap();
      if (modelMap) {
        for (let key in modelMap) {
          const item = modelMap[key]
          const setStyle = new DataSetStyle(this.context);
          setStyle.initPanel(this.bodyContainer, item);
          setStyle.initEvent({
            del: function () {
              self.del(item.property.id);
              self.refreshPanel();
            }
          });
        }
      }
    }
  }
  /**
   * 标题
   * @returns 
   */
  getTitle () {
    return "数据集";
  }
  /**
   * 添加
   */
  add (item, callback) {
    if (this.context) {
      const model = new DataModel(this.context);
      model.refreshProperty(item, true);
      model.queryData(callback);
      this.context.addData(model);
    }
  }
  /**
   * 删除
   * @param {*} index 
   */
  del (id) {
    if (this.context && id) {
      this.context.delDataById(id);
    }
  }
}