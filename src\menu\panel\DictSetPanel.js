import DictModel from "../../model/DictModel";
import DictSetStyle from "./style/DictSetStyle";
import DataDictDialog from "../../dialog/DataDictDialog";
import AbsSetPanel from "./AbsSetPanel";
/**
 * 字典集面板
 */
export default class DictSetPanel extends AbsSetPanel{
  constructor(context) {
    super(context);
  }
  /**
   * 描述:初始化面板
   * @param {*} parentContainer
   */
  // initPanel (parentContainer) {
  //   if (parentContainer) {
  //     this.container = $(`<div class="menu-dict-container"></div>`);
  //     parentContainer.append(this.container);
  //     this.initModel(this.container);
  //   }
  // }
  // initModel (container) {
  //   if (container) {
  //     const modelContainer = $(`<div class="comm-wrap menu-wrap">
  //       <div class="comm-head menu-head">
  //         <div class="head-title">`+ this.getTitle() + `</div>
  //         <div class="head-btn head-add">添加</div>
  //       </div>
  //       <div class="comm-body menu-body"></div>
  //     </div>`);
  //     container.append(modelContainer);
  //     const btnContainer = $(modelContainer).find(".menu-head .head-btn");
  //     this.bodyContainer = $(modelContainer).find(".menu-body");
  //     const self = this;
  //     $(btnContainer).on("click", function (event) {
  //       const dataDictDialog = new DataDictDialog(self.context);
  //       dataDictDialog.open(function (item) {
  //         self.add(item);
  //         self.refreshPanel();
  //       });
  //     });
  //   }
  // }
  refreshClick () {
    const self = this;
    const dataDictDialog = new DataDictDialog(this.context);
    dataDictDialog.open(function (item) {
      self.add(item);
      self.refreshPanel();
    });
  }
  /**
   * 描述:刷新面板
   */
  refreshPanel () {
    const self = this;
    if (this.bodyContainer) {
      this.bodyContainer.empty();
      const modelMap = this.context.getDictMap();
      if (modelMap) {
        for (let key in modelMap) {
          const item = modelMap[key]
          const setStyle = new DictSetStyle(this.context);
          setStyle.initPanel(this.bodyContainer, item);
          setStyle.initEvent({
            del: function () {
              self.del(item.property.id);
              self.refreshPanel();
            }
          });
        }
      }
    }
  }
  /**
   * 标题
   * @returns 
   */
  getTitle () {
    return "字典集";
  }
  /**
   * 添加
   */
  add (item) {
    if (this.context) {
      const model = new DictModel(this.context);
      model.refreshProperty(item, true);
      model.queryData();
      this.context.addDict(model);
    }
  }
  /**
   * 删除
   * @param {*} index 
   */
  del (id) {
    if (this.context && id) {
      this.context.delDictById(id);
    }
  }
}