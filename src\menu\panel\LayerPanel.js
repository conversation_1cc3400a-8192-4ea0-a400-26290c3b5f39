import TemplDialog from "../../dialog/TemplDialog";
import MouseRight from "../../assets/element/jquery/mouse/MouseRight";
export default class LayerPanel {
  constructor(context) {
    if (context) {
      this.context = context;
      this.designer = context.getDesigner();
    }
  }
  /**
   * 描述:初始化面板
   * @param {*} parentContainer
   */
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="menu-layer-container" ></div>`);
      parentContainer.append(this.container);
      this.addMouseRight();
    }
  }

  /**
   * 描述:刷新面板
   */
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      this.container.append(`<div class="comm-wrap menu-head-title">图层列表</div>`)
      const layers = this.context.getLayerTree();
      if (layers && layers.length) {
        for (let i = 0; i < layers.length; i++) {
          const layer = layers[i];
          this.refreshNode(this.container, layer);
        }
      }
    }
  }
  refreshNode (parentContainer, layer) {
    if (parentContainer && layer) {
      // 孩子 递归
      const childs = layer["childs"];
      // 是否组合图层
      const isGroupLayer = childs && childs.length;
      // 节点
      const item = layer.property;
      const modelContainer = $(`<div class="comm-wrap tree-wrap" id="` + item.id + `">
      <div class="comm-head tree-head layer-head-wrap">
        <div class="head-title ml4">`+ item.label + `</div>
        <div class="head-layer-icon icon-chakan-on ft-font mr10" visibility id="icon_look_`+ item.id + `"></div>
        <div class="head-layer-icon icon-jiesuo ft-font mr18" visibility id="icon_lock_`+ item.id + `"></div>
      </div>
      <div class="comm-body tree-body" id="body_`+ item.id + `"></div>
      </div>`);
      parentContainer.append(modelContainer);

      const openIcon = $(`<div class="head-layer-icon icon-shangla ft-font ml18" switch="close" id="icon_open_`+ item.id + `"></div>`);
      const folderIcon = $(`<div class="head-layer-icon icon-ziyuan1 ft-font ml10"></div>`);
      const layerIcon = $(`<div class="head-layer-icon icon-a-3D ft-font ml18" id="icon_open_`+ item.id + `"></div>`);

      const headContainer = $(modelContainer).find(".tree-head");
      // 是否组合图层
      if (isGroupLayer) {
        // 在标题前面增加折叠和文件夹的图标
        headContainer.prepend(openIcon, folderIcon);
      } else {
        // 图层图标
        headContainer.prepend(layerIcon)
      }

      const bodyContainer = $(modelContainer).find(".tree-body");

      const iconOpenContainer = $(modelContainer).find(".tree-head .icon-shangla");
      const titleContainer = $(modelContainer).find(".tree-head .head-title");
      const iconLookContainer = $(modelContainer).find(".tree-head .icon-chakan-on");
      const iconLockContainer = $(modelContainer).find(".tree-head .icon-jiesuo");

      //初始化
      this.refreshOpen(modelContainer, layer);
      this.refreshChecked(modelContainer, layer);
      this.refreshHide(modelContainer, layer);
      this.refreshLock(modelContainer, layer);
      // this.refreshMouseRight(modelContainer, layer);
      const self = this;
      //点击 open
      $(iconOpenContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        layer.isOpen = !layer.isOpen;
        self.refreshOpen(modelContainer, layer);
      });
      //点击 checked
      $(titleContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        self.refreshLink(layer);
        // layer.isChecked = true;
        self.cleanAllChecked(self.container);
        self.refreshChecked(modelContainer, layer);
      });
      //点击 Hide
      $(iconLookContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        item.isHide = !item.isHide;
        self.refreshHide(modelContainer, layer);
        layer.refreshHide();
        self.refreshLink(layer);
      });
      //点击 lock
      $(iconLockContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        layer.isLock = !layer.isLock;
        self.refreshLock(modelContainer, layer);
        self.refreshLink(layer);
      });
      //鼠标右键
      $(modelContainer).on('contextmenu', (event) => {
        event.preventDefault(); // 阻止
        event.stopPropagation(); //阻止父节点冒泡
        self.refreshMouseRight(modelContainer, layer, event);
      });

      if (isGroupLayer) {
        for (let i = 0; i < childs.length; i++) {
          const child = childs[i];
          this.refreshNode(bodyContainer, child);
        }
      }
    }
  }
  /**##################[开启]################## */
  refreshOpen (modelContainer, layer) {
    if (modelContainer && layer) {
      const childs = layer["childs"];
      if (childs && childs.length) {
        const item = layer.property;
        //改用Id 防止向下穿透
        const iconContainer = $(modelContainer).find("#icon_open_" + item.id);
        const bodyContainer = $(modelContainer).find("#body_" + item.id);
        if (layer.isOpen) {
          $(bodyContainer).show();
          $(iconContainer).attr({ switch: 'open' })
        } else {
          $(bodyContainer).hide();
          $(iconContainer).attr({ switch: 'close' })
        }
      }
    }
  }
  /**##################[隐藏]################## */
  refreshHide (modelContainer, layer) {
    if (modelContainer && layer) {
      const item = layer.property;
      const iconContainer = $(modelContainer).find("#icon_look_" + item.id);
      if (item.isHide) {
        $(iconContainer).removeClass("icon-chakan-on");
        $(iconContainer).addClass("icon-chakan-off");
      } else {
        $(iconContainer).removeClass("icon-chakan-off");
        $(iconContainer).addClass("icon-chakan-on");
      }
    }
  }
  /**##################[锁]################## */
  refreshLock (modelContainer, layer) {
    if (modelContainer && layer) {
      const item = layer.property;
      const iconContainer = $(modelContainer).find("#icon_lock_" + item.id);
      if (layer.isLock) {
        $(iconContainer).addClass("icon-suoding");
        $(iconContainer).removeClass("icon-jiesuo");
      } else {
        $(iconContainer).addClass("icon-jiesuo");
        $(iconContainer).removeClass("icon-suoding");
      }
      layer.refreshLock();
    }
  }
  /**##################[选中]################## */
  refreshChecked (modelContainer, layer) {
    if (modelContainer && layer) {
      if (layer.isChecked) {
        $(modelContainer).css("background", "#409EFF");
      } else {
        $(modelContainer).css("background", "");
      }
    }
  }
  cleanAllChecked (container) {
    if (container) {
      const modelContainer = $(container).find(".tree-wrap");
      $(modelContainer).css("background", "");
    }
  }
  // 图层选中
  refreshLink (layer) {
    if (this.designer && layer) {
      this.designer.linkByMenu(layer);
    }
  }
  /**##################[鼠标右键]################## */
  refreshMouseRight (modelContainer, layer, event) {
    if (modelContainer && layer && this.mouseRight) {
      const item = layer.property;
      const self = this;
      this.mouseRight.refreshEvent(event, {
        click: function (result) {
          // console.info("result:", result);
          // console.info("layer:", layer);
          const type = result.code;
          if (type) {
            switch (type) {
              case 'top':
                if (self.designer) {
                  self.designer.moveLayer(type, layer);
                  self.refreshPanel();
                }
                break;
              case 'bottom':
                if (self.designer) {
                  self.designer.moveLayer(type, layer);
                  self.refreshPanel();
                }
                break;
              case 'up':
                if (self.designer) {
                  self.designer.moveLayer(type, layer);
                  self.refreshPanel();
                }
                break;
              case 'down':
                if (self.designer) {
                  self.designer.moveLayer(type, layer);
                  self.refreshPanel();
                }
                break;
              case 'copy':
                if (self.designer) {
                  self.designer.localCopyLayer(layer);
                }
                break;
              case 'paste':
                if (self.designer) {
                  self.designer.localPasteLayer(layer);
                  self.refreshPanel();
                }
                break;
              case 'delete':
                if (self.designer) {
                  self.designer.delLayer(layer);
                  self.refreshPanel();
                }
                break;
              case 'hide':
                item.isHide = !item.isHide;
                self.refreshHide(modelContainer, layer);
                layer.refreshHide();
                break;
              case 'lock':
                layer.isLock = !layer.isLock;
                self.refreshLock(modelContainer, layer);
                break;
              case 'change':
                //弹框
                const dialog = new TemplDialog(self.context, layer);
                dialog.open(function (result) { });
                break;
              default:
                console.error('触发失败![不存在' + type + '类型]');
            }
          }
        },
      });
    }
  }
  addMouseRight () {
    const property = { id: "menu-mouse-right", width: 150 };
    const datas = [
      { code: "top", name: "置顶", iconFamily: "icon-font", iconClazz: "icon-uniE001", },
      { code: "bottom", name: "置底", iconFamily: "icon-font", iconClazz: "icon-uniE003", },
      { code: "up", name: "上移", iconFamily: "icon-font", iconClazz: "icon-upon", },
      { code: "down", name: "下移", iconFamily: "icon-font", iconClazz: "icon-shangyi", },
      { code: "copy", name: "复制", iconFamily: "fd-font", iconClazz: "fd-fuzhi", },
      { code: "paste", name: "粘贴", iconFamily: "fd-font", iconClazz: "fd-niantie", },
      { code: "hide", name: "隐藏", iconFamily: "fd-font", iconClazz: "fd-Notvisible1", },
      { code: "lock", name: "锁定", iconFamily: "fd-font", iconClazz: "fd-suo", },
      { code: "change", name: "转换成组件", iconFamily: "fd-font", iconClazz: "fd-exchangerate", },
      { code: "delete", name: "删除", iconFamily: "fd-font", iconClazz: "fd-shanchu", },
    ];
    this.mouseRight = MouseRight.getInstance();
    this.mouseRight.initPanel(property);
    this.mouseRight.refreshPanel(datas);
  }
  /**
   * 刷新鼠标事件
   */
  // refreshMouse (modelContainer, layer) {
  //   if (modelContainer && layer) {
  //     //鼠标右键
  //     $(container).on('contextmenu', (event) => {
  //       event.preventDefault(); // 阻止
  //       const menuStyle = {
  //         display: 'block',
  //         top: event.pageY,
  //         left: event.pageX
  //       }
  //       this.mouseContainer.css(menuStyle);
  //       // 点击那个图层就塞对应图层的信息，绑定事件要取对应图层
  //       this.mouseContainer.attr('layer-name', layer.property.label);
  //     });
  //     // 点击任意位置 关闭右键弹窗
  //     $(document).mousedown((event) => {
  //       var target = $(event.target);
  //       if (!target.closest('.menu-mouse-wrap').length) {
  //         this.mouseContainer.css('display', 'none')
  //       }
  //     })
  //   }
  // }
  // initRightClickNode () {
  //   const self = this;
  //   this.mouseContainer = $(`<ul class="menu-mouse-wrap"></ul>`);
  //   this.mouseContainer.css('display', 'none');
  //   const topContainer = $(`<li class="menu-mouse-item"> <i class="icon-font icon-uniE001"></i> 置顶</li>`);
  //   const bottomContainer = $(`<li class="menu-mouse-item"> <i class="icon-font icon-uniE003"></i> 置底</li>`);
  //   const moveUpContainer = $(`<li class="menu-mouse-item"> <i class="icon-font icon-upon"></i> 上移</li>`);
  //   const overDownContainer = $(`<li class="menu-mouse-item"><i class="icon-font icon-shangyi"></i> 下移</li>`);
  //   const copyContainer = $(`<li class="menu-mouse-item" > <span class="icon fd-font">&#xe767;</span> 复制</li>`);
  //   const pasteContainer = $(`<li class="menu-mouse-item"> <span class="icon fd-font">&#xe6de;</span> 粘贴</li>`);
  //   const delContainer = $(`<li class="menu-mouse-item"><span class="icon fd-font">&#xe6aa;</span> 删除</li>`);
  //   const hideContainer = $(`<li class="menu-mouse-item"><span class="icon fd-font fd-Notvisible1"></span> 隐藏</li>`);
  //   const lockContainer = $(`<li class="menu-mouse-item"><span class="icon fd-font fd-suo"></span> 锁定</li>`);
  //   const changeComContainer = $(`<li class="menu-mouse-item"><span class="icon fd-font fd-exchangerate"></span> 转换成组件</li>`);
  //   this.mouseContainer.append(topContainer);
  //   this.mouseContainer.append(bottomContainer);
  //   this.mouseContainer.append(moveUpContainer);
  //   this.mouseContainer.append(overDownContainer);
  //   this.mouseContainer.append(copyContainer);
  //   this.mouseContainer.append(pasteContainer);
  //   this.mouseContainer.append(delContainer);
  //   this.mouseContainer.append(hideContainer);
  //   this.mouseContainer.append(lockContainer)
  //   this.mouseContainer.append(changeComContainer);
  //   topContainer.on('click', () => {
  //     // 取对应图层的信息
  //     const layerName = this.mouseContainer.attr('layer-name')
  //     alert(`置顶图层：${layerName}`)
  //   });
  //   // ...其它事件绑定

  //   //转换成组件
  //   $(changeComContainer).on("click", function (event) {
  //     const layer = self.context.getLayer();
  // //保存信息
  // const submit = Submit.getInstance(self.context);
  // const materials = self.context.getMaterialPropertyList() || [];
  // const jsonExpr = submit.getLayerExpr(layer, true);
  // const params = { type: 1, jsonExpr: jsonExpr, materials: materials };
  // //弹框
  // const dialog = new TemplDialog(self.context, params);
  // dialog.open(function (result) {
  //   submit.saveTempOrComp(result);
  // });
  //   });
  //   // changeComContainer.on('click')
  //   $('body').append(this.mouseContainer)
  // }
}