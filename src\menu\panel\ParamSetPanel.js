import ParamModel from "../../model/ParamModel";
import ParamSetStyle from "./style/ParamSetStyle";
import AbsSetPanel from "./AbsSetPanel";
/**
 * 参数集面板
 */
export default class ParamSetPanel extends AbsSetPanel{
  constructor(context) {
    super(context);
  }
  /**
   * 描述:初始化面板
   * @param {*} parentContainer
   */
  // initPanel (parentContainer) {
  //   if (parentContainer) {
  //     this.container = $(`<div class="menu-param-container"></div>`);
  //     parentContainer.append(this.container);
  //     this.initModel(this.container);
  //   }
  // }
  // initModel (container) {
  //   if (container) {
  //     const modelContainer = $(`<div class="comm-wrap menu-wrap">
  //       <div class="comm-head menu-head">
  //         <div class="head-title">`+ this.getTitle() + `</div>
  //         <div class="head-btn head-add">添加</div>
  //       </div>
  //       <div class="comm-body menu-body"></div>
  //     </div>`);
  //     container.append(modelContainer);
  //     const btnContainer = $(modelContainer).find(".menu-head .head-btn");
  //     this.bodyContainer = $(modelContainer).find(".menu-body");
  //     const self = this;
  //     $(btnContainer).on("click", function (event) {
  //       self.add();
  //       self.refreshPanel();
  //     });
  //   }
  // }
  refreshClick () {
    this.add();
    this.refreshPanel();
  }
  /**
   * 描述:刷新面板
   */
  refreshPanel () {
    const self = this;
    if (this.bodyContainer) {
      this.bodyContainer.empty();
      const modelMap = this.context.getParamMap();
      if (modelMap) {
        for (let key in modelMap) {
          const item = modelMap[key]
          const setStyle = new ParamSetStyle(this.context, true);
          setStyle.initPanel(this.bodyContainer, item);
          setStyle.initEvent({
            del: function () {
              self.del(item.property.id);
              self.refreshPanel();
            }
          });
        }
      }
    }
  }
  /**
   * 标题
   * @returns 
   */
  getTitle () {
    return "参数集";
  }
  /**
   * 添加
   */
  add () {
    if (this.context) {
      const model = new ParamModel(this.context);
      this.context.addParam(model);
    }
  }
  /**
   * 删除
   * @param {*} index 
   */
  del (id) {
    if (this.context && id) {
      this.context.delParamById(id);
    }
  }
}