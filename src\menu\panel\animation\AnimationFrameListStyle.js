import AbstractStyle from "../../../AbstractStyle";

/**
 * 帧列表面板
 */
export default class AnimationFrameListStyle extends AbstractStyle{
  constructor(context) {
    super(context)
  }

  getContainer() {
    return $(`<div class="frame-list-scroll-wrap"></div>`);
  }

  initPanel(container, ) {
    if (container) {
      this.container = this.getContainer();
      container.append(this.container);
    }
  }

  setEvents(events) {
    this.events = events;
  }

  refreshPanel(item, selectIndex = 0) {
    this.container.empty();
    if (item.frames && item.frames.length) {
      // 重新放入
      for(let i = 0; i < item.frames.length; i++) {
        const frame = item.frames[i];
        const frameDomItem = $(`<div class="frame-list-scroll-item no-select-pointer">
                    <div class="frame-item">${frame.name}${frame.frameIndex}</div>
                    <div class="head-layer-icon icon-guanbi ft-font ml6""></div>
                </div>`)
        this.container.append(frameDomItem)

        this.refreshItemSelect(frameDomItem, frame, i);
        const deleteBtn = frameDomItem.find('.icon-guanbi');
        this.refreshDelete(deleteBtn, item, i);
      }
      // 默认选中第一个
      if (item.frames.length > 0) {
        this.container.find('.frame-list-scroll-item').eq(selectIndex).trigger('click');
      }

      this.refreshModel(item);
      this.bindModel(item);
    }
  }

  /**
   * 刷新单个项目标题
   * @param property 帧属性
   * @param selectIndex 帧下标
   */
  refreshSingleItemTitle (property, selectIndex) {
    const frameList = $('.frame-list-scroll-item');
    if (frameList && frameList.length && property && property.frames && property.frames.length) {
      const frame = property.frames[selectIndex];
      const frameDom = frameList.eq(selectIndex).find('.frame-item');
      if (frame && frameDom && frameDom.length) {
        if (frame.frameIndex || frame.frameIndex === 0) {
          frameDom.text(`${frame.name}${frame.frameIndex}`);
        } else {
          frameDom.text(`${frame.name}`);
        }
      }
    }
  }

  refreshItemSelect(frameDomItem, frame, selectIndex) {
    if (frameDomItem && frameDomItem.length) {
      // 帧点击选中
      frameDomItem.click(() => {
        // 回调
        this.events && this.events.refreshFrameSelect && this.events.refreshFrameSelect(frame, selectIndex)
        // 选中样式
        $(frameDomItem).attr('active', true);
        $(frameDomItem).siblings('.frame-list-scroll-item').each((i, e) => {
          $(e).removeAttr('active');
        })
      })
    }
  }

  refreshDelete(deleteBtn, item, selectIndex) {
    if (deleteBtn && deleteBtn.length) {
      if (item.isPublish) {
        deleteBtn.hide();
      } else {
        deleteBtn.show();
        deleteBtn.click(() => {
          this.events && this.events.refreshFrameDelete && this.events.refreshFrameDelete(selectIndex)
        })
      }
    }
  }

  getModelName() {
    return 'frame-list-model';
  }
}