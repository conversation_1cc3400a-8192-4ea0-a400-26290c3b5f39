import AnimationFramePositionStyle from "./style/AnimationFramePositionStyle";
import AnimationFrameTransformStyle from "./style/AnimationFrameTransformStyle";
import GapStyle from "./style/GapStyle";
import AnimationFrameBgStyle from "./style/AnimationFrameBgStyle";
import AbstractStyle from "../../../AbstractStyle";
import AnimationFrameFontStyle from "./style/AnimationFrameFontStyle";
import AnimationFrameBorderStyle from "./style/AnimationFrameBorderStyle";
import AnimationFrameBaseStyle from "./style/AnimationFrameBaseStyle";

/**
 * 帧面板
 */
export default class AnimationFrameStyle extends AbstractStyle{
  constructor(context) {
    super(context);
  }

  setEvents(events) {
    this.events = events;
  }

  getContainer() {
    return $(`<div class="frame-panel"></div>`)
  }

  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = this.getContainer();
      parentContainer.append(this.container);
    }
  }

  refreshPanel (item, selectIndex, isDisabled = false) {
    this.container.empty();
    if (item) {
      // 动画基础属性
      const baseStyle = new AnimationFrameBaseStyle(this.context, isDisabled);
      baseStyle.initPanel(this.container, item, () => {
        this.events && this.events.refreshTitle && this.events.refreshTitle(selectIndex)
      })

      if (!item.transform) {
        item.transform ={};
      }
      // 动画转换属性
      const frameTransformStyle = new AnimationFrameTransformStyle(this.context, isDisabled);
      frameTransformStyle.initPanel(this.container, item.transform)

      if (!item.font) {
        item.font ={};
      }
      new GapStyle(this.container, 8)

      if (!item.position) {
        item.position ={};
      }
      // 动画位置属性
      const framePositionStyle = new AnimationFramePositionStyle(this.context, isDisabled);
      framePositionStyle.initPanel(this.container, item.position)

      new GapStyle(this.container, 8)

      // 动画文字属性
      const fontStyle = new AnimationFrameFontStyle(this.context, isDisabled);
      fontStyle.initPanel(this.container, item.font)

      new GapStyle(this.container, 8)

      if (!item.border) {
        item.border ={};
      }
      // 动画边框属性
      const borderStyle = new AnimationFrameBorderStyle(this.context, isDisabled);
      borderStyle.initPanel(this.container, item.border)

      new GapStyle(this.container, 8)

      if (!item.bg) {
        item.bg ={};
      }
      // 动画背景属性
      const frameBgStyle = new AnimationFrameBgStyle(this.context, isDisabled);
      frameBgStyle.initPanel(this.container, item.bg)

      this.refreshModel(item);
      this.bindModel(item);
    }
  }

  getModelName() {
    return 'frame-model'
  }
}