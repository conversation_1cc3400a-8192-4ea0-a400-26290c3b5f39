import AbsSetPanel from "../AbsSetPanel";
import AnimationDialog from "../../../dialog/AnimationDialog";
import AnimationPropertyPanel from "./AnimationPropertyPanel";
import AnimationModel from "../../../model/AnimationModel";
export default class AnimationPanel extends AbsSetPanel{
  constructor(context) {
    super(context)
    this.animationPropertyPanel = new AnimationPropertyPanel(this.context);
  }

  /**
   * 点击添加按钮
   */
  refreshClick(){
    const animationDialog = new AnimationDialog(this.context);
    animationDialog.open("新增动画", (result) => {
      this.add(result, () => {
        this.refreshPanel();
      });
    });
  }
  /**
   * 描述:刷新面板
   */
  refreshPanel () {
    if (this.bodyContainer && this.bodyContainer.length) {
      // 获取计算动态高度所需要的dom
      this.refreshCalcEl();

      const animationMap = this.context.getAnimationMap();
      if (animationMap) {
        this.bodyContainer.empty();
        for (const key in animationMap) {
          const animation = animationMap[key];
          this.refreshItem(animation, this.bodyContainer)
        }
      }
    }
  }

  /**
   * 刷新面板每一项渲染
   * @param item 动画数据
   * @param bodyContainer 容器
   */
  refreshItem(item, bodyContainer) {
    const modelContainer = $(`<div class="comm-wrap tree-wrap">
            <div class="comm-head tree-head layer-head-wrap">
              <div class="head-title">${item.property.name}</div>
              <div class="head-animation-icon icon-edit-outline ft-font mr10" visibility ></div>
              <div class="head-animation-icon icon-delete ft-font mr18" visibility></div>
            </div>
            </div>`);
    const titleContainer = modelContainer.find('.head-title');
    const edit = modelContainer.find('.icon-edit-outline');
    const del = modelContainer.find('.icon-delete');

    // 刷新标题点击
    this.buildClickTitle(titleContainer, item);
    // 刷新编辑按钮点击
    this.buildClickEditIcon(edit, item);
    // 刷新删除按钮点击
    this.buildClickDelIcon(del, item);

    bodyContainer.append(modelContainer);
  }

  /**
   * 标题点击
   * @param titleContainer
   * @param item
   */
  buildClickTitle (titleContainer, item) {
    if (titleContainer && titleContainer.length) {
      titleContainer.click(() => {
        // 刷新标题选中状态
        this.refreshChecked(titleContainer);
        // 展开浮窗
        this.refreshFloatPanel(item);

        // 鼠标点击浮窗以外的区域关闭
        $(document).off('mousedown').on('mousedown', (event) => {
          const target = $(event.target);
          if (!target.closest('.drawer-open').length) {
            const className = target[0].className;
            const classNames = ['head-title', 'comm-head menu-set-head-wrap', 'head-animation-icon icon-bianji ft-font mr10']
            if (!classNames.includes(className)) {
              this.refreshFloatPanel()
            }
          }
        })
      })
    }
  }

  /**
   * 编辑按钮点击
   * @param icon
   * @param item
   */
  buildClickEditIcon (icon, item) {
    icon.click(() => {
      const animationDialog = new AnimationDialog(this.context);
      animationDialog.open("修改动画", (result) => {
        item.property.name = result;
        this.refreshPanel();
        this.refreshFloatPanel();
      }, item.property.name);
    })
  }

  /**
   * 删除按钮点击
   * @param icon
   * @param item
   */
  buildClickDelIcon (icon, item) {
    icon.click(() => {
      this.context.delAnimationById(item.property.id);
      this.refreshPanel();
      this.refreshFloatPanel();
    })
  }

  /**
   * 悬浮窗展示/隐藏
   * @param item 帧数据
   */
  refreshFloatPanel (item) {
    // 显示悬浮层
    if (this.floatContainer && this.floatContainer.length) {
      if (!item) {
        this.floatContainer.css("width", "0");
        this.floatContainer.addClass('drawer-close');
      } else {
        this.floatContainer.css("bottom", "0px");
        this.floatContainer.addClass('drawer-open');
        // 画布容器高度 - x刻度高度 - 缩放工具条高度 - 冗余高度
        this.floatContainer.css("height", (this.mainContainer.outerHeight() - 10 - 40) + 'px');
        this.floatContainer.css("width", "360px");

        this.floatContainer.empty();

        this.animationPropertyPanel.initPanel(this.floatContainer)
        this.animationPropertyPanel.refreshPanel(item);
        this.animationPropertyPanel.initEvent({
          close: () => {
            // 关闭浮窗
            this.refreshFloatPanel();
            // 清空选择
            this.refreshChecked();
          }
        })
      }
    }
  }

  /**
   * 标题选择状态刷新
   * @param container
   */
  refreshChecked (container) {
    if (container) {
      // 清空所有背景
      container.parents('.tree-wrap').siblings('.tree-wrap').each((i, e) => {
        $(e).css("background", "");
      })
      // 添加选中背景
      container.parents('.tree-wrap').css("background", "#409EFF");
    } else {
      this.bodyContainer.find('.tree-wrap').css("background", "");
    }
  }

  /**
   * 添加自定义动画
   * @param result
   * @param callback
   */
  add(result, callback) {
    if (result) {
      const animation = new AnimationModel(this.context)
      animation.initProperty({
        name: result,
      })
      this.context.addAnimation(animation)
    }
    callback && callback();
  }

  refreshCalcEl() {
    if (this.context.designer) {
      if (this.context.designer.menuPanel &&
        this.context.designer.menuPanel.floatContainer &&
        this.context.designer.menuPanel.floatContainer.length) {
        // 浮动容器
        this.floatContainer = this.context.designer.menuPanel.floatContainer;
      }
      if (this.context.designer.drawPanel &&
        this.context.designer.drawPanel.mainContainer &&
        this.context.designer.drawPanel.mainContainer.length) {
        // 画布容器，用来动态浮窗高度
        this.mainContainer = this.context.designer.drawPanel.mainContainer;
      }
    }
  }

  getTitle() {
    return '自定义动画';
  }
}