import AnimationFrameListStyle from "./AnimationFrameListStyle";
import AnimationFrameStyle from "./AnimationFrameStyle";
import {addKeyFrameTag, checkAndRemoveKeyFrameTag} from "../../../utils/AnimateUtils";

export default class AnimationPropertyPanel {
  constructor(context) {
    this.context = context;
  }

  initPanel(container) {
    if (container && container.length) {
      const propertyContainer = $(`
        <div class="menu-ani-property-panel">
            <div class="property-title-wrap">
                <div class="property-title"></div>
                <div class="property-title-gap"></div>
                <div class="property-btn-group">
                    <div class="property-btn property-btn-publish">发布</div>
                    <div class="property-btn property-btn-add">新增</div>
                    <div class="icon-close ft-font""></div>
                </div>
            </div>
            <div class="frame-list-wrap"></div>
            <div class="frame-wrap"></div>
        </div>
    `);
      container.append(propertyContainer)

      // 添加帧
      this.btnAdd = propertyContainer.find('.property-btn-add');
      // 发布动画
      this.btnPublish = propertyContainer.find('.property-btn-publish');
      // 关闭按钮
      this.btnClose = propertyContainer.find('.icon-close');
      // 帧标题
      this.propertyTitle = propertyContainer.find('.property-title')

      // 帧列表容器
      this.frameListWrap = propertyContainer.find('.frame-list-wrap')
      // 帧面板容器
      this.frameWrap = propertyContainer.find('.frame-wrap')

      // 帧列表
      this.frameListStyle = new AnimationFrameListStyle(this.context)
      this.frameListStyle.initPanel(this.frameListWrap)

      // 帧面板
      this.frameStyle = new AnimationFrameStyle(this.context)
      this.frameStyle.initPanel(this.frameWrap)
    }

  }

  /**
   * 悬浮面板刷新
   * @param item
   */
  refreshPanel(item) {
    if (item && item.property) {
      // 更新动画标题
      this.refreshTitle(item.property)

      this.frameStyle.setEvents({
        // 修改帧基本信息回调刷新帧列表标题信息
        refreshTitle: (selectIndex = 0) => {
          this.frameListStyle.refreshSingleItemTitle(item.property, selectIndex)
        }
      })
      this.frameListStyle.setEvents({
        // 帧列表选择帧，刷新帧面板，并记录选择的下标
        refreshFrameSelect: (frame, selectIndex) => {
          // 第一次会默认选中触发
          this.frameStyle.refreshPanel(frame, selectIndex, item.isPublish)
        },
        refreshFrameDelete: (selectIndex) => {
          item.deleteFrame(selectIndex);
          this.frameListStyle.refreshPanel(item.property)
          // 帧列表被清空时，再清空属性面板
          if (item.property.frames.length === 0) {
            this.frameStyle.refreshPanel();
          }
        }
      })
      // 更新帧列表面板
      this.frameListStyle.refreshPanel(item.property)


      this.buildAdd(item);
      this.buildPublish(item);
      this.buildClose();

      if (item.property.frames && item.property.frames.length === 0) {
        this.btnAdd.trigger('click');
      }
    }
  }
  initEvent(events) {
    this.events = events;
  }

  buildAdd(item) {
    if (item.isPublish) {
      this.btnAdd.attr({ 'disabled': true });
      this.btnAdd.off('click');
    } else {
      this.btnAdd.removeAttr('disabled');
      this.buildClick(this.btnAdd, () => {
        item.addFrame()
        this.frameListStyle.refreshPanel(item.property)
      })
    }
  }
  buildPublish(item) {
    this.buildClick(this.btnPublish, () => {
      item.isPublish = !item.isPublish;
      this.btnPublish.text(item.isPublish ? '取消发布' : '发布')
      // 刷新面板
      this.refreshPanel(item);
      this.buildAdd(item);
    })
    this.btnPublish.text(item.isPublish ? '取消发布' : '发布')
  }

  buildClose() {
    this.buildClick(this.btnClose, () => {
      this.events && this.events.close && this.events.close()
    })
  }

  buildClick(el, callback) {
    if (el && callback) {
      el.off('click').on('click', callback)
    }
  }

  refreshTitle(property) {
    if (property) {
      this.propertyTitle.attr('title', property.name)
      this.propertyTitle.text(property.name)
    }
  }
}