import AbstractStyle from "../../../../AbstractStyle";

export default class AnimationAbsCardStyle extends AbstractStyle {
  constructor(context, isDisabled, isOpen) {
    super(context);
    this.isOpen = isOpen || false;
    this.isDisabled = isDisabled || false;
  }
  getClazzColor () {
    return "chart-color";
  }
  getContainer () {
    return $(`<div class="ani-card-wrap"></div>`);
  }
  initModel (container) {
    if (container) {
      const modelContainer = $(`<div>
        <div class="ani-card-head no-select-pointer" >
          <div class="head-title">` + this.getTitle() + `</div>
          <div class="head-icon ft-font icon-ArrowDownBold"></div>
        </div>
        <div class="ani-card-body"></div>
      </div>`);
      container.append(modelContainer);
      this.chartHead = $(modelContainer).find(".ani-card-head");
      this.chartBody = $(modelContainer).find(".ani-card-body");

      this.chartTitle = $(modelContainer).find(".ani-card-head .head-title");
      this.chartIcon = $(modelContainer).find(".ani-card-head .head-icon");
      this.refreshHead();
      this.refreshOpen();
    }
  }
  /**
   *
   */
  refreshHead () {
    const self = this;
    $(this.chartHead).on("click", function (event) {
      self.isOpen = !self.isOpen;
      self.refreshOpen();
    });
  }
  refreshOpen () {
    if (this.isOpen) {
      $(this.chartIcon).attr({ switch: 'open' })
      $(this.chartBody).show();
      this.refreshBody(this.chartBody);
    } else {
      $(this.chartIcon).attr({ switch: 'close' })
      $(this.chartBody).hide();
    }
    this.refreshDisabled();
  }
  refreshDisabled() {
    try {
      const inputContainer = this.chartBody.find('input, select');
      // 禁用输入框和下拉框
      inputContainer.prop({ 'disabled': this.isDisabled });
      // 修改输入框和下拉框父级背景
      inputContainer.parent('.chart-control').css('background', this.isDisabled ? '#373737' : '#1D1D1D');
      // 禁用颜色选择
      $(`.${this.getClazzColor()}`).spectrum(this.isDisabled ? 'disable' : 'enable')
    } catch (e) {
      console.error(e)
    }
  }
  /**
   *
   * @param {*} title
   */
  refreshTitle (title) {
    if (this.chartTitle && title) {
      $(this.chartTitle).text(title);
    }
  }
}