import AbstractStyle from "../../../../AbstractStyle";

export default class AnimationFrameBaseStyle extends AbstractStyle{
  constructor(context, isDisabled) {
    super(context);
    this.isDisabled = isDisabled;
  }

  refreshPanel (chartBody, modelName, item, callback) {
    const frameBaseContainer = $(`<div class="frame-base">
            <div class="frame-base-head">
                <div class="head-title">${item.name}${item.frameIndex}</div>
                <!-- <div class="flex">
                    <div class="base-btn btn-revert">还原</div>
                    <div class="base-btn btn-giveup ml12">拾取</div>
                </div> -->
            </div>
            <div class="chart-item">
                <div class="chart-label">帧(%)</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="帧(%)" ${modelName}="frameIndex">
                </div>
            </div>
            <!-- <div class="chart-item">
                <div class="chart-label">偏移路径</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="偏移路径 offset-path" ${modelName}="offset-path">
                </div>
            </div>
            <div class="chart-item">
                <div class="chart-label">偏移轨迹(%)</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="偏移轨迹(%) offset-distance" ${modelName}="offset-distance">
                </div>
            </div> -->
            <div class="chart-item">
                <div class="chart-label">不透明度(%)</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="不透明度(%)" ${modelName}="opacity">
                </div>
            </div>
        </div>`);

    chartBody.append(frameBaseContainer);

    this.titleContainer = frameBaseContainer.find('.head-title')
    this.btnRevert = frameBaseContainer.find('.btn-revert')
    this.btnGiveup = frameBaseContainer.find('.btn-giveup')

    this.buildClick(this.btnRevert, () => {

    });
    this.buildClick(this.btnGiveup, () => {

    });

    this.refreshModel(item);
    this.bindModel(item, callback);

    this.buildDisabled(frameBaseContainer);
  }

  refreshEvent(key, value) {
    if (key === 'frameIndex') {
      // 修改基础文本标题
      if (this.item.frameIndex || this.item.frameIndex === 0) {
        this.titleContainer.text(`${this.item.name}${this.item.frameIndex}`);
      } else {
        this.titleContainer.text(`${this.item.name}`);
      }
    }
  }

  buildClick(el, callback) {
    if (el && el.length) {
      el.off('click').on('click', () => {
        callback && callback()
      });
    }
  }

  buildDisabled(container) {
    if (container && container.length) {
      container.find('input').prop('disabled', this.isDisabled);
      container.find('input').parent('.chart-control').css('background', this.isDisabled ? '#373737' : '#1D1D1D');
    }
  }

  getModelName() {
    return 'frame-base';
  }
}