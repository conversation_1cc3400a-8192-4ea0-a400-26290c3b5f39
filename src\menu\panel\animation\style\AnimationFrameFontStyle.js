import TextShadowStyle from "../../../../property/style/layer/style/TextShadowStyle";
import AnimationAbsCardStyle from "./AnimationAbsCardStyle";
import {getFontFamily} from "../../../../utils/StyleUtil";

export default class AnimationFrameFontStyle extends AnimationAbsCardStyle{
  constructor(context, isDisable) {
    super(context, isDisable, false);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    //字体大小
    const fontSizeContainer = $(`<div class="chart-item">
          <div class="chart-label">大小</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="字体大小" ` + modelName + `="fontSize" />
          </div>
      </div>`);
    chartBody.append(fontSizeContainer);

    //颜色
    const colorContainer = $(`<div class="chart-item">
            <div class="chart-label">颜色</div>
            <div class="chart-control" style="padding: 0">
                <input type="text" class="chart-color" ` + modelName + `="color"   />
            </div>
        </div>`);
    chartBody.append(colorContainer);
    //行高
    const lineHeightContainer = $(`<div class="chart-item">
          <div class="chart-label">行高</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="" ` + modelName + `="lineHeight" />
          </div>
      </div>`);
    chartBody.append(lineHeightContainer);
    //粗细
    const fontWeightContainer = $(`<div class="chart-item">
        <div class="chart-label">粗细</div>
        <div class="chart-control">
            <select class="chart-select" ` + modelName + `="fontWeight" >
                <option value="">--请选择--</option>
                <option value="normal">正常</option>
                <option value="bold">粗体</option>
                <option value="bolder">加粗</option>
                <option value="lighter">细体</option>
            </select>
        </div>
      </div>`);
    chartBody.append(fontWeightContainer);
    //样式
    const fontStyleContainer = $(`<div class="chart-item">
        <div class="chart-label">样式</div>
        <div class="chart-control">
            <select class="chart-select" ` + modelName + ` = "fontStyle" >
                <option value="">--请选择--</option>
                <option value="normal">正常</option>
                <option value="italic">斜体</option>
                <option value="oblique">倾斜</option>
                <option value="inherit">继承</option>
            </select>
        </div>
      </div>`);
    chartBody.append(fontStyleContainer);
    //装饰
    const decorationContainer = $(`<div class="chart-item">
        <div class="chart-label">装饰</div>
        <div class="chart-control">
            <select class="chart-select" ` + modelName + ` = "textDecoration" >
                <option value="">--请选择--</option>
                <option value="overline">上划线</option>
                <option value="line-through">删除线</option>
                <option value="underline">下划线</option>
                <option value="blink">闪烁</option>
                <option value="none">默认</option>
            </select>
        </div>
      </div>`);
    chartBody.append(decorationContainer);
    //字体
    const familyAndColorContainer = $(`<div class="chart-item">
            <div class="chart-label">字体</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + `="fontFamily" ></select>
            </div>
        </div>`);
    chartBody.append(familyAndColorContainer);
    const fontFamilySelect = familyAndColorContainer.find(".chart-select");
    let fontList;
    const fontMap = getFontFamily();
    for (let key in fontMap) {
      const fontItem = { key: key, text: fontMap[key] };
      if (!fontList) {
        fontList = [];
      }
      fontList.push(fontItem);
    }
    this.refreshOption(fontFamilySelect, fontList);

    //字体水平和垂直对齐
    const alignContainer = $(`<div class="chart-item">
        <div class="chart-label">水平</div>
        <div class="chart-control">
            <select class="chart-select" ` + modelName + `="textAlign" >
                <option value="">--请选择--</option>
                <option value="left">左</option>
                <option value="right">右</option>
                <option value="center">居中</option>
            </select>
        </div>
      </div>`);
    chartBody.append(alignContainer);

    const verticalAlignContainer = $(`<div class="chart-item">
        <div class="chart-label">垂直</div>
        <div class="chart-control">
            <select class="chart-select" ` + modelName + `="verticalAlign" >
                <option value="">--请选择--</option>
                <option value="top">顶</option>
                <option value="middle">居中</option>
                <option value="bottom">底</option>
            </select>
        </div>
      </div>`);
    chartBody.append(verticalAlignContainer);
    //字体缩进
    const textIndentContainer = $(`<div class="chart-item">
          <div class="chart-label">缩进</div>
          <div class="chart-control">
              <input type="text" class="chart-text" min="0" max="" placeholder="" ` + modelName + `="textIndent" />
          </div>
      </div>`);
    chartBody.append(textIndentContainer);
    //字体间距
    const spacingContainer = $(`<div class="chart-item">
        <div class="chart-label">间距</div>
        <div class="chart-control">
            <input type="number" class="chart-number"  min="0" max="" placeholder="" ` + modelName + `="letterSpacing" />
        </div>
      </div>`);
    chartBody.append(spacingContainer);
    //下拉字体大小
    const optionFontSizeContainer = $(`<div class="chart-item">
        <div class="chart-label">下拉字体</div>
        <div class="chart-control">
          <input type="number" class="chart-number"  min="0" max="" placeholder="大小" ` + modelName + `="optionFontSize" />
        </div>
      </div>`);
    chartBody.append(optionFontSizeContainer);

    //文本阴影
    if (!item["textShadow"]) {
      item["textShadow"] = {};
    }
    const textShadowStyle = new TextShadowStyle(this.context);
    textShadowStyle.initPanel(chartBody, item["textShadow"], callback);

    this.refreshModel(item);
    this.bindModel(item, callback);
  }

  getModelName () {
    return "animation-position";
  }

  getTitle() {
    return "文字属性";
  }
}