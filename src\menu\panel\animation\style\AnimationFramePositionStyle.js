import AnimationAbsCardStyle from "./AnimationAbsCardStyle";

export default class AnimationFramePositionStyle extends AnimationAbsCardStyle{
  constructor(context, isDisable) {
    super(context, isDisable, false);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    const positionContainer = $(` 
            <div class="chart-item">
                <div class="chart-label">宽度</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="宽度" ${modelName}="width">
                </div>
            </div>
            <div class="chart-item">
                <div class="chart-label">高度</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="高度" ${modelName}="height">
                </div>
            </div>
            <div class="chart-item">
                <div class="chart-label">位置</div>
                <div class="chart-control flex-1 pl8">
                    <input type="number" min="0" class="chart-number" placeholder="top" ${modelName}="top" />
                    <input type="number" min="0" class="chart-number" placeholder="right" ${modelName}="right" />
                    <input type="number" min="0" class="chart-number" placeholder="bottom" ${modelName}="bottom" />
                    <input type="number" min="0" class="chart-number" placeholder="left" ${modelName}="left" />
                </div>
            </div>
            <div class="chart-item">
                <div class="chart-label">内间距</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="内间距" ${modelName}="padding">
                </div>
            </div>
            <div class="chart-item">
                <div class="chart-label">外间距</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="外间距" ${modelName}="margin">
                </div>
            </div>
            `)
    chartBody.append(positionContainer)

    this.refreshModel(item);
    this.bindModel(item, callback);
  }

  getModelName () {
    return "animation-position";
  }

  getTitle() {
    return "位置属性";
  }
}