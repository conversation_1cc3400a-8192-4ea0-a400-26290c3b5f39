import AnimationAbsCardStyle from "./AnimationAbsCardStyle";

export default class AnimationFrameTransformStyle extends AnimationAbsCardStyle {
  constructor(context, isDisable) {
    super(context, isDisable, true);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    const transformContainer = $(` 
            <div class="chart-item">
                <div class="chart-label">移动</div>
                <div class="chart-control flex-1 pl8">
                    <input type="number" min="0" class="chart-number" placeholder="X" ` + modelName + `="translateX" />
                    <input type="number" min="0" class="chart-number mlr10" placeholder="Y" ` + modelName + `="translateY" />
                    <input type="number" min="0" class="chart-number" placeholder="Z" ` + modelName + `="translateZ" />
                </div>
            </div>
            <div class="chart-item">
                <div class="chart-label">缩放</div>
                <div class="chart-control flex-1 pl8">
                    <input type="number" min="0" class="chart-number" placeholder="X" ` + modelName + `="scaleX" />
                    <input type="number" min="0" class="chart-number mlr10" placeholder="Y" ` + modelName + `="scaleY" />
                    <input type="number" min="0" class="chart-number" placeholder="Z" ` + modelName + `="scaleZ" />
                </div>
            </div>
            <div class="chart-item">
                <div class="chart-label">倾斜</div>
                <div class="chart-control flex-1 pl8">
                    <input type="number" min="0" class="chart-number" placeholder="X" ` + modelName + `="skewX" />
                    <input type="number" min="0" class="chart-number mlr10" placeholder="Y" ` + modelName + `="skewY" />
                    <input type="number" min="0" class="chart-number" placeholder="Z" ` + modelName + `="skewZ" />
                </div>
            </div>
            <div class="chart-item">
                <div class="chart-label">旋转</div>
                <div class="chart-control flex-1 pl8">
                    <input type="number" min="0" class="chart-number" placeholder="X" ` + modelName + `="rotateX" />
                    <input type="number" min="0" class="chart-number mlr10" placeholder="Y" ` + modelName + `="rotateY" />
                    <input type="number" min="0" class="chart-number" placeholder="Z" ` + modelName + `="rotateZ" />
                </div>
            </div>
            <div class="chart-item">
                <div class="chart-label">基点位置</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="基点位置" ` + modelName + `="transformOrigin" />
                </div>
            </div>
            `)
    chartBody.append(transformContainer)

    this.refreshModel(item);
    this.bindModel(item, callback);
  }

  getModelName () {
    return "animation-transform";
  }

  getTitle() {
    return "转换属性";
  }
}