import DataSetStyle from "./style/DataSetStyle";
import AbsSetPanel from "../AbsSetPanel";
import DataSetModel from "../../../model/DataSetModel";
import DataSetPreviewPanel from "./DataSetPreviewPanel";
import DataSetAddDialog from "../../../dialog/DataSetAddDialog";
import Dialog from "../../../assets/element/jquery/dialog/Dialog";
/**
 * 数据集面板
 */
export default class DataSetPanel extends AbsSetPanel{
  constructor(context) {
    super(context);
    this.dataSetPreviewPanel = new DataSetPreviewPanel(context);
  }
  refreshClick(){
    const dataSetAddDialog = new DataSetAddDialog(this.context);
    dataSetAddDialog.open((item) => {
      if (item) {
        Dialog.closeAllModel();
        this.add(item, () => {
          this.refreshPanel();
        });
      }
    });
  }
  /**
   * 描述:刷新面板
   */
  refreshPanel () {
    const self = this;
    if (this.bodyContainer) {
      this.bodyContainer.empty();
      const modelMap = this.context.getDataSetMap();
      if (modelMap) {
        for (let key in modelMap) {
          const item = modelMap[key]
          const setStyle = new DataSetStyle(this.context, false);
          setStyle.initEvent({
            del: function () {
              self.del(item.property.id);
              self.refreshPanel();
            },
            preview: (item, sheetId) => {
              this.refreshPreview(item, sheetId)
            }
          });
          setStyle.initPanel(this.bodyContainer, item);
        }
        // 获取计算动态高度所需要的dom
        this.refreshCalcEl();

      }
    }
  }
  refreshPreview (item, sheetId) {
    if (this.floatContainer && this.floatContainer.length) {
      if (!item) {
        this.floatContainer.addClass('drawer-close');
        this.floatContainer.css("width", "0");
        this.floatContainer.css("max-width", 'unset');
        this.floatContainer.css("max-height", 'unset');
      } else {
        this.floatContainer.addClass('drawer-open');
        this.floatContainer.css("width", "auto");
        this.floatContainer.css("height", "auto");
        this.floatContainer.css("max-height", (this.mainContainer.outerHeight() - 52) + 'px');
        this.floatContainer.css("max-width", (this.mainContainer.outerWidth() - 20) + 'px');

        this.floatContainer.empty();
        item.previewData((data) => {
          this.dataSetPreviewPanel.initPanel(this.floatContainer)
          this.dataSetPreviewPanel.refreshPanel(data);
        }, sheetId)

        // 鼠标点击浮窗以外的区域关闭
        $(document).off('mousedown').on('mousedown', (event) => {
          const target = $(event.target);
          if (!target.closest('.drawer-open').length) {
            const className = target[0].className;
            const classNames = ['head-title', 'menu-set-head-title', 'head-animation-icon icon-bianji ft-font mr10']
            if (!classNames.includes(className)) {
              this.refreshPreview()
              $(document).off('mousedown')
            }
          }
        })
      }

    }
  }
  refreshCalcEl() {
    if (this.context.designer) {
      if (this.context.designer.menuPanel &&
        this.context.designer.menuPanel.floatContainer &&
        this.context.designer.menuPanel.floatContainer.length) {
        // 浮动容器
        this.floatContainer = this.context.designer.menuPanel.floatContainer;
      }
      if (this.context.designer.drawPanel &&
        this.context.designer.drawPanel.mainContainer &&
        this.context.designer.drawPanel.mainContainer.length) {
        // 画布容器，用来动态浮窗高度
        this.mainContainer = this.context.designer.drawPanel.mainContainer;
      }
    }
  }
  /**
   * 标题
   * @returns 
   */
  getTitle () {
    return "数据集";
  }
  /**
   * 添加
   */
  add (item, callback) {
    if (this.context) {
      const model = new DataSetModel(this.context);
      model.refreshProperty(item);
      this.context.addDataSet(model);
      model.refreshData(callback);

    }
  }
  /**
   * 删除
   * @param {*} index 
   */
  del (id) {
    if (this.context && id) {
      this.context.delDataSetById(id);
    }
  }
}