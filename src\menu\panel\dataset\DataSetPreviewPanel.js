import PageTable from "../../../assets/element/jquery/table/PageTable";

export default class DataSetPreviewPanel {
  constructor(context) {
    this.context = context;
  }

  initPanel(container) {
    if (container && container.length) {
      const propertyContainer = $(`
        <div class="data-set-preview-panel">
            <div class="table-wrap"></div>
        </div>
    `);
      container.append(propertyContainer)
      //表格容器
      this.tableWrap = propertyContainer.find('.table-wrap')
    }

  }

  /**
   * 悬浮面板刷新
   * @param data
   * @param isText
   */
  refreshPanel(data) {
    if (data && data.columns && data.data) {
      const table = new PageTable(true);
      const columns = data.columns.map(item => {
        return {
          key: item,
          name: item,
          type: 'text',
        }
      })
      this.tableWrap.css('marginLeft', '-24px')
      table.initPanel(this.tableWrap, {
        columns,
      });
      table.refreshPanel(data.data);
      table.initPanel()
    }
  }
}