import AbstractStyle from "../../../../AbstractStyle";
export default class DataSetFileStyle extends AbstractStyle {
  constructor(context, isOpen) {
    super(context);
    this.isOpen = isOpen || true;
  }
  getContainer () {
    return $(`<div class="menu-chart-wrap"></div>`);
  }

  initModel (container) {
    if (container) {
      container.empty();
      const modelName = this.getModelName();
      this.refreshPanel(container, modelName, this.item, this.callback);
    }
  }
  refreshPanel (container, modelName, item, callback) {
    console.log('item.property.columns', item.property.columns)
    if (item.property.columns && item.property.columns.length) {
      for (let i = 0; i < item.property.columns.length; i++) {
        const sheet = item.property.columns[i];
        const sheetContainer = $(`<div class="comm-wrap menu-set-card-wrap">
            <div class="comm-head menu-set-card-head" switch="close" style="padding: 0; border: none">
              <div class="menu-set-head-icon head-icon-fold ft-font icon-shangla" style="font-size: 10px"></div>
              <div class="menu-set-head-title" switch="${sheet.isOpen ? 'open' : 'close'}" title="${sheet.name}">${sheet.name}</div>
              <div class="menu-set-head-preview">预览</div>
            </div>
            <div class="comm-body menu-set-card-body" style="padding: 0"></div>
          </div>`);
        container.append(sheetContainer);
        const sheetBodyContainer = sheetContainer.find('.menu-set-card-body');
        const sheetHeaderContainer = sheetContainer.find('.menu-set-card-head');
        if (sheet.columns && sheet.columns.length) {
          for (let j = 0; j < sheet.columns.length; j++) {
            this.refreshItem(sheetBodyContainer, sheet.columns[j])
          }
        }

        // 刷新sheet折叠状态
        function refreshSheetFolderStatus (sheet) {
          if (sheet) {
            if (sheet.isOpen) {
              sheetHeaderContainer.attr({ switch: 'open' });
              sheetBodyContainer.show();
            } else {
              sheetBodyContainer.hide();
              sheetHeaderContainer.attr({ switch: 'close' });
            }
          }
        }
        refreshSheetFolderStatus(sheet)

        const titleContainer = $(sheetContainer).find(".menu-set-card-head .menu-set-head-title");
        // 标题点击折叠
        titleContainer.on("click", (event) => {
          event.stopPropagation();
          sheet.isOpen = !sheet.isOpen;
          refreshSheetFolderStatus(sheet)
        });
        const previewContainer = $(sheetContainer).find(".menu-set-card-head .menu-set-head-preview");
        // 预览点击
        previewContainer.on("click", (event) => {
          event.stopPropagation();
          if (callback) {
            callback(item, sheet.id);
          }
        });
      }

      this.refreshOpen();
    }
  }

  refreshOpen () {
    if (this.titleContainer) {
      if (this.isOpen) {
        this.titleContainer.attr({ switch: 'open' })
        $(this.bodyContainer).show();
      } else {
        this.titleContainer.attr({ switch: 'close' })
        $(this.bodyContainer).hide();
      }
    }
  }

  refreshItem(bodyContainer,item){
    if(bodyContainer && item){
      const itemContainer=$(`<div class="set-item" title="${item}">
          ${item}
        </div>`);
      bodyContainer.append(itemContainer);
    }
  }
}