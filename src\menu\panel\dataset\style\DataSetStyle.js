import AbsCardDelStyle from "../../style/AbsCardDelStyle";
import DataSetFileStyle from "./DataSetFileStyle";
export default class DataSetStyle extends AbsCardDelStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      // 数据模型显示
      if (item.property.type === 'model') {
        this.refreshModelPanel(chartBody, modelName, item, callback);
      }
      // 文件显示
      if (item.property.type === 'file' && item.property.columns) {
        this.refreshFilePanel(chartBody, modelName, item, callback);
      }
    }

    this.bindModel(item.property)
    this.refreshModel(item.property)
  }

  /**
   * 文件类型面板显示
   */
  refreshFilePanel (chartBody, modelName, item, callback) {
    const file = item.property.file;
    switch (file.fileType) {
      case 'xlsx':
      case 'csv':
        this.refreshFileXlsxPanel(chartBody, modelName, item, callback);
        break
      case 'json':
        this.refreshFileJsonPanel(chartBody, modelName, item, callback);
        break
    }
  }
  /**
   * excel文件类型面板显示
   */
  refreshFileXlsxPanel (chartBody, modelName, item, callback) {
    const textContainer = $(`<div class="chart-item flex mb0">
            <div class="chart-label">名称</div>
            <div class="chart-control">
                <input type="text" class="chart-text" placeholder="名称" ` + modelName + `="name" />
            </div>
        </div>`);
    chartBody.append(textContainer);

    const datasetFileStyle = new DataSetFileStyle(this.context);
    datasetFileStyle.initPanel(chartBody, item, this.events.preview);
    // 使用内部预览，所以这个预览移除
    this.previewContainer.remove();
  }
  /**
   * json文件类型面板显示
   */
  refreshFileJsonPanel (chartBody, modelName, item, callback) {
    const textContainer = $(`<div class="chart-item flex mb0">
            <div class="chart-label">名称</div>
            <div class="chart-control">
                <input type="text" class="chart-text" placeholder="名称" ` + modelName + `="name" />
            </div>
        </div>`);
    chartBody.append(textContainer);

    const columns=item.property.columns;
    if(columns && columns.length){
      for(let i=0;i<columns.length;i++){
        const column = columns[i];
        this.refreshItem(chartBody, column);
      }
    }
  }

  /**
   * 数据模型面板显示
   */
  refreshModelPanel (chartBody, modelName, item, callback) {
    const textContainer = $(`<div class="chart-item flex">
            <div class="chart-label">名称</div>
            <div class="chart-control">
                <input type="text" class="chart-text" placeholder="名称" ` + modelName + `="name" />
            </div>
        </div>`);
    chartBody.append(textContainer);
    const refreshContainer = $(`<div class="chart-item flex">
          <div class="chart-label">刷新方式</div>
          <div class="chart-control">
              <select class="chart-select" ` + modelName + `="refreshType" >
                  <option value="">-请选择-</option>
                  <option value="1">定时刷新</option>
                  <option value="2">不刷新</option>
              </select>
          </div>
      </div>`);
    chartBody.append(refreshContainer);
    const refreshTimeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">刷新时间</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" placeholder="[毫秒]" ` + modelName + `="refreshTime" />
          </div>
      </div>`);
    chartBody.append(refreshTimeContainer);

    const columns=item.property.columns;
    if(columns && columns.length){
      for(let i=0;i<columns.length;i++){
        const column = columns[i];
        this.refreshItem(chartBody, column);
      }
    }
  }

  refreshItem(bodyContainer,item){
    if(bodyContainer && item){
      const itemContainer=$(`<div class="set-item" title="${item}">
          ${item}
        </div>`);
      bodyContainer.append(itemContainer);
    }
  }

  initModel(container) {
    if (container) {
      const modelContainer = $(`<div class="comm-wrap menu-set-card-wrap">
        <div class="comm-head menu-set-card-head">
          <div class="menu-set-head-icon head-icon-fold ft-font icon-shangla"></div>
          <div class="menu-set-head-title" title="${this.getTitle()}">${this.getTitle()}</div>
          <div class="menu-set-head-preview">预览</div>
          <div class="menu-set-head-icon head-icon-del ft-font icon-guanbi"></div>
        </div>
        <div class="comm-body menu-set-card-body" style="padding: 8px 16px"></div>
      </div>`);
      container.append(modelContainer);
      this.headContainer = $(modelContainer).find(".menu-set-card-head");
      this.bodyContainer = $(modelContainer).find(".menu-set-card-body");

      this.titleContainer = $(modelContainer).find(".menu-set-card-head .menu-set-head-title");
      this.previewContainer = $(modelContainer).find(".menu-set-card-head .menu-set-head-preview");

      const btnDelContainer = $(modelContainer).find(".menu-set-card-head .head-icon-del");
      btnDelContainer.on("click", (event) => {
        event.stopPropagation();
        if (this.events.del) {
          this.events.del();
        }
      });
      this.headContainer.on("click", (event) => {
        event.stopPropagation();
        this.isOpen = !this.isOpen;
        this.refreshOpen()
      });
      this.previewContainer.on("click", (event) => {
        event.stopPropagation();
        if (this.events.preview) {
          this.events.preview(this.item);
        }
      })
      this.refreshOpen();
      this.refreshPreviewDisplay();
    }
  }
  refreshPreviewDisplay () {
    this.previewContainer.hide();
    if (this.item) {
      const property = this.item.property;
      if (property.type === 'model') {
        this.previewContainer.show();
      }
      if (property.type === 'file') {
        const file = property.file;
        if (file.fileType === 'xlsx' || file.fileType === 'csv') {
          this.previewContainer.hide();
        }
        if (file.fileType === 'json') {
          this.previewContainer.show();
        }
      }
    }
  }
  refreshOpen () {
    if (this.isOpen) {
      this.headContainer.attr({ switch: 'open' })
      $(this.bodyContainer).show();
      this.refreshBody(this.bodyContainer);
    } else {
      this.headContainer.attr({ switch: 'close' })
      $(this.bodyContainer).hide();
    }
  }
  refreshEvent (key, value) {
    if (key === 'name') {
      this.titleContainer.text(value);
      this.titleContainer.attr('title', value);
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "dataset-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
   getTitle () {
    let text;
    if (this.item && this.item.property&& this.item.property.name) {
      text = this.item.property.name;
    }
    return text || "数据";
  }
}