import AbstractStyle from "../../../AbstractStyle";
export default class AbsCardDelStyle extends AbstractStyle {
  constructor(context, isOpen) {
    super(context);
    this.isOpen = isOpen;
  }
  getContainer () {
    return $(`<div class="menu-chart-wrap"></div>`);
  }
  getClazzColor () {
    return "chart-color";
  }

  initModel (container) {
    if (container) {
      const modelContainer = $(`<div class="comm-wrap menu-set-card-wrap">
        <div class="comm-head menu-set-card-head">
          <div class="menu-set-head-title">` + this.getTitle() + `</div>
          <div class="menu-set-head-icon ft-font icon-guanbi"></div>
        </div>
        <div class="comm-body menu-set-card-body"></div>
      </div>`);
      container.append(modelContainer);
      this.headContainer = $(modelContainer).find(".menu-set-card-head");
      this.bodyContainer = $(modelContainer).find(".menu-set-card-body");

      this.titleContainer = $(modelContainer).find(".menu-set-card-head .menu-set-head-title");
      const self = this;
      const btnContainer = $(modelContainer).find(".menu-set-card-head .menu-set-head-icon");
      $(btnContainer).on("click", function (event) {
        if (self.events.del) {
          self.events.del();
        }
      });
      this.refreshHead();
      this.refreshOpen();
    }
  }
  /**
   * 
   */
  refreshHead () {
    const self = this;
    $(this.titleContainer).on("click", function (event) {
      self.isOpen = !self.isOpen;
      self.refreshOpen();
    });
  }
  refreshOpen () {
    if (this.bodyContainer) {
      if (this.isOpen) {
        $(this.bodyContainer).show();
        this.refreshBody(this.bodyContainer);
      } else {
        $(this.bodyContainer).hide();
      }
    }
  }
  /**
   * 
   * @param {*} title 
   */
  refreshTitle (title) {
    const text = title || '标题';
    if (this.titleContainer && text) {
      $(this.titleContainer).text(text);
    }
  }
  initEvent(events){
    if(events){
      this.events=events
    }
  }
}