import AbsCardDelStyle from "./AbsCardDelStyle";
export default class DataSetStyle extends AbsCardDelStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const columns=item.columns;
      if(columns && columns.length){
        for(let i=0;i<columns.length;i++){
          const column = columns[i];
          // const col = property[column];
          this.refreshItem(chartBody, column);
        }
      }
    }
  }

  initModel(container) {
    if (container) {
      const modelContainer = $(`<div class="comm-wrap menu-set-card-wrap">
        <div class="comm-head menu-set-card-head">
          <div class="menu-set-head-icon head-icon-fold ft-font icon-shangla"></div>
          <div class="menu-set-head-title" title="${this.getTitle()}">${this.getTitle()}</div>
          <div class="menu-set-head-icon head-icon-del ft-font icon-guanbi"></div>
        </div>
        <div class="comm-body menu-set-card-body" style="padding: 8px 16px"></div>
      </div>`);
      container.append(modelContainer);
      this.headContainer = $(modelContainer).find(".menu-set-card-head");
      this.bodyContainer = $(modelContainer).find(".menu-set-card-body");

      this.titleContainer = $(modelContainer).find(".menu-set-card-head .menu-set-head-title");
      const self = this;
      const btnDelContainer = $(modelContainer).find(".menu-set-card-head .head-icon-del");
      $(btnDelContainer).on("click", function (event) {
        event.stopPropagation();
        if (self.events.del) {
          self.events.del();
        }
      });
      this.refreshHead();
      this.refreshOpen();
    }
  }
  refreshOpen () {
    if (this.bodyContainer) {
      if (this.isOpen) {
        this.headContainer.attr({ switch: 'open' })
        $(this.bodyContainer).show();
        this.refreshBody(this.bodyContainer);
      } else {
        this.headContainer.attr({ switch: 'close' })
        $(this.bodyContainer).hide();
      }
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "dataset-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
   getTitle () {
    let text;
    if (this.item && this.item.property&& this.item.property.name) {
      text = this.item.property.name;
    }
    return text || "数据";
  }

  refreshItem(bodyContainer,item){
    if(bodyContainer && item){
      const itemContainer=$(`<div class="set-item" title="${item}">
          ${item}
        </div>`);
      bodyContainer.append(itemContainer);
    }
  }
}