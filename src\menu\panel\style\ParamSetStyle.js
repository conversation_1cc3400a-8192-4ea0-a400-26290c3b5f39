import AbsCardDelStyle from "./AbsCardDelStyle";
export default class ParamSetStyle extends AbsCardDelStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    //编码
    const codeContainer = $(`<div class="menu-chart-item">
      <div class="menu-chart-label">编码</div>
        <div class="menu-chart-control">
            <input type="text" class="chart-text" placeholder="编码" ` + modelName + `="code" />
        </div>
      </div>`);
    chartBody.append(codeContainer);
    //类型
    const typeContainer = $(`<div class="menu-chart-item">
      <div class="menu-chart-label">类型</div>
        <div class="menu-chart-control">
          <select class="chart-select" `+ modelName + `="type">
              <option value="">默认</option>
              <option value="url">地址栏</option>
              <option value="sys">系统</option>
          </select>
        </div>
      </div>`);
    chartBody.append(typeContainer);
    //表达式
    const exprContainer = $(`<div class="menu-chart-item">
      <div class="menu-chart-label">表达式</div>
        <div class="menu-chart-control">
            <input type="text" class="chart-text" placeholder="\${code}" ` + modelName + `="expr" />
        </div>
      </div>`);
    chartBody.append(exprContainer);
    //值
    const valueContainer = $(`<div class="menu-chart-item">
      <div class="menu-chart-label">值</div>
        <div class="menu-chart-control">
            <input type="text" class="chart-text" placeholder="值" ` + modelName + `="value" disabled/>
        </div>
      </div>`);
    chartBody.append(valueContainer);
    this.inputContainer=$(valueContainer).find(".chart-text");

    this.refreshModel(item.property);
    this.bindModel(item.property, callback);
  }

  refreshEvent (key, value) {
    if (key && key === 'code' && value) {
      this.refreshTitle(value);
    }
  }
  refreshEventByBind(key, value){
    if (key && key === 'expr' && value) {
      this.item.refreshData();
      $(this.inputContainer).val(this.item.property.value).attr({ title: this.item.property.value });
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "param-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    let text;
    if (this.item && this.item.property&& this.item.property.code) {
      text = this.item.property.code;
    }
    return text || "参数";
  }
}