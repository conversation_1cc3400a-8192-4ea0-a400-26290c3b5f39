import { uuid } from "../utils/Util";
import { addKeyFrameTag, checkAndRemoveKeyFrameTag } from "../utils/AnimateUtils";
/**
 * 参数对象
 */
export default class AnimationModel {
  constructor(context) {
    this.context = context;
    this.isChecked = false;
  }
  getDefaultProperty () {
    return {
      id: uuid(),
      code: "",
      name: "自定义动画",
      isPublish: false, // 是否发布
      remark: '备注',
      frames: []
    }
  }
  /**
   * 初始化-属性
   * @param property
   */
  initProperty (property) {
    this.property = this.getDefaultProperty();
    if (property) {
      for (let key in property) {
        if (!this.property) {
          this.property = {};
        }
        this.property[key] = property[key];
      }
    }
  }
  set isPublish(isPublish) {
    if (this.property) {
      this.property.isPublish = !!isPublish;
      this.refreshPublish();
    }
  }
  get isPublish () {
    return !!this.property.isPublish;
  }

  /**
   * 刷新当前发布动画
   */
  refreshPublish () {
    checkAndRemoveKeyFrameTag(this.property);
    if (this.isPublish) {
      this.publishAnimation();
    }

    // 刷新使用当前动画的图层样式
    const layerMap = this.context.getLayerMap();
    for (const key in layerMap) {
      const layer = layerMap[key];
      if (layer.property && layer.property.animate && layer.property.animate.name === this.property.id) {
        layer.property.animate.isAnimate = this.isPublish;
        layer.refreshCompCss();
      }
    }
  }

  /**
   * 发布动画到style
   */
  publishAnimation () {
    addKeyFrameTag(this.context, this.property);
  }

  /**
   * 添加帧
   */
  addFrame() {
    if (this.property && this.property.frames && Array.isArray(this.property.frames)) {
      this.property.frames.push({
        name: '帧',
        frameIndex: 0
      })
    } else {
      this.property.frames = [{
        name: '帧',
        frameIndex: 0
      }];
    }
  }

  /**
   * 删除帧
   * @param index 下标
   */
  deleteFrame (index) {
    if (this.property && this.property.frames && this.property.frames.length) {
      this.property.frames.splice(index, 1);
    }
  }
}