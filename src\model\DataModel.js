import { uuid } from "../utils/Util";
import DataModelService from "../service/DataModelService";
import BindQueryHandler from "../service/BindQueryHandler";
/**
 * 数据集对象
 */
export default class DataModel {
  constructor(context) {
    this.context = context;
    this.$$isLoading = false;
    this.initProperty();
    this.columns = ["areaCode", "year", "month", "amount"];
    // this.params = [];
    this.datas = [];
  }
  getDefaultProperty () {
    return {
      id: uuid(),
      code: "编码",
      name: "名称",
      remark: "备注",
      linkId: "模型id",
      params: [],//回显-处理参数列表转Map
    }
  }
  /**
   * 初始化-属性
   * @param {*} property 
   */
  initProperty (property) {
    this.property = this.getDefaultProperty();
    if (property) {
      for (let key in property) {
        if (!this.property) {
          this.property = {};
        }
        this.property[key] = property[key];
      }
    }
  }
  /**
   * 刷新-属性
   * @param {*} property 
   * @param {*} isnew 是否新数据
   */
  refreshProperty (property, isNew) {
    if (property) {
      let json = JSON.parse(JSON.stringify(property));
      if (json["id"] && isNew) {
        this.property.id = json["id"];
        this.property.linkId = json["id"];
      }
      if (json["code"]) {
        this.property.code = json["code"];
      }
      if (json["name"]) {
        this.property.name = json["name"];
      }
      if (json["remark"]) {
        this.property.remark = json["remark"];
      }
      if (json["params"] && isNew) {
        this.property.params = json["params"];
      }
    }
  }
  /**
   * 关联-异步存储(数据库只存了关联Id)
   * @param {*} callback 
   */
  refreshData (callback) {
    const self = this;
    this.$$isLoading = true;
    const params = { id: this.property.linkId };
    //查询基本信息
    const dataModelService = new DataModelService(self.context);
    dataModelService.queryById(params, function (model) {
      //刷新属性
      self.refreshProperty(model);
      self.queryData(callback);
    },
      function (error) {
        //是否加载中
        self.$$isLoading = false;
        //回调
        if (callback) {
          callback();
        }
      });
  }
  queryData (callback) {
    const self = this;
    this.$$isLoading = true;
    //查询数据
    const bindQueryHandler = new BindQueryHandler(self.context, self.property);
    bindQueryHandler.queryData(function (result) {
      //字段
      if (result && result.columns) {
        self.columns = result.columns;
      } else {
        self.columns = [];
      }
      //数据
      if (result && result.data) {
        self.datas = result.data;
      } else {
        self.data = [];
      }
      // 获取模型参数
      self.queryParamByModelId((model) => {
        if (model && model.length) {
          // 设置模型参数
          self.property.params = model.map(m => {
            return {
              name: m.name,
              type: m.type,
              defValue: m.defValue
            }
          })
        } else {
          self.property.params = undefined;
        }
      })
      //是否加载中
      self.$$isLoading = false;
      //回调
      if (callback) {
        callback();
      }
    }, self.property.params);
  }

  /**
   * 根据模型id获取模型设置的参数
   * @param callback 回调
   */
  queryParamByModelId (callback) {
    const self = this;
    this.$$isLoading = true;
    const params = { modelId: this.property.id };
    //查询基本信息
    const dataModelService = new DataModelService(self.context);
    dataModelService.queryByModelId(params, function (model) {
        if (!(model && model.length)) {
          console.error('接口数据为空', model)
        }
        callback && callback(model)
        self.$$isLoading = false;
      },
      function (error) {
        //是否加载中
        self.$$isLoading = false;
        //回调
        if (callback) {
          callback();
        }
      });
  }
  getModelParams () {
    if (this.property && this.property.params && this.property.params.length) {
      return this.property.params;
    }
  }
  getModelColumns () {
    if (this.columns && this.columns.length) {
      return this.columns;
    }
  }
  /**
   * 获取数据
   * @returns 
   */
  getModelDatas () {
    if (this.datas && this.datas.length) {
      return this.datas;
    }
  }

}