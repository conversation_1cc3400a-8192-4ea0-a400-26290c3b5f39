import {endsWith, is<PERSON>um, json<PERSON><PERSON>, startsWith, uuid} from "../utils/Util";
import BindQueryHandler from "../service/BindQueryHandler";
import DataModelService from "../service/FilelService";
import {decodeBase64} from "../utils/DecodeUtil";
import {error} from "../assets/element/jquery/msg/MsgUtil";

/**
 * 数据集对象
 */
export default class DataSetModel {
  constructor(context) {
    this.context = context;
    this.$$isLoading = false;
    this.initProperty();
    this.datas = [];
  }
  getDefaultProperty () {
    return {
      id: uuid(),
      code: "编码",
      name: "名称",
      remark: "备注",
      modelId: "模型id",
      refreshType: undefined,
      refreshTime: undefined,
      type: "model",
      columns: [],
      params: [],//回显-处理参数列表转Map
    }
  }
  /**
   * 初始化-属性
   * @param {*} property 
   */
  initProperty (property) {
    this.property = this.getDefaultProperty();
    if (property) {
      for (let key in property) {
        if (!this.property) {
          this.property = {};
        }
        this.property[key] = property[key];
      }
    }
  }
  /**
   * 刷新-属性
   * @param {*} property 
   * @param {*} isnew 是否新数据
   */
  refreshProperty (property) {
    if (property) {
      let json = JSON.parse(JSON.stringify(property));
      if (json["id"]) {
        this.property.modelId = json["id"];
      }
      if (json["code"]) {
        this.property.code = json["code"];
      }
      if (json["name"]) {
        this.property.name = json["name"];
      }
      if (json["remark"]) {
        this.property.remark = json["remark"];
      }
      if (json["type"]) {
        this.property.type = json["type"];
        if (this.property.type === "file") {
          this.property.file = json['file'];
        }
      }
      if (json["params"]) {
        this.property.params = json["params"];
      }
      if (json["columns"]) {
        this.property.columns = json["columns"];
      }
    }
  }
  transformRequestId () {
    return Object.assign(jsonClone(this.property), {
      id: this.property.modelId,
    });
  }
  getModelColumns () {
    if (this.property.type === "model") {
      return this.property.columns;
    }
  }
  getDatas () {
    if (this.datas && this.datas.length) {
      return this.datas;
    }
  }
  /**
   * 关联-异步存储(数据库只存了关联Id)
   * @param {*} callback 
   */
  refreshData (callback) {
    if (this.property.type === "model") {
      this.$$isLoading = true;
      const bindQueryHandler = new BindQueryHandler(this.context, this.transformRequestId())
      bindQueryHandler.queryData((result) => {
        //数据
        if (result && result.data) {
          this.datas = result.data;
          // 临时使用
          // this.property.columns = result.columns;
        } else {
          this.datas = [];
          error('模型返回数据为空')
        }
        //是否加载中
        this.$$isLoading = false;
        //回调
        if (callback) {
          callback();
        }
      }, this.property.params)
    } else if (this.property.type === 'file') {
      this.$$isLoading = true;
      const file = this.property.file;
      if (file) {
        if (file.datas) {
          // 本地新增
          if (file.fileType === 'xlsx' || file.fileType === 'csv') {
            this.datas = file.datas || [];
            this.property.columns = file.datas.map(d => {
              return {
                id: d.id,
                name: d.name,
                columns: d.columns,
              }
            });
          }
          if (file.fileType === 'json') {
            this.datas = file.datas || {};
            this.property.columns = file.columns || [];
          }
          delete this.property.file.datas
        } else {
          // 大屏初始化
          const dataModelService = new DataModelService(this.context)
          const params = {
            id: this.property.id
          }
          dataModelService.queryFileData(params,(result) => {
            if (result && result.datas) {
              try {
                this.datas = JSON.parse(decodeBase64(result.datas))
              } catch (e) {
                console.error(e)
              }
            }
            //是否加载中
            this.$$isLoading = false;
            //回调
            callback && callback();
          }, (err) => {
            console.error(err)
            callback && callback();
          })
        }
        this.$$isLoading = false;
      }
      callback && callback();
    }
  }

  /**
   * 定时刷新数据
   * @param callback
   */
  refreshInterval (callback) {
    this.clearInterval();
    if (this.property.type === "model" && this.property.refreshType === "1" && this.property.refreshTime) {
      this.refreshIntervalTimer = setInterval(() => {
        this.refreshData(callback)
        this.refreshInterval(callback)
      }, this.property.refreshTime)
    }
  }
  clearInterval () {
    clearInterval(this.refreshIntervalTimer)
  }

  previewData (callback, sheetId) {
    if (this.property.type === "model") {
      this.$$isLoading = true;
      const bindQueryHandler = new BindQueryHandler(this.context, this.transformRequestId())
      bindQueryHandler.queryPreview((result) => {
        //是否加载中
        this.$$isLoading = false;
        //回调
        callback && callback(result);
      })
    }
    if (this.property.type === "file") {
      let result;
      const file = this.property.file;
      if (file.fileType === 'xlsx' || file === 'csv') {
        if (this.datas.length && sheetId) {
          const sheet = this.datas.find(d => d.id === sheetId);
          result = {
            columns: sheet.columns,
            data: sheet.datas,
          };
        }
        callback && callback(result);
      }

      if (file.fileType === 'json') {
        result = {
          data: this.datas,
          columns: this.property.columns
        }
        callback && callback(result);
      }
    }
  }

  /**
   * 筛选器筛选数据
   * @param bind 图层绑定信息
   * @returns {*}
   */
  filterData(bind) {
    if (!bind.isOpen || !this.datas || this.datas.length === 0) {
      return;
    }

    const filterRules = bind.filterRules || [];
    const datas = jsonClone(this.datas);
    let resultDatas;
    const isModel = this.property.type === 'model';
    const isFile = this.property.type === 'file';
    const file = this.property.file;

    // 没有筛选条件 || 筛选条件无效
    const noValidRules = filterRules.length === 0 || !filterRules.some(function (rule) {
      return rule.field && rule.filterType;
    });
    if (noValidRules) {
      return this.handleNoValidRules(datas, isModel, isFile, file, bind);
    }
    // 遍历规则进行筛选
    for (let i = 0; i < filterRules.length; i++) {
      const rule = filterRules[i];

      if (!rule.field) continue;

      const filterType = rule.filterType;
      const field = rule.field;

      // 获取初始数据（第一轮用源数据）
      const initialData = resultDatas || this.getInitialData(datas, isFile, file, bind);

      // 基本筛选
      if (filterType === 'basic' && rule.fieldValue) {
        const fieldValue = rule.fieldValue;
        const fieldValues = fieldValue.split(',');
        resultDatas = this.basicFilter(initialData, fieldValues, field);
      }
      // 范围筛选
      if (filterType === 'range' && rule.range && rule.num) {
        resultDatas = this.rangeFilter(initialData, rule.range, rule.num);
      }
      // 高级筛选
      if (filterType === 'advanced' && rule.operator && (rule.value === 0 || rule.value)) {
        resultDatas = this.advancedFilter(initialData, rule.operator, field, rule.value);
      }
    }

    return resultDatas;
  }

  /**
   * 检查是否没有有效筛选条件
   * @param datas 数据
   * @param isModel 是否模型
   * @param isFile 是否文件
   * @param file 文件
   * @param bind 绑定信息
   * @returns {*|null}
   */
  handleNoValidRules(datas, isModel, isFile, file, bind) {
    if (isModel) {
      return datas;
    }
    if (isFile && file) {
      if (bind.sheetId && (file.fileType === 'xlsx' || file.fileType === 'csv')) {
        const sheet = datas.find(function (d) {
          return d.id === bind.sheetId;
        });
        if (sheet) {
          return sheet.datas
        }
      }
      if (file.fileType === 'json') {
        return datas;
      }
    }
    return null;
  }

  /**
   * 获取初始数据
   * @param datas 数据
   * @param isFile 是否文件
   * @param file 文件
   * @param bind 绑定信息
   * @returns {*}
   */
  getInitialData(datas, isFile, file, bind) {
    if (isFile && file) {
      if (bind.sheetId && (file.fileType === 'xlsx' || file.fileType === 'csv')) {
        const sheet = datas.find(function (d) {
          return d.id === bind.sheetId;
        });
        if (sheet) {
          return sheet.datas;
        }
      }
    }
    return datas;
  }

  /**
   * 处理基础筛选
   * @param datas 数据
   * @param fieldValues 字段值
   * @param field 字段
   * @returns {*}
   */
  basicFilter (datas, fieldValues, field) {
    return datas.filter(item => {
      // `${item[field]}` 转字符串，避免与数字比较时等于false
      return fieldValues.includes(`${item[field]}`);
    })
  }

  /**
   * 处理范围筛选
   * @param datas 数据
   * @param range 范围
   * @param num 数量
   * @returns {*}
   */
  rangeFilter (datas, range, num) {
    let resultDatas;
    switch (range) {
      case 'before':
        resultDatas = datas.slice(0, num);
        break
      case 'after':
        resultDatas = datas.slice(-num);
        break
      default:
        resultDatas = datas;
        break
    }
    return resultDatas;
  }

  /**
   * 处理高级筛选
   * @param datas 数据
   * @param operator 操作符
   * @param field 字段
   * @param value 值
   * @returns {*}
   */
  advancedFilter (datas, operator, field, value) {
    let resultDatas;
    switch (operator) {
      case 'More':
        resultDatas = datas.filter(d => Number(d[field]) > Number(value))
        break
      case 'MoreEqual':
        resultDatas = datas.filter(d => Number(d[field]) >= Number(value))
        break
      case 'Less':
        resultDatas = datas.filter(d => Number(d[field]) < Number(value))
        break
      case 'LessEqual':
        resultDatas = datas.filter(d => Number(d[field]) <= Number(value))
        break
      case 'Equal':
        resultDatas = datas.filter(d => d[field] == value)
        break
      case 'NotEqual':
        resultDatas = datas.filter(d => d[field] != value)
        break
      case 'Start':
        resultDatas = datas.filter(d => startsWith(`${d[field]}`, value))
        break
      case 'End':
        resultDatas = datas.filter(d => endsWith(`${d[field]}`, value))
        break
      case 'Like':
        resultDatas = datas.filter(d => `${d[field]}`.indexOf(value) !== -1)
        break
      case 'NotLike':
        resultDatas = datas.filter(d => `${d[field]}`.indexOf(value) === -1)
        break
      default:
        resultDatas = datas;
        break
    }
    return resultDatas;
  }

}