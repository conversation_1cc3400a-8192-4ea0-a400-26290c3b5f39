import { uuid } from "../utils/Util";
import DictService from "../service/DictService";
/**
 * 字典集对象
 */
export default class DictModel {
  constructor(context) {
    this.context = context;
    this.$$isLoading = false;
    this.initProperty();
    this.columns = ["id", "itemKey", "itemName", "itemLevel", "parentId"];
    this.datas = [];
  }
  getDefaultProperty () {
    return {
      id: uuid(),
      code: "编码",
      name: "名称",
      remark: "备注",
      linkId: "字典id",
    }
  }
  /**
   * 初始化-属性
   * @param {*} property 
   */
  initProperty (property) {
    this.property = this.getDefaultProperty();
    if (property) {
      for (let key in property) {
        if (!this.property) {
          this.property = {};
        }
        this.property[key] = property[key];
      }
    }
  }
  /**
   * 刷新-属性
   * @param {*} property 
   * @param {*} isnew 是否新数据
   */
  refreshProperty (property, isNew) {
    if (property) {
      let json = JSON.parse(JSON.stringify(property));
      if (json["id"] && isNew) {
        this.property.id = json["id"];
        this.property.linkId = json["id"];
      }
      if (json["nodeKey"]) {
        this.property.code = json["nodeKey"];
      }
      if (json["typeName"]) {
        this.property.name = json["typeName"];
      }
      if (json["remark"]) {
        this.property.remark = json["remark"];
      }
      if (json["dictType"]) {
        this.property.type = json["dictType"];
      }
      if (json["status"]) {
        this.property.status = json["status"];
      }
    }
  }
  /**
   * 关联-异步存储(数据库只存了关联Id)
   * @param {*} callback 
   */
  refreshData (callback) {
    const self = this;
    this.$$isLoading = true;
    const params = { id: this.property.linkId };
    //查询基本信息
    const dictService = new DictService(this.context);
    dictService.queryById(params, function (model) {
      //刷新属性
      self.refreshProperty(model);
      self.queryData(callback);
    },
      function (error) {
        //是否加载中
        self.$$isLoading = false;
        //回调
        if (callback) {
          callback();
        }
      });
  }
  queryData (callback) {
    const self = this;
    this.$$isLoading = true;
    //查询基本信息
    const dictService = new DictService(self.context);
    //查询数据
    const _params = { status: 1, nodeKey: self.property.code };
    dictService.queryDataByKey(_params, function (result) {
      let datas;
      const columns = self.columns;
      if (result && result.length) {
        for (let i = 0; i < result.length; i++) {
          const data = result[i];
          if (!datas) {
            datas = [];
          }
          datas.push(data);
        }
      }
      self.datas = datas || [];
      //是否加载中
      self.$$isLoading = false;
      //回调
      if (callback) {
        callback();
      }
    });
  }
  getModelColumns(){
    if(this.columns && this.columns.length){
      return this.columns;
    }
  }
  /**
   * 获取-数据
   * @param {*} level 
   * @returns 
   */
  getModelDatas (level) {
    let list;
    if (level) {
      const datas = this.datas;
      if (datas && datas.length) {
        for (let i = 0; i < datas.length; i++) {
          const data = datas[i];
          if (data["itemLevel"] && data["itemLevel"] === level) {
            if (!list) {
              list = [];
            }
            list.push(data);
          }
        }
      }
    } else {
      list = this.datas;
    }
    return list;
  }

}