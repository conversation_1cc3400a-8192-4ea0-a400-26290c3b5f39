import {uuid} from "../utils/Util";
/**
 * 素材 (平台/本地)
 */
export default class MaterialModel {
  constructor(context) {
    this.context = context;
    this.$$isLoading=false;
    this.initProperty();
    this.isLocal=false;//是否本地选择
  }
  getDefaultProperty () {
    return {
      id: uuid(),
      code: "",
      name: "素材",
      remark: "备注",
      type: "1",//1图片，2背景，3边框，4.svg，5.主题，6.图标
      fileId: "",
      fileUrl: "",
      borderLeft: "",
      borderTop: "",
      borderRight: "",
      borderBottom: "",
      fontFamily: "iconfont",//图标序列 iconfont
      fontPrefix: "icon-",//图标前缀 icon-
      jsons: "",//主题/图标
    }
  }
  /**
   * 初始化-属性
   * @param {*} property 
   */
  initProperty (property) {
    this.property = this.getDefaultProperty();
    if (property) {
      for (let key in property) {
        if (!this.property) {
          this.property = {};
        }
        this.property[key] = property[key];
      }
    }
  }
  /**
   * 刷新-属性
   * @param {*} property 
   */
  refreshProperty (property) {
    if (property) {
      let json = JSON.parse(JSON.stringify(property));
      if (json["id"]) {
        this.property.id = json["id"];
      }
      if (json["code"]) {
        this.property.code = json["code"];
      }
      if (json["name"]) {
        this.property.name = json["name"];
      }
      if (json["remark"]) {
        this.property.remark = json["remark"];
      }
      //类型
      if (json["type"]) {
        this.property.type = json["type"];
      }
      //文件(图片)
      if (json["fileUrl"]) {
        this.property.fileUrl = json["fileUrl"];
      }
      //边框裁剪
      if (json["borderLeft"]) {
        this.property.borderLeft = json["borderLeft"];
      }
      if (json["borderTop"]) {
        this.property.borderTop = json["borderTop"];
      }
      if (json["borderRight"]) {
        this.property.borderRight = json["borderRight"];
      }
      if (json["borderBottom"]) {
        this.property.borderBottom = json["borderBottom"];
      }
      //主题/图标
      if (json["jsons"]) {
        this.property.jsons = json["jsons"];
      }
      //图标
      if (json["fontFamily"]) {
        this.property.fontFamily = json["fontFamily"];
      }
      if (json["fontPrefix"]) {
        this.property.fontPrefix = json["fontPrefix"];
      }
    }
  }
  getId(){
    if(this.property && this.property.id){
      return this.property.id;
    }
  }
  getUrl(){
    if(this.property && this.property.fileUrl){
      return this.property.fileUrl;
    }
  }
}