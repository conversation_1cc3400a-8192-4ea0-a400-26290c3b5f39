import { uuid } from "../utils/Util";
import Expression from "../utils/Expression";
/**
 * 参数对象
 */
export default class ParamModel {
  constructor(context) {
    this.context = context;
    this.expression = new Expression(context);
    this.$$isLoading=false;
    this.initProperty();
  }
  getDefaultProperty () {
    return {
      id: uuid(),
      code: "",
      name: "参数",
      remark: "备注",
      modelId:"",//关联Id(绑定Id)//修改与LinkId
      type: "",//param,bind,url,sys
      expr: "",
      value: "",
    }
  }
  /**
   * 初始化-属性
   * @param {*} property 
   */
  initProperty (property) {
    this.property = this.getDefaultProperty();
    if (property) {
      for (let key in property) {
        if (!this.property) {
          this.property = {};
        }
        this.property[key] = property[key];
      }
    }
  }
  /**
   * 刷新-属性
   * @param {*} property 
   */
  // refreshProperty (property) {
  //   if (property) {
  //     let json = JSON.parse(JSON.stringify(property));
  //     if (json["id"]) {
  //       this.property.id = json["id"];
  //     }
  //     if (json["code"]) {
  //       this.property.code = json["code"];
  //     }
  //     if (json["name"]) {
  //       this.property.name = json["name"];
  //     }
  //     if (json["remark"]) {
  //       this.property.remark = json["remark"];
  //     }
  //   }
  // }
  refreshData () {
    this.expression.parseExpr(this.property);
  }
}