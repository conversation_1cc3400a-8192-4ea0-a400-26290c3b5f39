import { createPanel } from "./PropertyUtil";
import "./css/property.css";
export default class PropertyPanel {
  constructor(context) {
    this.context = context;
  }
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="property-container"></div>`);
      parentContainer.append(this.container);
      this.initModel(this.container);
    }
  }
  initModel (container) {
    if (container) {
      const modelContainer = $(`<div class="comm-wrap panel-wrap">
        <div class="comm-body"></div>
      </div>`);
      container.append(modelContainer);
      this.titleContainer = $(this.container).find(".head-title");
      this.bodyContainer = $(this.container).find(".comm-body");
    }
  }

  /**
   * 描述:刷新面板
   */
  refreshPanel (model) {
    if (this.bodyContainer) {
      $(this.bodyContainer).empty();
      if (model) {
        const panel = createPanel(this.context, model);
        const panelContainer = panel.getContainer();
        if (panelContainer) {
          this.bodyContainer.append(panelContainer);
        }
      }
    }
  }
  refreshTitle (title) {
    const text = title || "属性配置";
    if (this.titleContainer) {
      this.titleContainer.text(text);
    }
  }
  refreshLinkPanel (item) {
      let model;
    if (item) {
      model = item;
    } else {
      model = this.context.getConfig();
    }
    this.refreshPanel(model);
  }
}