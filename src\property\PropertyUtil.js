import { alert, warn, error } from "../assets/element/jquery/msg/MsgUtil";
import ConfigPropertyPanel from "./panel/other/ConfigPropertyPanel";
//基础 base
import FullScreenPropertyPanel from "./panel/layer/base/comp/FullScreenPropertyPanel";
import GroupPropertyPanel from "./panel/layer/base/comp/GroupPropertyPanel";
import IframePropertyPanel from "./panel/layer/base/comp/IframePropertyPanel";
import Iframe3DPropertyPanel from "./panel/layer/base/comp/Iframe3DPropertyPanel";
import NumPropertyPanel from "./panel/layer/base/comp/NumPropertyPanel";
import TextPropertyPanel from "./panel/layer/base/comp/TextPropertyPanel";
import DatePropertyPanel from "./panel/layer/base/comp/DatePropertyPanel";
import ImagePropertyPanel from "./panel/layer/base/comp/ImagePropertyPanel";
import BorderPropertyPanel from "./panel/layer/base/comp/BorderPropertyPanel";
import SvgPropertyPanel from "./panel/layer/base/comp/SvgPropertyPanel";
import SvgAnimationPropertyPanel from "./panel/layer/base/comp/SvgAnimationPropertyPanel";
import IconPropertyPanel from "./panel/layer/base/comp/IconPropertyPanel";
import VideoPropertyPanel from "./panel/layer/base/comp/VideoPropertyPanel";
import XgPlayerPropertyPanel from "./panel/layer/base/comp/XgPlayerPropertyPanel";
import YsPlayerPropertyPanel from "./panel/layer/base/comp/YsPlayerPropertyPanel";
import TablePropertyPanel from "./panel/layer/base/comp/TablePropertyPanel";
import MarqueePropertyPanel from "./panel/layer/base/comp/MarqueePropertyPanel";
import TreePropertyPanel from "./panel/layer/base/comp/TreePropertyPanel";
import GridPropertyPanel from "./panel/layer/base/comp/GridPropertyPanel";
//形状 shape
import CircularShapePropertyPanel from "./panel/layer/shape/comp/CircularShapePropertyPanel";
import RectangleShapePropertyPanel from "./panel/layer/shape/comp/RectangleShapePropertyPanel";
//过滤 filter
import DateFilterPropertyPanel from "./panel/layer/filter/comp/DateFilterPropertyPanel";
import RadioFilterPropertyPanel from "./panel/layer/filter/comp/RadioFilterPropertyPanel";
import SelectFilterPropertyPanel from "./panel/layer/filter/comp/SelectFilterPropertyPanel";
//图表 chart
import LineChartPropertyPanel from "./panel/layer/echarts/chart/comp/LineChartPropertyPanel"; //折线图
import LineBarChartPropertyPanel from "./panel/layer/echarts/chart/comp/LineBarChartPropertyPanel"; //折柱混合
import BarChartPropertyPanel from "./panel/layer/echarts/chart/comp/BarChartPropertyPanel"; //柱状图
import Bar3DChartPropertyPanel from "./panel/layer/echarts/chart/comp/Bar3DChartPropertyPanel"; //3D柱状图
import BarYChartPropertyPanel from "./panel/layer/echarts/chart/comp/BarYChartPropertyPanel"; //条形图
import BarPictorChartPropertyPanel from "./panel/layer/echarts/chart/comp/BarPictorChartPropertyPanel"; //立体柱状图
import BarPictorYChartPropertyPanel from "./panel/layer/echarts/chart/comp/BarPictorYChartPropertyPanel"; //水平柱状图
import BarDoughnutChartPropertyPanel from "./panel/layer/echarts/chart/comp/BarDoughnutChartPropertyPanel"; //环形柱状图

import PieChartPropertyPanel from "./panel/layer/echarts/chart/comp/PieChartPropertyPanel"; //饼图
import Pie3DChartPropertyPanel from "./panel/layer/echarts/chart/comp/Pie3DChartPropertyPanel";
import PieRoseChartPropertyPanel from "./panel/layer/echarts/chart/comp/PieRoseChartPropertyPanel"; //玫瑰图
import PieDoughnutChartPropertyPanel from "./panel/layer/echarts/chart/comp/PieDoughnutChartPropertyPanel"; //环形图
import PieDoughnutGapChartPropertyPanel from "./panel/layer/echarts/chart/comp/PieDoughnutGapChartPropertyPanel"; //环形图断开
import PieNestChartPropertyPanel from "./panel/layer/echarts/chart/comp/PieNestChartPropertyPanel"; //嵌套环形图
//
import ScatterChartPropertyPanel from "./panel/layer/echarts/chart/comp/ScatterChartPropertyPanel"; //散点（气泡）
import RadarChartPropertyPanel from "./panel/layer/echarts/chart/comp/RadarChartPropertyPanel"; //雷达
import GaugeChartPropertyPanel from "./panel/layer/echarts/chart/comp/GaugeChartPropertyPanel"; //仪表盘
import FunnelChartPropertyPanel from "./panel/layer/echarts/chart/comp/FunnelChartPropertyPanel"; //漏斗
import CandlestickChartPropertyPanel from "./panel/layer/echarts/chart/comp/CandlestickChartPropertyPanel"; //K线图
import SankeyChartPropertyPanel from "./panel/layer/echarts/chart/comp/SankeyChartPropertyPanel"; //桑基图
import GraphChartPropertyPanel from "./panel/layer/echarts/chart/comp/GraphChartPropertyPanel"; //关系图
import LinesChartPropertyPanel from "./panel/layer/echarts/chart/comp/LinesChartPropertyPanel"; //路径图
import BoxplotChartPropertyPanel from "./panel/layer/echarts/chart/comp/BoxplotChartPropertyPanel"; //盒须图
import ParallelChartPropertyPanel from "./panel/layer/echarts/chart/comp/ParallelChartPropertyPanel"; //平行坐标系
import WordChartPropertyPanel from "./panel/layer/echarts/chart/comp/WordChartPropertyPanel"; //词云
import LiquidChartPropertyPanel from "./panel/layer/echarts/chart/comp/LiquidChartPropertyPanel"; //水晶球
//地图 map
import ZgScMapPropertyPanel from "./panel/layer/echarts/map/comp/ZgScMapPropertyPanel"; //'中国色彩', type: 'ZgScMapLayer'
import ZgRlMapPropertyPanel from "./panel/layer/echarts/map/comp/ZgRlMapPropertyPanel"; //'中国热力', type: 'ZgRlMapLayer'
import ZgQpMapPropertyPanel from "./panel/layer/echarts/map/comp/ZgQpMapPropertyPanel"; //'中国气泡', type: 'ZgQpMapLayer'
import ZgFxMapPropertyPanel from "./panel/layer/echarts/map/comp/ZgFxMapPropertyPanel"; //'中国飞线', type: 'ZgFxMapLayer'
import Zg3DMapPropertyPanel from "./panel/layer/echarts/map/comp/Zg3DMapPropertyPanel"; //'中国3d', type: 'Zg3DMapLayer'
export function createPanel(context, model) {
  let panel;
  if (context && model) {
    let type;
    if (model["type"]) {
      type = model["type"];
    }
    if (model.property && model.property["type"]) {
      type = model.property["type"];
    }
    if (type) {
      //遍历
      switch (type) {
        //基础 base
        case "FullScreenLayer":
          panel = new FullScreenPropertyPanel(context, true);
          break;
        case "GroupLayer":
          panel = new GroupPropertyPanel(context, true);
          break;
        case "IframeLayer":
          panel = new IframePropertyPanel(context, true);
          break;
        case "Iframe3DLayer":
          panel = new Iframe3DPropertyPanel(context, true);
          break;
        case "NumLayer":
          panel = new NumPropertyPanel(context, true);
          break;
        case "TextLayer":
          panel = new TextPropertyPanel(context, true);
          break;
        case "DateLayer":
          panel = new DatePropertyPanel(context, true);
          break;
        case "ImageLayer":
          panel = new ImagePropertyPanel(context, true);
          break;
        case "BorderLayer":
          panel = new BorderPropertyPanel(context, true);
          break;
        case "SvgLayer":
          panel = new SvgPropertyPanel(context, true);
          break;
        case "SvgAnimationLayer":
          panel = new SvgAnimationPropertyPanel(context, true);
          break;
        case "IconLayer":
          panel = new IconPropertyPanel(context, true);
          break;
        case "VideoLayer":
          panel = new VideoPropertyPanel(context, true);
          break;
        case "XgPlayerLayer":
          panel = new XgPlayerPropertyPanel(context, true);
          break;
        case "YsPlayerLayer":
          panel = new YsPlayerPropertyPanel(context, true);
          break;
        case "TableLayer":
          panel = new TablePropertyPanel(context, true);
          break;
        case "MarqueeLayer":
          panel = new MarqueePropertyPanel(context, true);
          break;
        case "TreeLayer":
          panel = new TreePropertyPanel(context, true);
          break;
        case "GridLayer":
          panel = new GridPropertyPanel(context, true);
          break;
        //形状 shape
        case "CircularShapeLayer":
          panel = new CircularShapePropertyPanel(context, true);
          break;
        case "RectangleShapeLayer":
          panel = new RectangleShapePropertyPanel(context, true);
          break;
        //过滤 filter
        case "DateFilterLayer":
          panel = new DateFilterPropertyPanel(context, true);
          break;
        case "RadioFilterLayer":
          panel = new RadioFilterPropertyPanel(context, true);
          break;
        case "SelectFilterLayer":
          panel = new SelectFilterPropertyPanel(context, true);
          break;
        //图表 chart
        case "LineChartLayer":
          panel = new LineChartPropertyPanel(context, true); //折线图
          break;
        case "LineBarChartLayer":
          panel = new LineBarChartPropertyPanel(context, true); //折柱混合
          break;

        case "BarChartLayer":
          panel = new BarChartPropertyPanel(context, true); //柱状图
          break;
        case "Bar3DChartLayer":
          panel = new Bar3DChartPropertyPanel(context, true); //柱状图
          break;
        case "BarYChartLayer":
          panel = new BarYChartPropertyPanel(context, true); //条形图
          break;
        case "BarPictorChartLayer":
          panel = new BarPictorChartPropertyPanel(context, true); //立体柱状图
          break;
        case "BarPictorYChartLayer":
          panel = new BarPictorYChartPropertyPanel(context, true); //水平柱状图
          break;
        case "BarDoughnutChartLayer":
          panel = new BarDoughnutChartPropertyPanel(context, true); //环形柱状图
          break;

        case "PieChartLayer":
          panel = new PieChartPropertyPanel(context, true); //饼图
          break;
        case "Pie3DChartLayer":
          panel = new Pie3DChartPropertyPanel(context, true); //3d饼图
          break;
        case "PieRoseChartLayer":
          panel = new PieRoseChartPropertyPanel(context, true); //玫瑰图
          break;
        case "PieDoughnutChartLayer":
          panel = new PieDoughnutChartPropertyPanel(context, true); //环形图
          break;
        case "PieDoughnutGapChartLayer":
          panel = new PieDoughnutGapChartPropertyPanel(context, true); //环形图断开
          break;
        case "PieNestChartLayer":
          panel = new PieNestChartPropertyPanel(context, true); //嵌套环形图
          break;
        case "ScatterChartLayer":
          panel = new ScatterChartPropertyPanel(context, true); //散点（气泡）
          break;
        case "RadarChartLayer":
          panel = new RadarChartPropertyPanel(context, true); //雷达
          break;
        case "GaugeChartLayer":
          panel = new GaugeChartPropertyPanel(context, true); //仪表盘
          break;
        case "FunnelChartLayer":
          panel = new FunnelChartPropertyPanel(context, true); //漏斗
          break;
        case "CandlestickChartLayer":
          panel = new CandlestickChartPropertyPanel(context, true); //K线图
          break;
        case "SankeyChartLayer":
          panel = new SankeyChartPropertyPanel(context, true); //桑基
          break;
        case "GraphChartLayer":
          panel = new GraphChartPropertyPanel(context, true); //关系图
          break;
        case "LinesChartLayer":
          panel = new LinesChartPropertyPanel(context, true); //路径图
          break;
        case "BoxplotChartLayer":
          panel = new BoxplotChartPropertyPanel(context, true); //盒须图
          break;
        case "ParallelChartLayer":
          panel = new ParallelChartPropertyPanel(context, true); //平行坐标系
          break;
        case "WordChartLayer":
          panel = new WordChartPropertyPanel(context, true); //盒须图
          break;
        case "LiquidChartLayer":
          panel = new LiquidChartPropertyPanel(context, true); //平行坐标系
          break;
        //地图 map
        case "ZgScMapLayer":
          panel = new ZgScMapPropertyPanel(context, true); //中国色彩
          break;
        case "ZgRlMapLayer":
          panel = new ZgRlMapPropertyPanel(context, true); //中国热力
          break;
        case "ZgQpMapLayer":
          panel = new ZgQpMapPropertyPanel(context, true); //中国气泡
          break;
        case "ZgFxMapLayer":
          panel = new ZgFxMapPropertyPanel(context, true); //中国飞线
          break;
        case "Zg3DMapLayer":
          panel = new Zg3DMapPropertyPanel(context, true); //中国3D
          break;
        default:
          alert("创建失败![不存在" + type + "类型-属性面板]");
      }
    } else {
      panel = new ConfigPropertyPanel(context); //基础配置
    }
    //图层-初始化
    if (panel) {
      panel.initProperty(model);
      panel.initPanel();
    }
  }
  return panel;
}
