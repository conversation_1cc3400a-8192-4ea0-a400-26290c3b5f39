/*===========================属性样式======================*/
.property-container {
  width: 100%;
  height: 100%;
  background: #191919;
  color: #fff;
  overflow-y: auto;
  overflow-x: hidden;
}

/*===========tabs 选项卡 样式===========*/
#tabs ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
  display: flex;
  border-top: 1px solid #0e0e0e;
  border-bottom: 1px solid #0e0e0e;
}

#tabs ul li {
  cursor: pointer;
  flex: 1;
  height: 40px;
  border-right: 1px solid #414243;
  display: flex;
  align-items: center;
  justify-content: center;
}

#tabs ul li a {
  text-decoration: none;
  font-weight: 400;
  font-size: 15px;
  color: #E5EAF3;
  user-select: none;
}

#tabs .tabs-body {
  clear: both;
  box-sizing: border-box;
}

.lsd-property-remark {
  padding: 8px;
  border-radius: 2px 2px 2px 2px;
  background: #000000;
  font-weight: 400;
  font-size: 12px;
  color: #CFD3DC;
  line-height: 17px;
  user-select: none;
}

.sp-dd {
  padding: 0;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background: #262727;
  color: #a3a6ad;
  font-size: 12px;
  float: right;
}

.dropdown-container {
  position: relative;
  width: 100%;
}

.dropdown-container .dropdown-header {
  display: flex;
  flex-wrap: wrap;
}

.dropdown-header .dropdown-search {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  height: unset;
}

.dropdown-header .selected-tags {
  display: flex;
  flex-wrap: wrap;
  margin-right: 25px;
}

.selected-tags .selected-tag {
  background: #1967d2;
  color: white;
  height: 100%;
  font-size: 12px;
  border-radius: 4px;
  margin: 0 1px;
  padding: 0 2px;
  max-height: 20px;
  display: inline-flex;
  align-items: center;
}
.selected-tags .selected-tag .tag-text {
  max-width: 60px;
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selected-tags .selected-tag .remove-tag {
  margin-left: 3px;
  cursor: pointer;
}

.dropdown-container .dropdown-options {
  position: absolute;
  top: 110%;
  z-index: 1;
  left: 0;
  width: 100%;
  max-height: 150px;
  overflow-y: auto;
  display: none;
  border: 1px solid #434343;
  border-radius: 5px;
}

.dropdown-container .dropdown-options .dropdown-option {
  font-size: 12px;
  cursor: pointer;
  background: #000;
  color: #fff;
  padding: 2px 8px;
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-options .dropdown-option.selected {
  background-color: #1967d2;
  color: white;
}

.dropdown-options .dropdown-option:hover {
  background-color: #1967d2;
}

/* 显示下拉框 */
.dropdown-container.active .dropdown-options {
  display: block;
}
