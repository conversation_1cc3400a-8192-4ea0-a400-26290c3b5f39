
export default class TabsPanel {
  constructor(context, isTabs) {
    if (context) {
      this.context = context;
      this.designer = context.getDesigner();
    }
    this.isTabs = isTabs || false;
    this.index = 0;
  }
  initProperty (model) {
    this.model = model || {};
  }
  initPanel () {
    this.container = $(`<div class="panel-container"></div>`);
    if (this.isTabs) {
      this.initTabs();
    } else {
      this.refreshPanel();
    }
  }
  /**
   * 描述:刷新面板
   */
  refreshPanel () {
    if (this.container) {
      this.container.empty();
    }
  }
  getContainer () {
    if (this.container) {
      return this.container;
    }
  }
  addPage (name) {
    if (name) {
      this.index++;
      return this.addTabPage(this.index, name);
    } else {
      if (this.container) {
        this.container.empty();
        const panelContainer = $(`<div style="padding: 10px;"></div>`);
        this.container.append(panelContainer);
        return panelContainer;
      }
    }
  }
  /**
   * Tabs
   */
  initTabs () {
    if (this.container) {
      this.container.empty();
      //tabs-包
      this.tabsContainer = $(`<div id='tabs' class='tabs-wrap'></div>`);
      this.container.append(this.tabsContainer);
      //ul
      this.ulContainer = $(`<ul></ul>`);
      this.tabsContainer.append(this.ulContainer);
      //刷新面板
      this.refreshPanel();
      //刷新选项卡事件
      this.refreshTabsClick(this.container);
    }
  }
  addTabPage (code, name) {
    if (this.ulContainer && this.tabsContainer && code && name) {
      const liContainer = $(`<li><a href="#` + this.getTabId() + code + `">` + name + `</a></li>`);
      this.ulContainer.append(liContainer);
      const tabPanelContainer = $(`<div class="tabs-body" id="` + this.getTabId() + code + `" ></div>`);
      this.tabsContainer.append(tabPanelContainer);
      return tabPanelContainer;
    }
  }
  getTabId () {
    return "tab_property_id_";
  }
  refreshTabsClick (container) {
    if (container) {
      $(container).find("#tabs ul li").on("click", function (event) {
        event.preventDefault(); // 阻止链接默认行为
        //head-选中-高亮 3F4B5F 353f50
        $(container).find("#tabs ul li a").css("color", "#A3A6AD");
        $(this).find('a').css("color", "#E5EAF3");
        //body-显示/隐藏
        const tabId = $(this).find('a').attr("href");
        // $(container).find("#tabs div").css("display", "none");
        // $(container).find(tabId).css("display", "block");
        $(container).find("#tabs .tabs-body").hide();
        $(container).find(tabId).show();
      });
      //单击第一个选项卡
      $(container).find("#tabs ul li:first").trigger('click');
    }
  }
}