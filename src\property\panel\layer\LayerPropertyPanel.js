import TabsPanel from "../TabsPanel";
//基础
import BaseStyle from "../../style/common/BaseStyle";
import TransformStyle from "../../style/common/TransformStyle";
import AnimateStyle from "../../style/common/AnimateStyle";
import JumpCard from "../../style/common/jump/JumpCard";
//绑定
import BindStyle from "../../style/common/bind/BindStyle";
import BindDataStyle from "../../style/common/bind/BindDataStyle";
import BindLayerStyle from "../../style/common/bind/BindLayerStyle";
import BindModelStyle from "../../style/common/bind/BindModelStyle";
import BindDictStyle from "../../style/common/bind/BindDictStyle";
// import BindParamStyle from "../../style/common/bind/BindParamStyle";
import BindMockStyle from "../../style/common/bind/BindMockStyle";
import BindRemarkStyle from "../../style/common/bind/BindRemarkStyle";
//联动
import RelationStyle from "../../style/common/relation/RelationStyle";
import RelationParamStyle from "../../style/common/relation/RelationParamStyle";
import RelationLayerStyle from "../../style/common/relation/RelationLayerStyle";
//条件
import ConditionList from "../../style/common/condition/ConditionList";
import DatasetStyle from "../../style/common/dataset/DatasetStyle";
import InteractiveStyle from "../../style/common/interactive/InteractiveStyle";
import ConditionAnimateStyle from "../../style/common/condition/ConditionAnimateStyle";

export default class LayerPropertyPanel extends TabsPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  initProperty (layer) {
    if (layer) {
      this.layer = layer;
    }
  }
  refreshPanel () {
    if (this.layer) {
      const self = this;
      const callback = function () {
        // self.layer.refreshLayer();
        self.layer.refreshCss();
      };
      const property = this.layer.property;
      //刷新属性
      this.refreshProperty(property, callback, false);
    }
  }
  /**
   * 刷新属性
   * @param {*} property 
   * @param {*} callback 
   * @param {*} isOpen 
   */
  refreshProperty (property, callback, isOpen) { }



  /*##########################[公共方法]########################## */
  addBasePage (property, callback, isOpen) {
    const panelContainer = this.addPage("基础");
    if (panelContainer && property) {
      this.addBaseProperty(panelContainer, property, callback, isOpen);
      this.refreshBase(panelContainer, property, callback, isOpen);
    }
  }
  addBindPage (property, callback, isOpen) {
    const panelContainer = this.addPage("绑定");
    if (panelContainer && property) {
      const self = this;
      const callbackBind = function () {
        self.layer.refreshData();
      }
      this.addBindProperty(panelContainer, property, callbackBind, isOpen);
    }
  }
  addRelationPage (property, callback, isOpen) {
    const panelContainer = this.addPage("联动");
    if (panelContainer && property) {
      this.addRelationProperty(panelContainer, property, callback, isOpen);
    }
  }
  addConditionPage (property, callback, isOpen) {
    const panelContainer = this.addPage("条件");
    if (panelContainer && property) {
      this.addConditionProperty(panelContainer, property, callback, isOpen);
    }
  }
  addInteractivePage (property, callback, isOpen) {
    const panelContainer = this.addPage("交互");
    if (panelContainer && property) {
      this.addInteractiveProperty(panelContainer, property, callback, isOpen);
    }
  }
  /*********************************************************** */
  /**
   * 基础
   */
  addBaseProperty (parentContainer, property, callback, isOpen) {
    if (parentContainer && this.context && property) {
      //属性-基础
      const baseStyle = new BaseStyle(this.context, isOpen);
      baseStyle.initPanel(parentContainer, property, callback);
      //属性-转换
      if (!property["transform"]) {
        property["transform"] = {};
      }
      const transformStyle = new TransformStyle(this.context, true);
      transformStyle.initPanel(parentContainer, property["transform"], callback);
      //属性-动画
      if (!property["animate"]) {
        property["animate"] = {};
      }
      const animateStyle = new AnimateStyle(this.context, isOpen);
      animateStyle.initPanel(parentContainer, property["animate"], callback);
      //超链接
      const options = this.layer.getColOptions();
      const params = this.getParamOptions();
      //属性-超链接
      if (!property["jump"]) {
        property["jump"] = {};
      }
      const jumpCard = new JumpCard(this.context, isOpen);
      jumpCard.initPanel(parentContainer, property["jump"], callback);
      jumpCard.setOptions(options);
      jumpCard.setParams(params);
    }
  }
  /**
   * 基础:其他自定义属性配置需要 各自实现
   * @param {*} parentContainer 
   * @param {*} property 
   * @param {*} callback 
   * @param {*} isOpen 
   */
  refreshBase (parentContainer, property, callback, isOpen) { };
  /**
   * 
   * @param {*} parentContainer 
   * @param {*} property 
   * @param {*} callback 
   * @param {*} isOpen 
   */
  addBindProperty (parentContainer, property, callback, isOpen) {
    if (parentContainer && this.context && property) {
      $(parentContainer).empty();
      const self = this;

      const bindCallback=function(){
        if (self.layer && self.layer.refreshChartColumns) {
          self.layer.refreshChartColumns();
        }
        if (callback) {
          callback();
        }
      }

      if (!property["bind"]) {
        property["bind"] = {};
      }
      // 数据集样式
      const datasetStyle = new DatasetStyle(this.context);
      datasetStyle.initPanel(parentContainer, property["bind"], bindCallback);
    }
  }
  /**
   * 添加-联动属性
   * @param {*} parentContainer 
   * @param {*} property 
   * @param {*} callback 
   * @param {*} isOpen 
   */
  addRelationProperty (parentContainer, property, callback, isOpen) {
    if (parentContainer && this.context && property) {
      $(parentContainer).empty();
      const self = this;
      //属性-联动
      if (!property["relation"]) {
        property["relation"] = { isOpen: false };
      }
      const relationStyle = new RelationStyle(this.context);
      relationStyle.initPanel(parentContainer, property["relation"], callback);
      relationStyle.setTypeCallback(function(){
        self.addRelationProperty(parentContainer, property, callback, isOpen);
      });

      const relation = property["relation"];
      const layers = this.getLayerOptions();
      const options = this.layer.getColOptions(true);
      const params = this.getParamOptions();

      if (relation["linkage"] === "param") {
        if (!relation["paramLink"]) {
          relation["paramLink"] = {};
        }
        const paramStyle = new RelationParamStyle(this.context);
        paramStyle.initPanel(parentContainer, relation["paramLink"], callback);
        paramStyle.setOptions(options);
        paramStyle.setParams(params);
      }
      if (relation["linkage"] === "layer") {
        if (!relation["layerLink"]) {
          relation["layerLink"] = {};
        }
        const layerStyle = new RelationLayerStyle(this.context);
        layerStyle.initPanel(parentContainer, relation["layerLink"], callback);
        layerStyle.setLayers(layers);
        layerStyle.setOptions(options);

      }
    }
  }
  /**
   * 添加-条件属性
   * @param {*} parentContainer 
   * @param {*} property 
   * @param {*} callback 
   * @param {*} isOpen 
   */
  addConditionProperty (parentContainer, property, callback, isOpen) {
    if (parentContainer && this.context && property) {
      $(parentContainer).empty();
      const self = this;

      const rowNum = this.layer.getBindRowNum();
      const options = this.layer.getColOptions();
      const params = this.getParamOptions();
      //属性-条件
      if (!property["conditions"]) {
        property["conditions"] = [];
      }
      const conditionList = new ConditionList(this.context, isOpen);
      conditionList.initPanel(parentContainer, property["conditions"], callback);
      conditionList.setOptions(options);
      conditionList.setParams(params);
      conditionList.setRowNum(rowNum);
      conditionList.addFunCallback(function (itemContainer, item, callback) {
        self.addConditionStyle(itemContainer, item, callback);

        if (!item["animate"]) {
          item["animate"] = {};
        }
        const animateStyle = new ConditionAnimateStyle(self.context);
        animateStyle.initPanel(itemContainer, item["animate"], callback);
      });
    }
  }
  addConditionStyle (itemContainer, item, callback) {
    // console.info(item);
    console.error("抽象-创建条件方法!");
  }
  addInteractiveProperty (parentContainer, property, callback, isOpen) {
    if (parentContainer && this.context && property) {
      $(parentContainer).empty();
      //属性-条件
      if (!property["interactive"]) {
        property["interactive"] = {};
      }
      const interactiveStyle = new InteractiveStyle(this.context);
      interactiveStyle.setBind(this.layer.getBind());
      interactiveStyle.setParams(this.getParamOptions());
      interactiveStyle.initPanel(parentContainer, property["interactive"], callback);
    }
  }
  /**
   * 参数选项
   */
  getParamOptions (isExpr) {
    let options;
    const paramMap = this.context.getParamMap();
    if (paramMap) {
      for (let key in paramMap) {
        const param = paramMap[key];
        const text = param.property.code;
        let expr;
        if (isExpr) {
          expr = "${" + text + "}"
        } else {
          expr = text;
        }
        const option = { key: expr, text: expr };
        if (!options) {
          options = [];
        }
        options.push(option);
      }
    }
    return options;
  }
  /**
   * 除自己，有图层选项
   * @returns 
   */
  getLayerOptions () {
    let options;
    const ownLayer = this.layer;
    const layerMap = this.context.getLayerMap();
    if (layerMap && ownLayer) {
      for (let key in layerMap) {
        const layer = layerMap[key];
        if (key && key !== ownLayer.property.id) {
          const text = layer.property.label;
          const option = { key: key, text: text };
          if (!options) {
            options = [];
          }
          options.push(option);
        }
      }
      return options;
    }
  }
  getDatasetOptions () {
    let options;
    const modelMap = this.context.getDataSetMap();
    for (let key in modelMap) {
      const item = modelMap[key];
      const option = { key: item.property.id, text: item.property.name };
      if (!options) {
        options = [];
      }
      options.push(option);
    }
    return options;
  }
}