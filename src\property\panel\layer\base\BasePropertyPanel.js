import LayerPropertyPanel from "../LayerPropertyPanel";
export default class BasePropertyPanel extends LayerPropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  // refreshProperty (property, callback, isOpen) {
  //   //基础
  //   this.addBasePage(property, callback, isOpen);
  //   //属性
  //   this.addChartPage(property, callback, isOpen);
  //   //绑定
  //   this.addBindPage(property, callback, isOpen);
  //   //联动
  //   this.addRelationPage(property, callback, isOpen);
  //   //条件
  //   this.addConditionPage(property, callback, isOpen);
  // }
  /**
   * 添加属性面板
   * @param {*} property
   * @param {*} callback
   * @param {*} isOpen
   */
  addChartPage(property, callback, isOpen) {
    const pageContainer = this.addPage("属性");
    console.log("addChartPage", property["chart"]);
    if (pageContainer && property) {
      this.refreshChart(pageContainer, property["chart"], callback, isOpen);
    }
  }
  //抽象-基础
  refreshBase(parentContainer, property, callback, isOpen) {}
  //抽象-属性
  refreshChart(parentContainer, item, callback, isOpen) {}
  //抽象-条件
  addConditionStyle(itemContainer, item, callback) {}
}
