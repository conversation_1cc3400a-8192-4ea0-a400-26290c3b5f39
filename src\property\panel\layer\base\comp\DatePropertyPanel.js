import TextStyle from "../../../../style/layer/base/DateStyle";
import BasePropertyPanel from "../BasePropertyPanel";
export default class TextPropertyPanel extends BasePropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  refreshProperty(property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //属性
    this.addChartPage(property, callback, isOpen);
    //绑定
    this.addBindPage(property, callback, isOpen);
    //联动
    // this.addRelationPage(property, callback, isOpen);
    //条件
    this.addConditionPage(property, callback, isOpen);
    // 交互
    this.addInteractivePage(property, callback, isOpen);
  }
  refreshChart(parentContainer, item, callback, isOpen) {
    //文本
    const textStyle = new TextStyle(this.context, isOpen);
    textStyle.initPanel(parentContainer, item, callback);
  }

  //条件的样式
  addConditionStyle(parentContainer, item, callback) {
    //文本
    const textStyle = new TextStyle(this.context);
    textStyle.initPanel(parentContainer, item, callback);
  }
}
