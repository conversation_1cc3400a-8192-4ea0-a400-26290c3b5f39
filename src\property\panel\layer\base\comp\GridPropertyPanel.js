import GridStyle from "../../../../style/layer/base/grid/GridStyle";
import TabsSeries from "../../../../style/layer/base/grid/TabsSeries";
import BasePropertyPanel from "../BasePropertyPanel";
export default class GridPropertyPanel extends BasePropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //属性
    this.addChartPage(property, callback, isOpen);
    //绑定
    this.addBindPage(property, callback, isOpen);
    //联动
    this.addRelationPage(property, callback, isOpen);
    //条件
    // this.addConditionPage(property, callback, isOpen);
    // 交互
    this.addInteractivePage(property, callback, isOpen);
  }

  refreshBase(parentContainer, property, callback, isOpen){

    if(!property["tabs"]){
      property["tabs"] = [];
    }
    const tabsSeries = new TabsSeries(this.context,isOpen);
    tabsSeries.initPanel(parentContainer, property["tabs"], callback);
  }
  
  refreshChart (parentContainer, item, callback, isOpen) {
    //栅格
    const gridStyle = new GridStyle(this.context,isOpen);
    gridStyle.initPanel(parentContainer, item, callback);

    
  }
}