import IframeStyle from "../../../../style/layer/base/Iframe/IframeStyle";
import BasePropertyPanel from "../BasePropertyPanel";
export default class IframePropertyPanel extends BasePropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }

  refreshProperty(property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //属性
    this.addChartPage(property, callback, isOpen);
    //绑定
    // this.addBindPage(property, callback, isOpen);
    //联动
    // this.addRelationPage(property, callback, isOpen);
    //条件
    this.addConditionPage(property, callback, isOpen);
  }
  refreshChart(parentContainer, item, callback, isOpen) {
    //链接
    const iframeStyle = new IframeStyle(this.context, isOpen);
    iframeStyle.initPanel(parentContainer, item, callback);

  }
}