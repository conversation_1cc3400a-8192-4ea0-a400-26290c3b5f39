import ImageCompStyle from "../../../../style/layer/base/ImageCompStyle";
import BasePropertyPanel from "../BasePropertyPanel";
export default class ImagePropertyPanel extends BasePropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //属性
    this.addChartPage(property, callback, isOpen);
    //绑定
    this.addBindPage(property, callback, isOpen);
    //联动
    // this.addRelationPage(property, callback, isOpen);
    //条件
    this.addConditionPage(property, callback, isOpen);
    // 交互
    this.addInteractivePage(property, callback, isOpen);
  }
  refreshChart (parentContainer, item, callback, isOpen) {
    //图片
    const imageCompStyle = new ImageCompStyle(this.context,isOpen);
    imageCompStyle.initPanel(parentContainer, item, callback);
    
  }

  //条件的样式
  addConditionStyle (parentContainer, item, callback) {
    //图片
    const imageCompStyle = new ImageCompStyle(this.context);
    imageCompStyle.initPanel(parentContainer, item, callback);
  }
}