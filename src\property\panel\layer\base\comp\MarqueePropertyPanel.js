import MarqueeStyle from "../../../../style/layer/base/MarqueeStyle";
import BasePropertyPanel from "../BasePropertyPanel";
export default class MarqueePropertyPanel extends BasePropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //属性
    this.addChartPage(property, callback, isOpen);
    //绑定
    // this.addBindPage(property, callback, isOpen);
    //联动
    // this.addRelationPage(property, callback, isOpen);
    //条件
    // this.addConditionPage(property, callback, isOpen);
  }
  refreshChart (parentContainer, item, callback, isOpen) {
    const self=this;
    //跑马灯
    const marqueeStyle = new MarqueeStyle(this.context, isOpen);
    marqueeStyle.initPanel(parentContainer, item, callback);
    
  }
}