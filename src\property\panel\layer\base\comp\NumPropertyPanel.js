import NumQuotaStyle from "../../../../style/layer/base/num/NumQuotaStyle";
import BasePropertyPanel from "../BasePropertyPanel";
export default class NumPropertyPanel extends BasePropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //属性
    this.addChartPage(property, callback, isOpen);
    //绑定
    this.addBindPage(property, callback, isOpen);
    //联动
    // this.addRelationPage(property, callback, isOpen);
    //条件
    this.addConditionPage(property, callback, isOpen);
    // 交互
    this.addInteractivePage(property, callback, isOpen);
  }
  refreshChart (parentContainer, item, callback, isOpen) {
    
    const numQuotaStyle = new NumQuotaStyle(this.context,isOpen);
    numQuotaStyle.initPanel(parentContainer, item, callback);
   
  }

  //条件的样式
  addConditionStyle (parentContainer, item, callback) {
    //数字
    const numQuotaStyle = new NumQuotaStyle(this.context);
    numQuotaStyle.initPanel(parentContainer, item, callback);
  }
}