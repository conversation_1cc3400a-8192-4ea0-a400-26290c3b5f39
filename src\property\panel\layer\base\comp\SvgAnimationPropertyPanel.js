import BasePropertyPanel from "../BasePropertyPanel";
import SvgAnimationStyle from "../../../../style/layer/base/SvgAnimationStyle";
export default class SvgAnimationPropertyPanel extends BasePropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //属性
    this.addChartPage(property, callback, isOpen);
    //绑定
    this.addBindPage(property, callback, isOpen);
    //联动
    // this.addRelationPage(property, callback, isOpen);
    //条件
    this.addConditionPage(property, callback, isOpen);
    // 交互
    this.addInteractivePage(property, callback, isOpen);
  }
  refreshChart (parentContainer, item, callback, isOpen) {
    //矢量图
    const svgAnimationStyle = new SvgAnimationStyle(this.context,isOpen);
    svgAnimationStyle.initPanel(parentContainer, item, callback);

  
  }
}