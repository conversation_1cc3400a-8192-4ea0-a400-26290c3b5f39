import TableMarqueeStyle from "../../../../style/layer/base/table/TableMarqueeStyle";
import OverflowStyle from "../../../../style/layer/base/table/OverflowStyle";
import NoDataStyle from "../../../../style/layer/base/table/NoDataStyle";
import TableStyle from "../../../../style/layer/base/table/TableStyle";
import TableColumnList from "../../../../style/layer/base/table/columns/TableColumnList";
import ColumnStyleList from "../../../../style/layer/base/table/column/ColumnStyleList";
import BasePropertyPanel from "../BasePropertyPanel";
export default class TablePropertyPanel extends BasePropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  baseOtherProperty (panelContainer, property, callback, isOpen) {

  };
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //属性
    this.addChartPage(property, callback, isOpen);
    //绑定
    this.addBindPage(property, callback, isOpen);
    //联动
    this.addRelationPage(property, callback, isOpen);
    //条件
    this.addConditionPage(property, callback, isOpen);
    // 交互
    this.addInteractivePage(property, callback, isOpen);
  }

  refreshBase (parentContainer, property, callback, isOpen) {

    //属性字段
    if (!property["chart"]["columns"]) {
      property["chart"]["columns"] = [];
    }
    const tableColumnList = new TableColumnList(this.context, isOpen);
    tableColumnList.initPanel(parentContainer, property["chart"]["columns"], callback);
  }

  refreshChart (parentContainer, item, callback, isOpen) {
    //表格
    const tableStyle = new TableStyle(this.context, isOpen);
    tableStyle.initPanel(parentContainer, item, callback);
    const list = this.layer.getColOptions();
    tableStyle.setOptions(list);

    //跑马灯
    if (!item["overflow"]) {
      item["overflow"] = {};
    }
    const overflowStyle = new OverflowStyle(this.context, isOpen);
    overflowStyle.initPanel(parentContainer, item["overflow"], callback);

    //跑马灯
    if (!item["marquee"]) {
      item["marquee"] = {};
    }
    const tableMarqueeStyle = new TableMarqueeStyle(this.context, isOpen);
    tableMarqueeStyle.initPanel(parentContainer, item["marquee"], callback);
    //无数据
    if (!item["noData"]) {
      item["noData"] = {};
    }
    const noDataStyle = new NoDataStyle(this.context, isOpen);
    noDataStyle.initPanel(parentContainer, item["noData"], callback);

  }

  addConditionStyle (itemContainer, item, callback) {
    if (!item["columnStyles"]) {
      item["columnStyles"] = [];
    }
    const columnStyleList = new ColumnStyleList(this.context);
    columnStyleList.initPanel(itemContainer, item["columnStyles"], callback);
    const list = this.layer.getOptions();
    columnStyleList.setOptions(list);
  }
}