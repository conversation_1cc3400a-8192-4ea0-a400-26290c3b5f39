
import BaseTreeStyle from "../../../../style/layer/base/tree/BaseTreeStyle";
import BasePropertyPanel from "../BasePropertyPanel";
export default class TreePropertyPanel extends BasePropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //属性
    this.addChartPage(property, callback, isOpen);
    //绑定
    this.addBindPage(property, callback, isOpen);
    //联动
    this.addRelationPage(property, callback, isOpen);
    //条件
    // this.addConditionPage(property, callback, isOpen);
  }
  refreshChart (parentContainer, item, callback, isOpen) {
    //树形
    const treeStyle = new BaseTreeStyle(this.context,isOpen);
    treeStyle.initPanel(parentContainer, item, callback);
   
  }
}