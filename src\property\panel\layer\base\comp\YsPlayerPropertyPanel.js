import YsPlayerStyle from "../../../../style/layer/base/YsPlayerStyle";
import BasePropertyPanel from "../BasePropertyPanel";
export default class YsPlayerPropertyPanel extends BasePropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //属性
    this.addChartPage(property, callback, isOpen);
    //绑定
    // this.addBindPage(property, callback, isOpen);
    //联动
    // this.addRelationPage(property, callback, isOpen);
    //条件
    this.addConditionPage(property, callback, isOpen);
  }
  refreshChart (parentContainer, item, callback, isOpen) {
    //视频
    const videoStyle = new YsPlayerStyle(this.context, isOpen);
    videoStyle.initPanel(parentContainer, item, callback);

  }
  //条件的样式
  addConditionStyle (itemContainer, item, callback) {
    if (!item["videoStyle"]) {
      item["videoStyle"] = {};
    }
    const videoStyle = new YsPlayerStyle(this.context);
    videoStyle.initPanel(itemContainer, item["videoStyle"], callback);
  }
}