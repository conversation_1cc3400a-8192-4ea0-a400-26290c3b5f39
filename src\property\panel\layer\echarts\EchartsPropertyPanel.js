import Title from '../../../style/layer/echarts/option/Title';
import Toolbox from '../../../style/layer/echarts/option/Toolbox';
import Tooltip from '../../../style/layer/echarts/option/Tooltip';
import Legend from '../../../style/layer/echarts/option/Legend';

import TextStyle from '../../../style/layer/echarts/option/TextStyle';

import XAxis from '../../../style/layer/echarts/option/axis/XAxis';
import YAxis from '../../../style/layer/echarts/option/axis/YAxis';
import XAxisList from '../../../style/layer/echarts/option/axis/XAxisList';
import YAxisList from '../../../style/layer/echarts/option/axis/YAxisList';

import Geo from '../../../style/layer/echarts/option/coord/Geo';
import Grid from '../../../style/layer/echarts/option/coord/Grid';
import Radar from '../../../style/layer/echarts/option/coord/Radar';
import Polar from '../../../style/layer/echarts/option/coord/Polar';
import Parallel from '../../../style/layer/echarts/option/coord/Parallel';

import ColorStyle from '../../../style/layer/echarts/option/color/ColorStyle';
import RadiusAxis from '../../../style/layer/echarts/option/axis/RadiusAxis';
import AngleAxis from '../../../style/layer/echarts/option/axis/AngleAxis';
import SingleAxis from '../../../style/layer/echarts/option/axis/SingleAxis';
import ParallelAxis from '../../../style/layer/echarts/option/axis/ParallelAxis';

import VisualMap from '../../../style/layer/echarts/option/visual/VisualMap';

import LayerPropertyPanel from "../LayerPropertyPanel";
import Grid3D from "../../../style/layer/echarts/option/coord/Grid3D";
import Geo3D from "../../../style/layer/echarts/option/coord/Geo3D";
import XAxis3D from "../../../style/layer/echarts/option/axis/XAxis3D";
import YAxis3D from "../../../style/layer/echarts/option/axis/YAxis3D";
import ZAxis3D from "../../../style/layer/echarts/option/axis/ZAxis3D";
export default class EchartsPropertyPanel extends LayerPropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //图表
    this.addChartPage(property, callback, isOpen);
    //绑定
    this.addBindPage(property, callback, isOpen);
    //联动
    this.addRelationPage(property, callback, isOpen);
    //条件
    // this.addConditionPage(property, callback, isOpen);
    // 交互
    this.addInteractivePage(property, callback, isOpen);
  }

  /**
   * 添加图表
   * @param {*} chartContainer 
   * @param {*} property 
   * @param {*} callback 
   * @param {*} isOpen 
   */
  addChartPage (property, callback, isOpen) {
    const pageContainer = this.addPage("图表");
    if (pageContainer && property) {
      if (!property["chart"]) {
        property["chart"] = {};
      }
      this.refreshChart(pageContainer, property["chart"], property["loads"], callback, isOpen);
    }
  }

  /**
   * 抽象方法-刷新序列
   */
  refreshSeries (parentContainer, chart, callback, isOpen) { }
  /**
   * 刷新Option
   * @param {*} parentContainer 
   * @param {*} chart 
   * @param {*} loads 
   * @param {*} callback 
   * @param {*} isOpen 
   */
  refreshChart (parentContainer, chart, loads, callback, isOpen) {
    if (this.context && parentContainer && chart && loads && loads.length) {
      /**======================[通用]====================== */
      //标题
      if (loads.indexOf("title") !== -1) {
        if (!chart.title) {
          chart.title = {};
        }
        const title = new Title(this.context, isOpen);
        title.initPanel(parentContainer, chart.title, callback);
      }
      //工具栏
      if (loads.indexOf("toolbox") !== -1) {
        if (!chart.toolbox) {
          chart.toolbox = {};
        }
        const toolbox = new Toolbox(this.context, isOpen);
        toolbox.initPanel(parentContainer, chart.toolbox, callback);
      }
      //提示框
      if (loads.indexOf("tooltip") !== -1) {
        if (!chart.tooltip) {
          chart.tooltip = {};
        }
        const tooltip = new Tooltip(this.context, isOpen);
        tooltip.initPanel(parentContainer, chart.tooltip, callback);
      }
      //图例
      if (loads.indexOf("legend") !== -1) {
        if (!chart.legend) {
          chart.legend = {};
        }
        const legend = new Legend(this.context, isOpen);
        legend.initPanel(parentContainer, chart.legend, callback);
      }
      //文本样式
      if (loads.indexOf("textStyle") !== -1) {
        if (!chart.textStyle) {
          chart.textStyle = {};
        }
        const textStyle = new TextStyle(this.context, isOpen);
        textStyle.initPanel(parentContainer, chart.textStyle, callback);
      }
      //颜色
      if (loads.indexOf("color") !== -1) {
        if (!chart.color) {
          chart.color = {};
        }
        const colorStyle = new ColorStyle(this.context, isOpen);
        colorStyle.initPanel(parentContainer, chart.color, callback);
      }
      /**======================[坐标系]====================== */
      //直角坐标系(网格)
      if (loads.indexOf("grid") !== -1) {
        if (!chart.grid) {
          chart.grid = {};
        }
        const grid = new Grid(this.context, isOpen);
        grid.initPanel(parentContainer, chart.grid, callback);
      }
      //3d直角坐标系(网格)
      if (loads.indexOf("grid3D") !== -1) {
        if (!chart.grid3D) {
          chart.grid3D = {};
        }
        const grid3D = new Grid3D(this.context, isOpen);
        grid3D.initPanel(parentContainer, chart.grid3D, callback);
      }

      //雷达图坐标系
      if (loads.indexOf("radar") !== -1) {
        if (!chart.radar) {
          chart.radar = {};
        }
        const radar = new Radar(this.context, isOpen);
        radar.initPanel(parentContainer, chart.radar, callback);
      }
      //极坐标系
      if (loads.indexOf("polar") !== -1) {
        if (!chart.polar) {
          chart.polar = {};
        }
        const polar = new Polar(this.context, isOpen);
        polar.initPanel(parentContainer, chart.polar, callback);
      }
      //地理坐标系
      if (loads.indexOf("geo") !== -1) {
        if (!chart.geo) {
          chart.geo = {};
        }
        const geo = new Geo(this.context, isOpen);
        geo.initPanel(parentContainer, chart.geo, callback);
      }
      //3d地理坐标系
      if (loads.indexOf("geo3D") !== -1) {
        if (!chart.geo3D) {
          chart.geo3D = {};
        }
        const geo3D = new Geo3D(this.context, isOpen);
        geo3D.initPanel(parentContainer, chart.geo3D, callback);
      }
      //平行坐标系
      if (loads.indexOf("parallel") !== -1) {
        if (!chart.parallel) {
          chart.parallel = {};
        }
        const parallel = new Parallel(this.context, isOpen);
        parallel.initPanel(parentContainer, chart.parallel, callback);
      }
      //视图特效
      if (loads.indexOf("visualMap") !== -1) {
        if (!chart.visualMap) {
          chart.visualMap = {};
        }
        const visualMap = new VisualMap(this.context, isOpen);
        visualMap.initPanel(parentContainer, chart.visualMap, callback);
      }
      /**======================[坐标轴]====================== */
      //直角坐标系( x 轴)
      if (loads.indexOf("xAxis") !== -1) {
        if (!chart.xAxis) {
          chart.xAxis = {};
        }
        const xAxis = new XAxis(this.context, isOpen);
        xAxis.initPanel(parentContainer, chart.xAxis, callback);
      }
      //直角坐标系( y 轴)
      if (loads.indexOf("yAxis") !== -1) {
        if (!chart.yAxis) {
          chart.yAxis = {};
        }
        const yAxis = new YAxis(this.context, isOpen);
        yAxis.initPanel(parentContainer, chart.yAxis, callback);
      }
      //3d直角坐标系( x 轴)
      if (loads.indexOf("xAxis3D") !== -1) {
        if (!chart.xAxis3D) {
          chart.xAxis3D = {};
        }
        const xAxis3D = new XAxis3D(this.context, isOpen);
        xAxis3D.initPanel(parentContainer, chart.xAxis3D, callback);
      }
      //3d直角坐标系( y 轴)
      if (loads.indexOf("yAxis3D") !== -1) {
        if (!chart.yAxis3D) {
          chart.yAxis3D = {};
        }
        const yAxis3D = new YAxis3D(this.context, isOpen);
        yAxis3D.initPanel(parentContainer, chart.yAxis3D, callback);
      }
      //3d直角坐标系( z 轴)
      if (loads.indexOf("zAxis3D") !== -1) {
        if (!chart.zAxis3D) {
          chart.zAxis3D = {};
        }
        const zAxis3D = new ZAxis3D(this.context, isOpen);
        zAxis3D.initPanel(parentContainer, chart.zAxis3D, callback);
      }
      //直角坐标系( x 轴)
      if (loads.indexOf("xAxisList") !== -1) {
        if (!chart.xAxisList) {
          chart.xAxisList = [];
        }
        const xAxis = new XAxisList(this.context, isOpen);
        xAxis.initPanel(parentContainer, chart.xAxisList, callback);
      }
      //直角坐标系( y 轴)
      if (loads.indexOf("yAxisList") !== -1) {
        if (!chart.yAxisList) {
          chart.yAxisList = [];
        }
        const yAxis = new YAxisList(this.context, isOpen);
        yAxis.initPanel(parentContainer, chart.yAxisList, callback);
      }
      //极坐标系( 径向轴)
      if (loads.indexOf("radiusAxis") !== -1) {
        if (!chart.radiusAxis) {
          chart.radiusAxis = {};
        }
        const radiusAxis = new RadiusAxis(this.context, isOpen);
        radiusAxis.initPanel(parentContainer, chart.radiusAxis, callback);
      }
      //极坐标系( 角度轴)
      if (loads.indexOf("angleAxis") !== -1) {
        if (!chart.angleAxis) {
          chart.angleAxis = {};
        }
        const angleAxis = new AngleAxis(this.context, isOpen);
        angleAxis.initPanel(parentContainer, chart.angleAxis, callback);
      }
      //平行坐标系
      if (loads.indexOf("parallelAxis") !== -1) {
        if (!chart.parallelAxis) {
          chart.parallelAxis = {};
        }
        const parallelAxis = new ParallelAxis(this.context, isOpen);
        parallelAxis.initPanel(parentContainer, chart.parallelAxis, callback);
      }
      //平行坐标系
      if (loads.indexOf("singleAxis") !== -1) {
        if (!chart.singleAxis) {
          chart.singleAxis = {};
        }
        const singleAxis = new SingleAxis(this.context, isOpen);
        singleAxis.initPanel(parentContainer, chart.singleAxis, callback);
      }
      if (loads.indexOf("series") !== -1) {
        this.refreshSeries(parentContainer, chart, callback, isOpen);
      }
    }
  }
  //条件的样式
  addConditionStyle (itemContainer, item, callback) {

  }
}