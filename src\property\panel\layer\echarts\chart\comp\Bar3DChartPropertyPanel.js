import ChartPropertyPanel from "../ChartPropertyPanel";

export default class Bar3DChartPropertyPanel extends ChartPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //图表
    this.addChartPage(property, callback, isOpen);
    //绑定
    this.addBindPage(property, callback, isOpen);
    //联动
    this.addRelationPage(property, callback, isOpen);
    //条件
    this.addConditionPage(property, callback, isOpen);
    // 交互
    this.addInteractivePage(property, callback, isOpen);
  }
  //柱状图
  refreshSeries (parentContainer, chart, callback, isOpen) {
    // if(!chart["series"]){
    //   chart["series"]=[];
    // }
    // const series=new SeriesList(this.context,"bar",isOpen);
    // series.initPanel(parentContainer,chart["series"],callback);
    // series.refreshTitle("序列-柱状");

  }

  addConditionStyle (itemContainer, item, callback) {
    // if(!item["itemStyle"]){
    //   item["itemStyle"]={};
    // }
    // const seriesLine=new SeriesList(this.context,"line");
    // seriesLine.initPanel(itemContainer,item["itemStyle"],callback);
  }
}