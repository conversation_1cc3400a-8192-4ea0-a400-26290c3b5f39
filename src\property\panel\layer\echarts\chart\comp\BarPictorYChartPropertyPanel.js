import ChartPropertyPanel from "../ChartPropertyPanel";
import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
export default class BarPictorYChartPropertyPanel extends ChartPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //图表
    this.addChartPage(property, callback, isOpen);
    //绑定
    this.addBindPage(property, callback, isOpen);
    //联动
    this.addRelationPage(property, callback, isOpen);
    //条件
    this.addConditionPage(property, callback, isOpen);
    
  }
  //柱状图
  refreshSeries (parentContainer, chart, callback, isOpen) {
    if(!chart["barPictorSeries"]){
      chart["barPictorSeries"]=[];
    }
    const series=new SeriesList(this.context,"barPictor",isOpen);
    series.initPanel(parentContainer,chart["barPictorSeries"],callback);
    series.refreshTitle("序列-柱状象形");
  }

  addConditionStyle (itemContainer, item, callback) {
    if(!item["itemStyle"]){
      item["itemStyle"]={};
    }
    const seriesLine=new SeriesList(this.context,"line");
    seriesLine.initPanel(itemContainer,item["itemStyle"],callback);
  }
}