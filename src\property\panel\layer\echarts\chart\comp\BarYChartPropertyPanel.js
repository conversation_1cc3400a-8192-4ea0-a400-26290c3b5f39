// import ChartPropertyPanel from "../ChartPropertyPanel";
// import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
import BarChartPropertyPanel from "./BarChartPropertyPanel";
export default class BarYChartPropertyPanel extends BarChartPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  // refreshProperty (property, callback, isOpen) {
  //   //基础
  //   this.addBasePage(property, callback, isOpen);
  //   //图表
  //   this.addChartPage(property, callback, isOpen);
  //   //绑定
  //   this.addBindPage(property, callback, isOpen);
  //   //联动
  //   this.addRelationPage(property, callback, isOpen);
  //   //条件
  //   this.addConditionPage(property, callback, isOpen);
    
  // }
  // refreshSeries (parentContainer, chart, callback, isOpen) {
  //   if(!chart["series"]){
  //     chart["series"]=[];
  //   }
  //   const seriesBar=new SeriesList(this.context,"bar",isOpen);
  //   seriesBar.initPanel(parentContainer,chart["series"],callback);
  //   seriesBar.refreshTitle("序列-柱状");

  // }

}