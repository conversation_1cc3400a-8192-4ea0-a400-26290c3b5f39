import ChartPropertyPanel from "../ChartPropertyPanel";
// import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
// import DataList from "../../../../../style/layer/echarts/option/series/data/DataList";
import CandlestickSeries from "../../../../../style/layer/echarts/option/series/CandlestickSeries";
export default class CandlestickChartPropertyPanel extends ChartPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  //K线图
  refreshSeries (parentContainer, chart, callback, isOpen) {
    // if(!chart["series"]){
    //   chart["series"]=[];
    // }
    // const series=new SeriesList(this.context,"candlestick",isOpen);
    // series.initPanel(parentContainer,chart["series"],callback);
    // series.refreshTitle("序列-K线");
    
    // if(!chart["datas"]){
    //   chart["datas"]=[];
    // }
    // const datas=new DataList(this.context,isOpen);
    // datas.initPanel(parentContainer,chart["datas"],callback);

    if(!chart["serie"]){
      chart["serie"]={};
    }
    const serie = new CandlestickSeries(this.context,isOpen);
    serie.initPanel(parentContainer, chart["serie"], callback);
  }
}