import ChartPropertyPanel from "../ChartPropertyPanel";
// import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
// import DataList from "../../../../../style/layer/echarts/option/series/data/DataList";
import GaugeSeries from "../../../../../style/layer/echarts/option/series/GaugeSeries";
export default class GaugeChartPropertyPanel extends ChartPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  //仪表盘
  refreshSeries (parentContainer, chart, callback, isOpen) {
    // if(!chart["series"]){
    //   chart["series"]=[];
    // }
    // const series=new SeriesList(this.context,"gauge",isOpen);
    // series.initPanel(parentContainer,chart["series"],callback);
    // series.refreshTitle("序列-仪表盘");
    
    // if(!chart["datas"]){
    //   chart["datas"]=[];
    // }
    // const datas=new DataList(this.context,isOpen);
    // datas.initPanel(parentContainer,chart["datas"],callback);

    if(!chart["serie"]){
      chart["serie"]={};
    }
    const serie = new GaugeSeries(this.context,isOpen);
    serie.initPanel(parentContainer, chart["serie"], callback);
  }
}