import ChartPropertyPanel from "../ChartPropertyPanel";
import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
export default class LineBarChartPropertyPanel extends ChartPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  //折柱图
  refreshSeries (parentContainer, chart, callback, isOpen) {
    if(!chart["lineVal"]){
      chart["lineVal"]=[];
    }
    const valSeries=new SeriesList(this.context,"val",isOpen);
    valSeries.initPanel(parentContainer,chart["lineVal"],callback);
    valSeries.refreshTitle("折线-值");
    const list = this.layer.getOptions();
    valSeries.setOptions(list);

    if(!chart["barSeries"]){
      chart["barSeries"]=[];
    }
    const seriesBar=new SeriesList(this.context,"bar",isOpen);
    seriesBar.initPanel(parentContainer,chart["barSeries"],callback);
    seriesBar.refreshTitle("序列-柱状");

    if(!chart["lineSeries"]){
      chart["lineSeries"]=[];
    }
    const seriesLine=new SeriesList(this.context,"line",isOpen);
    seriesLine.initPanel(parentContainer,chart["lineSeries"],callback);
    seriesLine.refreshTitle("序列-折线");

  }
}