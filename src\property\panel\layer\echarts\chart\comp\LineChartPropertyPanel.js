import ChartPropertyPanel from "../ChartPropertyPanel";
import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
export default class LineChartPropertyPanel extends ChartPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  //折线图
  refreshSeries (parentContainer, chart, callback, isOpen) {
    if(!chart["series"]){
      chart["series"]=[];
    }
    const series=new SeriesList(this.context,"line",isOpen);
    series.initPanel(parentContainer,chart["series"],callback);
    series.refreshTitle("序列-折线");
    
  }

  addConditionStyle (itemContainer, item, callback) {
    // if(!item["series"]){
    //   item["series"]=[];
    // }
    // const seriesLine=new SeriesList(this.context,"line");
    // seriesLine.initPanel(itemContainer,item["series"],callback);
    // seriesLine.refreshTitle("序列-折线");
  }
}