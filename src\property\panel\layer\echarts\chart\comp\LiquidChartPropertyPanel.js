import ChartPropertyPanel from "../ChartPropertyPanel";
// import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
// import DataList from "../../../../../style/layer/echarts/option/series/data/DataList";
import LiquidSeries from "../../../../../style/layer/echarts/option/series/LiquidSeries";
export default class LiquidChartPropertyPanel extends ChartPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  //水晶球
  refreshSeries (parentContainer, chart, callback, isOpen) {
    // if(!chart["series"]){
    //   chart["series"]=[];
    // }
    // const series=new SeriesList(this.context,"liquidFill",isOpen);
    // series.initPanel(parentContainer,chart["series"],callback);
    // series.refreshTitle("序列-水晶球");
    
    // if(!chart["datas"]){
    //   chart["datas"]=[];
    // }
    // const datas=new DataList(this.context,isOpen);
    // datas.initPanel(parentContainer,chart["datas"],callback);

    if(!chart["serie"]){
      chart["serie"]={};
    }
    const serie = new LiquidSeries(this.context,isOpen);
    serie.initPanel(parentContainer, chart["serie"], callback);
  }
}