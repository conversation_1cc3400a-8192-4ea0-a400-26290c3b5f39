import ChartPropertyPanel from "../ChartPropertyPanel";
// import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
// import DataList from "../../../../../style/layer/echarts/option/series/data/DataList";
import ParallelSeries from "../../../../../style/layer/echarts/option/series/ParallelSeries";
export default class ParallelChartPropertyPanel extends ChartPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  //平行坐标系
  refreshSeries (parentContainer, chart, callback, isOpen) {
    // if(!chart["series"]){
    //   chart["series"]=[];
    // }
    // const series=new SeriesList(this.context,"parallel",isOpen);
    // series.initPanel(parentContainer,chart["series"],callback);
    // series.refreshTitle("序列-平行坐标系");
    
    // if(!chart["datas"]){
    //   chart["datas"]=[];
    // }
    // const datas=new DataList(this.context,isOpen);
    // datas.initPanel(parentContainer,chart["datas"],callback);

    if(!chart["serie"]){
      chart["serie"]={};
    }
    const serie = new ParallelSeries(this.context,isOpen);
    serie.initPanel(parentContainer, chart["serie"], callback);
  }
}