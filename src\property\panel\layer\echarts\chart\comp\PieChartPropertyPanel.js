import ChartPropertyPanel from "../ChartPropertyPanel";
// import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
import PieSeries from "../../../../../style/layer/echarts/option/series/PieSeries";
export default class PieChartPropertyPanel extends ChartPropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  //饼图
  refreshSeries (parentContainer, chart, callback, isOpen) {

    // if(!chart["series"]){
    //   chart["series"]=[];
    // }
    // const series=new SeriesList(this.context,"pie",isOpen);
    // series.initPanel(parentContainer,chart["series"],callback);
    // series.refreshTitle("序列-饼图");

    if(!chart["serie"]){
      chart["serie"]={};
    }
    const serie = new PieSeries(this.context,isOpen);
    serie.initPanel(parentContainer, chart["serie"], callback);
  }

}