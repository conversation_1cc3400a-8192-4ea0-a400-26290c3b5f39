import ChartPropertyPanel from "../ChartPropertyPanel";
import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
export default class PieNestChartPropertyPanel extends ChartPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  //饼图
  refreshSeries (parentContainer, chart, callback, isOpen) {
    if(!chart["series"]){
      chart["series"]=[];
    }
    const series=new SeriesList(this.context,"pie",isOpen);
    series.initPanel(parentContainer,chart["series"],callback);
    series.refreshTitle("序列-饼图");

  }

}