import ChartPropertyPanel from "../ChartPropertyPanel";
// import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
// import DataList from "../../../../../style/layer/echarts/option/series/data/DataList";
import RadarSeries from "../../../../../style/layer/echarts/option/series/RadarSeries";
export default class RadarChartPropertyPanel extends ChartPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  //雷达
  refreshSeries (parentContainer, chart, callback, isOpen) {
    // if(!chart["series"]){
    //   chart["series"]=[];
    // }
    // const series=new SeriesList(this.context,"radar",isOpen);
    // series.initPanel(parentContainer,chart["series"],callback);
    // series.refreshTitle("序列-雷达图");

    // if(!chart["datas"]){
    //   chart["datas"]=[];
    // }
    // const datas=new DataList(this.context,isOpen);
    // datas.initPanel(parentContainer,chart["datas"],callback);

    if(!chart["serie"]){
      chart["serie"]={};
    }
    const serie = new RadarSeries(this.context,isOpen);
    serie.initPanel(parentContainer, chart["serie"], callback);
  }
}