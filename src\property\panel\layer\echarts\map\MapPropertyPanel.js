import MapDrillStyle from "../../../../style/common/map/MapDrillStyle";
import MapBindStyle from "../../../../style/common/map/MapBindStyle";
import MapSelectStyle from "../../../../style/common/map/MapSelectStyle";
import EchartsPropertyPanel from "../EchartsPropertyPanel";
export default class MapPropertyPanel extends EchartsPropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //图表
    this.addChartPage(property, callback, isOpen);
    //绑定(地图)
    this.addMapPage(property, callback, isOpen);
    //联动
    this.addRelationPage(property, callback, isOpen);
    //条件
    // this.addConditionPage(property, callback, isOpen);

  }
  addMapPage (property, callback, isOpen) {
    const panelContainer = this.addPage("地图");
    if (panelContainer && property) {
      const self = this;
      const callbackBind = function () {
        self.layer.refreshData();
      }
      if (!property["map"]) {
        property["map"] = {};
      }
      this.refreshMap(panelContainer, property["bind"], property["map"], callbackBind, isOpen);
    }
  }
  refreshMap (parentContainer, bind, map, callback, isOpen) {
    //地图选择
    const mapSelectStyle = new MapSelectStyle(this.context);
    mapSelectStyle.initPanel(parentContainer, map, callback);
    mapSelectStyle.setChangeMapCallback((isCustom) => {
      const data = this.layer.getOptions(isCustom);
      mapSelectStyle.refreshData(data);
    });
    const data = this.layer.getOptions(map.isCustom);
    mapSelectStyle.refreshData(data);
    //地图绑定
    const mapBindStyle = new MapBindStyle(this.context);
    mapBindStyle.initPanel(parentContainer, bind, callback);
    mapBindStyle.addMapProp(map);
    //地图钻取
    const mapDrillStyle = new MapDrillStyle(this.context);
    mapDrillStyle.initPanel(parentContainer, bind, callback);
    mapDrillStyle.addMapProp(map);
  }
}