import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
import MapPropertyPanel from "../MapPropertyPanel";
export default class Zg3DMapPropertyPanel extends MapPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  //地图
  refreshSeries (parentContainer, chart, callback, isOpen) {
    if(!chart["mapSeries"]){
      chart["mapSeries"]=[];
    }
    const seriesMap=new SeriesList(this.context,"map3D",isOpen);
    seriesMap.initPanel(parentContainer,chart["mapSeries"],callback);
    seriesMap.refreshTitle("序列-3D地图");
  }
}