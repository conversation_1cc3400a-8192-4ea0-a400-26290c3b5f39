import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
import MapPropertyPanel from "../MapPropertyPanel";
export default class ZgFxMapPropertyPanel extends MapPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  //地图
  refreshSeries (parentContainer, chart, callback, isOpen) {
    if(!chart["mapSeries"]){
      chart["mapSeries"]=[];
    }
    const seriesMap=new SeriesList(this.context,"map",isOpen);
    seriesMap.initPanel(parentContainer,chart["mapSeries"],callback);
    seriesMap.refreshTitle("序列-地图");

    if(!chart["linesSeries"]){
      chart["linesSeries"]=[];
    }
    const linesSeriesMap=new SeriesList(this.context,"lines",isOpen);
    linesSeriesMap.initPanel(parentContainer,chart["linesSeries"],callback);
    linesSeriesMap.refreshTitle("序列-关系");

    if(!chart["effectScatterSeries"]){
      chart["effectScatterSeries"]=[];
    }
    const effectScatterSeriesMap=new SeriesList(this.context,"effectScatter",isOpen);
    effectScatterSeriesMap.initPanel(parentContainer,chart["effectScatterSeries"],callback);
    effectScatterSeriesMap.refreshTitle("序列-涟漪散点");
  }
}