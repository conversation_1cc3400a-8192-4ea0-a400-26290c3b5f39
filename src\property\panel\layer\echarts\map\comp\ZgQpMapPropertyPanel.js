import SeriesList from "../../../../../style/layer/echarts/option/series/SeriesList";
import MapPropertyPanel from "../MapPropertyPanel";
export default class ZgQpMapPropertyPanel extends MapPropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  //地图
  refreshSeries (parentContainer, chart, callback, isOpen) {
    if(!chart["mapSeries"]){
      chart["mapSeries"]=[];
    }
    const seriesMap=new SeriesList(this.context,"map",isOpen);
    seriesMap.initPanel(parentContainer,chart["mapSeries"],callback);
    seriesMap.refreshTitle("序列-地图");

    if(!chart["scatterSeries"]){
      chart["scatterSeries"]=[];
    }
    const seriesScatterMap=new SeriesList(this.context,"scatter",isOpen);
    seriesScatterMap.initPanel(parentContainer,chart["scatterSeries"],callback);
    seriesScatterMap.refreshTitle("序列-气泡(散点)");
    
  }
}