import InitList from "../../../../style/layer/filter/InitList";
import DateStyle from "../../../../style/layer/filter/DateStyle";
import Datefilter from "../../../../style/layer/filter/Datefilter";
import FilterPropertyPanel from "../FilterPropertyPanel";
export default class DateFilterPropertyPanel extends FilterPropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  refreshBase (parentContainer, property, callback, isOpen) {
    if (!property["datefilter"]) {
      property["datefilter"] = {};
    }
    const datefilter = new Datefilter(this.context, isOpen);
    datefilter.initPanel(parentContainer, property["datefilter"], callback);

    const datasets = this.getDatasetOptions();
    const params = this.getParamOptions();
    const callbackInit = this.callbackInit();
    //初始化
    if (!property["inits"]) {
      property["inits"] = [];
    }
    const initList = new InitList(this.context, isOpen);
    initList.initPanel(parentContainer, property["inits"], callbackInit);
    initList.setDatasets(datasets)
    initList.setParams(params);
  }
  refreshChart (parentContainer, item, callback, isOpen) {
    //日期
    const dateStyle = new DateStyle(this.context, isOpen);
    dateStyle.initPanel(parentContainer, item, callback);
  }
}