import InitList from "../../../../style/layer/filter/InitList";
import RadioStyle from "../../../../style/layer/filter/RadioStyle";
import FilterPropertyPanel from "../FilterPropertyPanel";
export default class RadioFilterPropertyPanel extends FilterPropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  refreshBase(parentContainer, property, callback, isOpen) {

    const datasets = this.getDatasetOptions();
    const params = this.getParamOptions();
    const callbackInit = this.callbackInit();
    //初始化
    if (!property["inits"]) {
      property["inits"] = [];
    }
    const initList = new InitList(this.context, isOpen);
    initList.initPanel(parentContainer, property["inits"], callbackInit);
    initList.setDatasets(datasets)
    initList.setParams(params);
  }
  refreshChart (parentContainer, item, callback, isOpen) {
    //单选
    const radioStyle = new RadioStyle(this.context, isOpen);
    radioStyle.initPanel(parentContainer, item, callback);

  }
}