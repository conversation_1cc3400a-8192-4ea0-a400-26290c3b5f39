import InitList from "../../../../style/layer/filter/InitList";
import SelectStyle from "../../../../style/layer/filter/SelectStyle";
import FilterPropertyPanel from "../FilterPropertyPanel";
export default class SelectFilterPropertyPanel extends FilterPropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  refreshBase (parentContainer, property, callback, isOpen) {

    const datasets = this.getDatasetOptions();
    const params = this.getParamOptions();
    const callbackInit = this.callbackInit();
    //初始化
    if (!property["inits"]) {
      property["inits"] = [];
    }
    const initList = new InitList(this.context, isOpen);
    initList.initPanel(parentContainer, property["inits"], callbackInit);
    initList.setDatasets(datasets)
    initList.setParams(params);
  }
  refreshChart (parentContainer, item, callback, isOpen) {
    //选择
    const selectStyle = new SelectStyle(this.context, isOpen);
    selectStyle.initPanel(parentContainer, item, callback);

  }
}