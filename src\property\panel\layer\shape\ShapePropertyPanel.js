import LayerPropertyPanel from "../LayerPropertyPanel";
export default class ShapePropertyPanel extends LayerPropertyPanel {
  constructor(context, isTabs) {
    super(context, isTabs);
  }
  refreshProperty (property, callback, isOpen) {
    //基础
    this.addBasePage(property, callback, isOpen);
    //图表
    this.addChartPage(property, callback, isOpen);
    //绑定
    // this.addBindPage(property, callback, isOpen);
    //联动
    this.addRelationPage(property, callback, isOpen);
    //条件
    this.addConditionPage(property, callback, isOpen);
    // 交互
    this.addInteractivePage(property, callback, isOpen);

  }
  /**
   * 添加属性面板
   * @param {*} property 
   * @param {*} callback 
   * @param {*} isOpen 
   */
  addChartPage (property, callback, isOpen) {
    const pageContainer = this.addPage("组件");
    if (pageContainer && property) {
      this.refreshChart(pageContainer, property["chart"], callback, isOpen);
    }
  }
  /**
   * 抽象
   * @param {*} parentContainer 
   * @param {*} props 
   * @param {*} callback 
   * @param {*} isOpen 
   */
  refreshChart (parentContainer, item, callback, isOpen) {

  }
  //条件的样式
  addConditionStyle (itemContainer, item, callback) {

  }
}