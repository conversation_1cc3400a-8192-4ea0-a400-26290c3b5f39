import CircularShapeStyle from "../../../../style/layer/shape/CircularShapeStyle";
import ShapePropertyPanel from "../ShapePropertyPanel";
export default class CircularShapePropertyPanel extends ShapePropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  refresh<PERSON>hart (parentContainer, item, callback, isOpen) {
      
      const circularShapeStyle = new CircularShapeStyle(this.context,isOpen);
      circularShapeStyle.initPanel(parentContainer,item,callback);

  }

  //条件的样式
  addConditionStyle (parentContainer, item, callback) {
    const circularShapeStyle = new CircularShapeStyle(this.context);
      circularShapeStyle.initPanel(parentContainer,item,callback);
 }
}