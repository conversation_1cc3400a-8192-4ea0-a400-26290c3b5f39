import RectangleShapeStyle from "../../../../style/layer/shape/RectangleShapeStyle";
import ShapePropertyPanel from "../ShapePropertyPanel";
export default class RectangleShapePropertyPanel extends ShapePropertyPanel{
  constructor(context,isTabs) {
    super(context,isTabs);
  }
  refresh<PERSON>hart (parentContainer, item, callback, isOpen) {
   
    const rectangleShapeStyle = new RectangleShapeStyle(this.context,isOpen);
    rectangleShapeStyle.initPanel(parentContainer,item,callback);

  }

  //条件的样式
  addConditionStyle (parentContainer, item, callback) {
    const rectangleShapeStyle = new RectangleShapeStyle(this.context);
    rectangleShapeStyle.initPanel(parentContainer,item,callback);
 }
}