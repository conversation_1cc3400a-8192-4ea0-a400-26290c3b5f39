import ChartStyle from "./ChartStyle";
export default class CardBtnStyle extends ChartStyle {
  constructor(context, isOpen) {
    super(context);
    this.isOpen = isOpen || false;
  }
  initModel(container) {
    if (container) {
      const modelContainer = $(`<div class="comm-wrap card-wrap">
        <div class="comm-head card-head" >
          <div class="head-title">` + this.getTitle() + `</div>
          <div class="head-btn head-add">`+ this.getBtnTitle() + `</div>
        </div>
        <div class="comm-body card-body"></div>
      </div>`);
      container.append(modelContainer);
      this.chartHead = $(modelContainer).find(".card-head");
      this.chartBody = $(modelContainer).find(".card-body");

      this.chartTitle = $(modelContainer).find(".card-head .head-title");
      // this.chartIcon = $(modelContainer).find(".card-head .head-icon");
      this.refreshHead();
      this.refreshOpen();
      const self = this;
      const btnContainer = $(modelContainer).find(".card-head .head-btn");
      $(btnContainer).on("click", function (event) {
        self.refreshBtnEvent();
      });
    }
  }
  /**
   * 
   */
  refreshHead() {
    const self = this;
    $(this.chartTitle).on("click", function (event) {
      self.isOpen = !self.isOpen;
      self.refreshOpen();
    });
  }
  refreshOpen() {
    if (this.isOpen) {
      $(this.chartBody).show();
      this.refreshBody(this.chartBody);
    } else {
      $(this.chartBody).hide();
    }
  }
  /**
  * 
  * @param {*} title 
  */
  refreshTitle(title) {
    if (this.chartTitle && title) {
      $(this.chartTitle).text(title);
    }
  }
  /**
   * 获取按钮标题
   * @returns 
   */
  getBtnTitle() {
    return "绑定"
  };
  /**
   * 按钮事件触发
   */
  refreshBtnEvent() {
    if (this.callback) {
      this.callback();
    }
  }

  // getColOptions(columns,isExpr) {
  //   let options;
  //   if (columns && columns.length) {
  //     for (let i = 0; i < columns.length; i++) {
  //       const column = columns[i];
  //       let expr;
  //       if (isExpr) {
  //         expr = "${" + column + "}";
  //       } else {
  //         expr = column;
  //       }
  //       const item = { key: expr, text: expr };
  //       if (!options) {
  //         options = [];
  //       }
  //       options.push(item);
  //     }
  //   }
  //   return options;
  // }
}