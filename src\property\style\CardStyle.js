import ChartStyle from "./ChartStyle";
export default class CardStyle extends ChartStyle {
  constructor(context, isOpen) {
    super(context);
    this.isOpen = isOpen || false;
  }
  initModel (container) {
    if (container) {
      const modelContainer = $(`<div class="comm-wrap card-wrap">
        <div class="comm-head card-head" >
          <div class="head-title">` + this.getTitle() + `</div>
          <div class="head-icon ft-font icon-shangla"></div>
        </div>
        <div class="comm-body card-body"></div>
      </div>`);
      container.append(modelContainer);
      this.chartHead = $(modelContainer).find(".card-head");
      this.chartBody = $(modelContainer).find(".card-body");

      this.chartTitle = $(modelContainer).find(".card-head .head-title");
      this.chartIcon = $(modelContainer).find(".card-head .head-icon");
      this.refreshHead();
      this.refreshOpen();
    }
  }
  /**
   * 
   */
  refreshHead () {
    const self = this;
    $(this.chartHead).on("click", function (event) {
      self.isOpen = !self.isOpen;
      self.refreshOpen();
    });
  }
  refreshOpen () {
    if (this.isOpen) {
      $(this.chartIcon).attr({ switch: 'open' })
      $(this.chartBody).show();
      this.refreshBody(this.chartBody);
    } else {
      $(this.chartIcon).attr({ switch: 'close' })
      $(this.chartBody).hide();
    }
  }
  /**
   * 
   * @param {*} title 
   */
  refreshTitle (title) {
    if (this.chartTitle && title) {
      $(this.chartTitle).text(title);
    }
  }
}