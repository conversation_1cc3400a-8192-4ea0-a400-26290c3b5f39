import ChartStyle from "./ChartStyle";
export default class TreeStyle extends ChartStyle {
  constructor(context, isAdd, isOpen) {
    super(context);
    this.isAdd = isAdd || false;
    this.isOpen = isOpen || false;
  }
  getContainer () {
    return $(`<div class="tree-wrap"></div>`);
  }
  initModel (container) {
    if (container) {
      const modelContainer = $(`<div class="comm-wrap tree-wrap" id="` + this.getId() + `">
        <div class="comm-head tree-head">
          <div class="head-icon ft-font icon-shangla"></div>
          <div class="head-title">` + this.getTitle() + `</div>
        </div>
        <div class="comm-body tree-body"></div>
      </div>`);
      container.append(modelContainer);
      this.chartHead = $(modelContainer).find(".tree-head");
      this.chartBody = $(modelContainer).find(".tree-body");

      this.headTitle = $(modelContainer).find(".tree-head .head-title");
      this.headIcon = $(modelContainer).find(".tree-head .head-icon");
      this.refreshHead();
      this.refreshOpen();
      this.refreshAdd();
    }
  }
  refreshBody () {
    if (this.chartBody) {
      this.chartBody.empty();
      if (this.item) {
        const modelName = this.getModelName();
        this.refreshPanel(this.chartBody, modelName, this.item, this.callback);
        // this.refreshBind(this.item, this.callback);
      }
      if (this.list && this.list.length) {
        for (let i = 0; i < this.list.length; i++) {
          const item = this.list[i];
          this.refreshItem(this.chartBody, item, i, this.callback);
        }
      }
      //刷新颜色
      const clazzColor = this.getClazzColor();
      this.refreshClazzColor(this.chartBody, clazzColor);
    }
  }
  /**
   * 
   * @param {*} parentContainer 
   * @param {*} item 
   * @param {*} index 
   */
  refreshItem (bodyContainer, item, index, callback) {
    //后置添加Item
    this.afterAddItem(bodyContainer, item, index, callback);
    //添加删除按钮
    if (this.isAdd) {
      const id = this.getId(index);
      const headContainer = $(bodyContainer).find("#" + id).find(".tree-head");
      if (headContainer && headContainer.length) {
        const delContainer = $(`<div class="head-btn head-del">删除</div>`);
        headContainer.append(delContainer);
        const self = this;
        $(delContainer).on("click", function (event) {
          self.del(index);
          //刷新
          self.refreshBody();
          if (self.callback) {
            self.callback();
          }
        });
      }
    }
  }
  /**
   * 
   * @param {*} chartBody 
   * @param {*} modelName 
   * @param {*} item 
   * @param {*} callback 
   */
  refreshPanel (chartBody, modelName, item, callback) {
    console.info("刷新配置未处理");
  }
  /**
   * 
   * @param {*} itemContainer 
   * @param {*} index 
   * @param {*} item 
   * @param {*} callback 
   */
  afterAddItem (itemContainer, item, index, callback) {
    console.info("后置添加配置未处理");
  }
  /**
   * 
   */
  refreshHead () {
    const self = this;
    $(this.chartHead).on("click", function (event) {
      self.isOpen = !self.isOpen;
      self.refreshOpen();
    });
  }
  refreshOpen (callback) {
    if (this.headIcon && this.chartBody) {
      if (this.isOpen) {
        $(this.headIcon).attr({ switch: 'open' })
        $(this.chartBody).show();
        this.refreshBody();
        if (callback) {
          callback();
        }
      } else {
        $(this.headIcon).attr({ switch: 'close' })
        $(this.chartBody).hide();
      }
    }
  }
  refreshAdd (callback) {
    if (this.chartHead && this.isAdd) {
      const chartAdd = $(`<div class="head-btn head-add">添加</div>`);
      this.chartHead.append(chartAdd);
      //点击增加事件
      const self = this;
      $(chartAdd).on("click", function (event) {
        self.add();
        //刷新
        if (self.isOpen) {
          // 已经展开时，阻止冒泡，防止被折叠
          event.stopPropagation();
          //刷新
          self.refreshBody();
          if (self.callback) {
            self.callback();
          }
        }
      });
    }
  }
  /**
   * 
   * @param {*} index 
   * @returns 
   */
  refreshId (index) {
    const id = this.getId(index);
    if (this.container && id) {
      $(this.container).attr("id", id);
    }
  }
  /**
   * 
   * @param {*} title 
   */
  refreshTitle (title) {
    if (this.headTitle && title) {
      $(this.headTitle).text(title);
    }
  }
  getId (index) {
    if (index || index === 0) {
      return "tree_index_" + index;
    } else {
      return "tree_index_01";
    }
  }
  /**
   * 
   * @returns 
   */
  getTitle () {
    return "标题";
  }
  /**
   * 添加
   */
  add () {
    const item = {};
    if (this.list) {
      this.list.push(item);
    } else {
      this.list = [item];
    }
  }
  /**
   * 删除
   * @param {*} index 
   */
  del (index) {
    if (this.list && this.list.length) {
      this.list.splice(index, 1);
    }
  }
}