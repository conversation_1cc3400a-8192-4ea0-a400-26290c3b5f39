import ChartStyle from "../ChartStyle";
export default class BaseStyle extends ChartStyle {
  constructor(context, isOpen) {
    super(context);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //是否隐藏
      // const isHideContainer = $(`<div class="chart-item">
      //     <div class="w-50">
      //         <input type="checkbox" class="chart-checkbox"  ` + modelName + `="isHide" />
      //         <span class="chart-span">是否隐藏</span>
      //     </div>
      // </div>`);
      // chartBody.append(isHideContainer);
      const nameContainer = $(`<div class="chart-item">
                <div class="chart-label" >名称</div>
                <div class="chart-control" >
                    <input type="text" class="chart-text" placeholder="图层名称" ` + modelName + `="label"/>
                </div>
            </div>`);
      chartBody.append(nameContainer);
      // const displayContainer = $(`<div class="chart-item">
      //           <div class="chart-label">显示</div>
      //           <div class="chart-control">
      //               <select class="chart-select" ` + modelName + `="display" >
      //                   <option value="">--请选择--</option>
      //                   <option value="block">显示</option>
      //                   <option value="none">隐藏</option>
      //               </select>
      //           </div>
      //       </div>`);
      // chartBody.append(displayContainer);
      // const visibilityContainer = $(`<div class="chart-item">
      //           <div class="chart-label">显示</div>
      //           <div class="chart-control">
      //               <select class="chart-select" ` + modelName + `="visibility" >
      //                   <option value="">--请选择--</option>
      //                   <option value="visible">显示</option>
      //                   <option value="hidden">隐藏</option>
      //               </select>
      //           </div>
      //       </div>`);
      // chartBody.append(visibilityContainer);
      //图层宽度与高度配置
      const widthContainer = $(`<div class="chart-item">
                <div class="chart-label">宽度</div>
                <div class="chart-control">
                    <input type="number" class="chart-number" min="0" placeholder="图层的宽度" ` + modelName + `="width" />
                </div>
            </div>`);
      chartBody.append(widthContainer);
      const heightContainer = $(`<div class="chart-item">
                <div class="chart-label">高度</div>
                <div class="chart-control">
                    <input type="number" class="chart-number" min="0" placeholder="图层的高度" ` + modelName + `="height" />
                </div>
            </div>`);
      chartBody.append(heightContainer);
      //图层left与top配置
      const leftContainer = $(`<div class="chart-item">
               <div class="chart-label">X轴</div>
                <div class="chart-control">
                    <input type="number" class="chart-number" min="0" placeholder="X轴坐标" ` + modelName + `="left" />
                </div>
            </div>`);
      chartBody.append(leftContainer);
      const topContainer = $(`<div class="chart-item">
                <div class="chart-label">Y轴</div>
                <div class="chart-control">
                    <input type="number" class="chart-number" min="0" placeholder="Y轴坐标" ` + modelName + `="top" />
                </div>
            </div>`);
      chartBody.append(topContainer);
      //透明度与显示
      const opacityContainer = $(`<div class="chart-item">
                 <div class="chart-label">透明度</div>
                  <div class="chart-control">
                      <input type="number" class="chart-number" min="0" max="100" step=10 placeholder="[0至100]" ` + modelName + `="opacity" />
                  </div>
            </div>`);
      chartBody.append(opacityContainer);
      // const timeoutContainer = $(`<div class="chart-item">
      //           <div class="chart-label">延时</div>
      //           <div class="chart-control">
      //               <input type="number" class="chart-number" min="0" placeholder="加载[毫秒]" ` + modelName + `="timeout" />
      //           </div>
      //       </div>`);
      // chartBody.append(timeoutContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);

    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "base-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "基础"
  }
}