import CardStyle from "../CardStyle";
export default class TransformStyle extends CardStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      //移动 translate
      const translateContainer = $(`<div class="chart-item">
          <div class="chart-label">移动</div>
          <div class="chart-control flex-1 pl8">
              <input type="number" min="0" class="chart-number" placeholder="X[px]" ` + modelName + `="translateX" />
              <input type="number" min="0" class="chart-number mlr10" placeholder="Y[px]" ` + modelName + `="translateY" />
              <input type="number" min="0" class="chart-number" placeholder="Z[px]" ` + modelName + `="translateZ" />
          </div>
      </div>`);
      chartBody.append(translateContainer);
      //缩放 scale
      const scaleContainer = $(`<div class="chart-item">
          <div class="chart-label">缩放</div>
          <div class="chart-control flex-1 pl8">
              <input type="number" min="0" class="chart-number" placeholder="X[0-N]" ` + modelName + `="scaleX" />
              <input type="number" min="0" class="chart-number mlr10" placeholder="Y[0-N]" ` + modelName + `="scaleY" />
              <input type="number" min="0" class="chart-number" placeholder="Z[0-N]" ` + modelName + `="scaleZ" />
          </div>
      </div>`);
      chartBody.append(scaleContainer);
      //倾斜 skew 0~360 deg
      const skewContainer = $(`<div class="chart-item">
          <div class="chart-label">倾斜</div>
          <div class="chart-control flex-1 pl8">
              <input type="number" min="-360" max="360" class="chart-number" placeholder="X[+-360]" ` + modelName + `="skewX" />
              <input type="number" min="-360" max="360" class="chart-number mlr10" placeholder="Y[0-360]" ` + modelName + `="skewY" />
              <input type="number" min="-360" max="360" class="chart-number" placeholder="Z[+-360]" ` + modelName + `="skewZ" />
          </div>
      </div>`);
      chartBody.append(skewContainer);
      //旋转 rotate
      const rotateContainer = $(`<div class="chart-item">
          <div class="chart-label">旋转</div>
          <div class="chart-control flex-1 pl8">
              <input type="number" min="-360" max="360" class="chart-number" placeholder="X[+-360]" ` + modelName + `="rotateX" />
              <input type="number" min="-360" max="360" class="chart-number mlr10" placeholder="Y[+-360]" ` + modelName + `="rotateY" />
              <input type="number" min="-360" max="360" class="chart-number" placeholder="Z[+-360]" ` + modelName + `="rotateZ" />
          </div>
      </div>`);
      chartBody.append(rotateContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "transform-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "转换样式"
  }
}