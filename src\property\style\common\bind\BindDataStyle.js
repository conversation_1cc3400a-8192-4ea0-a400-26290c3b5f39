import BindColumnStyle from "./item/BindColumnStyle";
import BindDataDialog from "../../../../dialog/BindDataDialog";
import CardBtnStyle from "../../CardBtnStyle";
export default class BindDataStyle extends CardBtnStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  refreshBindPanel (bindContainer) {
    const chartBody = bindContainer;
    const item = this.item;
    const callback = this.callback;
    if (chartBody) {
      chartBody.empty();

      const bindColumnStyle = new BindColumnStyle(this.context);
      bindColumnStyle.initPanel(chartBody, item, callback);
    }
  }
  /**
   * 
   * @param {*} chartBody 
   * @param {*} modelName 
   * @param {*} item 
   * @param {*} callback 
   */
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // this.refreshModel(item);
      // this.bindModel(item, callback);
      //绑定容器
      this.bindContainer = $(`<div class="chart-bind"></div>`);
      chartBody.append(this.bindContainer);
      //刷新绑定面板
      this.refreshBindPanel(this.bindContainer);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "bindData-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "绑定-数据"
  }

  /**
   * 
   * @returns 
   */
  getBtnTitle () {
    return "绑定"
  }
  /**
   * 
   */
  refreshBtnEvent () {
    const self = this;
    const dialog = new BindDataDialog(self.context, self.item);
    dialog.open(function (result) {
      for (let key in result) {
        self.item[key] = result[key];
      }
      //刷新绑定
      self.refreshBindPanel(self.bindContainer);
      //触发(回调)
      if (self.callback) {
        self.callback();
      }
    });
  }
}