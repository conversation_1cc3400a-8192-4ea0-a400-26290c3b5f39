import BindColumnStyle from "./item/BindColumnStyle";
import BindMappingStyle from "./item/BindMappingStyle";
import CardBtnStyle from "../../CardBtnStyle";
export default class BindDictStyle extends CardBtnStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  setDicts(dicts) {
    this.dicts = dicts;
  }
  refreshBindPanel(bindContainer) {
    const chartBody = bindContainer;
    const item = this.item;
    const callback = this.callback;
    if (chartBody) {
      chartBody.empty();
      const bindMappingStyle = new BindMappingStyle(this.context);
      bindMappingStyle.initPanel(chartBody, item, callback);
      // const options=this.getColOptions(item.columns,true);
      // bindMappingStyle.setOptions(options);
      // const bindColumnStyle = new BindColumnStyle(this.context);
      // bindColumnStyle.initPanel(chartBody, item, callback);
    }
  }

  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      const dictIdContainer = $(`<div class="chart-item flex">
        <div class="chart-label">选择字典</div>
        <div class="chart-control">
            <select class="chart-select" ` + modelName + `="dictId" ></select>
        </div>
      </div>`);
      chartBody.append(dictIdContainer);
      //选择
      const dictSelecter = $(dictIdContainer).find(".chart-select");
      this.refreshOption(dictSelecter,this.dicts);

      const dictLevelContainer = $(`<div class="chart-item flex">
        <div class="chart-label">绑定层级</div>
        <div class="chart-control">
            <input type="text" class="chart-text" min="0"` + modelName + `="dictLevel" />
        </div>
      </div>`);
      chartBody.append(dictLevelContainer);

      //绑定容器
      this.bindContainer = $(`<div class="chart-bind"></div>`);
      chartBody.append(this.bindContainer);
      //刷新绑定面板
      this.refreshBindPanel(this.bindContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);

    }
  }
  refreshEvent(key, value) {
    if (key && key === "dictId") {
      if (value != "") {
        const model = this.context.getDictById(value);
        if (model) {
          const modelColumns = model.getModelColumns();
          // const modelParams = model.getModelParams();
          this.item["columns"] = modelColumns;
          // this.item["params"] = modelParams;
          this.refreshBindPanel(this.bindContainer);
        }
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "bindDict-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "绑定-字典"
  }
  /**
   * 
   * @returns 
   */
  getBtnTitle() {
    return "触发"
  }
}