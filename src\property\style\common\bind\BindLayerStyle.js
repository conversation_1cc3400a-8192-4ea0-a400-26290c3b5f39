import BindLayerSelectStyle from "./item/BindLayerSelectStyle";
import BindColumnStyle from "./item/BindColumnStyle";
import BindMappingStyle from "./item/BindMappingStyle";
import CardBtnStyle from "../../CardBtnStyle";
export default class BindLayerStyle extends CardBtnStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  refreshBindPanel (bindContainer, options) {
    const chartBody = bindContainer;
    const item = this.item;
    const callback = this.callback;
    if(chartBody){
      chartBody.empty();
      const bindMappingStyle = new BindMappingStyle(this.context);
      bindMappingStyle.initPanel(chartBody, item, callback);
      if (options) {
        bindMappingStyle.setOptions(options);
      }
      // const options=this.getColOptions(item.columns,true);
      // bindMappingStyle.setOptions(options);
      // const bindColumnStyle = new BindColumnStyle(this.context);
      // bindColumnStyle.initPanel(chartBody, item, callback);
    }
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // this.refreshModel(item);
      // this.bindModel(item, callback);
      const self=this;
      const bindLayerSelectStyle = new BindLayerSelectStyle(this.context);
      bindLayerSelectStyle.initPanel(chartBody, item, function(options){
        self.refreshBindPanel(self.bindContainer, options);
      });
      //绑定容器
      this.bindContainer = $(`<div class="chart-bind"></div>`);
      chartBody.append(this.bindContainer);
      //刷新绑定面板
      this.refreshBindPanel(this.bindContainer);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "bindLayer-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "绑定-图层"
  }
  /**
   * 
   * @returns 
   */
  getBtnTitle () {
    return "触发"
  }
}