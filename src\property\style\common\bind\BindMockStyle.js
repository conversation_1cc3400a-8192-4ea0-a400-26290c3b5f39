import CardStyle from "../../CardStyle";
import BindColumnStyle from "./item/BindColumnStyle";
export default class BindMockStyle extends CardStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // this.refreshModel(item);
      // this.bindModel(item, callback);
      const bindColumnStyle = new BindColumnStyle(this.context);
      bindColumnStyle.initPanel(chartBody, item, callback);
    }
  }
  
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "bindMock-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "绑定-模拟"
  }
}