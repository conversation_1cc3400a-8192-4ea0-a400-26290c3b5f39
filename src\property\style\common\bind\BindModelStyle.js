import BindColumnStyle from "./item/BindColumnStyle";
import BindMappingStyle from "./item/BindMappingStyle";
import BindParamsStyle from "./item/BindParamsStyle";
import CardBtnStyle from "../../CardBtnStyle";
export default class BindModelStyle extends CardBtnStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  setModels(models) {
    this.models = models;
  }
  refreshBindPanel(bindContainer) {
    const chartBody = bindContainer;
    const item = this.item;
    const callback = this.callback;

    if (chartBody) {
      chartBody.empty();

      // 模型字段表格
      const bindMappingStyle = new BindMappingStyle(this.context);
      bindMappingStyle.initPanel(chartBody, item, callback);
      bindMappingStyle.setOptions(item.columns);

      // 模型参数表格
      const bindParamsStyle = new BindParamsStyle(this.context);
      bindParamsStyle.initPanel(chartBody, item, callback);
    }

  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      const modelIdContainer = $(`<div class="chart-item flex">
        <div class="chart-label">选择模型</div>
        <div class="chart-control">
            <select class="chart-select" ` + modelName + `="modelId" ></select>
        </div>
      </div>`);
      chartBody.append(modelIdContainer);;
      const dataSelecter = $(modelIdContainer).find(".chart-select")
      this.refreshOption(dataSelecter, this.models);
      //绑定容器
      this.bindContainer = $(`<div class="chart-bind"></div>`);
      chartBody.append(this.bindContainer);
      //刷新绑定面板
      this.refreshBindPanel(this.bindContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);

    }
  }
  refreshEvent(key, value) {
    if (key && key === "modelId") {
      if (value != "") {
        const model = this.context.getDataById(value);
        if (model) {
          const modelColumns = model.getModelColumns();
          const modelParams = model.getModelParams();
          this.item["columns"] = modelColumns;
          this.item["params"] = modelParams;
          this.refreshBindPanel(this.bindContainer);
        }
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "bindMdoel-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "绑定-模型"
  }
  /**
   * 
   * @returns 
   */
  getBtnTitle() {
    return "触发"
  }
}