import BindMappingStyle from "./item/BindMappingStyle";
import CardBtnStyle from "../../CardBtnStyle";
export default class BindParamStyle extends CardBtnStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  setParams(params){
    this.params = params;
  }
  refreshBindPanel(bindContainer) {
    const chartBody = bindContainer;
    const item = this.item;
    const callback = this.callback;
    if (chartBody) {
      chartBody.empty();
      const bindMappingStyle = new BindMappingStyle(this.context);
      bindMappingStyle.initPanel(chartBody, item, callback);
      // bindMappingStyle.setOptions(this.params);
    }
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //绑定容器
      this.bindContainer = $(`<div class="chart-bind"></div>`);
      chartBody.append(this.bindContainer);
      //刷新绑定面板
      this.refreshBindPanel(this.bindContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);

    }
  }
  refreshEvent(key, value) {
    
  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "bindParam-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "绑定-参数"
  }
  /**
   * 
   * @returns 
   */
  getBtnTitle() {
    return "触发"
  }
}