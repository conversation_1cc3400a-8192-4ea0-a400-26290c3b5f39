import ChartStyle from "../../ChartStyle";
export default class BindRemarkStyle extends ChartStyle {
  constructor(context) {
    super(context);
  }
  refreshBody(container){
    if (container) {
      //contenteditable: 'false';/* 设置为可编辑，但用户不能编辑 */
      //user-select: 'none';/* 防止文本被选中 */
      const remarkContainer = $(`<div class='lsd-property-remark'>
        <p>1.绑定数据使用Json格式类型</p>
        <p>2.系统全局属性-#{属性名}表示系统自定义属性，如:token date 等</p>
        <p>3.系统全局参数-` + "${参数名称}" + `表示大屏可视化地址传入的参数</p>
        <p>4.API返回数据结构格式必须满足以下:</p>
        <p>  {msg:"消息",status:"0:成功,1:失败",data:[{"属性":"值"},{"属性":"值"}]}</p>
      </div>`);
      container.append(remarkContainer);
    }
  }
  refreshPanel (chartBody, modelName, item, callback) {

  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "remark-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "绑定-备注"
  }
}