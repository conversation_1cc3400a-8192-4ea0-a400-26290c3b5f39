import ChartStyle from "../../ChartStyle";
export default class BindStyle extends ChartStyle {
  constructor(context) {
    super(context);
  }
  setTypeCallback(typeCallback){
    this.typeCallback=typeCallback;
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //<option value="templ">绑定-模板</option>
      const bindTypeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">绑定方式</div>
        <div class="chart-control">
            <select class="chart-select" ` + modelName + `="bindType" >
                <option value="">--请选择--</option>
                <option value="data">绑定-数据</option>
                <option value="layer">绑定-图层</option>
                <option value="model">绑定-模型</option>
                <option value="dict">绑定-字典</option>
                <option value="mock">模板/模拟</option>
            </select>
        </div>
      </div>`);
      chartBody.append(bindTypeContainer);
      const refreshContainer = $(`<div class="chart-item flex">
          <div class="chart-label">刷新方式</div>
          <div class="chart-control">
              <select class="chart-select" ` + modelName + `="refreshType" >
                  <option value="">-请选择-</option>
                  <option value="1">定时刷新</option>
                  <option value="2">不刷新</option>
              </select>
          </div>
      </div>`);
      chartBody.append(refreshContainer);
      const refreshTimeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">刷新时间</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" placeholder="[毫秒]" ` + modelName + `="refreshTime" />
          </div>
      </div>`);
      chartBody.append(refreshTimeContainer);
      // const typeContainer = $(`<div class="chart-item flex">
      //     <div class="chart-label">刷新方式</div>
      //     <div class="chart-control">
      //         <select class="chart-select" ` + modelName + `="refreshType" >
      //             <option value="">--请选择--</option>
      //             <option value="1">定时刷新</option>
      //             <option value="2">不刷新</option>
      //         </select>
      //     </div>
      // </div>`);
      // chartBody.append(typeContainer);
      // const timeContainer = $(`<div class="chart-item flex">
      //     <div class="chart-label">刷新时间</div>
      //     <div class="chart-control">
      //         <input type="number" class="chart-number" min="0" placeholder="[毫秒]" ` + modelName + `="refreshTime" />
      //     </div>
      // </div>`);
      // chartBody.append(timeContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);

    }
  }
  refreshEvent(key, value) {
    if(key && key==="bindType" && value){
      if(this.typeCallback){
        this.typeCallback();
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "bind-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "绑定"
  }
}