import CardStyle from "../../../CardStyle";
export default class BindColumnStyle extends CardStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }

  /**
   * 重写父类方法，这里是为了覆盖card-body样式，不改变功能
   * @param container
   */
  initModel (container) {
    if (container) {
      const modelContainer = $(`<div class="comm-wrap card-wrap">
        <div class="comm-head card-head" >
          <div class="head-title">` + this.getTitle() + `</div>
          <div class="head-icon ft-font icon-shangla"></div>
        </div>
        <div class="comm-body card-body" style="padding: 0; margin: 0"></div>
      </div>`);
      container.append(modelContainer);
      this.chartHead = $(modelContainer).find(".card-head");
      this.chartBody = $(modelContainer).find(".card-body");

      this.chartTitle = $(modelContainer).find(".card-head .head-title");
      this.chartIcon = $(modelContainer).find(".card-head .head-icon");
      this.refreshHead();
      this.refreshOpen();
    }
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // this.refreshModel(item);
      // this.bindModel(item, callback);
      
      const name = item.name || "无";
      const tableContainer = $(`<table class="bind-table">
      <tr><td>模型名称</td><td>` + name + `</td></tr>
      <tr class="table-title"><td>序号</td><td>字段</td></tr>
      </table>`);
      chartBody.append(tableContainer);

      const columns = item.columns;
      if (columns && columns.length) {
        for (let i = 0; i < columns.length; i++) {
          const column = columns[i];
          const trContainer = $(`<tr><td>` + i + `</td><td>` + column + `</td></tr>`);
          tableContainer.append(trContainer);
        }
      }
      
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "bindColumn-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "字段"
  }
}