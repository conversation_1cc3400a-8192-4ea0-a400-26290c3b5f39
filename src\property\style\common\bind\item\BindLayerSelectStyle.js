import CardStyle from "../../../CardStyle";
export default class BindLayerSelectStyle extends CardStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  /**
   * 重写父类方法，这里是为了覆盖card-body样式，不改变功能
   * @param container
   */
  initModel (container) {
    if (container) {
      const modelContainer = $(`<div class="comm-wrap card-wrap">
        <div class="comm-head card-head" >
          <div class="head-title">` + this.getTitle() + `</div>
          <div class="head-icon ft-font icon-shangla"></div>
        </div>
        <div class="comm-body card-body" style="padding: 1px 0 3px 0; margin: 0"></div>
      </div>`);
      container.append(modelContainer);
      this.chartHead = $(modelContainer).find(".card-head");
      this.chartBody = $(modelContainer).find(".card-body");

      this.chartTitle = $(modelContainer).find(".card-head .head-title");
      this.chartIcon = $(modelContainer).find(".card-head .head-icon");
      this.refreshHead();
      this.refreshOpen();
    }
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // this.refreshModel(item);
      // this.bindModel(item, callback);

      const modelContainer = $(`
      <div class="bind-layer">
        <div class="bind-head chart-head" >
          <div class="chart-item">
            <div class="chart-label">选择图层</div>
            <div class="chart-control">
                <input type="text" class="chart-text" placeholder="请选择要绑定的图层" disabled>
            </div>
          </div>
        </div>
        <div class="bind-body chart-body" style="height: 200px;overflow-x: hidden;overflow-y: auto;"></div>
      </div>`);
      chartBody.append(modelContainer);

      this.textContainer = $(modelContainer).find(".bind-head .chart-text");
      const bodyContainer = $(modelContainer).find(".bind-body");
      const layers = this.context.getLayerTree();
      this.tree = layers;
      this.treeContainer = bodyContainer;
      if (layers && layers.length) {
        for (let i = 0; i < layers.length; i++) {
          const layer = layers[i];
          this.refreshNode(bodyContainer, layer);
        }
      }


    }
  }
  refreshNode (parentContainer, layer) {
    if (parentContainer && layer) {
      //节点
      const item = layer.property;
      const modelContainer = $(`<div class="comm-wrap tree-wrap" id="` + item.id + `">
      <div class="comm-head tree-head">
        <div class="head-icon fd-font" id="icon`+ item.id + `"></div>
        <div class="head-title">`+ item.label + `</div>
      </div>
      <div class="comm-body tree-body" id="body`+ item.id + `"></div>
      </div>`);
      parentContainer.append(modelContainer);
      const headContainer = $(modelContainer).find(".tree-head");
      const bodyContainer = $(modelContainer).find(".tree-body");
      const titleContainer = $(modelContainer).find(".tree-head .head-title");

      this.refreshLayerOpen(modelContainer, layer);
      this.refreshChecked(modelContainer, layer);
      //点击Open
      const self = this;
      $(headContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        layer.isOpen = !layer.isOpen;
        self.refreshLayerOpen(modelContainer, layer);
      });
      //点击Checked
      $(headContainer).on("click", function (event) {
        // 多选
        // layer.isChecked = !layer.isChecked;
        // self.refreshChecked(titleContainer, layer);
        // 单选
        self.cleanCheckedData(self.tree);
        self.cleanCheckedStyle(self.treeContainer);
        layer.isChecked = true;
        self.refreshChecked(titleContainer, layer);
      });

      //孩子 递归
      const childs = layer["childs"];
      if (childs && childs.length) {
        for (let i = 0; i < childs.length; i++) {
          const child = childs[i];
          this.refreshNode(bodyContainer, child);
        }
      }
    }
  }
  refreshLayerOpen (container, layer) {
    if (container && layer) {
      const childs = layer["childs"];
      if (childs && childs.length) {
        const item = layer.property;
        //改用Id 防止向下穿透
        const iconContainer = $(container).find("#icon" + item.id);
        const bodyContainer = $(container).find("#body" + item.id);
        if (layer.isOpen) {
          $(bodyContainer).show();
          $(iconContainer).removeClass("fd-add-select");
          $(iconContainer).addClass("fd-sami-select");
        } else {
          $(bodyContainer).hide();
          $(iconContainer).removeClass("fd-sami-select");
          $(iconContainer).addClass("fd-add-select");
        }
      }
    }
  }
  /**#################[选中]################# */
  cleanCheckedData (list) {
    // const list=this.tree;
    if (list && list.length) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        item["isChecked"] = false;
        const childs = item["childs"];
        this.cleanCheckedData(childs);
      }
    }
  }
  cleanCheckedStyle (container) {
    // const container=this.leftContainer;
    if (container) {
      const listContainer = $(container).find(".tree-selected");
      $(listContainer).each(function (index, element) {
        $(element).removeClass("tree-selected");
      });
    }
  }
  refreshChecked (container, layer) {
    if (container && layer) {
      if (layer.isChecked) {
        $(container).addClass("tree-selected");
        this.refreshSelect(layer);
      } else {
        //多选情况
        $(container).removeClass("tree-selected");
      }
    }
  }
  refreshSelect (layer) {
    const layerProperty = layer.property;
    const layerColumns = layer.getBindColumns();
    // const layerParams = layer.getBindParams();
    this.textContainer.val(layerProperty["label"]);
    this.item["layerId"] = layerProperty["id"];
    this.item["columns"] = layerColumns;
    // this.item["params"] = layerParams;
    const colOptions = layer.getColOptions(true);
    //触发(回调)
    if(this.callback){
      this.callback(colOptions);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "layer-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "图层"
  }
}