import PageTable from "../../../../../assets/element/jquery/table/PageTable";
import CardStyle from "../../../CardStyle";
export default class BindMappingStyle extends CardStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  setOptions(options){
    this.options=options;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const rowNumContainer = $(`<div class="chart-item flex">
      <div class="chart-label">行号</div>
      <div class="chart-control">
          <input type="number" class="chart-number"  min="0" ` + modelName + `="rowNum" />
        </div>
      </div>`);
      chartBody.append(rowNumContainer);

      //刷新/绑定
      this.refreshModel(item);
      this.bindModel(item, callback);

      //配置(映射)
      if (item.mappings && item.mappings.length) {
        //表格
        const table = new PageTable(true);
        table.initPanel(chartBody, {
          columns: [
            { key: "key", name: "键(映射)", type: "text", },
            { key: "desc", name: "名称", type: "text", },
            // { key: "value", name: "值(字段)", type: "text",},
            { key: "value", name: "值(字段)", type: "select", options: this.options },
          ],
        });
        table.refreshPanel(item.mappings);
      }

    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "bindMapping-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "映射"
  }
}