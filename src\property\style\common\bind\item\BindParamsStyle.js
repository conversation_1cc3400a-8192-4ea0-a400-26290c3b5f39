import PageTable from "../../../../../assets/element/jquery/table/PageTable";
import CardStyle from "../../../CardStyle";
export default class BindParamsStyle extends CardStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // this.refreshModel(item);
      // this.bindModel(item, callback);

      //配置(参数)
      if (item.params && item.params.length) {
        //表格
        const table = new PageTable(true);
        table.initPanel(chartBody, {
          columns: [
            { key: "name", name: "名称", type: "text", },
            { key: "type", name: "类型", type: "text", options: [{ key: "1", text: "字符串" }, { key: "2", text: "数字" }, { key: "3", text: "日期" }, { key: "4", text: "浮点" },] },
            { key: "defValue", name: "属性值|表达式", type: "input", },
          ],
        });
        table.refreshPanel(item.params);
      }

    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "bindParams-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "参数"
  }
}