import TreeStyle from "../../TreeStyle";
export default class CondFun extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  setOptions(options){
    this.options=options;
  }
  setParams(params){
    this.params=params
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const andorContainer = $(`<div class="chart-item flex">
        <div class="chart-label">条件</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="andor">
                  <option value="">--请选择--</option>
                  <option value="and">and</option>
                  <option value="or">or</option>
              </select>
          </div>
      </div>`);
      chartBody.append(andorContainer);

      const typeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" `+modelName+`= "type">
                  <option value="">--请选择--</option>
                  <option value="bind">数据绑定</option>
                  <option value="param">访问参数</option>
              </select>
          </div>
      </div>`);
      chartBody.append(typeContainer);
      
      const columnContainer = $(`<div class="chart-item flex">
        <div class="chart-label">属性</div>
          <div class="chart-control">
              <select class="chart-select"` + modelName + `="column"></select>
          </div>
      </div>`);
      chartBody.append(columnContainer);
      this.columnSelector = $(columnContainer).find(".chart-select");
      // this.refreshOption(columnSelector, this.options);


      const optContainer = $(`<div class="chart-item flex">
            <div class="chart-label">操作符</div>
            <div class="chart-control">
                <select class="chart-select" `+modelName+`= "opt">
                    <option value="">--请选择--</option>
                    <option value="More">大于</option>
                    <option value="MoreEqual">大于等于</option>
                    <option value="Less">小于</option>
                    <option value="LessEqual">小于等于</option>
                    <option value="Equal">等于</option>
                    <option value="NotEqual">不等于</option>
                    <option value="Start">开头</option>
                    <option value="End">结尾</option>
                    <option value="Like">包含</option>
                    <option value="NotLike">不包含</option>
                    <option value="In">存在</option>
                    <option value="NotIn">不存在</option>
                </select>
            </div>
      </div>`);
      chartBody.append(optContainer);

      const exprContainer = $(`<div class="chart-item flex">
        <div class="chart-label">表达式</div>
          <div class="chart-control">
              <input type="text" class="chart-text" min="0" max="" placeholder="表达式" ` + modelName + `="expr" />
          </div>
        </div>`);
      chartBody.append(exprContainer);


      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

    if(key && key === "type"){
      const columnSelector = this.columnSelector;
      if(value === "bind"){
        this.refreshOption(columnSelector, this.options);
      }else if(value === "param"){
        this.refreshOption(columnSelector, this.params);
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "condFun-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "函数"
  }
}