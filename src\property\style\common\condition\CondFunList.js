import CondFun from "./CondFun";
import TreeStyle from "../../TreeStyle";
export default class CondFunList extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  setOptions(options){
    this.options=options;
  }
  setParams(params){
    this.params=params
  }
  afterAddItem (itemContainer, item, index, callback) {
    const model = new CondFun(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("函数[" + (index + 1) + "]配置");
    model.setOptions(this.options);
    model.setParams(this.params);
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "函数列表"
  }
}
