import CondFunList from "./CondFunList";
import TreeStyle from "../../TreeStyle";
export default class Condition extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  addFunCallback(funCallback){
    this.funCallback = funCallback;
  }
  setOptions(options){
    this.options=options;
  }
  setParams(params){
    this.params=params
  }
  setRowNum(rowNum){
    this.rowNum = rowNum;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      //填充行号
      if(!item.rowNum){
        item.rowNum=this.rowNum || 0;
      }
      const rowNumContainer = $(`<div class="chart-item flex">
        <div class="chart-label">行号</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="行号" ` + modelName + `="rowNum" />
          </div>
        </div>`);
      chartBody.append(rowNumContainer);

      // 函数
      if(!item["funList"]){
        item["funList"]=[];
      }
      const condFunList=new CondFunList(this.context);
      condFunList.initPanel(chartBody,item["funList"],callback);
      condFunList.setOptions(this.options);
      condFunList.setParams(this.params);

      const typeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">展示</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="display">
                  <option value="">--请选择--</option>
                  <option value="show">显示</option>
                  <option value="hide">隐藏</option>
              </select>
          </div>
      </div>`);
      chartBody.append(typeContainer);


      this.refreshModel(item);
      this.bindModel(item, callback);
      //方法回调
      if(this.funCallback){
        this.funCallback(chartBody, item, callback);
      }
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "condition-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "条件"
  }
}