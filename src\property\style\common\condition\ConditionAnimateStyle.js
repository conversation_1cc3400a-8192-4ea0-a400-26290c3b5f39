import TreeStyle from "../../TreeStyle";
import {getDefaultAnimates} from "../../../../utils/StyleUtil";
export default class ConditionAnimateStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const isAnimateAndInfiniteContainer = $(`<div class="chart-item">
            <div class="w-50 pr5 flex">
                <span class="chart-span">是否开启</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isAnimate">
                    <div class="slider round"></div>
                </label>
            </div>
            <div class="w-50 pl5 flex">
                <span class="chart-span">是否循环</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isInfinite">
                    <div class="slider round"></div>
                </label>
            </div>
            
        </div>`);
      chartBody.append(isAnimateAndInfiniteContainer);

      //选择内置动画名称
      const nameContainer = $(`<div class="chart-item">
            <div class="chart-label">动画名称</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + `="name"></select>
            </div>
        </div>`);
      chartBody.append(nameContainer);
      const animateNameSelect = $(nameContainer).find(".chart-select");
      this.refreshOption(animateNameSelect);

      //动画时长 延时时间
      const childDelayAndDelayContainer = $(`<div class="chart-item">
            <div class="w-50 pr5 flex">
                <div class="chart-label">延迟</div>
                <div class="chart-control" >
                    <input type="number" class="chart-number" min="0" placeholder="毫秒" ` + modelName + `="delay" />
                </div>
            </div>
            <div class="w-50 pl5 flex">
                <div class="chart-label">延迟子图</div>
                <div class="chart-control">
                    <input type="number" class="chart-number" min="0" placeholder="毫秒" ` + modelName + `="childDelay" />
                </div>
            </div>
        </div>`);
      chartBody.append(childDelayAndDelayContainer);

      const timingFunctionAndDirectionContainer = $(`<div class="chart-item">
            <div class="w-50 pr5 flex" >
                <div class="chart-label">过渡函数</div>
                <div class="chart-control" >
                    <select class="chart-select" ` + modelName + `="timingFunction" >
                        <option value="linear">匀速</option>
                        <option value="ease">慢快慢</option>
                        <option value="ease-in">加速</option>
                        <option value="ease-out">减速</option>
                        <option value="ease-in-out">慢速</option>
                    </select>
                </div>
            </div>
            <div class="w-50 pl5 flex">
                <div class="chart-label">动画方向</div>
                <div class="chart-control">
                    <select class="chart-select" ` + modelName + `="direction" >
                        <option value="normal">默认</option>
                        <option value="reverse">反向</option>
                        <option value="alternate">奇正偶反</option>
                        <option value="alternate-reverse">奇反偶正</option>
                        <option value="inherit">父元素</option>
                    </select>
                </div>
            </div>
            
        </div>`);
      chartBody.append(timingFunctionAndDirectionContainer);

      const iterationCountAndDirectionContainer = $(`<div class="chart-item">
            <div class="w-50 pr5 flex" >
                <div class="chart-label">运行时长</div>
                <div class="chart-control">
                    <input type="number" class="chart-number" min="0" ` + modelName + `="duration" />
                </div>
            </div>
            <div class="w-50 pl5 flex">
                <div class="chart-label">循环次数</div>
                <div class="chart-control">
                    <input type="number" class="chart-number" min="0" ` + modelName + `="iterationCount" />
                </div>
            </div>
        </div>`);
      chartBody.append(iterationCountAndDirectionContainer);


      const fillModelContainer = $(`<div class="chart-item">
                <div class="chart-label">属性填充</div>
                <div class="chart-control flex-1">
                    <select class="chart-select" ` + modelName + `="fillMode" >
                        <option value=none>无</option>
                        <option value=forwards>动画完成</option>
                        <option value=backwards>动画开始</option>
                        <option value=both>前后填充</option>
                        <option value=initial>图层本身</option>
                        <option value=inherit>继承</option>
                    </select>
                </div>
        </div>`);
      chartBody.append(fillModelContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshOption(selectContainer){
    if(selectContainer){
      $(selectContainer).empty();
      selectContainer.prepend("<option value='-1'>---请选择---</option>");//添加第一个option值

      const myAnimationMap = this.context.getPublishedAnimations()
      if (myAnimationMap) {
        for(let key in myAnimationMap){
          const value = myAnimationMap[key];
          selectContainer.append("<option value='" + key + "'>" + value + "</option>");
        }
      }
      const dataMap=getDefaultAnimates();
      if(dataMap){
        for(let key in dataMap){
          const value=dataMap[key];
          selectContainer.append("<option value='" + key + "'>" + value + "</option>");
        }
      }
      // if (list && list.length) {
      //   for (let i = 0; i < list.length; i++) {
      //     const item = list[i];
      //     selectContainer.append("<option value='" + item.key + "'>" + item.text + "</option>");
      //   }
      // }
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "anim-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "动画"
  }
}