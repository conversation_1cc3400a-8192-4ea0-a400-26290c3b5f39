import Condition from "./Condition";
import TreeStyle from "../../TreeStyle";
export default class ConditionList extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  addFunCallback(funCallback){
    this.funCallback = funCallback;
  }
  setOptions(options){
    this.options=options;
  }
  setParams(params){
    this.params=params
  }
  setRowNum(rowNum){
    this.rowNum = rowNum;
  }
  //添加
  // addConditionStyle (chartBody, item, callback) {
  //   // console.error("条件属性样式面板addConditionItem方法");
  // }

  afterAddItem (itemContainer, item, index, callback) {
    const model = new Condition(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("条件[" + (index + 1) + "]配置");
    model.addFunCallback(this.funCallback);
    model.setOptions(this.options);
    model.setParams(this.params);
    model.setRowNum(this.rowNum);
    // if (model && model.chartBody) {
    //   if(this.addConditionStyle){
    //     this.addConditionStyle(model.chartBody, item, callback);
    //   }
    // }
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "条件列表"
  }
}
