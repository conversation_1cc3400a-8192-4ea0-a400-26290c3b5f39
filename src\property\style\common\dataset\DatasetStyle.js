import ChartStyle from "../../ChartStyle";
import PageTable from "../../../../assets/element/jquery/table/PageTable";
import FilterListStyle from "./filter/FilterListStyle";
export default class DatasetStyle extends ChartStyle {
  constructor(context) {
    super(context);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const isOpenContainer = $(`<div class="chart-item">
            <div class="w-50 pr5 flex">
                <span class="chart-span">是否开启</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isOpen">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
      chartBody.append(isOpenContainer);

      const datasetContainer = $(`<div class="chart-item flex">
        <div class="chart-label">数据集</div>
        <div class="chart-control">
            <select class="chart-select" ` + modelName + `="datasetId" >
            </select>
        </div>
      </div>`);
      chartBody.append(datasetContainer);
      // 数据集选择项
      this.refreshItem(datasetContainer, this.context.getDataSetPropertyList());


      // 当选择xlsx csv 文件时，显示数据表
      this.datasetSheetContainer = $(`<div class="chart-item flex">
        <div class="chart-label">数据表</div>
        <div class="chart-control">
            <select class="chart-select" ` + modelName + `="sheetId" >
            </select>
        </div>
      </div>`);
      chartBody.append(this.datasetSheetContainer);
      this.datasetSheetContainer.hide();

      // 数据行号
      const rowNumContainer = $(`<div class="chart-item flex">
      <div class="chart-label">行号</div>
      <div class="chart-control">
          <input type="number" class="chart-number"  min="0" ` + modelName + `="rowNum" />
        </div>
      </div>`);
      chartBody.append(rowNumContainer);

      // 表格映射绑定
      this.tableContainer = $(`<div></div>`);
      chartBody.append(this.tableContainer);

      // 筛选器容器
      this.filterContainer = $(`<div class="mt10"></div>`);
      chartBody.append(this.filterContainer);

      if (!item["filterRules"]) {
        item["filterRules"] = [];
      }
      // 筛选器样式
      const datasetFilterStyle = new FilterListStyle(this.context, false);
      datasetFilterStyle.initPanel(this.filterContainer, item["filterRules"], callback);
      datasetFilterStyle.setBind(item);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }

  /**
   * 刷新下拉框字段映射
   * @param container 容器
   * @param options 字段列表
   */
  refreshItem (container, options) {
    if (container) {
      const selectContainer = container.find('.chart-select');
      selectContainer.empty();
      selectContainer.append(`<option value="">--请选择--</option>`)
      if (options && options.length) {
        for (let i = 0; i < options.length; i++) {
          const  option = options[i];
          const optionEl = $(`<option value="${option.id}">${option.name}</option>`);
          selectContainer.append(optionEl);
        }
      }
    }
  }

  /**
   * 选择数据集时刷新下级
   */
  refreshDatasetBind () {
    this.refreshTable();
    if (this.item.datasetId) {
      const dataset = this.context.getDataSetById(this.item.datasetId);
      if (dataset) {
        this.datasetSheetContainer.hide();
        if (dataset.property.type === 'model') {
          this.refreshTable(dataset.property.columns);
        }
        if (dataset.property.type === 'file') {
          const file = dataset.property.file;
          if (file) {
            if (file.fileType === 'xlsx' || file.fileType === 'csv') {
              this.datasetSheetContainer.show();
              this.refreshItem(this.datasetSheetContainer, dataset.property.columns);
            }
            if (file.fileType === 'json') {
              this.refreshTable(dataset.property.columns);
            }
          }

        }
      }
    }
  }

  /**
   * 选择数据表时刷新下级
   * @param sheetId
   */
  refreshSheetBind (sheetId) {
    const dataset = this.context.getDataSetById(this.item.datasetId);
    if (dataset) {
      const data = dataset.datas.find(item => item.id === sheetId);
      if (data) {
        this.refreshTable(data.columns);
      }
    }
  }

  /**
   * 刷新表格
   * @param columns
   */
  refreshTable (columns) {
    this.tableContainer.empty();
    //配置(映射)
    if (columns && columns.length) {
      this.item.columns = columns;
      if (this.item.mappings && this.item.mappings.length) {
        //表格
        const table = new PageTable(true);
        table.initPanel(this.tableContainer, {
          columns: [
            { key: "key", name: "键(映射)", type: "text", },
            { key: "desc", name: "名称", type: "text", },
            { key: "value", name: "值(字段)", type: "select", options: columns },
          ],
        }, this.callback);
        table.refreshPanel(this.item.mappings);
      }
    }
  }

  refreshEvent(key, value) {
    if (key === "isOpen") {
      this.item.bindType = Boolean(value) ? 'dataset' : 'mock';
    } else if (key === 'datasetId' && value) {
      this.refreshDatasetBind();
    } else if (key === 'sheetId') {
      this.refreshSheetBind(value);
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "dataset-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "数据"
  }
}