import AbstractStyle from "../../../../../AbstractStyle";

export default class FilterAdvancedStyle extends AbstractStyle {
  constructor(context) {
    super(context);
  }

  getContainer () {
    return $(`<div class="filter-advanced-wrap"></div>`);
  }

  initModel (container) {
    if (container) {
      this.refreshBody(container);
    }
  }

  refreshPanel (chartBody, modelName, item, callback) {
    this.chartBody = chartBody;
    const selectContainer = $(`<div class="chart-item flex" style="margin-top: 0">
        <div class="chart-label">操作符</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="operator">
                    <option value="">--请选择--</option>
                    <option value="More">大于</option>
                    <option value="MoreEqual">大于等于</option>
                    <option value="Less">小于</option>
                    <option value="LessEqual">小于等于</option>
                    <option value="Equal">等于</option>
                    <option value="NotEqual">不等于</option>
                    <option value="Start">开头</option>
                    <option value="End">结尾</option>
                    <option value="Like">包含</option>
                    <option value="NotLike">不包含</option>
              </select>
          </div>
      </div>`);
    chartBody.append(selectContainer);
    const itemContainer = $(`<div class="chart-item flex">
        <div class="chart-label">值</div>
          <div class="chart-control">
              <input type="text" `+ modelName + `="value" />
          </div>
      </div>`);
    chartBody.append(itemContainer);

    this.hide();

    this.refreshModel(item);
    this.bindModel(item, callback);
  }

  show () {
    this.chartBody.show();
  }
  hide () {
    this.chartBody.hide();
  }
}