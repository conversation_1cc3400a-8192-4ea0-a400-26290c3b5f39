import AbstractStyle from "../../../../../AbstractStyle";

export default class FilterBasicStyle extends AbstractStyle {
  constructor(context) {
    super(context);
    // 选中的值
    this.selectedValues = new Set();
    this.dataOptions = [];
  }

  setDataOptions (dataOptions) {
    this.dataOptions = dataOptions;
    this.selectedValues = new Set();
    this.renderSelectedTags();
  }

  getContainer () {
    return $(`<div class="filter-basic-wrap"></div>`);
  }

  initModel (container) {
    if (container) {
      this.refreshBody(container);
    }
  }

  refreshPanel (chartBody, modelName, item, callback) {
    this.chartBody = chartBody;
    // 实现一个可以多选的下拉框
    this.selectContainer = $(`<div class="chart-item flex" style="margin-top: 0">
        <div class="chart-label">字段筛选</div>
          <div class="chart-control">
              <div class="chart-select dropdown-container">
                <div class="dropdown-header">
                  <input type="text" class="dropdown-search" placeholder="搜索字段数据">
                  <input type="hidden" `+ modelName + `="fieldValue"/>
                  <div class="selected-tags"></div>
                </div>
                <div class="dropdown-options"></div>
              </div>
          </div>
      </div>`);
    this.dropdownContainer = this.selectContainer.find('.dropdown-container');
    this.dropdownValueContainer = this.selectContainer.find('input[type=hidden]')

    const self = this;
    // 绑定搜索输入事件
    this.dropdownContainer.find(".dropdown-search").on("input", function () {
      const searchValue = $(this).val();
      self.renderOptions(searchValue);
    });
    // 点击其他区域关闭下拉框
    $(document).on("click", function (e) {
      if (!$(e.target).closest(".dropdown-container").length) {
        self.dropdownContainer.removeClass("active");
      }
    });
    // 阻止点击搜索框时关闭下拉框
    this.dropdownContainer.on("click", function (e) {
      e.stopPropagation();
      self.dropdownContainer.addClass('active')
    });

    // 初始化
    chartBody.append(this.selectContainer);
    this.hide();

    this.refreshModel(item);
    this.bindModel(item, callback);
  }

  show () {
    this.chartBody.show();
  }
  hide () {
    this.chartBody.hide();
  }

  // 渲染选项列表
  renderOptions(filter = "") {
    const dropdownOptions = this.dropdownContainer.find(".dropdown-options");
    dropdownOptions.empty();
    const filteredOptions = this.dataOptions.filter((option) =>
      option.includes(filter)
    );
    filteredOptions.forEach((option) => {
      const optionElement = $(`<div class="dropdown-option">${option}</div>`);
      if (this.selectedValues.has(option)) {
        optionElement.addClass("selected");
      }
      optionElement.on("click", () => this.toggleOption(option));
      dropdownOptions.append(optionElement);
    });
  }

  // 渲染选中的标签
  renderSelectedTags() {
    const selectedTagsContainer = this.dropdownContainer.find(".selected-tags");
    selectedTagsContainer.empty();
    this.selectedValues.forEach((value) => {
      const tag = $(`<span class="selected-tag">
                    <span class="tag-text">${value}</span>
                    <span class="remove-tag">×</span>
                </span>`);
      tag.find(".remove-tag").on("click", () => {
        this.selectedValues.delete(value);
        this.renderSelectedTags();
        this.renderOptions();
      });
      selectedTagsContainer.append(tag);
    });
  }

  // 切换选项状态
  toggleOption(option) {
    if (this.selectedValues.has(option)) {
      this.selectedValues.delete(option);
    } else {
      this.selectedValues.add(option);
    }
    const values = [];
    this.selectedValues.forEach(v => values.push(v));
    this.dropdownValueContainer.val(values.toString()).trigger("change");
    this.renderSelectedTags();
    this.renderOptions();
  }
}