import TreeStyle from "../../../TreeStyle";
import FilterStyle from "./FilterStyle";

export default class FilterListStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }

  setBind (bind) {
    this.bind = bind;
  }

  afterAddItem(itemContainer, item, index, callback) {
    const model = new FilterStyle(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("筛选条件[" + (index + 1) + "]配置");
    model.setBind(this.bind)
  }

  getTitle() {
    return '筛选器';
  }
}