import AbstractStyle from "../../../../../AbstractStyle";

export default class FilterRangeStyle extends AbstractStyle {
  constructor(context) {
    super(context);
  }

  getContainer () {
    return $(`<div class="filter-range-wrap"></div>`);
  }

  initModel (container) {
    if (container) {
      this.refreshBody(container);
    }
  }

  refreshPanel (chartBody, modelName, item, callback) {
    this.chartBody = chartBody;
    const selectContainer = $(`<div class="chart-item flex" style="margin-top: 0">
        <div class="chart-label">数据范围</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="range">
                  <option value="">--请选择--</option>
                  <option value="before">前</option>
                  <option value="after">后</option>
              </select>
          </div>
      </div>`);
    chartBody.append(selectContainer);
    const itemContainer = $(`<div class="chart-item flex">
        <div class="chart-label">项</div>
          <div class="chart-control">
              <input type="number" `+ modelName + `="num" />
          </div>
      </div>`);
    chartBody.append(itemContainer);

    this.hide();

    this.refreshModel(item);
    this.bindModel(item, callback);
  }

  show () {
    this.chartBody.show();
  }
  hide () {
    this.chartBody.hide();
  }
}