import TreeStyle from "../../../TreeStyle";
import FilterBasicStyle from "./FilterBasicStyle";
import FilterRangeStyle from "./FilterRangeStyle";
import FilterAdvancedStyle from "./FilterAdvancedStyle";

export default class FilterStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
    this.filterBasicStyle = new FilterBasicStyle(context);
    this.filterRangeStyle = new FilterRangeStyle(context);
    this.filterAdvancedStyle = new FilterAdvancedStyle(context);
  }
  setBind (bind) {
    this.bind = bind;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const fieldContainer = $(`<div class="chart-item flex">
        <div class="chart-label">字段</div>
          <div class="chart-control">
            <select class="chart-select" ` + modelName + `="field" ></select>
          </div>
        </div>`);
      chartBody.append(fieldContainer);
      this.refreshOption(fieldContainer.find('.chart-select'), this.bind.columns);

      const filterContainer = $(`<div class="chart-item flex">
        <div class="chart-label">筛选方式</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="filterType">
                  <option value="">--请选择--</option>
                  <option value="basic">基本筛选</option>
                  <option value="range">范围筛选</option>
                  <option value="advanced">高级筛选</option>
              </select>
          </div>
      </div>`);
      chartBody.append(filterContainer);

      this.filterBasicStyle.initPanel(chartBody, item, callback);
      this.filterRangeStyle.initPanel(chartBody, item, callback);
      this.filterAdvancedStyle.initPanel(chartBody, item, callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }



  refreshBasicFilterData () {
    console.log('asdf', this.bind)
    if (this.bind.bindType === 'dataset') {
      const dataset = this.context.getDataSetById(this.bind.datasetId);
      if (dataset.property.type === 'model') {
        const temps = [];
        dataset.datas.forEach(data => {
          temps.push(`${data[this.item.field]}`)
        })
        this.filterBasicStyle.setDataOptions(temps)
        this.filterBasicStyle.renderOptions()
      } else if (dataset.property.type === 'file' && dataset.property.file) {
        if (['xlsx', 'csv'].includes(dataset.property.file.fileType)) {
          const sheet = dataset.datas.find(d => d.id === this.bind.sheetId);
          if (sheet) {
            const temps = [];
            sheet.datas.forEach(data => {
              temps.push(`${data[this.item.field]}`)
            })
            this.filterBasicStyle.setDataOptions(temps)
            this.filterBasicStyle.renderOptions()
          }
        }
        if (dataset.property.file.fileType === 'json') {
          const temps = [];
          dataset.datas.forEach(data => {
            temps.push(`${data[this.item.field]}`)
          })
          this.filterBasicStyle.setDataOptions(temps)
          this.filterBasicStyle.renderOptions()
        }
      }
    }
    else if (this.bind.bindType === 'mock') {
      const temps = [];
      this.bind.mock && this.bind.mock.forEach(data => {
        temps.push(`${data[this.item.field]}`)
      })
      this.filterBasicStyle.setDataOptions(temps)
      this.filterBasicStyle.renderOptions()
    }
  }

  refreshFilterData () {
    switch (this.item.filterType) {
      case 'basic':
        this.filterBasicStyle.show();
        this.filterRangeStyle.hide();
        this.filterAdvancedStyle.hide();
        this.refreshBasicFilterData()
        break
      case 'range':
        this.filterRangeStyle.show();
        this.filterBasicStyle.hide();
        this.filterAdvancedStyle.hide();
        break
      case 'advanced':
        this.filterAdvancedStyle.show();
        this.filterRangeStyle.hide();
        this.filterBasicStyle.hide();
        break
      default:
        this.filterRangeStyle.hide();
        this.filterBasicStyle.hide();
        this.filterAdvancedStyle.hide();
        break
    }
  }

  refreshEvent (key, value) {
    if (key === 'field' || key === 'filterType') {
      this.refreshFilterData(value)
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "dataset-filter-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "筛选条件"
  }
}