import TreeStyle from "../../TreeStyle";
import InteractiveEventStyle from "./InteractiveEventStyle";
export default class InteractiveEventListStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  setBind (bind) {
    this.bind = bind;
  }
  setParams (params) {
    this.params = params;
  }
  afterAddItem(itemContainer, item, index, callback) {
    const interactiveEventStyle = new InteractiveEventStyle(this.context);
    interactiveEventStyle.setBind(this.bind);
    interactiveEventStyle.setParams(this.params);
    interactiveEventStyle.initPanel(itemContainer, item, callback);
    interactiveEventStyle.refreshId(index);
    interactiveEventStyle.refreshTitle("交互事件[" + (index + 1) + "]配置");
  }

  getTitle() {
    return '交互事件';
  }
}