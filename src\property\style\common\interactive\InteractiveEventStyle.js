import TreeStyle from "../../TreeStyle";
import InteractivePropertyListStyle from "./InteractivePropertyListStyle";

export default class InteractiveEventStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  setBind (bind) {
    this.bind = bind;
  }
  setParams (params) {
    this.params = params;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const nameContainer = $(`<div class="chart-item flex">
            <div class="chart-label">消息名称</div>
            <div class="chart-control">
                <input type="text" class="chart-text" ` + modelName + `="messageName" />
            </div>
          </div>`);
      chartBody.append(nameContainer);

      const triggerContainer = $(`<div class="chart-item flex">
        <div class="chart-label">触发事件</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="triggerEvent">
                  <option value="">--请选择--</option>
                  <option value="singleClick">鼠标单击</option>
                  <option value="doubleClick">鼠标双击</option>
                  <option value="mouseEnter">鼠标移入</option>
                  <option value="mouseLeave">鼠标移出</option>
              </select>
          </div>
      </div>`);
      chartBody.append(triggerContainer);

      const dataTypeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">数据类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="dataType">
                  <option value="">--请选择--</option>
                  <option value="dataBind">数据绑定</option>
                  <option value="paramBind">访问参数</option>
              </select>
          </div>
      </div>`);
      chartBody.append(dataTypeContainer);

      if (!item.properties) {
        item.properties = [];
      }
      this.interactivePropertyListStyle = new InteractivePropertyListStyle(this.context)
      this.interactivePropertyListStyle.setParams(this.params);
      this.interactivePropertyListStyle.setBind(this.bind);
      this.interactivePropertyListStyle.initPanel(chartBody, item.properties, callback)

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }

  refreshEvent (key, value) {
    if (key === "dataType") {
      this.interactivePropertyListStyle && this.interactivePropertyListStyle.setDataType(value)
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "interactive-event-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "交互事件"
  }
}