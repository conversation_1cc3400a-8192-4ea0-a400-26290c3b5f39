import TreeStyle from "../../TreeStyle";
import InteractivePropertyStyle from "./InteractivePropertyStyle";
export default class InteractivePropertyListStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  setBind (bind) {
    this.bind = bind;
  }
  setParams (params) {
    this.params = params;
  }
  setDataType (dataType) {
    this.dataType = dataType;
    this.refreshBody()
  }
  afterAddItem(itemContainer, item, index, callback) {
    const interactivePropertyStyle = new InteractivePropertyStyle(this.context);
    interactivePropertyStyle.setBind(this.bind);
    interactivePropertyStyle.setParams(this.params);
    interactivePropertyStyle.initPanel(itemContainer, item, callback);
    interactivePropertyStyle.setDataType(this.dataType);
    interactivePropertyStyle.refreshId(index);
    interactivePropertyStyle.refreshTitle("属性[" + (index + 1) + "]配置");
  }

  getTitle() {
    return '交互属性';
  }
}