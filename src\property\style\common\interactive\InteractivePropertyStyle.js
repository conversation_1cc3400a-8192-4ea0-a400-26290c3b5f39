import TreeStyle from "../../TreeStyle";

export default class InteractivePropertyStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  setBind (bind) {
    this.bind = bind;
  }
  setParams (params) {
    this.params = params;
  }
  setDataType (dataType) {
    this.dataType = dataType;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      this.propertyNameContainer = $(`<div class="chart-item flex">
        <div class="chart-label">属性名</div>
          <div class="chart-control">
            <input type="text" class="chart-text" placeholder="属性名" ` + modelName + `="key" />
          </div>
        </div>`);
      chartBody.append(this.propertyNameContainer);
      this.propertyValueContainer = $(`<div class="chart-item flex">
        <div class="chart-label">属性值</div>
          <div class="chart-control">
            <select class="chart-select" ` + modelName + `="value" ></select>
          </div>
        </div>`);
      chartBody.append(this.propertyValueContainer);
      this.propertyValueSelector = this.propertyValueContainer.find(".chart-select");

      switch (this.dataType) {
        case "dataBind":
          if (this.bind.mappings && this.bind.mappings.length) {
            const columns = this.bind.mappings.map((item) => item.key)
            this.refreshOption(this.propertyValueSelector, columns);
          } else {
            this.refreshOption(this.propertyValueSelector, this.bind.columns);
          }
          break
        case "paramBind":
          this.refreshOption(this.propertyValueSelector, this.params);
          break
        default:
          this.refreshOption(this.propertyValueSelector, []);
          break
      }

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "interactive-event-property-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "交互事件"
  }
}