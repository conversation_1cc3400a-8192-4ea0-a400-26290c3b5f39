import ChartStyle from "../../ChartStyle";
import InteractiveEventListStyle from "./InteractiveEventListStyle";
export default class InteractiveStyle extends ChartStyle {
  constructor(context) {
    super(context);
  }
  setBind (bind) {
    this.bind = bind;
  }
  setParams (params) {
    this.params = params;
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const isOpenContainer = $(`<div class="chart-item">
            <div class="w-50 pr5 flex">
                <span class="chart-span">是否开启</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isOpen">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
      chartBody.append(isOpenContainer);

      if (!item["events"]) {
        item["events"] = [];
      }
      // 筛选器样式
      const interactiveEventListStyle = new InteractiveEventListStyle(this.context, false);
      interactiveEventListStyle.setBind(this.bind);
      interactiveEventListStyle.setParams(this.params);
      interactiveEventListStyle.initPanel(chartBody, item["events"], callback);


      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }

  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "interactive-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "交互"
  }
}