import JumpTree from "./JumpTree";
import CardStyle from "../../CardStyle";
export default class JumpCard extends CardStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  setOptions (options) {
    this.options = options;
  }
  setParams (params) {
    this.params = params
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      const jumpTree = new JumpTree(this.context);
      jumpTree.initPanel(chartBody, item, callback);
      jumpTree.setOptions(this.options);
      jumpTree.setParams(this.params);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "jump-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "超链接"
  }
}