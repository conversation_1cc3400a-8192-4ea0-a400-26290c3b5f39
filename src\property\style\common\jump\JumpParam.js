import TreeStyle from "../../TreeStyle";
export default class JumpParam extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  setOptions (options) {
    this.options = options;
  }
  setParams (params) {
    this.params = params
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      const codeContainer = $(`<div class="chart-item">
          <div class="chart-label">编码</div>
          <div class="chart-control flex-1">
              <input type="text" class="chart-text"  placeholder="编码" `+ modelName + `="code"  />
          </div>
      </div>`);
      chartBody.append(codeContainer);

      const typeContainer = $(`<div class="chart-item">
          <div class="chart-label">类型</div>
          <div class="chart-control flex-1">
              <select class="chart-select" `+ modelName + `="type">
                  <option value="">--请选择--</option>
                  <option value="EXPR">表达式</option>
                  <option value="PARAM">参数</option>
                  <option value="LAYER">图层</option>
              </select>
          </div>
      </div>`);
      chartBody.append(typeContainer);

      this.valueContainer = $(`<div class="chart-item">
          <div class="chart-label">属性</div>
          <div class="chart-control flex-1">
              <select class="chart-select" `+ modelName + `="key"></select>
          </div>
      </div>`);
      chartBody.append(this.valueContainer);
      this.valueSelector = $(this.valueContainer).find(".chart-select");
      // this.refreshOption(valueSelector,this.options);

      this.exprContainer = $(`<div class="chart-item">
      <div class="chart-label">表达式</div>
      <div class="chart-control flex-1">
          <input type="text" class="chart-text"  readonly placeholder="\${code}" `+ modelName + `="expr"  />
      </div>
      </div>`);
      chartBody.append(this.exprContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    if (key && key === "type") {
      if (value) {
        if (value === "EXPR") {
          $(this.exprContainer).show();
          $(this.valueContainer).hide();
        } else if (value === "PARAM") {
          $(this.exprContainer).hide();
          $(this.valueContainer).show();
          this.refreshOption(this.valueSelector, this.params);

        } else if (value === "LAYER") {
          $(this.exprContainer).hide();
          $(this.valueContainer).show();
          this.refreshOption(this.valueSelector, this.options);
        } else {
          $(this.exprContainer).hide();
          $(this.valueContainer).hide();
        }
      } else {
        $(this.exprContainer).hide();
        $(this.valueContainer).hide();
      }

    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "param-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "参数"
  }
}