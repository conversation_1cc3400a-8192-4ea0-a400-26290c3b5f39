import TreeStyle from "../../TreeStyle";
import JumpParam from "./JumpParam";
export default class JumpParamList extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  setOptions(options){
    this.options=options;
  }
  setParams(params){
    this.params=params
  }
  afterAddItem (itemContainer, item, index, callback) {
    const model = new JumpParam(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("参数[" + (index + 1) + "]配置");
    model.setOptions(this.options);
    model.setParams(this.params);
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "参数列表"
  }
}
