import JumpParamList from "./JumpParamList";
import TreeStyle from "../../TreeStyle";
export default class JumpTree extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  setOptions (options) {
    this.options = options;
  }
  setParams (params) {
    this.params = params
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      const isShowAndTokenContainer = $(`<div class="chart-item">
            <div class="w-50 pr5 flex">
                <span class="chart-span">是否开启</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isOpen">
                    <div class="slider round"></div>
                </label>
            </div>
            <div class="w-50 pl5 flex">
                <span class="chart-span">是否TOKEN</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isToken">
                    <div class="slider round"></div>
                </label>
            </div>
            
        </div>`);
      chartBody.append(isShowAndTokenContainer);

      const urlContainer = $(`<div class="chart-item">
            <div class="chart-label">地址</div>
            <div class="chart-control flex-1">
                <input type="text" class="chart-text"  placeholder="地址" `+ modelName + `="url" />
            </div>
        </div>`);
      chartBody.append(urlContainer);

      const targetContainer = $(`<div class="chart-item">
            <div class="chart-label">目标</div>
            <div class="chart-control flex-1">
                <select class="chart-select" `+ modelName + `="target">
                    <option value="">--请选择--</option>
                    <option value="CURRENT">本页面</option>
                    <option value="WINDOW">新窗口</option>
                    <option value="DOWNLOAD">下载</option>
                </select>
            </div>
        </div>`);
      chartBody.append(targetContainer);
      //下载
      this.downloadContainer = $(`<div class="chart-download"></div>`);
      chartBody.append(this.downloadContainer);

      const methodContainer = $(`<div class="chart-item">
          <div class="chart-label">请求方式</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="download.method">
                  <option value="">--请选择--</option>
                  <option value="GET">GET</option>
                  <option value="POST">POST</option>
              </select>
          </div>
      </div>`);
      this.downloadContainer.append(methodContainer);

      const fileNameContainer = $(`<div class="chart-item">
          <div class="chart-label">文件名称</div>
          <div class="chart-control">
              <input type="text" class="chart-text"  placeholder="请输入导出|下载文件名称" `+ modelName + `="download.fileName" />
          </div>
      </div>`);
      this.downloadContainer.append(fileNameContainer);

      const fileExtContainer = $(`<div class="chart-item">
          <div class="chart-label">文件类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="download.fileExt">
                  <option value="">--请选择--</option>
                  <option value="jpg">jpg</option>
                  <option value="png">png</option>
                  <option value="gif">gif</option>
                  <option value="svg">svg</option>
                  <option value="pdf">pdf</option>
                  <option value="zip">zip</option>
                  <option value="txt">txt</option>
                  <option value="doc">doc</option>
                  <option value="docx">docx</option>
                  <option value="xlsx">xlsx</option>
                  <option value="xls">xls</option>
              </select>
          </div>
      </div>`);
      this.downloadContainer.append(fileExtContainer);

      if (!item["params"]) {
        item["params"] = [];
      }
      const paramList = new JumpParamList(this.context);
      paramList.initPanel(chartBody, item["params"], callback);
      paramList.setOptions(this.options);
      paramList.setParams(this.params);
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    if (key && key === "target") {
      if (value === "DOWNLOAD") {
        $(this.downloadContainer).show();
      } else {
        $(this.downloadContainer).hide();
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "jumpTree-model";
  }

  /**
* 描述:标题信息
* @returns {string}
*/
  getTitle () {
    return "超链接"
  }
}