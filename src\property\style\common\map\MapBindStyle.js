import BindDataDialog from "../../../../dialog/BindDataDialog";
import BindColumnStyle from "../bind/item/BindColumnStyle";
import CardBtnStyle from "../../CardBtnStyle";
export default class MapBindStyle extends CardBtnStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  addMapProp (map) {
    this.map = map;
  }
  refreshBindPanel (bindContainer) {
    const chartBody = bindContainer;
    const callback = this.callback;
    let item;
    if (this.map.level === "china") {
      item = this.map.chinaBind
    } else if (this.map.level === "province") {
      item = this.map.provinceBind
    } else if (this.map.level === "city") {
      item = this.map.cityBind
    }
    if (chartBody) {
      chartBody.empty();
      const bindColumnStyle = new BindColumnStyle(this.context);
      bindColumnStyle.initPanel(chartBody, item, callback);
    }
  }
  /**
   * 
   * @param {*} chartBody 
   * @param {*} modelName 
   * @param {*} item 
   * @param {*} callback 
   */
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // this.refreshModel(item);
      // this.bindModel(item, callback);
      //绑定容器
      this.bindContainer = $(`<div class="chart-bind"></div>`);
      chartBody.append(this.bindContainer);
      //刷新绑定面板
      this.refreshBindPanel(this.bindContainer);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "mapBind-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "地图绑定"
  }

  /**
   * 
   * @returns 
   */
  getBtnTitle () {
    return "绑定"
  }
  /**
   * 
   */
  refreshBtnEvent () {
    const self = this;
    // const bind=JSON.parse(JSON.stringify(self.item));
    const dialog = new BindDataDialog(self.context, self.item);
    dialog.open(function (result) {
      const map = self.map;
      if (map) {
        // self.refreshPanel(self.bodyContainer,null,result,self.callback);
        if (map["level"] === "china") {
          self.refreshMapBind(map, "chinaBind", result);
        }
        if (map["level"] === "province") {
          self.refreshMapBind(map, "provinceBind", result);
        }
        if (map["level"] === "city") {
          self.refreshMapBind(map, "cityBind", result);
        }
      }
      //刷新绑定
      self.refreshBindPanel(self.bindContainer);
      //触发(回调)
      if (self.callback) {
        self.callback();
      }
    });
  }

  refreshMapBind (map, name, result) {
    if (map && name && result) {
      if (!map[name]) {
        map[name] = { bindType: "data" };
      }
      for (let key in result) {
        map[name][key] = result[key];
      }
      map[name]["bindType"] = "data";
    }
  }
}