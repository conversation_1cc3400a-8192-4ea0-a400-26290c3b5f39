import MapBindStyle from "./MapBindStyle";
import CardStyle from "../../CardStyle";
export default class MapDrillStyle extends CardStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  addMapProp (map) {
    this.map = map;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      if (this.map.level == "china") {
        const isProvinceContainer = $(`<div class="chart-item flex">
        <div class="flex">
            <span class="chart-span">是否省钻取</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="isProvinceDrill">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
        chartBody.append(isProvinceContainer);
        //省分
        const pItemStyle = new MapBindStyle(this.context);
        pItemStyle.initPanel(chartBody, item, callback);
        pItemStyle.refreshTitle("省份");
        pItemStyle.addMapProp(this.map);

        const isCityContainer = $(`<div class="chart-item flex">
        <div class="flex">
            <span class="chart-span">是否市钻取</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="isCityDrill">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
        chartBody.append(isCityContainer);
        //市区
        const cItemStyle = new MapBindStyle(this.context);
        cItemStyle.initPanel(chartBody, item, callback);
        cItemStyle.refreshTitle("市区");
        cItemStyle.addMapProp(this.map);

      } else if (this.map.level == "province") {

        const isCityContainer = $(`<div class="chart-item flex">
        <div class="flex">
            <span class="chart-span">是否市钻取</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="isCityDrill">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
        chartBody.append(isCityContainer);
        //市区
        const cItemStyle = new MapBindStyle(this.context);
        cItemStyle.initPanel(chartBody, item, callback);
        cItemStyle.refreshTitle("市区");
        cItemStyle.addMapProp(this.map);
      }


      this.refreshModel(this.map);
      this.bindModel(this.map, callback);
    }
  }
  refreshOption (selectContainer) {

  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "mapDrill-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "钻取"
  }
}