import ChartStyle from "../../ChartStyle";
export default class MapSelectStyle extends ChartStyle {
  constructor(context) {
    super(context);
  }
  setChangeMapCallback (callback) {
    this.changeMapCallback = callback;
  }
  refreshData(dataMap){
    if(this.treeContainer && dataMap){
      this.treeMap=dataMap;
      this.refreshPanel(this.treeContainer,dataMap);
    }
  }
  refreshBody (container) {
    if (container) {
      container.empty();
      const modelContainer = $(`
      <div class="cust-bind-layer">
        <div class="bind-head chart-head" >
          <div class="chart-item flex">
             <div class="chart-label" >自定义地图</div>
             <label class="chart-switch">
                  <input type="checkbox" class="chart-checkbox">
                  <div class="slider round"></div>
             </label>
          </div>
          <div class="chart-item flex">
            <div class="chart-label" >选择区域</div>
            <div class="chart-control">
                <input type="text" class="chart-text" placeholder="请选择要绑定的区域" disabled>
            </div>
          </div>
        </div>
        <div class="bind-body chart-body" style="height: 200px;overflow-x: hidden;overflow-y: auto;"></div>
      </div>`);
      container.append(modelContainer);
      this.textContainer = $(modelContainer).find(".bind-head .chart-text");
      this.treeContainer = $(modelContainer).find(".bind-body");
      this.switchContainer = $(modelContainer).find(".chart-switch .chart-checkbox");
      this.switchContainer.prop('checked', !!this.item.isCustom);
      this.switchContainer.change((e) => {
        const value = e.target.checked;
        this.item.isCustom = value;
        this.changeMapCallback && this.changeMapCallback(value);
      })
    }
  }
  refreshPanel (bodyContainer,dataMap) {
    if (bodyContainer) {
      bodyContainer.empty();
      if(dataMap){
        for(let key in dataMap){
          const data=dataMap[key];
          this.refreshNode(bodyContainer, data);
        }
      }
    }
  }
  refreshNode (parentContainer, item) {
    if (parentContainer && item) {
      const modelContainer = $(`<div class="comm-wrap tree-wrap" id="` + item.id + `">
      <div class="comm-head tree-head">
        <div class="head-icon fd-font" id="icon`+ item.id + `"></div>
        <div class="head-title">`+ item.name + `</div>
      </div>
      <div class="comm-body tree-body" id="body`+ item.id + `"></div>
      </div>`);
      parentContainer.append(modelContainer);
      const headContainer = $(modelContainer).find(".tree-head");
      const bodyContainer = $(modelContainer).find(".tree-body");
      const iconContainer = $(modelContainer).find(".tree-head .head-icon");
      const titleContainer = $(modelContainer).find(".tree-head .head-title");

      this.refreshOpen(modelContainer, item);
      this.refreshChecked(modelContainer, item);
      //点击
      const self = this;
      $(iconContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        item.isOpen = !item.isOpen;
        self.refreshOpen(modelContainer, item);
      });
      $(titleContainer).on("click", function (event) {
        event.preventDefault(); // 阻止
        // 多选
        // layer.isChecked = !layer.isChecked;
        // self.refreshChecked(titleContainer, layer);
        // 单选
        self.cleanCheckedData(self.treeMap);
        self.cleanCheckedStyle(self.treeContainer);
        item.isChecked = true;
        self.refreshChecked(titleContainer, item);
      });

      //孩子 递归
      const childMap = item["childs"];
      for(let key in childMap){
        const child = childMap[key];
        this.refreshNode(bodyContainer, child);
      }
    }
  }
  refreshOpen (container, item) {
    if (container && item) {
      const childMap = item["childs"];
      if (childMap) {
        //改用Id 防止向下穿透
        const iconContainer = $(container).find("#icon" + item.id);
        const bodyContainer = $(container).find("#body" + item.id);
        if (item.isOpen) {
          $(bodyContainer).show();
          $(iconContainer).removeClass("fd-add-select");
          $(iconContainer).addClass("fd-sami-select");
        } else {
          $(bodyContainer).hide();
          $(iconContainer).removeClass("fd-sami-select");
          $(iconContainer).addClass("fd-add-select");
        }
      }
    }
  }
  /**#################[选中]################# */
  cleanCheckedData (map) {
    if (map) {
      for(let key in map){
        const item = map[key];
        item["isChecked"] = false;
        const childs = item["childs"];
        this.cleanCheckedData(childs);
      }
    }
  }
  cleanCheckedStyle (container) {
    // const container=this.leftContainer;
    if (container) {
      const listContainer = $(container).find(".tree-selected");
      $(listContainer).each(function (index, element) {
        $(element).removeClass("tree-selected");
      });
    }
  }
  refreshChecked (container, item) {
    if (container && item) {
      if (item.isChecked) {
        $(container).addClass("tree-selected");
        this.refreshSelect(item);
      } else {
        //多选情况
        $(container).removeClass("tree-selected");
      }
    }
  }

  refreshSelect (item) {
    if(item){
      $(this.textContainer).val(item["name"]);
      //填充属性
      if(this.item){
        for(let key in item){
          if(key==="id"){
            this.item[key]=item[key];
            this.item["code"]=item[key];
            this.item["initCode"]=item[key];
          }
          if(key==="name"){
            this.item[key]=item[key];
          }
          if(key==="level"){
            this.item[key]=item[key];
          }
          if(key==="parentId"){
            this.item[key]=item[key];
          }
        }
        //响应
        if(this.callback){
          this.callback();
        }
      }
    }
  }
  
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "mapSelect-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "地图选择"
  }
}