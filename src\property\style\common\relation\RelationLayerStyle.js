import RelationLayerList from "./item/RelationLayerList";
import CardStyle from "../../CardStyle";
export default class RelationLayerStyle extends CardStyle {
  constructor(context,isOpen) {
    super(context,isOpen);
  }
  setLayers (layers) {
    this.layers = layers;
  }
  setOptions (options) {
    this.options = options;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      //图层// layers  targets
      if (!item["targets"]) {
        item["targets"] = [];
      }
      const layerList = new RelationLayerList(this.context);
      layerList.initPanel(chartBody, item["targets"], callback);
      layerList.setLayers(this.layers);
      layerList.setOptions(this.options);

      this.refreshModel(item);
      this.bindModel(item, callback);

    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "relation-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "图层联动"
  }
}