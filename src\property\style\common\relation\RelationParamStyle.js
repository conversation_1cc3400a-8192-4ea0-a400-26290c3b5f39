import RelationParamList from "./item/RelationParamList";
import CardStyle from "../../CardStyle";
export default class RelationParamStyle extends CardStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  setOptions (options) {
    this.options = options;
  }
  setParams (params) {
    this.params = params;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //参数
      if (!item["params"]) {
        item["params"] = [];
      }
      const paramList = new RelationParamList(this.context);
      paramList.initPanel(chartBody, item["params"], callback);
      paramList.setOptions(this.options);
      paramList.setParams(this.params);

      this.refreshModel(item);
      this.bindModel(item, callback);

    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "relation-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "参数联动"
  }
}