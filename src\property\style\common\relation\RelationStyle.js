import ChartStyle from "../../ChartStyle";
export default class RelationStyle extends ChartStyle {
  constructor(context) {
    super(context);
  }
  setTypeCallback(typeCallback){
    this.typeCallback=typeCallback;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const isShowAndTokenContainer = $(`<div class="chart-item flex">
          <div class="w-35 flex">
              <span class="chart-span">是否开启</span>
              <label class="chart-switch">
                  <input type="checkbox" class="chart-checkbox" `+ modelName + `="isOpen">
                  <div class="slider round"></div>
              </label>
          </div>
          <div class="w-65 flex">
              <div class="chart-label">联动类型</div>
              <div class="chart-control">
                <select class="chart-select" `+ modelName + `="linkage">
                    <option value="">--请选择--</option>
                    <option value="param">参数联动</option>
                    <option value="layer">图层联动</option>
                </select>
              </div>
              
          </div>
        </div>`);
      chartBody.append(isShowAndTokenContainer);

      const refreshTypeContainer = $(`<div class="chart-item flex">
          <div class="w-35 flex">
              <span class="chart-span">是否延时</span>
              <label class="chart-switch">
                  <input type="checkbox" class="chart-checkbox" `+ modelName + `="interval.isInterval">
                  <div class="slider round"></div>
              </label>
          </div>
          <div class="w-65 flex">
              <div class="chart-label">延时时间</div>
              <div class="chart-control">
                  <input type="number" class="chart-number" min="0" placeholder="[毫秒]" ` + modelName + `="interval.time" />
              </div>
          </div>
      </div>`);
      chartBody.append(refreshTypeContainer);

      

      // const refreshTimeContainer = $(`<div class="chart-item flex">
      //   <div class="chart-label">延时时间</div>
      //   <div class="chart-control">
      //       <input type="number" class="chart-number" min="0" placeholder="[毫秒]" ` + modelName + `="refreshTime" />
      //   </div>
      // </div>`);
      // chartBody.append(refreshTimeContainer);
      // const typeContainer = $(`<div class="chart-item flex">
      //   <div class="chart-label">联动类型</div>
      //   <div class="chart-control">
      //     <select class="chart-select" `+ modelName + `="type">
      //         <option value="">--请选择--</option>
      //         <option value="param">参数</option>
      //         <option value="layer">图层</option>
      //     </select>
      //   </div>
      // </div>`);
      // chartBody.append(typeContainer);


      this.refreshModel(item);
      this.bindModel(item, callback);

    }
  }
  refreshEvent (key, value) {
    if(key && key==="linkage" && value){
      if(this.typeCallback){
        this.typeCallback();
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "relation-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "联动"
  }
}