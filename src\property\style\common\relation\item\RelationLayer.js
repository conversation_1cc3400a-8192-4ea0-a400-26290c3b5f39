import RelationParamList from "./RelationParamList";
import TreeStyle from "../../../TreeStyle";
export default class RelationLayer extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  setLayers (layers) {
    this.layers = layers;
  }
  setOptions (options) {
    this.options = options;
  }
  // setParams (params) {
  //   this.params = params;
  // }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //是否开启联动
      const statusContainer = $(`<div class="chart-item flex">
          <div class="chart-label">联动状态</div>
          <div class="chart-control">
              <select class="chart-select" ` + modelName + `="status">
                  <option value="0">关闭</option>
                  <option value="1">开启</option>
              </select>
          </div>
      </div>`);
      chartBody.append(statusContainer);

      //联动图层
      const layerContainer = $(`<div class="chart-item flex">
          <div class="chart-label">联动图层</div>
          <div class="chart-control">
              <select class="chart-select"` + modelName + `="layerId"></select>
          </div>
      </div>`);
      chartBody.append(layerContainer);
      const layerSelector = layerContainer.find(".chart-select");
      this.refreshOption(layerSelector, this.layers);

      this.paramContainer = $(`<div class="params-body"></div>`);
      chartBody.append(this.paramContainer);
      this.refreshParam();

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshParam (layerId) {
    const chartBody = this.paramContainer;
    const item = this.item;
    const callback = this.callback;

    if (chartBody && item) {
      $(chartBody).empty();

      if (!item["params"]) {
        item["params"] = [];
      }
      const paramList = new RelationParamList(this.context);
      paramList.initPanel(chartBody, item["params"], callback);
      paramList.setOptions(this.options);
      // paramList.setParams(this.params);
      if(layerId){
        const layer = this.context.getLayerById(layerId);
        if (layer) {
          const params = layer.getParamOptions();
          paramList.setParams(params);
        }
      }
    }
  }
  refreshEvent (key, value) {
    if (key && key === "layerId") {
      if(value){
        this.refreshParam(value);
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "layer-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "联动图层"
  }
}