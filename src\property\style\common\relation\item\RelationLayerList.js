import RelationLayer from "./RelationLayer";
import TreeStyle from "../../../TreeStyle";
export default class RelationLayerList extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  setLayers (layers) {
    this.layers = layers;
  }
  setOptions (options) {
    this.options = options;
  }
  afterAddItem (itemContainer, item, index, callback) {
    const model = new RelationLayer(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("联动[" + (index + 1) + "]配置");
    model.setLayers(this.layers);
    model.setOptions(this.options);
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "联动列表"
  }
}
