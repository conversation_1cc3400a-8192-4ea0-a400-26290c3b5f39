import TreeStyle from "../../../TreeStyle";
export default class RelationParam extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  setOptions(options){
    this.options=options;
  }
  setParams(params){
    this.params=params;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //属性
      const paramContainer = $(`<div class="chart-item flex">
          <div class="chart-label">参数</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="code"></select>
          </div>
      </div>`);
      chartBody.append(paramContainer);
      const paramSelector = $(paramContainer).find(".chart-select");
      this.refreshOption(paramSelector,this.params);

      const columnContainer = $(`<div class="chart-item flex">
          <div class="chart-label">属性</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="value"></select>
          </div>
      </div>`);
      chartBody.append(columnContainer);
      const columnSelector = $(columnContainer).find(".chart-select");
      this.refreshOption(columnSelector,this.options);

      const valueContainer = $(`<div class="chart-item flex">
          <div class="chart-label">常量</div>
          <div class="chart-control">
              <input type="text" class="chart-text"  placeholder="请输入常量" `+ modelName + `="constant"  />
          </div>
      </div>`);
      chartBody.append(valueContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "param-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "联动参数"
  }
}