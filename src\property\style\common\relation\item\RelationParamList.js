import RelationParam from "./RelationParam";
import TreeStyle from "../../../TreeStyle";
export default class RelationParamList extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  setOptions(options){
    this.options=options;
  }
  setParams(params){
    this.params=params;
  }
  afterAddItem (itemContainer, item, index, callback) {
    const model = new RelationParam(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("联动[" + (index + 1) + "]配置");
    model.setOptions(this.options);
    model.setParams(this.params);
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "联动列表"
  }
}
