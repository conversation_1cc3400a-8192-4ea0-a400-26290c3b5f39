import BorderStyle from "../style/BorderStyle";
import TreeStyle from "../../TreeStyle";
export default class BdStyle extends TreeStyle{
    constructor(context, isOpen) {
        super(context, false, isOpen);
        }

refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        if(!item["border"]){
          item["border"]={};
        }
        const borderStyle = new BorderStyle(this.context);
        borderStyle.initPanel(chartBody,item["border"],callback);
        
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
}
    refreshEvent(key, value) {

    }
    /**
     *
     * @returns {string}
     */
    getModelName() {
        return "bd-model";
    }

     /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "边框"
  }
}