import BorderStyle from "../style/BorderStyle";
import FontStyle from "../style/FontStyle";
import BgStyle from "../style/BgStyle";
import DateFormatStyle from "../style/DateFormatStyle";
import TreeStyle from "../../TreeStyle";
export default class TextStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //初始化文本样式-是否超长隐藏
      const isLengthHiddenContainer = $(
        `<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否超长隐藏</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" ` +
          modelName +
          `="isLengthHidden">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`
      );
      chartBody.append(isLengthHiddenContainer);
      const isScrollContainer = $(
        `<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否自动滚动</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" ` +
          modelName +
          `="isScroll">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`
      );
      chartBody.append(isScrollContainer);

      // //初始化文本内容
      // const textContainer = $(
      //   `<div class="chart-item flex">
      //       <div class="chart-label">文本</div>
      //       <div class="chart-control">
      //           <input type="text" class="chart-text" placeholder="文本" ` +
      //     modelName +
      //     `="text" />
      //       </div>
      //   </div>`
      // );
      // chartBody.append(textContainer);

      if (!item["font"]) {
        item["font"] = {};
      }
      const fontStyle = new FontStyle(this.context);
      fontStyle.initPanel(chartBody, item["font"], callback);

      if (!item["border"]) {
        item["border"] = {};
      }
      const borderStyle = new BorderStyle(this.context);
      borderStyle.initPanel(chartBody, item["border"], callback);

      if (!item["bg"]) {
        item["bg"] = {};
      }
      const bgStyle = new BgStyle(this.context);
      bgStyle.initPanel(chartBody, item["bg"], callback);

      if (!item["format"]) {
        item["format"] = {};
      }
      const formatStyle = new DateFormatStyle(this.context);
      formatStyle.initPanel(chartBody, item["format"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {}
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "textStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "文本样式";
  }
}
