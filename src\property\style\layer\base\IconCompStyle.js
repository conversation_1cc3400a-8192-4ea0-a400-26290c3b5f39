import IconOptionsStyle from "../style/IconStyle";
import BorderStyle from "../style/BorderStyle";
import FontStyle from "../style/FontStyle";
import TreeStyle from "../../TreeStyle";
export default class IconCompStyle extends TreeStyle{
    constructor(context, isOpen) {
        super(context, false, isOpen);
        }

refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //图标
      if (!item["icon"]) {
        item["icon"] = {};
      }
      const iconOptionsStyle = new IconOptionsStyle(this.context);
      iconOptionsStyle.initPanel(chartBody, item["icon"], callback);
      //字体
      if(!item["font"]){
        item["font"]={};
      }
      const fontStyle = new FontStyle(this.context);
      fontStyle.initPanel(chartBody, item["font"], callback);
      //边框
      if(!item["border"]){
        item["border"]={};
      }
      const borderStyle = new BorderStyle(this.context);
      borderStyle.initPanel(chartBody, item["border"], callback);
        
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
}
    refreshEvent(key, value) {

    }
    /**
     *
     * @returns {string}
     */
    getModelName() {
        return "IconComp-model";
    }

     /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "图标"
  }
}