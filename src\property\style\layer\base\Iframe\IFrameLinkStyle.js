
import IFrameParamList from "./IFrameParamList";
import TreeStyle from "../../../TreeStyle";
export default class IFrameLinkStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        const isTokenContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否TOKEN</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isToken">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(isTokenContainer);

        const urlContainer = $(`<div class="chart-item flex">
            <div class="chart-label">地址</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  placeholder="地址" ` + modelName + `="url" />
            </div>
        </div>`);
        chartBody.append(urlContainer);

        if (!item["params"]) {
            item["params"] = [];
          }
        const iFrameParamList = new IFrameParamList(this.context);
        iFrameParamList.initPanel(chartBody,item["params"],callback);
      
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "iFrameLinkStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "链接"
  }
}