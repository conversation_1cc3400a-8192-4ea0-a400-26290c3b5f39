import TreeStyle from "../../../TreeStyle";
export default class IFrameParam extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        const iFrameParamKeyContainer=$(`<div class="chart-item flex">
            <div class="chart-label">编码</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  placeholder="参数编码" `+modelName+`="code"  />
            </div>
        </div>`);
        chartBody.append(iFrameParamKeyContainer);

        const iFrameParamValueSelectorContainer=$(`<div class="chart-item flex">
            <div class="chart-label">属性</div>
            <div class="chart-control">
                <select class="chart-select" `+modelName+`="value"></select>
            </div>
        </div>`);
        chartBody.append(iFrameParamValueSelectorContainer);
        const valueSelector = iFrameParamValueSelectorContainer.find(".chart-select");
        let valueList;
        const paramMap = this.context.getParamMap();
        for (let key in paramMap ) {
          if (!valueList) {
            valueList = [];
          }
          const param = paramMap[key];
          const property = param["property"];
          const code = property["code"];
          const paramItem = { key: code, text: code };
          valueList.push(paramItem);
        }
        this.refreshOption(valueSelector, valueList);

        const iFrameParamConstantContainer=$(`<div class="chart-item flex">
            <div class="chart-label">常量</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  placeholder="请输入常量" `+modelName+`="constant"  />
            </div>
        </div>`);
        chartBody.append(iFrameParamConstantContainer);
      
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }

  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "iFrameParam-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "参数"
  }
}