import IFrameParam from "./IFrameParam";
import TreeStyle from "../../../TreeStyle";
export default class IFrameParamList extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  afterAddItem (itemContainer, item, index, callback) {
    const model = new IFrameParam(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("参数[" + (index + 1) + "]配置");
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "参数列表"
  }
}