import IFrameLinkStyle from "./IFrameLinkStyle";
import TreeStyle from "../../../TreeStyle";
export default class IframeStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        // //宽度
        // const widthContainer = $(`<div class="chart-item flex">
        //     <div class="chart-label">宽度</div>
        //     <div class="chart-control">
        //         <input type="text" class="chart-text" placeholder="5px 5px 5px 5px" ` + modelName + `="width" />
        //     </div>
        // </div>`);
        // chartBody.append(widthContainer);
        // //高度
        // const heightContainer = $(`<div class="chart-item flex">
        // <div class="chart-label">高度</div>
        //     <div class="chart-control">
        //         <input type="text" class="chart-text" placeholder="5px 5px 5px 5px" ` + modelName + `="height" />
        //     </div>
        // </div>`);
        // chartBody.append(heightContainer);
        //上下 左右边距
        const marginWidthContainer = $(`<div class="chart-item flex">
            <div class="chart-label">左右边距</div>
            <div class="chart-control">
                <input type="text" class="chart-text" `+modelName+`="marginwidth"  />
            </div>
        </div>`);
        chartBody.append(marginWidthContainer);

        const marginHeightContainer = $(`<div class="chart-item flex">
            <div class="chart-label">上下边距</div>
            <div class="chart-control">
                <input type="text" class="chart-text" `+modelName+`="marginheight"  />
            </div>
        </div>`);
        chartBody.append(marginHeightContainer);

        const scrollingContainer=$(`<div class="chart-item flex">
            <div class="chart-label">滚动设置</div>
            <div class="chart-control">
                <select class="chart-select"`+modelName+`="scrolling" >
                    <option value="yes">滚动</option>
                    <option value="no">不滚动</option>
                    <option value="auth">自动</option>
                </select>
            </div>
        </div>`);
        chartBody.append(scrollingContainer);

        const frameborderContainer=$(`<div class="chart-item flex">
            <div class="chart-label">边框宽度</div>
            <div class="chart-control">
                <input type="number" class="chart-number" `+modelName+`="frameborder"  />
            </div>
        </div>`);
        chartBody.append(frameborderContainer);

        if(!item["link"]){
            item["link"]={};
        }
        const iFrameLinkStyle=new IFrameLinkStyle(this.context);
        iFrameLinkStyle.initPanel(chartBody,item["link"],callback);
      
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "iframeStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "框架属性"
  }
}