import BgStyle from "../style/BgStyle";
import TreeStyle from "../../TreeStyle";
export default class ImageCompStyle extends TreeStyle{
    constructor(context, isOpen) {
        super(context, false, isOpen);
        }

refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        if(!item["bg"]){
          item["bg"]={};
        }
        const bgStyle = new BgStyle(this.context);
        bgStyle.initPanel(chartBody,item["bg"],callback);
        
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
}
    refreshEvent(key, value) {

    }
    /**
     *
     * @returns {string}
     */
    getModelName() {
        return "imageComp-model";
    }

     /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "图片"
  }
}