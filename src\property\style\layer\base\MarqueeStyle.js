import Marquee from "../style/Marquee";
import FontStyle from "../style/FontStyle";
import BorderStyle from "../style/BorderStyle";
import TreeStyle from "../../TreeStyle";
export default class MarqueeStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        ///跑马灯
      if (!item["marquee"]) {
        item["marquee"] = {};
      }
      const marquee = new Marquee(this.context);
      marquee.initPanel(chartBody, item["marquee"], callback);
      //字体
      if (!item["font"]) {
        item["font"] = {};
      }
      const fontStyle = new FontStyle(this.context);
      fontStyle.initPanel(chartBody, item["font"], callback);
      //边框
      if (!item["border"]) {
        item["border"] = {};
      }
      const borderStyle = new BorderStyle(this.context);
      borderStyle.initPanel(chartBody, item["border"], callback);

        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "marqueeStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "跑马灯"
  }
}