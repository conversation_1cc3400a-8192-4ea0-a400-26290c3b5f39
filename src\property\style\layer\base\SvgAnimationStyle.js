import TreeStyle from "../../TreeStyle";
import SvgAnimationSelectorStyle from "../style/SvgAnimationSelectorStyle";

export default class SvgAnimationStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      const svgAnimationStyle = new SvgAnimationSelectorStyle(this.context);
      svgAnimationStyle.initPanel(chartBody, item, callback);

      // 速度
      const speedContainer = $(`<div class="chart-item">
          <div class="chart-label">速度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="1" max="" placeholder="动画速度" ` + modelName + `="speed" />
          </div>
      </div>`);
      chartBody.append(speedContainer);

      // 循环播放
      const isLoopContainer = $(`<div class="chart-item">
          <div class="w-50 flex">
              <span class="chart-span">是否循环播放</span>
              <label class="chart-switch">
                  <input type="checkbox" class="chart-checkbox" `+ modelName + `="loop">
                  <div class="slider round"></div>
              </label>
          </div>
      </div>`);
      chartBody.append(isLoopContainer);

      // 自动播放
      const isAutoPlayContainer = $(`<div class="chart-item">
          <div class="w-50 flex">
              <span class="chart-span">是否自动播放</span>
              <label class="chart-switch">
                  <input type="checkbox" class="chart-checkbox" `+ modelName + `="autoplay">
                  <div class="slider round"></div>
              </label>
          </div>
      </div>`);
      chartBody.append(isAutoPlayContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }

  refreshEvent(key, value) {

  }

  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "svg-animation";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "SVG动画"
  }
}