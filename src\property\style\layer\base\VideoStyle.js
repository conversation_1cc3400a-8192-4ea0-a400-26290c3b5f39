import TreeStyle from "../../TreeStyle";
export default class VideoStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
       //是否开启空间 是否自定播放
       const controlsContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">开启控件</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="controls">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
       chartBody.append(controlsContainer);

       const autoplayContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否自动播放</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="autoplay">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
       chartBody.append(autoplayContainer);

        //是否循环播放 是否静音
        const loopContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否循环</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="loop">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
       chartBody.append(loopContainer);

       const mutedContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否静音</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="muted">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
       chartBody.append(mutedContainer);

        // const widthContainer = $(`<div class="chart-item flex">
        //     <div class="chart-label">宽度</div>
        //     <div class="chart-control">
        //         <input type="text" class="chart-text" placeholder="视频的宽度" ` + modelName + `="width"  />
        //     </div>
        // </div>`);
        // chartBody.append(widthContainer);

        // const heightContainer = $(`<div class="chart-item flex">
        //     <div class="chart-label">高度</div>
        //     <div class="chart-control">
        //          <input type="text" class="chart-text" placeholder="视频的宽度" ` + modelName + `="height"  />
        //     </div>
        // </div>`);
        // this.chartBody.append(heightContainer);

        //视频类型
        const videoTypeContainer = $(`<div class='chart-item flex'>
            <div class="chart-label">视频类型</div>
            <div class="chart-control">
                <select class="chart-select"` + modelName + `="type"  >
                    <option value="">--请选择--</option>
                    <option value="m4v">m4v</option>
                    <option value="m3u8v">m3u8v</option>
                    <option value="m3uv">m3uv</option>
                    <option value="ogv">ogv</option>
                    <option value="flv">flv</option>
                    <option value="rtmpv">rtmpv</option>
                </select>
            </div>
        </div>`);
        this.chartBody.append(videoTypeContainer);

        //视频URL地址
        const videoUrlContainer = $(`<div class='chart-item flex'>
            <div class="chart-label">视频地址</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  ` + modelName + `="url" />
            </div>
        </div>`);
        this.chartBody.append(videoUrlContainer);
      
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "videoStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "视频样式"
  }
}