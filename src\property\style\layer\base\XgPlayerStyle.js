import TreeStyle from "../../TreeStyle";
export default class XgPlayerStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        // type 播放器类型  MP4 HLS FLV DASH
        const videoTypeContainer = $(`<div class='chart-item flex'>
            <div class="chart-label">播放器类型</div>
            <div class="chart-control">
                <select class="chart-select"` + modelName + `="type"  >
                    <option value="">--请选择--</option>
                    <option value="MP4">MP4</option>
                    <option value="HLS">HLS</option>
                    <option value="FLV">FLV</option>
                    <option value="DASH">MPEG-DASH</option>
                </select>
            </div>
        </div>`);
        this.chartBody.append(videoTypeContainer);
       // isLive: false, //是否为直播，默认值：false
       const isLiveContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">开启直播</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isLive">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(isLiveContainer);

       // autoplay: false,  // 自动播放 默认值: false
       const autoplayContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">开启自动播放</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="autoplay">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(autoplayContainer);

       // autoplayMuted: false, // 自动播放静音 默认值: false
       const autoplayMutedContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">自动播放静音</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="autoplayMuted">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(autoplayMutedContainer);

       //videoInit: true,//是否默认初始化video 默认值：true
       const videoInitContainer = $(`<div class="chart-item flex">
        <div class="w-50 flex">
            <span class="chart-span">是否默认初始化</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="videoInit">
                <div class="slider round"></div>
            </label>
        </div>
    </div>`);
    chartBody.append(videoInitContainer);


           //playsinline: true,//是否在当前页面播放 默认值：true 
           const playsInlineContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否当前页面播放</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="playsinline">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(playsInlineContainer);



        //是否循环播放 是否静音
        const loopContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否循环</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="loop">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
       chartBody.append(loopContainer);

        //视频URL地址
        const videoUrlContainer = $(`<div class='chart-item flex'>
            <div class="chart-label">视频地址</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  ` + modelName + `="url" />
            </div>
        </div>`);
        this.chartBody.append(videoUrlContainer);
      
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "videoStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "视频样式"
  }
}