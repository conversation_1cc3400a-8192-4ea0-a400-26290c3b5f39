import TreeStyle from "../../TreeStyle";

export default class YsPlayerStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // autoplayMuted: false, // 自动播放静音 默认值: false
      const autoplayMutedContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">播放声音</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" ` + modelName + `="audio">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
      chartBody.append(autoplayMutedContainer);

      const videoTypeContainer = $(`<div class='chart-item flex'>
            <div class="chart-label">播放器模板</div>
            <div class="chart-control">
                <select class="chart-select"` + modelName + `="template"  >
                    <option value="simple">极简版</option>
                    <option value="standard">标准版</option>
                    <option value="security">安防版</option>
                </select>
            </div>
        </div>`);
      chartBody.append(videoTypeContainer);

      // 视频ezopen协议播放地址
      const videoUrlContainer = $(`<div class='chart-item flex'>
            <div class="chart-label">ezopen协议地址</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  ` + modelName + `="url" />
            </div>
        </div>`);
      this.chartBody.append(videoUrlContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "videoStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "视频样式"
  }
}