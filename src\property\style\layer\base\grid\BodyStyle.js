import BgStyle from "../../style/BgStyle";
import BorderStyle from "../../style/BorderStyle";
import TreeStyle from "../../../TreeStyle";
export default class BodyStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        
        if(!item["bg"]){
            item["bg"] = {};
        }
        const bgStyle=new BgStyle(this.context);
        bgStyle.initPanel(chartBody,item["bg"],callback);

        //边框
        if(!item["border"]){
          item["border"] ={};
        }
        const borderStyle = new BorderStyle(this.context);
        borderStyle.initPanel(chartBody,item["border"],callback);

        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    
  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "bodyStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "网格配置"
  }
}