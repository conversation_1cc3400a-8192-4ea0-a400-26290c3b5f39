import HeadStyle from "./HeadStyle";
import TabStyle from "./TabStyle";
import TabSelectStyle from "./TabSelectStyle";
import BodyStyle from "./BodyStyle";
import ItemStyle from "./ItemStyle";
import TreeStyle from "../../../TreeStyle";
export default class GridStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //tabStyle
      if (!item["tabStyle"]) {
        item["tabStyle"] = {};
      }
      const tabStyle = new TabStyle(this.context);
      tabStyle.initPanel(chartBody, item["tabStyle"], callback);

      //tabSelectStyle
      if (!item["tabSelectStyle"]) {
        item["tabSelectStyle"] = {};
      }
      const tabSelectStyle = new TabSelectStyle(this.context);
      tabSelectStyle.initPanel(chartBody, item["tabSelectStyle"], callback);

      //HeadStyle
      if (!item["headStyle"]) {
        item["headStyle"] = {};
      }
      const headStyle = new HeadStyle(this.context);
      headStyle.initPanel(chartBody, item["headStyle"], callback);

      //bodyStyle
      if (!item["bodyStyle"]) {
        item["bodyStyle"] = {};
      }
      const bodyStyle = new BodyStyle(this.context);
      bodyStyle.initPanel(chartBody, item["bodyStyle"], callback);

      //tabSelectStyle
      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "gridStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "网格"
  }
}