import ImageStyle from "../../style/ImageStyle";
import TextStyle from "../TextStyle";
import VideoStyle from "../VideoStyle";
import TreeStyle from "../../../TreeStyle";
export default class ItemStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        //显示类型
        const playStyleContainer = $(`<div class="chart-item flex">
            <div class="chart-label">显示类型</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + `="type">
                    <option value="">请选择</option>
                    <option value="video">视频</option>
                    <option value="image">图片</option>
                </select>
            </div>
        </div>`);
        chartBody.append(playStyleContainer);

         //宽度
         const widthContainer = $(`<div class="chart-item flex">
         <div class="chart-label">宽度</div>
             <div class="chart-control">
                 <input type="number" class="chart-number" min="0" max="" placeholder="1" ` + modelName + `="width" />
             </div>
         </div>`);
         chartBody.append(widthContainer);
 
         //高度
         const heightContainer = $(`<div class="chart-item flex">
         <div class="chart-label">高度</div>
             <div class="chart-control">
                 <input type="number" class="chart-number" min="0" max="" placeholder="1" ` + modelName + `="height" />
             </div>
         </div>`);
         chartBody.append(heightContainer);

        //视频
        this.videoContainer = $(`<div class="chart-video"></div>`);
        chartBody.append(this.videoContainer);
        if(!item["video"]){
            item["video"] = {};
        }
        const videoStyle=new VideoStyle(this.context);
        videoStyle.initPanel(this.videoContainer,item["video"],callback);

        //图片
        this.imageContainer = $(`<div class="chart-image"></div>`);
        chartBody.append(this.imageContainer);
        if(!item["image"]){
            item["image"] = {};
        }
        const imageStyle=new ImageStyle(this.context);
        imageStyle.initPanel(this.imageContainer,item["image"],callback);

        if(!item["textStyle"]){
          item["textStyle"] ={};
        }
        const textStyle = new TextStyle(this.context);
        textStyle.initPanel(chartBody,item["textStyle"],callback);

        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    if (key && key === "type") {
      if (value === "video") {
        this.videoContainer.show();
        this.imageContainer.hide();
      } else if (value === "image") {
        this.videoContainer.hide();
        this.imageContainer.show();
      }else{
        this.videoContainer.hide();
        this.imageContainer.hide();
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "gridStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "全局项配置"
  }
}