import BgStyle from "../../style/BgStyle";
import BorderStyle from "../../style/BorderStyle";
import FontStyle from "../../style/FontStyle";
import TreeStyle from "../../../TreeStyle";
export default class TabSelectStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        //宽度
        const widthContainer = $(`<div class="chart-item flex">
        <div class="chart-label">宽度</div>
            <div class="chart-control">
                <input type="number" class="chart-number" min="0" max="" placeholder="1" ` + modelName + `="width" />
            </div>
        </div>`);
        chartBody.append(widthContainer);

        //高度
        const heightContainer = $(`<div class="chart-item flex">
        <div class="chart-label">高度</div>
            <div class="chart-control">
                <input type="number" class="chart-number" min="0" max="" placeholder="1" ` + modelName + `="height" />
            </div>
        </div>`);
        chartBody.append(heightContainer);
        //背景
        if(!item["bg"]){
            item["bg"] = {};
        }
        const bgStyle=new BgStyle(this.context);
        bgStyle.initPanel(chartBody,item["bg"],callback);

        //文本
        if(!item["font"]){
          item["font"] ={};
        }
        const fontStyle = new FontStyle(this.context);
        fontStyle.initPanel(chartBody,item["font"],callback);

        //边框
        if(!item["border"]){
          item["border"] ={};
        }
        const borderStyle = new BorderStyle(this.context);
        borderStyle.initPanel(chartBody,item["border"],callback);

        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {
  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "tabSelect-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "tab选中配置"
  }
}