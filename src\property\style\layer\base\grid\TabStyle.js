import BgStyle from "../../style/BgStyle";
import BorderStyle from "../../style/BorderStyle";
import FontStyle from "../../style/FontStyle";
import TreeStyle from "../../../TreeStyle";
export default class TabStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        //样式
        const tabStyleContainer = $(`<div class="chart-item flex">
            <div class="chart-label">显示类型</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + `="type">
                    <option value="">请选择</option>
                    <option value="text">文本</option>
                    <option value="img">图标</option>
                </select>
            </div>
        </div>`);
        chartBody.append(tabStyleContainer);

        //背景
        if(!item["bg"]){
            item["bg"] = {};
        }
        const bgStyle=new BgStyle(this.context);
        bgStyle.initPanel(chartBody,item["bg"],callback);

        //文本
        if(!item["font"]){
          item["font"] ={};
        }
        const fontStyle = new FontStyle(this.context);
        fontStyle.initPanel(chartBody,item["font"],callback);

        //边框
        if(!item["border"]){
          item["border"] ={};
        }
        const borderStyle = new BorderStyle(this.context);
        borderStyle.initPanel(chartBody,item["border"],callback);

        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {
    // if (key && key === "tabStyle") {
    //     if (value === "video") {
    //       this.videoContainer.show();
    //       this.imageContainer.hide();
    //     } else if (value === "img") {
    //       this.videoContainer.hide();
    //       this.imageContainer.show();
    //     }else{
    //       this.videoContainer.hide();
    //       this.imageContainer.hide();
    //     }
    //   }
  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "tab-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "tabs配置"
  }
}