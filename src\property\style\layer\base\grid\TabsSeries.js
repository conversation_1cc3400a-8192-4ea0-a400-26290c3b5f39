import TabsStyle from "./TabsStyle";
import TreeStyle from "../../../TreeStyle";
export default class TabsSeries extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  afterAddItem (itemContainer, item, index, callback) {
    const model = new TabsStyle(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("tab[" + (index + 1) + "]");
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "分屏系列"
  }
}
