import TabStyle from "./TabStyle";
import TreeStyle from "../../../TreeStyle";
export default class TabsStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, tabStyleItem, callback) {
    if (chartBody && modelName && tabStyleItem) {

      //是否显示
      const isShowContainer = $(`<div class="chart-item">
          <div class="w-50 flex">
              <span class="chart-span">是否显示</span>
              <label class="chart-switch">
                  <input type="checkbox" class="chart-checkbox" `+ modelName + `="isShow">
                  <div class="slider round"></div>
              </label>
          </div>
      </div>`);
      chartBody.append(isShowContainer);

      //样式
      const tabStyleContainer = $(`<div class="chart-item">
          <div class="chart-label">显示类型</div>
          <div class="chart-control">
              <select class="chart-select" ` + modelName + `="type">
                  <option value="">请选择</option>
                  <option value="text">文本</option>
                  <option value="img">图标</option>
              </select>
          </div>
      </div>`);
      chartBody.append(tabStyleContainer);

      //文本
      const textContainer = $(`<div class="chart-item">
          <div class="chart-label">文本</div>
          <div class="chart-control">
              <input type="text" class="chart-text" placeholder="文本" ` + modelName + `="text" />
          </div>
      </div>`);
      chartBody.append(textContainer);

      //屏数
      const totalContainer = $(`<div class="chart-item">
      <div class="chart-label">屏数</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="分屏数" ` + modelName + `="total" />
          </div>
      </div>`);
      chartBody.append(totalContainer);

      //屏数
      const colContainer = $(`<div class="chart-item">
      <div class="chart-label">列数</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="[1-5]" ` + modelName + `="col" />
          </div>
      </div>`);
      chartBody.append(colContainer);

      this.refreshModel(tabStyleItem);
      this.bindModel(tabStyleItem, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "splitScreenStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "分屏样式"
  }
}