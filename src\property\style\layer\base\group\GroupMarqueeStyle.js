import TreeStyle from "../../../TreeStyle";

export default class GroupMarqueeStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        //true-该行在鼠标悬停时停止，false-该行不停止
        const hoverStopContainer = $(`<div class="chart-item">
            <div class="w-50 flex">
                <span class="chart-span">是否鼠标悬停</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="hoverstop">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(hoverStopContainer);

        //方向设置
        const directionContainer = $(`<div class="chart-item">
            <div class="chart-label">方向</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + `="direction">
                    <option value="">---请选择---</option>
                    <option value="right">向右</option>
                    <option value="left">向左</option>
                    <option value="up">向上</option>
                    <option value="down">向下</option>
                </select>
            </div>
        </div>`);
        chartBody.append(directionContainer);

        //指定内容将滚动多少次。
        const loopContainer = $(`<div class='chart-item'>
            <div class="chart-label">循环次数</div>
            <div class="chart-control">
                <input type="number" class="chart-number" ` + modelName + `="loop" />
            </div>
        </div>`);
        chartBody.append(loopContainer);

        //两次移动之间的延迟量（以毫秒为单位）
        const scrollDelayContainer = $(`<div class='chart-item'>
            <div class="chart-label">滚动延迟</div>
            <div class="chart-control">
                <input type="number" class="chart-number" ` + modelName + `="scrolldelay" />
            </div>
        </div>`);
        chartBody.append(scrollDelayContainer);

        //内容移动速度（像素/秒）
        const scrollAmountContainer = $(`<div class='chart-item'>
            <div class="chart-label">速度(像素/毫秒)</div>
            <div class="chart-control">
                <input type="number" class="chart-number" ` + modelName + `="scrollamount" />
            </div>
        </div>`);
        chartBody.append(scrollAmountContainer);

        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {
    console.log(key, value)
  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "marquee-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "跑马灯"
  }
}