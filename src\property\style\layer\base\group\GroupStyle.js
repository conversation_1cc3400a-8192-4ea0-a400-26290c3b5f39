import FontStyle from "../../style/FontStyle";
import BorderStyle from "../../style/BorderStyle";
import BgStyle from "../../style/BgStyle";
import TreeStyle from "../../../TreeStyle";
import GroupMarqueeStyle from "./GroupMarqueeStyle";
export default class GroupStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        const isOverFlowContainer = $(`<div class="chart-item">
            <div class="w-50 flex">
                <span class="chart-span">是否溢出隐藏</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isOverFlow">
                    <div class="slider round"></div>
                </label>
            </div>
          </div>`);
        chartBody.append(isOverFlowContainer);

        const isMarqueeContainer = $(`<div class="chart-item">
            <div class="w-50 flex">
                <span class="chart-span">是否开启跑马灯</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isMarquee">
                    <div class="slider round"></div>
                </label>
            </div>
          </div>`);
        chartBody.append(isMarqueeContainer);

         //字体
         if(!item["bg"]){
          item["bg"]={};
        }
        const bgStyle = new BgStyle(this.context);
        bgStyle.initPanel(chartBody,item["bg"],callback);

        //字体
        if(!item["font"]){
          item["font"]={};
        }
        const fontStyle = new FontStyle(this.context);
        fontStyle.initPanel(chartBody,item["font"],callback);
        //边框
        if(!item["border"]){
          item["border"]={};
        }
        const borderStyle = new BorderStyle(this.context);
        borderStyle.initPanel(chartBody,item["border"],callback);

        //跑马灯
        if(!item["marquee"]){
          item["marquee"]={};
        }
        const groupMarqueeStyle = new GroupMarqueeStyle(this.context);
        groupMarqueeStyle.initPanel(chartBody,item["marquee"],callback);
      
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "groupStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "分组样式"
  }
}