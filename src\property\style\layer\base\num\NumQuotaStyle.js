import FlexStyle from "../../style/FlexStyle";
import NumStyle from "./NumStyle";
import NumUnitStyle from "./NumUnitStyle";
import ThousandStyle from "./ThousandStyle";
import TreeStyle from "../../../TreeStyle";
export default class NumQuotaStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        const isFlipContainer = $(`<div class="chart-item">
            <div class="w-50 flex">
                <span class="chart-span">是否翻牌</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isFlip">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(isFlipContainer);
        //步数
        const stepTotalContainer = $(`<div class="chart-item flex">
          <div class="chart-label">步数(次)</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="1" placeholder="步数" ` + modelName + ` = "stepTotal"/>
          </div>
        </div>`);
        chartBody.append(stepTotalContainer);
        const stepSpiltTimeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">间隔(ms)</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="10" placeholder="间隔时间(ms)" ` + modelName + ` = "stepSplitTime"/>
          </div>
        </div>`);
        chartBody.append(stepSpiltTimeContainer);

      const mockNumber = $(`<div class="chart-item flex">
          <div class="chart-label">数字</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" placeholder="数字" ` + modelName + ` = "amount"/>
          </div>
        </div>`);
      chartBody.append(mockNumber);
         //布局
        if(!item["flex"]){
            item["flex"]={};
        }
        const flexStyle = new FlexStyle(this.context);
        flexStyle.initPanel(chartBody,item["flex"],callback);
 
         //数字
        if(!item["num"]){
            item["num"]={};
        }
        const numStyle = new NumStyle(this.context);
        numStyle.initPanel(chartBody,item["num"],callback);

         //千分位
        if(!item["thousand"]){
            item["thousand"]={};
        }
        const thousandStyle = new ThousandStyle(this.context);
        thousandStyle.initPanel(chartBody,item["thousand"],callback);

         //单位
        if(!item["unit"]){
            item["unit"]={};
        }
        const numUnitStyle = new NumUnitStyle(this.context);
        numUnitStyle.initPanel(chartBody,item["unit"],callback);
      
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {
    // console.log(key, value)
  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "numQuotaStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "翻牌"
  }
}