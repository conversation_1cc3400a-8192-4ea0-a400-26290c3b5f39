import BorderStyle from "../../style/BorderStyle";
import FontStyle from "../../style/FontStyle";
import BgStyle from "../../style/BgStyle";
import TreeStyle from "../../../TreeStyle";
export default class NumStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //内外间距
      const marginContainer = $(`<div class="chart-item flex">
            <div class="chart-label">外间距</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  placeholder="5px 5px 5px 5px"  `+ modelName + `="margin"  />
            </div>
        </div>`);
      chartBody.append(marginContainer);
      const marginAndPaddingContainer = $(`<div class="chart-item flex">
            <div class="chart-label">内间距</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  placeholder="5px 5px 5px 5px" `+ modelName + `="padding"  />
            </div>
        </div>`);
      chartBody.append(marginAndPaddingContainer);

      //字体颜色
      const heightContainer = $(`<div class="chart-item flex">
            <div class="chart-label">高度</div>
            <div class="chart-control">
                <input type="number" class="chart-number" ` + modelName + `="height"  />
            </div>
        </div>`);
      chartBody.append(heightContainer);
      // const bgColorContainer = $(`<div class="chart-item flex">
      //       <div class="chart-label">背景</div>
      //       <div class="chart-control">
      //           <input type="text" class="chart-color"` + modelName + `="bg.color" />
      //       </div>
      //   </div>`);
      // chartBody.append(bgColorContainer);

      const decimalCountContainer = $(`<div class="chart-item flex">
            <div class="chart-label">保留小数</div>
            <div class="chart-control">
                <input type="number" class="chart-number" min="0" `+ modelName + `="decimalCount"  />
            </div>
        </div>`);
      chartBody.append(decimalCountContainer);

      if (!item["font"]) {
        item["font"] = {};
      }
      const fontStyle = new FontStyle(this.context);
      fontStyle.initPanel(chartBody, item["font"], callback);

      if (!item["border"]) {
        item["border"] = {};
      }
      const borderStyle = new BorderStyle(this.context);
      borderStyle.initPanel(chartBody, item["border"], callback);

      if(!item["bg"]){
        item["bg"]={};
      }
      const bgStyle = new BgStyle(this.context);
      bgStyle.initPanel(chartBody,item["bg"],callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "numStyle-model";
  }

  /**
* 描述:标题信息
* @returns {string}
*/
  getTitle () {
    return "数字"
  }
}