import FontStyle from "../../style/FontStyle";
import TreeStyle from "../../../TreeStyle";
export default class NumUnitStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        //是否显示单位
        const unitShowContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否显示单位</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="show">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(unitShowContainer);
        //单位类型
        const unitContainer=$(`<div class='chart-item flex'>
            <div class="chart-label">单位类型</div>
            <div class="chart-control">
                <select class="chart-select"`+modelName+`="type"  >
                    <option value="">--请选择--</option>
                    <option value="1">百分</option>
                    <option value="2">万</option>
                    <option value="3">十万</option>
                    <option value="4">百万</option>
                    <option value="5">千万</option>
                    <option value="6">亿元</option>
                    <option value="7">自定义</option>
                </select>
            </div>
        </div>`);
        chartBody.append(unitContainer);

        //自定义
        const unitTextContainer = $(`<div class='chart-item flex'>
            <div class="chart-label">自定义</div>
            <div class="chart-control">
                <input type="text" class="chart-text" `+modelName+`="text"  />
            </div>
        </div>`);
        chartBody.append(unitTextContainer);

        //内间距
        const marginContainer = $(`<div class='chart-item flex'>
            <div class="chart-label">内间距</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  placeholder="5px 5px 5px 5px" `+modelName+`="padding"  />
            </div>
        </div>`);
        chartBody.append(marginContainer);

        //外间距
        const paddingContainer = $(`<div class='chart-item flex'>
            <div class="chart-label">外间距</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  placeholder="5px 5px 5px 5px"  `+modelName+`="margin"  />
            </div>
        </div>`);
        chartBody.append(paddingContainer);

        if(!item["font"]){
            item["font"]={};
        }
        const fontStyle=new FontStyle(this.context);
        fontStyle.initPanel(chartBody,item["font"],callback);
      
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "numUnitStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "单位"
  }
}