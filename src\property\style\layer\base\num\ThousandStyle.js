import NumStyle from "./NumStyle";
import TreeStyle from "../../../TreeStyle";
export default class ThousandStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        const isShowContainer = $(`<div class="chart-item">
            <div class="w-50 flex">
                <span class="chart-span">是否显示</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isShow">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(isShowContainer);

        if(!item["num"]){
            item["num"]={};
        }
        const numStyle=new NumStyle(this.context);
        numStyle.initPanel(chartBody,item["num"],callback);
      
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "thousandStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "千分位"
  }
}