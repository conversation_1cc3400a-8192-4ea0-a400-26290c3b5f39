import TreeStyle from "../../../TreeStyle";
export default class NoDataStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const isShowContainer = $(`<div class="chart-item flex">
      <div class="w-50 flex">
          <span class="chart-span">是否显示</span>
          <label class="chart-switch">
              <input type="checkbox" class="chart-checkbox" `+ modelName + `="isShow">
              <div class="slider round"></div>
          </label>
          </div>
      </div>`);
      chartBody.append(isShowContainer);
      //页面
      const htmlContainer = $(`<div class="chart-item flex">
          <div class="chart-label">页面</div>
          <div class="chart-control">
              <textarea type="text" class="chart-area" `+modelName+`="html"></textarea>
          </div>
      </div>`);
      chartBody.append(htmlContainer);
      
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "noDataStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "无数据"
  }
}