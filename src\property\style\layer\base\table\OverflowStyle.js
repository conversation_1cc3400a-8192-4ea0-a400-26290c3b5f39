import TreeStyle from "../../../TreeStyle";
export default class OverflowStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        const isOpenContainer = $(`<div class="chart-item flex">
        <div class="w-50 flex">
            <span class="chart-span">是否开启</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="isOpen">
                <div class="slider round"></div>
            </label>
            </div>
        </div>`);
        chartBody.append(isOpenContainer);
        
        const isShowContainer = $(`<div class="chart-item flex">
        <div class="w-50 flex">
            <span class="chart-span">是否显示</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="isShow">
                <div class="slider round"></div>
            </label>
            </div>
        </div>`);
        chartBody.append(isShowContainer);
      
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "overflowStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "滑动条"
  }
}