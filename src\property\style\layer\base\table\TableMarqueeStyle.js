import TreeStyle from "../../../TreeStyle";
export default class TableMarqueeStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        //是否开启跑马灯
        const circularContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">开启跑马灯</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isOpen">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(circularContainer);
        
        //头高度。
        // const headerHeightContainer = $(`<div class='chart-item flex'>
        //     <div class="chart-label">头高度</div>
        //     <div class="chart-control">
        //         <input type="number" class="chart-number" ` + modelName + `="headerHeight" />
        //     </div>
        // </div>`);
        // chartBody.append(headerHeightContainer);

        //速度
        const speedContainer = $(`<div class='chart-item flex'>
            <div class="chart-label">速度</div>
            <div class="chart-control">
                <input type="number" class="chart-number" ` + modelName + `="speed" />
            </div>
        </div>`);
        chartBody.append(speedContainer);

        //最小条目数字
        const minRowContainer = $(`<div class='chart-item flex'>
            <div class="chart-label">最小</div>
            <div class="chart-control">
                <input type="number" class="chart-number" min="10" placeholder="[10至...]" ` + modelName + `="minRow" />
            </div>
        </div>`);
        chartBody.append(minRowContainer);

        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "tableMarqueeStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "跑马灯"
  }
}