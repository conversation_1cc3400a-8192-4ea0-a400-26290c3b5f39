import ColumnStyleList from "./column/ColumnStyleList";
import THeaderStyle from "./header/THeaderStyle";
import RowStyle from "./row/RowStyle";
// import OverflowStyle from "./OverflowStyle";
// import NoDataStyle from "./NoDataStyle";
import TreeStyle from "../../../TreeStyle";
export default class TableStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  setOptions (options) {
    this.options = options;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // columns: [],
      // columnStyles: [],
      // theadStyles: {},
      // rowStyles: {},

      //字段样式
      if (!item["columnStyles"]) {
        item["columnStyles"] = [];
      }
      const columnStyleList = new ColumnStyleList(this.context);
      columnStyleList.initPanel(chartBody, item["columnStyles"], callback);
      columnStyleList.setOptions(this.options);

      //头样式
      if (!item["theadStyles"]) {
        item["theadStyles"] = {};
      }
      const theaderStyle = new THeaderStyle(this.context);
      theaderStyle.initPanel(chartBody, item["theadStyles"], callback);

      //行样式
      if (!item["rowStyles"]) {
        item["rowStyles"] = {};
      }
      const rowStyle = new RowStyle(this.context);
      rowStyle.initPanel(chartBody, item["rowStyles"], callback);

      //滑动条
      // if (!item["overflow"]) {
      //   item["overflow"] = {};
      // }
      // const overflowStyle = new OverflowStyle(this.context);
      // overflowStyle.initPanel(chartBody, item["overflow"], callback);

      //无数据
      // const noDataStyle = new NoDataStyle(this.context);
      // noDataStyle.initPanel(chartBody, item, callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "tableStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "表格"
  }
}