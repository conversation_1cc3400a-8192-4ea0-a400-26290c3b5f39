import JumpTree from "../../../../common/jump/JumpTree";
import ProgressStyle from "./ProgressStyle";
import ColumnCellStyle from "./ColumnCellStyle";
import ColumnTextStyle from "./ColumnTextStyle";
import TreeStyle from "../../../../TreeStyle";
export default class ColumnStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  setOptions (options) {
    this.options = options;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //属性
      const columnKeyContainer = $(`<div class="chart-item">
            <div class="chart-label">字段</div>
            <div class="chart-control">
                <select class="chart-select" `+ modelName + `="key"></select>
            </div>
        </div>`);
      chartBody.append(columnKeyContainer);
      const columnSelector = $(columnKeyContainer).find(".chart-select");
      this.refreshOption(columnSelector, this.options);
      //进度条
      const progressStyle = new ProgressStyle(this.context);
      progressStyle.initPanel(chartBody, item, callback);
      //超链接
      if (!item["jump"]) {
        item["jump"] = { isShow: false };
      }
      const jumpSytle = new JumpTree(this.context);
      jumpSytle.initPanel(chartBody, item["jump"], callback);

      //1.单元格样式
      if (!item["cellStyle"]) {
        item["cellStyle"] = {};
      }
      const columnCellStyle = new ColumnCellStyle(this.context);
      columnCellStyle.initPanel(chartBody, item["cellStyle"], callback);
      //2.文本样式
      if (!item["textStyle"]) {
        item["textStyle"] = {};
      }
      const columnTextStyle = new ColumnTextStyle(this.context);
      columnTextStyle.initPanel(chartBody, item["textStyle"], callback);
      columnTextStyle.refreshTitle('文本');


      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }

  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "columnStyle-model";
  }

  /**
* 描述:标题信息
* @returns {string}
*/
  getTitle () {
    return "字段样式"
  }
}