import ColumnStyle from "./ColumnStyle";
import TreeStyle from "../../../../TreeStyle";
export default class ColumnStyleList extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  setOptions(options){
    this.options=options;
  }
  afterAddItem (itemContainer, item, index, callback) {
    const model = new ColumnStyle(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.setOptions(this.options);
    model.refreshTitle("字段样式[" + (index + 1) + "]配置");
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "字段样式列表"
  }
}
