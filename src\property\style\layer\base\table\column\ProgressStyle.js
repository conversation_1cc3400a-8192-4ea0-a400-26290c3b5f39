
import BorderStyle from "../../../style/BorderStyle";
import TreeStyle from "../../../../TreeStyle";
export default class ProgressStyle extends TreeStyle {
    constructor(context, isOpen) {
        super(context, false, isOpen);
    }

    refreshPanel(chartBody, modelName, item, callback) {
        if (chartBody && modelName && item) {
            //是否显示 是否文本
            const isContainer = $(`<div class="chart-item flex">
            <div class="flex-1 flex">
                <span class="chart-span">是否显示</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isShow">
                    <div class="slider round"></div>
                </label>
            </div>
            <div class="flex-1 flex">
                <span class="chart-span">是否文本</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isText">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
            chartBody.append(isContainer);

            //高度
            const heightContainer = $(`<div class="chart-item">
            <div class="chart-label">高度</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  `+ modelName + `="height"  />
            </div>
        </div>`);
            chartBody.append(heightContainer);

            const backgroundColorContainer = $(`<div class="chart-item">
            <div class="chart-label">背景</div>
            <div class="chart-control" style="padding: 0">
                <input type="color" class="chart-color" ` + modelName + `="backgroundColor"   />
            </div>
        </div>`);
            chartBody.append(backgroundColorContainer);

            //超链接
            if (!item["border"]) {
                item["border"] = { isShow: false };
            }
            const borderStyle = new BorderStyle(this.context);
            borderStyle.initPanel(chartBody, item["border"], callback);

            this.refreshModel(item);
            this.bindModel(item, callback);
        }
    }
    refreshEvent(key, value) {

    }
    /**
     *
     * @returns {string}
     */
    getModelName() {
        return "progressStyle-model";
    }

    /**
  * 描述:标题信息
  * @returns {string}
  */
    getTitle() {
        return "进度条"
    }
}