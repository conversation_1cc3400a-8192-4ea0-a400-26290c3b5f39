import TreeStyle from "../../../../TreeStyle";
export default class TableColumn extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        //是否显示
        const showContainer = $(`<div class="chart-item">
            <div class="w-50 flex">
                <span class="chart-span">是否显示</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="show">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(showContainer);
        
        //是否提示
        const titleContainer = $(`<div class="chart-item">
            <div class="w-50 flex">
                <span class="chart-span">是否提示</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="title">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(titleContainer);

        //是否分割
        const splitContainer = $(`<div class="chart-item">
            <div class="w-50 flex">
                <span class="chart-span">是否分割</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="split">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(splitContainer);

        //字段编码
        const columnCodeContainer = $(`<div class='chart-item'>
            <div class="chart-label">字段编码</div>
            <div class="chart-control flex-1">
                <input type="text" class="chart-text" autocomplete="true" ` + modelName + `="key" />
            </div>
        </div>`);
        chartBody.append(columnCodeContainer);

        //字段标题
        const minRowContainer = $(`<div class='chart-item'>
            <div class="chart-label">字段标题</div>
            <div class="chart-control flex-1">
                <input type="text" class="chart-text"  placeholder="字段标题" `+modelName+`="name"  />
            </div>
        </div>`);
        chartBody.append(minRowContainer);

        //字段标题
        const sortContainer = $(`<div class='chart-item'>
            <div class="chart-label">排序</div>
            <div class="chart-control flex-1">
                <input type="number" class="chart-number"  placeholder="排序" `+modelName+`="sort"  />
            </div>
        </div>`);
        chartBody.append(sortContainer);

         //保留小数
         const decimalCountContainer = $(`<div class='chart-item'>
              <div class="chart-label">保留小数</div>
              <div class="chart-control flex-1">
                  <input type="number" class="chart-number"  placeholder="排序" `+modelName+`="decimalCount"  />
              </div>
          </div>`);
          chartBody.append(decimalCountContainer);

        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "tableColumn-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "字段"
  }
}