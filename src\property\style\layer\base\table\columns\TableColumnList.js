import TableColumn from "./TableColumn";
import TreeStyle from "../../../../TreeStyle";
export default class TableColumnList extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  afterAddItem (itemContainer, item, index, callback) {
      const model = new TableColumn(this.context);
      model.initPanel(itemContainer, item, callback);
      model.refreshId(index);
      model.refreshTitle("字段[" + (item.key) + "]配置");
    // }
    
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "字段属性"
  }
}
