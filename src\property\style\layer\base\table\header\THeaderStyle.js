
import BorderStyle from "../../../style/BorderStyle";
import BgStyle from "../../../style/BgStyle";
import FontStyle from "../../../style/FontStyle";
import ColumnTextStyle from "../column/ColumnTextStyle";
import TreeStyle from "../../../../TreeStyle";
export default class THeaderStyle extends TreeStyle{
    constructor(context, isOpen) {
        super(context, false, isOpen);
        }

refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        //是否显示
        const isShowContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否显示</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isShow">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
        chartBody.append(isShowContainer);

        const heightContainer = $(`<div class="chart-item flex">
            <div class="chart-label">高度</div>
            <div class="chart-control">
                <input type="text" class="chart-text" `+modelName+`="height"  />
            </div>
        </div>`);
        chartBody.append(heightContainer);

        const lineHeightContainer = $(`<div class="chart-item flex">
            <div class="chart-label">行高</div>
            <div class="chart-control">
                <input type="text" class="chart-text" `+modelName+`="lineHeight"  />
            </div>
        </div>`);
        chartBody.append(lineHeightContainer);

        if(!item["bg"]){
            item["bg"]={};
        }
        const bgStyle = new BgStyle(this.context);
        bgStyle.initPanel(chartBody,item["bg"],callback);

        // if(!item["font"]){
        //     item["font"]={};
        // }
        // const fontStyle = new FontStyle(this.context);
        // fontStyle.initPanel(chartBody,item["font"],callback);

        if(!item["border"]){
            item["border"]={};
        }
        const borderStyle = new BorderStyle(this.context);
        borderStyle.initPanel(chartBody,item["border"],callback);

        if(!item["thStyle"]){
            item["thStyle"]={};
        }
        const columnTextStyle = new ColumnTextStyle(this.context);
        columnTextStyle.initPanel(chartBody,item["thStyle"],callback);
        columnTextStyle.refreshTitle("头单元格");
        
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
}
    refreshEvent(key, value) {

    }
    /**
     *
     * @returns {string}
     */
    getModelName() {
        return "theaderStyle-model";
    }

     /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "头样式"
  }
}