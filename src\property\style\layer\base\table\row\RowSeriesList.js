import RowSeriesStyle from "./RowSeriesStyle";
import TreeStyle from "../../../../TreeStyle";
export default class RowSeriesList extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  afterAddItem (itemContainer, item, index, callback) {
    const model = new RowSeriesStyle(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("系列[" + (index + 1) + "]配置");
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "系列列表"
  }
}
