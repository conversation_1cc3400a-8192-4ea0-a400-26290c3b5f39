
import BorderStyle from "../../../style/BorderStyle";
import BgStyle from "../../../style/BgStyle";
import FontStyle from "../../../style/FontStyle";
import ColumnTextStyle from "../column/ColumnTextStyle";
import ColumnCellStyle from "../column/ColumnCellStyle";
import TreeStyle from "../../../../TreeStyle";
export default class RowSeriesStyle extends TreeStyle{
    constructor(context, isOpen) {
        super(context, false, isOpen);
        }

refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        const heightAndLineHeightContainer = $(`<div class="chart-item flex">
            <div class="chart-label">行号</div>
            <div class="chart-control">
                <input type="number" class="chart-number"  `+modelName+`="rowNum"  />
            </div>
        </div>`);
        chartBody.append(heightAndLineHeightContainer);

        if(!item["bg"]){
            item["bg"]={};
        }
        const bgStyle = new BgStyle(this.context);
        bgStyle.initPanel(chartBody,item["bg"],callback);

        if(!item["font"]){
            item["font"]={};
        }
        const fontStyle = new FontStyle(this.context);
        fontStyle.initPanel(chartBody,item["font"],callback);

        if(!item["border"]){
            item["border"]={};
        }
        const borderStyle = new BorderStyle(this.context);
        borderStyle.initPanel(chartBody,item["border"],callback);

        if(!item["cellStyle"]){
            item["cellStyle"]={};
          }
        const columnCellStyle = new ColumnCellStyle(this.context);
        columnCellStyle.initPanel(chartBody,item["cellStyle"],callback);

        if(!item["textStyle"]){
            item["textStyle"]={};
        }
        const columnTextStyle = new ColumnTextStyle(this.context);
        columnTextStyle.initPanel(chartBody,item["textStyle"],callback);
        
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
}
    refreshEvent(key, value) {

    }
    /**
     *
     * @returns {string}
     */
    getModelName() {
        return "rowSeriesStyle-model";
    }

     /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "系列样式"
  }
}