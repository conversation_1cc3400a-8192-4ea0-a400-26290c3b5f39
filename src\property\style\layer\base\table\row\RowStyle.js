
import BorderStyle from "../../../style/BorderStyle";
import FontStyle from "../../../style/FontStyle";
import RowSeriesList from "./RowSeriesList";
import TreeStyle from "../../../../TreeStyle";
export default class RowStyle extends TreeStyle{
    constructor(context, isOpen) {
        super(context, false, isOpen);
        }

refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        //内外间距
        const paddingContainer = $(`<div class="chart-item flex">
            <div class="chart-label">内间距</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  placeholder="5px 5px 5px 5px" ` + modelName + `="padding"  />
            </div>
        </div>`);
        chartBody.append(paddingContainer);

        const marginContainer = $(`<div class="chart-item flex">
            <div class="chart-label">外间距</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  placeholder="5px 5px 5px 5px"  ` + modelName + `="margin"  />
            </div>
        </div>`);
        chartBody.append(marginContainer);

        const heightContainer = $(`<div class="chart-item flex">
            <div class="chart-label">高度</div>
            <div class="chart-control">
                <input type="text" class="chart-text" ` + modelName + `="height"  />
            </div>
        </div>`);
        chartBody.append(heightContainer);

        const heightAndLineHeightContainer = $(`<div class="chart-item flex">
            <div class="chart-label">行高</div>
            <div class="chart-control">
                <input type="text" class="chart-text" ` + modelName + `="lineHeight"  />
            </div>
        </div>`);
        chartBody.append(heightAndLineHeightContainer);

        if(!item["font"]){
            item["font"]={};
        }
        const fontStyle = new FontStyle(this.context);
        fontStyle.initPanel(chartBody,item["font"],callback);

        if(!item["border"]){
            item["border"]={};
        }
        const borderStyle = new BorderStyle(this.context);
        borderStyle.initPanel(chartBody,item["border"],callback);

        if(!item["series"]){
            item["series"]=[];
        }
        const rowSeriesList=new RowSeriesList(this.context);
        rowSeriesList.initPanel(chartBody,item["series"],callback);
        rowSeriesList.refreshTitle("系列列表");
        
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
}
    refreshEvent(key, value) {

    }
    /**
     *
     * @returns {string}
     */
    getModelName() {
        return "rowStyle-model";
    }

     /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "行样式"
  }
}