import TreeIconStyle from "./TreeIconStyle";
import TextStyle from "../TextStyle";
import SwitchStyle from "./SwitchStyle";
import TreeStyle from "../../../TreeStyle";
export default class BaseTreeStyle extends TreeStyle{
constructor(context, isOpen) {
  super(context, false, isOpen);
}

refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        //图标样式
      if(!item["iconStyle"]){
        item["iconStyle"]={};
      }
      const treeIconStyle = new TreeIconStyle(this.context);
      treeIconStyle.initPanel(chartBody, item["iconStyle"], callback);
      //文本TreeIconStyle
      if(!item["textStyle"]){
        item["textStyle"]={};
      }
      const textStyle = new TextStyle(this.context);
      textStyle.initPanel(chartBody, item["textStyle"], callback);
      //开关样式
      if(!item["switchStyle"]){
        item["switchStyle"]={};
      }
      const switchStyle = new SwitchStyle(this.context);
      switchStyle.initPanel(chartBody, item["switchStyle"], callback);
        
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
}
    refreshEvent(key, value) {

    }
    /**
     *
     * @returns {string}
     */
    getModelName() {
        return "baseTree-model";
    }

     /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "树形"
  }
}