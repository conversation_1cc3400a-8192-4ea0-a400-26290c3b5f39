
import TreeStyle from "../../../TreeStyle";
export default class SwitchStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const isShowContainer = $(`<div class="chart-item">
            <div class="w-50 flex">
                <span class="chart-span">是否显示</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="isShow">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
      chartBody.append(isShowContainer);
      //初始化文本样式-是否超长隐藏
      const widthContainer = $(`<div class="chart-item flex">
            <div class="chart-label">宽度</div>
            <div class="chart-control">
                <input type="number" class="chart-number" min="0" max="" ` + modelName + `="width" />
            </div>
        </div>`);
      chartBody.append(widthContainer);

      const heightContainer = $(`<div class="chart-item flex">
            <div class="chart-label">高度</div>
            <div class="chart-control">
                <input type="number" class="chart-number" min="0" max="" ` + modelName + `="height" />
            </div>
        </div>`);
      chartBody.append(heightContainer);

      const borderRadiusContainer = $(`<div class="chart-item flex">
            <div class="chart-label">圆角</div>
            <div class="chart-control">
                <input type="number" class="chart-number" min="0" max=""` + modelName + `="borderRadius" />
            </div>
        </div>`);
      chartBody.append(borderRadiusContainer);

      const backgroundColorContainer = $(`<div class="chart-item flex">
            <div class="chart-label">圆点颜色</div>
            <div class="chart-control">
                <input type="text" class="chart-color" ` + modelName + `="swRound.background" />
            </div>
        </div>`);
      chartBody.append(backgroundColorContainer);

      const backgroundOpenContainer = $(`<div class="chart-item flex">
            <div class="chart-label">开启颜色</div>
            <div class="chart-control">
                <input type="text" class="chart-color" ` + modelName + `="open.background" />
            </div>
        </div>`);
      chartBody.append(backgroundOpenContainer);

      const backgroundCloseContainer = $(`<div class="chart-item flex">
            <div class="chart-label">关闭颜色</div>
            <div class="chart-control">
                <input type="text" class="chart-color" ` + modelName + `="close.background" />
            </div>
        </div>`);
      chartBody.append(backgroundCloseContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "switchStyle-model";
  }

  /**
* 描述:标题信息
* @returns {string}
*/
  getTitle () {
    return "开关"
  }
}