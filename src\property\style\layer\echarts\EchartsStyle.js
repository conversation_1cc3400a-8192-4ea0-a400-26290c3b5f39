import TreeStyle from "../../TreeStyle";
export default class EchartsStyle extends TreeStyle {
  constructor(context, isAdd, isOpen) {
    super(context, isAdd, isOpen);
  }
  //显示
  loadShow(chartBody, modelName) {
    if (chartBody && modelName) {
      const showContainer = $(`<div class="chart-item flex">
        <div class="w-50 flex">
          <span class="chart-span">是否显示</span>
          <label class="chart-switch">
              <input type="checkbox" class="chart-checkbox" `+ modelName + `="show">
              <div class="slider round"></div>
          </label>
        </div>
      </div>`);
      chartBody.append(showContainer);
    }
  }
  //名称
  loadName(chartBody, modelName) {
    if (chartBody && modelName) {
      const nameContainer = $(`<div class="chart-item flex">
        <div class="chart-label">名称</div>
          <div class="chart-control">
              <input type="text" class="chart-text" placeholder="名称" ` + modelName + `="name" />
          </div>
        </div>`);
      chartBody.append(nameContainer);
    }
  }
  //文本
  loadText(chartBody, modelName) {
    if (chartBody && modelName) {
      const nameContainer = $(`<div class="chart-item flex">
        <div class="chart-label">文本</div>
          <div class="chart-control">
              <input type="text" class="chart-text" placeholder="文本" ` + modelName + `="text" />
          </div>
        </div>`);
      chartBody.append(nameContainer);
    }
  }
  //颜色
  // loadColor(chartBody, modelName) {
  //   if (chartBody && modelName) {
  //     const colorContainer = $(`<div class="chart-item flex">
  //       <div class="chart-label">颜色</div>
  //         <div class="chart-control">
  //           <input type="text" class="chart-color" ` + modelName + `="color" />
  //         </div>
  //       </div>`);
  //     chartBody.append(colorContainer);
  //   }
  // }
  //背景色
  // loadBackgroundColor(chartBody, modelName) {
  //   if (chartBody && modelName) {
  //     const backgroundColorContainer = $(`<div class="chart-item flex">
  //       <div class="chart-label">背景色</div>
  //         <div class="chart-control">
  //           <input type="text" class="chart-color" ` + modelName + `="backgroundColor" />
  //         </div>
  //       </div>`);
  //     chartBody.append(backgroundColorContainer);
  //   }
  // }
  //格式化
  loadFormatter(chartBody, modelName) {
    if (chartBody && modelName) {
      const formatterContainer = $(`<div class="chart-item flex">
        <div class="chart-label">格式化</div>
        <div class="chart-control">
              <textArea type="text" class="chart-area" ` + modelName + `="formatter"></textArea>
          </div>
        </div>`);
      chartBody.append(formatterContainer);
    }
  }
  // loadformatterFun (chartBody, modelName) {
  //   if (chartBody && modelName) {
  //     const formatterFunContainer = $(`<div class="chart-item flex">
  //     <div class="chart-label">格式方法</div>
  //     <div class="chart-control">
  //           <textArea type="text" class="chart-area" ` + modelName + `="formatterFun"></textArea>
  //       </div>
  //     </div>`);
  //     chartBody.append(formatterFunContainer);
  //   }
  // }
  //颜色
  // const itemStyleColor0Container=$(`<div class="chart-item flex">
  //     <div class="chart-label">颜色</div>
  //     <div class="chart-control">
  //         <input type="text" class="chart-color"  `+modelName+`="color00"  />
  //         <div class="chart-fl flex-1 text-center">至</div>
  //         <input type="text" class="chart-color"  `+modelName+`="color01"  />
  //     </div>
  // </div>`);
  // chartBody.append(itemStyleColor0Container);
  //长度
  loadLength(chartBody, modelName) {
    if (chartBody && modelName) {
      const lengthContainer = $(`<div class="chart-item flex">
        <div class="chart-label">长度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="长度" ` + modelName + `="length" />
          </div>
        </div>`);
      chartBody.append(lengthContainer);
    }
  }
  //宽高
  loadWidth(chartBody, modelName) {
    if (chartBody && modelName) {
      const widthContainer = $(`<div class="chart-item flex">
        <div class="chart-label">宽度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="宽度" ` + modelName + `="width" />
          </div>
        </div>`);
      chartBody.append(widthContainer);
    }
  }
  //宽高
  loadHeight(chartBody, modelName) {
    if (chartBody && modelName) {
      const heightContainer = $(`<div class="chart-item flex">
        <div class="chart-label">高度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="高度" ` + modelName + `="height" />
          </div>
        </div>`);
      chartBody.append(heightContainer);
    }
  }
  //间距
  loadPadding(chartBody, modelName) {
    if (chartBody && modelName) {
      const paddingContainer = $(`<div class="chart-item flex">
        <div class="chart-label">内间距</div>
          <div class="chart-control">
              <input type="text" class="chart-text" placeholder="内间距" ` + modelName + `="padding" />
          </div>
        </div>`);
      chartBody.append(paddingContainer);
    }
  }
  //间距
  loadMargin(chartBody, modelName) {
    if (chartBody && modelName) {
      const marginContainer = $(`<div class="chart-item flex">
        <div class="chart-label">外间距</div>
          <div class="chart-control">
              <input type="text" class="chart-text" placeholder="外间距" ` + modelName + `="margin" />
          </div>
        </div>`);
      chartBody.append(marginContainer);
    }
  }
  //透明
  loadOpacity(chartBody, modelName) {
    if (chartBody && modelName) {
      const opacityContainer = $(`<div class="chart-item flex">
            <div class="chart-label">透明度</div>
            <div class="chart-control">
                <input type="number" class="chart-number" min="0" max="" placeholder="透明度" ` + modelName + `="opacity" />
            </div>
          </div>`);
      chartBody.append(opacityContainer);
    }
  }
  //模糊
  loadBlur(chartBody, modelName) {
    if (chartBody && modelName) {
      const opacityContainer = $(`<div class="chart-item flex">
            <div class="chart-label">模糊</div>
            <div class="chart-control">
                <input type="number" class="chart-number" min="0" max="" placeholder="模糊度" ` + modelName + `="blur" />
            </div>
          </div>`);
      chartBody.append(opacityContainer);
    }
  }


  // refreshPanel (chartBody, modelName, item, callback) {
  //   if (chartBody && modelName && item) {
  //     this.refreshModel(item);
  //     this.bindModel(item, callback);
  //   }
  // }
  // refreshEvent (key, value) {

  // }
  // /**
  //  *
  //  * @returns {string}
  //  */
  // getModelName () {
  //   return "echarts-model";
  // }

  // /**
  //  * 描述:标题信息
  //  * @returns {string}
  //  */
  // getTitle () {
  //   return "echarts"
  // }
}