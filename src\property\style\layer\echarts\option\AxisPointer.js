import LineStyle from "./style/LineStyle";
import ShadowStyle from "./style/common/ShadowStyle";
import EchartsStyle from "../EchartsStyle";
export default class AxisPointer extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);

      const typeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="type">
                  <option value="">---请选择---</option>
                  <option value="line">直线指示器</option>
                  <option value="shadow">阴影指示器</option>
                  <option value="none">无指示器</option>
              </select>
          </div>
        </div>`);
      chartBody.append(typeContainer);

      if (!item["lineStyle"]) {
        item["lineStyle"] = {};
      }
      const lineStyle = new LineStyle(this.context);
      lineStyle.initPanel(chartBody,item["lineStyle"],callback);

      if (!item["shadowStyle"]) {
        item["shadowStyle"] = {};
      }
      const shadowStyle = new ShadowStyle(this.context);
      shadowStyle.initPanel(chartBody,item["shadowStyle"],callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "axisPointer-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "轴指示器"
  }
}