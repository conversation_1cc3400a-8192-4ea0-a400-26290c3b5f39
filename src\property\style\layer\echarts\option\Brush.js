import BorderStyle from "./style/common/BorderStyle";
import EchartsStyle from "../EchartsStyle";
export default class Brush extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const  brushTypeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">刷子类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="brushType">
                  <option value="">---请选择---</option>
                  <option value="rect">矩形选框</option>
                  <option value="polygon">任意形状选框</option>
                  <option value="lineX">横向选择</option>
                  <option value="lineY">纵向选择</option>
              </select>
          </div>
        </div>`);
      chartBody.append(brushTypeContainer);

      const  brushModeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">刷子模式</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="brushMode">
                  <option value="">---请选择---</option>
                  <option value="single">单选</option>
                  <option value="multiple">多选</option>
              </select>
          </div>
        </div>`);
      chartBody.append(brushModeContainer);

      const  removeOnClickContainer = $(`<div class="chart-item flex">
          <div class="flex">
            <span class="chart-span">是否支持『单击清除所有选框』</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="removeOnClick">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(removeOnClickContainer);

      if (!item["brushStyle"]) {
        item["brushStyle"] = {};
      }
      const brushStyle = new BorderStyle(this.context);
      brushStyle.initPanel(chartBody, item["brushStyle"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "brush-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "区域选择组件"
  }
}