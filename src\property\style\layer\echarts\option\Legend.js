import TextStyle from "./TextStyle";
import ItemStyle from "./style/ItemStyle";
import LineStyle from "./style/LineStyle";
import BorderStyle from "./style/common/BorderStyle";
import ShadowStyle from "./style/common/ShadowStyle";
import GapStyle from "./style/common/GapStyle";
import ColorStyle from "./color/ColorStyle";
import EchartsStyle from "../EchartsStyle";
export default class Legend extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);
      // this.loadBackgroundColor(chartBody, modelName);
      this.loadWidth(chartBody, modelName);
      this.loadHeight(chartBody, modelName);
      this.loadPadding(chartBody, modelName);
      this.loadFormatter(chartBody, modelName);

      const typeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="type">
                  <option value="">--请选择--</option>
                  <option value="plain">普通图例</option>
                  <option value="scroll">可滚动翻页图例</option>
              </select>
          </div>
        </div>`);
      chartBody.append(typeContainer);

      //对齐/方向
      const alignContainer = $(`<div class="chart-item flex">
          <div class="chart-label">对齐</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="align">
                  <option value="">--请选择--</option>
                  <option value="auto">默认</option>
                  <option value="left">左对齐</option>
                  <option value="right">右对齐</option>
              </select>
          </div>
        </div>`);
      chartBody.append(alignContainer);
      const orientContainer = $(`<div class="chart-item flex">
          <div class="chart-label">方向</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="orient">
                  <option value="">---请选择---</option>
                  <option value="horizontal">水平</option>
                  <option value="vertical">垂直</option>
              </select>
          </div>
        </div>`);
      chartBody.append(orientContainer);

      const iconContainer = $(`<div class="chart-item flex">
          <div class="chart-label">图例形状</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="icon">
                  <option value="">请选择</option>
                  <option value="circle">圆圈</option>
                  <option value="rect">长方形</option>
                  <option value="roundRect">角矩形</option>
                  <option value="triangle">三角形</option>
                  <option value="diamond">菱形</option>
                  <option value="pin">大头针</option>
                  <option value="arrow">箭头</option>
                  <option value="none">无</option>
              </select>
          </div>
        </div>`);
      chartBody.append(iconContainer);

      const itemGapContainer = $(`<div class="chart-item flex">
          <div class="chart-label">项间距</div>
          <div class="chart-control">
                <input type="number"  min="0" class="chart-number"  `+modelName+`="itemGap" />
          </div>
        </div>`);
      chartBody.append(itemGapContainer);

      const itemWidthContainer = $(`<div class="chart-item flex">
          <div class="chart-label">图形宽度</div>
          <div class="chart-control">
                <input type="number"  min="0" class="chart-number"  `+modelName+`="itemWidth" />
          </div>
        </div>`);
      chartBody.append(itemWidthContainer);

      const itemHeightContainer = $(`<div class="chart-item flex">
          <div class="chart-label">图形高度</div>
          <div class="chart-control">
                <input type="number"  min="0" class="chart-number"  `+modelName+`="itemHeight" />
          </div>
        </div>`);
      chartBody.append(itemHeightContainer);
  
      //距离
      const gapStyle = new GapStyle(this.context);
      gapStyle.initPanel(chartBody, item, callback);
      //边框
      const borderStyle = new BorderStyle(this.context);
      borderStyle.initPanel(chartBody, item, callback);
      //阴影
      const shadowStyle = new ShadowStyle(this.context);
      shadowStyle.initPanel(chartBody, item, callback);

       //颜色
      if(!item["backgroundColor"]){
        item["backgroundColor"] ={};
      }
      const colorStyle = new ColorStyle(this.context);
      colorStyle.initPanel(chartBody,item["backgroundColor"],callback);
      colorStyle.refreshTitle("背景色");
      
      if (!item["textStyle"]) {
        item["textStyle"] = {};
      }
      const textStyle = new TextStyle(this.context);
      textStyle.initPanel(chartBody, item["textStyle"], callback);

      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      if (!item["lineStyle"]) {
        item["lineStyle"] = {};
      }
      const lineStyle = new LineStyle(this.context);
      lineStyle.initPanel(chartBody, item["lineStyle"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "legend-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "图例组件"
  }
}