import FontStyle from "./style/common/FontStyle";
import TextShadowStyle from "./style/common/TextShadowStyle";
import TextBorderStyle from "./style/common/TextBorderStyle";
import BorderStyle from "./style/common/BorderStyle";
import ShadowStyle from "./style/common/ShadowStyle";
import ColorStyle from "./color/ColorStyle";
import EchartsStyle from "../EchartsStyle";
export default class TextStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      // this.loadColor(chartBody, modelName);
      // this.loadBackgroundColor(chartBody, modelName);
      this.loadWidth(chartBody, modelName);
      this.loadHeight(chartBody, modelName);
      this.loadPadding(chartBody, modelName);

       //颜色
      if(!item["color"]){
        item["color"] ={};
      }
      const colorStyle = new ColorStyle(this.context);
      colorStyle.initPanel(chartBody,item["color"],callback);

       //颜色
       if(!item["backgroundColor"]){
        item["backgroundColor"] ={};
      }
      const backgroundColorStyle = new ColorStyle(this.context);
      backgroundColorStyle.initPanel(chartBody,item["backgroundColor"],callback);
      backgroundColorStyle.refreshTitle("背景色");

      //字体
      const fontStyle = new FontStyle(this.context);
      fontStyle.initPanel(chartBody, item, callback);
      //边框
      const borderStyle = new BorderStyle(this.context);
      borderStyle.initPanel(chartBody, item, callback);
      //阴影
      const shadowStyle = new ShadowStyle(this.context);
      shadowStyle.initPanel(chartBody, item, callback);
      //文本边框
      const textBorderStyle = new TextBorderStyle(this.context);
      textBorderStyle.initPanel(chartBody, item, callback);
      //文本阴影
      const textShadowStyle = new TextShadowStyle(this.context);
      textShadowStyle.initPanel(chartBody, item, callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "textStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "文本样式"
  }
}