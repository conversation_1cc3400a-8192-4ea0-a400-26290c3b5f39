import TextStyle from "./TextStyle";
import GapStyle from "./style/common/GapStyle";
import BorderStyle from "./style/common/BorderStyle";
import ShadowStyle from "./style/common/ShadowStyle";
import ColorStyle from "./color/ColorStyle";
import EchartsStyle from "../EchartsStyle";
export default class Title extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);
      this.loadText(chartBody, modelName);
      // this.loadBackgroundColor(chartBody, modelName);

      //间距
      const gapStyle = new GapStyle(this.context);
      gapStyle.initPanel(chartBody, item, callback);
      //边框
      const borderStyle = new BorderStyle(this.context);
      borderStyle.initPanel(chartBody, item, callback);
      //阴影
      const shadowStyle = new ShadowStyle(this.context);
      shadowStyle.initPanel(chartBody, item, callback);

      //颜色
      if(!item["backgroundColor"]){
        item["backgroundColor"] ={};
      }
      const backgroundColorStyle = new ColorStyle(this.context);
      backgroundColorStyle.initPanel(chartBody,item["backgroundColor"],callback);
      backgroundColorStyle.refreshTitle("背景色");

      //文本
      if (!item["textStyle"]) {
        item["textStyle"] = {};
      }
      const textStyle = new TextStyle(this.context);
      textStyle.initPanel(chartBody, item["textStyle"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "title-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "标题组件"
  }
}