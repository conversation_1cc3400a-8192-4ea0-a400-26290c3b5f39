import Feature from "./style/feature/Feature";
import GapStyle from "./style/common/GapStyle";
import EchartsStyle from "../EchartsStyle";
export default class Toolbox extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);
      this.loadWidth(chartBody, modelName);
      this.loadHeight(chartBody, modelName);

      const orientContainer = $(`<div class="chart-item flex">
          <div class="chart-label">布局朝向</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="orient">
                  <option value="">---请选择---</option>
                  <option value="horizontal">水平</option>
                  <option value="vertical">垂直</option>
              </select>
          </div>
        </div>`);
      chartBody.append(orientContainer);

      const itemSizeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">工具栏</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="项大小" ` + modelName + `="itemSize" />
          </div>
        </div>`);
      chartBody.append(itemSizeContainer);

      const itemGapContainer = $(`<div class="chart-item flex">
          <div class="chart-label">项间隔</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="项间隔" ` + modelName + `="itemGap" />
          </div>
        </div>`);
      chartBody.append(itemGapContainer);
      //间距
      const gapStyle = new GapStyle(this.context);
      gapStyle.initPanel(chartBody, item, callback);

      if (!item["feature"]) {
        item["feature"] = {};
      }
      const feature = new Feature(this.context);
      feature.initPanel(chartBody, item["feature"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "toolbox-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "工具栏组件"
  }
}