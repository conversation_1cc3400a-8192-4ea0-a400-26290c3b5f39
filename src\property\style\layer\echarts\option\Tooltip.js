import TextStyle from "./TextStyle";
import BorderStyle from "./style/common/BorderStyle";
import AxisPointer from "./AxisPointer";
import ColorStyle from "./color/ColorStyle";
import EchartsStyle from "../EchartsStyle";
export default class Tooltip extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);
      //触发类型
      const roseTypeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">触发类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="trigger">
                  <option value=>请选择</option>
                  <option value="item">图形触发(散点图，饼图等)</option>
                  <option value="axis">坐标触发(柱状图，折线图)</option>
                  <option value="none">不触发</option>
              </select>
          </div>
      </div>`);
      chartBody.append(roseTypeContainer);
      this.loadPadding(chartBody, modelName);
      // this.loadBackgroundColor(chartBody, modelName);

      const positionContainer = $(`<div class="chart-item flex">
      <div class="chart-label">位置</div>
        <div class="chart-control">
            <input type="text" class="chart-text" min="0" max="" placeholder="10,20" ` + modelName + `="position" />
        </div>
      </div>`);
      chartBody.append(positionContainer);

      this.loadFormatter(chartBody, modelName);

      //边框
      const borderStyle = new BorderStyle(this.context);
      borderStyle.initPanel(chartBody, item, callback);

      //颜色
      if(!item["backgroundColor"]){
        item["backgroundColor"] ={};
      }
      const backgroundColorStyle = new ColorStyle(this.context);
      backgroundColorStyle.initPanel(chartBody,item["backgroundColor"],callback);
      backgroundColorStyle.refreshTitle("背景色");

      //
      if(!item["axisPointer"]){
        item["axisPointer"] = {};
      }
      const axisPointer = new AxisPointer(this.context);
      axisPointer.initPanel(chartBody, item["axisPointer"], callback);


      if (!item["textStyle"]) {
        item["textStyle"] = {};
      }
      const textStyle = new TextStyle(this.context);
      textStyle.initPanel(chartBody, item["textStyle"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "tooltip-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "提示框组件"
  }
}