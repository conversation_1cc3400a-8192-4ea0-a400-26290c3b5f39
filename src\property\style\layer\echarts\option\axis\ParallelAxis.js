import AxisLine from "../style/AxisLine";
import AxisLabel from "../style/AxisLabel";
import AxisTick from "../style/AxisTick";
import MinorTick from "../style/MinorTick";

import AxisStyle from "../style/common/AxisStyle";

import EchartsStyle from "../../EchartsStyle";
export default class ParallelAxis extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) { 
      const inverseContainer = $(`<div class="chart-item flex">
        <div class="flex">
          <span class="chart-span">是否反向坐标轴</span>
          <label class="chart-switch">
              <input type="checkbox" class="chart-checkbox" `+ modelName + `="inverse">
              <div class="slider round"></div>
          </label>
        </div>
      </div>`);
      chartBody.append(inverseContainer);
      //是否是脱离 0 值比例
      const scaleContainer = $(`<div class="chart-item flex">
        <div class="flex">
          <span class="chart-span">是否是脱离 0 值比例</span>
          <label class="chart-switch">
              <input type="checkbox" class="chart-checkbox" `+ modelName + `="scale">
              <div class="slider round"></div>
          </label>
        </div>
      </div>`);
      chartBody.append(scaleContainer);
      //通用
      // this.loadShow(chartBody, modelName);
      this.loadName(chartBody, modelName);

      const typeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="type">
                  <option value="">---请选择---</option>
                  <option value="value">数值轴</option>
                  <option value="category">类目轴</option>
                  <option value="time">时间轴</option>
                  <option value="log">对数轴</option>
              </select>
          </div>
        </div>`);
      chartBody.append(typeContainer);

      const nameLocationContainer = $(`<div class="chart-item flex">
          <div class="chart-label">名称位置</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="nameLocation">
                  <option value="">---请选择---</option>
                  <option value="start">开始</option>
                  <option value="middle">中间</option>
                  <option value="end">末尾</option>
              </select>
          </div>
        </div>`);
      chartBody.append(nameLocationContainer);

     //轴样式
     const axisStyle = new AxisStyle(this.context);
     axisStyle.initPanel(chartBody,item,callback);

      if (!item["axisLine"]) {
        item["axisLine"] = {};
      }
      const axisLine = new AxisLine(this.context);
      axisLine.initPanel(chartBody, item["axisLine"], callback);
      
      if (!item["axisLabel"]) {
        item["axisLabel"] = {};
      }
      const axisLabel = new AxisLabel(this.context);
      axisLabel.initPanel(chartBody, item["axisLabel"], callback);

      if (!item["axisTick"]) {
        item["axisTick"] = {};
      }
      const axisTick = new AxisTick(this.context);
      axisTick.initPanel(chartBody, item["axisTick"], callback);

      if (!item["minorTick"]) {
        item["minorTick"] = {};
      }
      const minorTick = new MinorTick(this.context);
      minorTick.initPanel(chartBody, item["minorTick"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "parallelAxis-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "平行坐标轴"
  }
}