import AxisPointer from "../AxisPointer";

import AxisLine from "../style/AxisLine";
import AxisLabel from "../style/AxisLabel";
import AxisTick from "../style/AxisTick";
import MinorTick from "../style/MinorTick";
import MinorSplitLine from "../style/MinorSplitLine";
import SplitLine from "../style/SplitLine";
import SplitArea from "../style/SplitArea";

import AxisStyle from "../style/common/AxisStyle";
import GapStyle from "../style/common/GapStyle";

import EchartsStyle from "../../EchartsStyle";
export default class SingleAxis extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadName(chartBody, modelName);
      this.loadWidth(chartBody, modelName);
      this.loadHeight(chartBody, modelName);

      const nameLocationContainer = $(`<div class="chart-item flex">
          <div class="chart-label">名称位置</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="nameLocation">
                  <option value="">---请选择---</option>
                  <option value="start">开始</option>
                  <option value="middle">中间</option>
                  <option value="end">末尾</option>
              </select>
          </div>
        </div>`);
      chartBody.append(nameLocationContainer);

      const typeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="type">
                  <option value="">---请选择---</option>
                  <option value="value">数值轴</option>
                  <option value="category">类目轴</option>
                  <option value="time">时间轴</option>
                  <option value="log">对数轴</option>
              </select>
          </div>
      </div>`);
      chartBody.append(typeContainer);
      
      //间距
      const gapStyle = new GapStyle(this.context);
      gapStyle.initPanel(chartBody, item, callback);

      //轴样式
      const axisStyle = new AxisStyle(this.context);
      axisStyle.initPanel(chartBody, item, callback);

      if (!item["axisLine"]) {
        item["axisLine"] = {};
      }
      const axisLine = new AxisLine(this.context);
      axisLine.initPanel(chartBody, item["axisLine"], callback);

      if (!item["axisLabel"]) {
        item["axisLabel"] = {};
      }
      const axisLabel = new AxisLabel(this.context);
      axisLabel.initPanel(chartBody, item["axisLabel"], callback);

      if (!item["axisTick"]) {
        item["axisTick"] = {};
      }
      const axisTick = new AxisTick(this.context);
      axisTick.initPanel(chartBody, item["axisTick"], callback);

      if (!item["minorTick"]) {
        item["minorTick"] = {};
      }
      const minorTick = new MinorTick(this.context);
      minorTick.initPanel(chartBody, item["minorTick"], callback);

      if (!item["minorSplitLine"]) {
        item["minorSplitLine"] = {};
      }
      const minorSplitLine = new MinorSplitLine(this.context);
      minorSplitLine.initPanel(chartBody, item["minorSplitLine"], callback);

      if (!item["splitLine"]) {
        item["splitLine"] = {};
      }
      const splitLine = new SplitLine(this.context);
      splitLine.initPanel(chartBody, item["splitLine"], callback);

      if (!item["splitArea"]) {
        item["splitArea"] = {};
      }
      const splitArea = new SplitArea(this.context);
      splitArea.initPanel(chartBody, item["splitArea"], callback);

      if (!item["axisPointer"]) {
        item["axisPointer"] = {};
      }
      const axisPointer = new AxisPointer(this.context);
      axisPointer.initPanel(chartBody, item["axisPointer"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "singleAxis-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "单轴"
  }
}