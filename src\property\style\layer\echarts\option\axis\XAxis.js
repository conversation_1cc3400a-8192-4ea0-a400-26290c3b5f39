import AxisLine from "../style/AxisLine";
import AxisLabel from "../style/AxisLabel";
import AxisTick from "../style/AxisTick";
import MinorTick from "../style/MinorTick";
import MinorSplitLine from "../style/MinorSplitLine";
import SplitLine from "../style/SplitLine";
import SplitArea from "../style/SplitArea";
import AxisPointer from "../AxisPointer";

import AxisStyle from "../style/common/AxisStyle";

import EchartsStyle from "../../EchartsStyle";
export default class XAxis extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      const inverseContainer = $(`<div class="chart-item flex">
        <div class="flex">
          <span class="chart-span">是否反向坐标轴</span>
          <label class="chart-switch">
              <input type="checkbox" class="chart-checkbox" `+ modelName + `="inverse">
              <div class="slider round"></div>
          </label>
        </div>
      </div>`);
      chartBody.append(inverseContainer);
      //通用
      this.loadShow(chartBody, modelName);
      this.loadName(chartBody, modelName);

      // const typeContainer = $(`<div class="chart-item flex">
      //     <div class="chart-label">类型</div>
      //     <div class="chart-control">
      //         <select class="chart-select" `+ modelName + `="type">
      //             <option value="">---请选择---</option>
      //             <option value="value">数值轴</option>
      //             <option value="category">类目轴</option>
      //             <option value="time">时间轴</option>
      //             <option value="log">对数轴</option>
      //         </select>
      //     </div>
      //  </div>`);
      // chartBody.append(typeContainer);

      const positionContainer = $(`<div class="chart-item flex">
          <div class="chart-label">位置</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="position">
                  <option value="">---请选择---</option>
                  <option value="top">顶部</option>
                  <option value="bottom">底部</option>
              </select>
          </div>
        </div>`);
      chartBody.append(positionContainer);

      const offsetContainer = $(`<div class="chart-item flex">
        <div class="chart-label">偏移量</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="偏移量" ` + modelName + `="offset" />
          </div>
        </div>`);
      chartBody.append(offsetContainer);

      const nameLocationContainer = $(`<div class="chart-item flex">
          <div class="chart-label">名称位置</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="nameLocation">
                  <option value="">---请选择---</option>
                  <option value="start">开始</option>
                  <option value="middle">中间</option>
                  <option value="end">末尾</option>
              </select>
          </div>
        </div>`);
      chartBody.append(nameLocationContainer);

      const nameGapContainer = $(`<div class="chart-item flex">
        <div class="chart-label">轴线间距</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="名称与轴线" ` + modelName + `="nameGap" />
          </div>
          </div>`);
      chartBody.append(nameGapContainer);

      const rotateContainer = $(`<div class="chart-item flex">
          <div class="chart-label">名称旋转</div>
            <div class="chart-control">
                <input type="number" class="chart-number" min="0" max="" placeholder="角度值" ` + modelName + `="nameRotate" />
            </div>
          </div>`);
      chartBody.append(rotateContainer);

       //轴样式
       const axisStyle=new AxisStyle(this.context);
       axisStyle.initPanel(chartBody,item,callback);

      if (!item["axisLine"]) {
        item["axisLine"] = {};
      }
      const axisLine = new AxisLine(this.context);
      axisLine.initPanel(chartBody, item["axisLine"], callback);
      
      if (!item["axisLabel"]) {
        item["axisLabel"] = {};
      }
      const axisLabel = new AxisLabel(this.context);
      axisLabel.initPanel(chartBody, item["axisLabel"], callback);

      if (!item["axisTick"]) {
        item["axisTick"] = {};
      }
      const axisTick = new AxisTick(this.context);
      axisTick.initPanel(chartBody, item["axisTick"], callback);

      if (!item["minorTick"]) {
        item["minorTick"] = {};
      }
      const minorTick = new MinorTick(this.context);
      minorTick.initPanel(chartBody, item["minorTick"], callback);

      if (!item["minorSplitLine"]) {
        item["minorSplitLine"] = {};
      }
      const minorSplitLine = new MinorSplitLine(this.context);
      minorSplitLine.initPanel(chartBody, item["minorSplitLine"], callback);
      
      if (!item["splitLine"]) {
        item["splitLine"] = {};
      }
      const splitLine = new SplitLine(this.context);
      splitLine.initPanel(chartBody, item["splitLine"], callback);

      if (!item["splitArea"]) {
        item["splitArea"] = {};
      }
      const splitArea = new SplitArea(this.context);
      splitArea.initPanel(chartBody, item["splitArea"], callback);
      
      if (!item["axisPointer"]) {
        item["axisPointer"] ={};
      }
      const axisPointer = new AxisPointer(this.context);
      axisPointer.initPanel(chartBody,item["axisPointer"],callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "XAxis-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "x 轴"
  }
}