import YAxis from "./YAxis";
import EchartsStyle from "../../EchartsStyle";
export default class YAxisList extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  afterAddItem (itemContainer, item, index, callback) {
    const model = new YAxis(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("Y轴[" + (index + 1) + "]配置");
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "Y轴列表"
  }
}
