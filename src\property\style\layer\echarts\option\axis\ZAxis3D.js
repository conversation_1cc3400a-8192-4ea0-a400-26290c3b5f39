import AxisLine from "../style/AxisLine";
import AxisLabel from "../style/AxisLabel";
import AxisTick from "../style/AxisTick";
import MinorTick from "../style/MinorTick";
import MinorSplitLine from "../style/MinorSplitLine";
import SplitLine from "../style/SplitLine";
import SplitArea from "../style/SplitArea";
import AxisPointer from "../AxisPointer";

import AxisStyle from "../style/common/AxisStyle";

import EchartsStyle from "../../EchartsStyle";
export default class ZAxis3D extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);
      this.loadName(chartBody, modelName);

      const nameGapContainer = $(`<div class="chart-item flex">
        <div class="chart-label">轴线间距</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="名称与轴线" ` + modelName + `="nameGap" />
          </div>
          </div>`);
      chartBody.append(nameGapContainer);

       //轴样式
       const axisStyle=new AxisStyle(this.context);
       axisStyle.initPanel(chartBody,item,callback);

      if (!item["axisLine"]) {
        item["axisLine"] = {};
      }
      const axisLine = new AxisLine(this.context);
      axisLine.initPanel(chartBody, item["axisLine"], callback);
      
      if (!item["axisLabel"]) {
        item["axisLabel"] = {};
      }
      const axisLabel = new AxisLabel(this.context);
      axisLabel.initPanel(chartBody, item["axisLabel"], callback);

      if (!item["axisTick"]) {
        item["axisTick"] = {};
      }
      const axisTick = new AxisTick(this.context);
      axisTick.initPanel(chartBody, item["axisTick"], callback);
      
      if (!item["splitLine"]) {
        item["splitLine"] = {};
      }
      const splitLine = new SplitLine(this.context);
      splitLine.initPanel(chartBody, item["splitLine"], callback);

      if (!item["splitArea"]) {
        item["splitArea"] = {};
      }
      const splitArea = new SplitArea(this.context);
      splitArea.initPanel(chartBody, item["splitArea"], callback);
      
      if (!item["axisPointer"]) {
        item["axisPointer"] ={};
      }
      const axisPointer = new AxisPointer(this.context);
      axisPointer.initPanel(chartBody,item["axisPointer"],callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "ZAxis3D-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "z 轴"
  }
}