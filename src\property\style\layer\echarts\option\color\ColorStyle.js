import Color from "./impl/Color";
import ListColor from "./impl/ListColor";
import GradientColor from "./impl/GradientColor";
import LinearColor from "./impl/LinearColor";
import RadialColor from "./impl/RadialColor";
import EchartsStyle from "../../EchartsStyle";
export default class ColorStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const typeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">类型</div>
        <div class="chart-control">
            <select class="chart-select" `+ modelName + `="type" >
                <option value="">--请选择--</option>
                <option value="color">简单颜色</option>
                <option value="list">颜色列表</option>
                <option value="gradient">简单渐变</option>
                <option value="linear">线性渐变</option>
                <option value="radial">径向渐变</option>
            </select>
        </div>
      </div>`);
      chartBody.append(typeContainer);

      this.colorContainer = $(`<div></div>`);
      this.listColorContainer = $(`<div></div>`);
      this.gradientColorContainer = $(`<div></div>`);
      this.linearColorContainer = $(`<div></div>`);
      this.radialColorContainer = $(`<div></div>`);
      chartBody.append(this.colorContainer);
      chartBody.append(this.listColorContainer);
      chartBody.append(this.gradientColorContainer);
      chartBody.append(this.linearColorContainer);
      chartBody.append(this.radialColorContainer);

      if(!item.color){
        item.color = 'red';
      }
      const color = new Color(this.context,true);
      color.initPanel(this.colorContainer,item, callback);

      if (!item.list) {
        item.list = [{color:'#5470c6'}, {color:'#91cc75'}, {color:'#fac858'},  {color:'#ee6666'}];
      }
      const listColor = new ListColor(this.context,true);
      listColor.initPanel(this.listColorContainer,item.list, callback);

      if (!item.gradient) {
        item.gradient = {type:"linear", color00: 'red', color01: 'blue' };
      }
      const gradientColor = new GradientColor(this.context,true);
      gradientColor.initPanel(this.gradientColorContainer,item.gradient, callback);

      if (!item.linear) {
        item.linear = {
          type: "linear", x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(0, 97, 226, 0.2)' }, // 0% 处的颜色
            { offset: 1, color: 'rgba(0, 213, 255, 0.9)' },// 100% 处的颜色
          ],
          global: false // 缺省为 false
        };
      }
      const linearColor = new LinearColor(this.context,true);
      linearColor.initPanel(this.linearColorContainer,item.linear, callback);

      if (!item.radial) {
        item.radial = {
          type: "radial", x: 0.5, y: 0.5, r: 0.5,
          colorStops: [
            { offset: 0, color: 'red' },// 0% 处的颜色
            { offset: 1, color: 'blue' },// 100% 处的颜色
          ],
          global: false // 缺省为 false
        };
      }
      const radialColor = new RadialColor(this.context,true);
      radialColor.initPanel(this.radialColorContainer,item.radial, callback);


      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    if (key && key === "type") {
      if (value === "color") {
        this.colorContainer.show();
        this.listColorContainer.hide();
        this.gradientColorContainer.hide();
        this.linearColorContainer.hide();
        this.radialColorContainer.hide();
      } else if (value === "list") {
        this.colorContainer.hide();
        this.listColorContainer.show();
        this.gradientColorContainer.hide();
        this.linearColorContainer.hide();
        this.radialColorContainer.hide();
      } else if (value === "gradient") {
        this.colorContainer.hide();
        this.listColorContainer.hide();
        this.gradientColorContainer.show();
        this.linearColorContainer.hide();
        this.radialColorContainer.hide();
      } else if (value === "linear") {
        this.colorContainer.hide();
        this.listColorContainer.hide();
        this.gradientColorContainer.hide();
        this.linearColorContainer.show();
        this.radialColorContainer.hide();
      } else if (value === "radial") {
        this.colorContainer.hide();
        this.listColorContainer.hide();
        this.gradientColorContainer.hide();
        this.linearColorContainer.hide();
        this.radialColorContainer.show();
      } else {
        // this.colorContainer.show();
        this.colorContainer.hide();
        this.listColorContainer.hide();
        this.gradientColorContainer.hide();
        this.linearColorContainer.hide();
        this.radialColorContainer.hide();
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "colorStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "颜色"
  }
}