import EchartsStyle from "../../../EchartsStyle";
export default class Color extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      const colorContainer = $(`<div class="chart-item flex">
        <div class="chart-label">颜色</div>
        <div class="chart-control">
          <input type="text" class="chart-color"` + modelName + `="color" />
        </div>
      </div>`);
      chartBody.append(colorContainer);
      
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "color-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "颜色"
  }
}