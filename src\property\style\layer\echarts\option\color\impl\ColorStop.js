import EchartsStyle from "../../../EchartsStyle";
export default class ColorStop extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const offset_colorContainer = $(`<div class="chart-item flex">
          <div class="w-50 pr5 flex">
            <div class="chart-label">比例</div>
            <div class="chart-control">
              <input type="number" class="chart-number" placeholder="[0-1]" ` + modelName + `="offset" />
            </div>
          </div>
          <div class="w-50 pl5 flex">
            <div class="chart-label">颜色</div>
            <div class="chart-control">
              <input type="text" class="chart-color"` + modelName + `="color" />
            </div>
          </div>
      </div>`);
      chartBody.append(offset_colorContainer);
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "colorStop-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "停止"
  }
}