import EchartsStyle from "../../../EchartsStyle";
export default class GradientColor extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      const typeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">类型</div>
        <div class="chart-control">
            <select class="chart-select" `+ modelName + `="type" >
                <option value="">--请选择--</option>
                <option value="linear">线性渐变</option>
                <option value="radial">径向渐变</option>
            </select>
        </div>
      </div>`);
      chartBody.append(typeContainer);

      const colorContainer = $(`<div class="chart-item flex">
        <div class="chart-label">颜色</div>
        <div class="chart-control flex">
          <input type="text" class="chart-color"  `+ modelName + `="color00"  />
          <div class="text-center">至</div>
          <input type="text" class="chart-color"  `+ modelName + `="color01"  />
        </div>
      </div>`);
      chartBody.append(colorContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "gradientColor-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "简单渐变"
  }
}