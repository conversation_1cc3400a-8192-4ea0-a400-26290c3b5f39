import ColorStops from "./ColorStops";
import EchartsStyle from "../../../EchartsStyle";
export default class LinearColor extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      //   const globalContainer = $(`<div class="chart-item flex">
      //   <input class="chart-checkbox" type="checkbox" `+modelName+`="global" />是否全局
      // </div>`);
      // chartBody.append(globalContainer);
      //   const xyContainer = $(`<div class="chart-item flex">
      //   <div class="chart-control flex">
      //     <div class="chart-label">x: </div>
      //       <input type="number" class="chart-number" placeholder="x[0-1]" ` + modelName + `="x" />
      //       <div class="chart-label">y: </div>
      //       <input type="number" class="chart-number" placeholder="y[0-1]" ` + modelName + `="y" />
      //     </div>
      // </div>`);
      //   chartBody.append(xyContainer);
      //   const xy2Container = $(`<div class="chart-item flex">
      //     <div class="chart-control flex">
      //       <div class="chart-label">x2: </div>
      //       <input type="number" class="chart-number" placeholder="x2[0-1]" ` + modelName + `="x2" />
      //       <div class="chart-label">y2: </div>
      //       <input type="number" class="chart-number" placeholder="y2[0-1]" ` + modelName + `="y2" />
      //     </div>
      //   </div>`);
      //   chartBody.append(xy2Container);
      const globalContainer = $(`<div class="chart-item flex">
        <div class="flex">
          <span class="chart-span">是否全局</span>
          <label class="chart-switch">
              <input type="checkbox" class="chart-checkbox" `+ modelName + `="global">
              <div class="slider round"></div>
          </label>
        </div>
      </div>`);
      chartBody.append(globalContainer);

      const xyContainer = $(`<div class="chart-item flex">
                <div class="w-50 pr5 flex">
                    <div class="chart-label">x</div>
                    <div class="chart-control">
                        <input type="number" class="chart-number" min="0"  placeholder="[0-1]" ` + modelName + `="x" />
                    </div>
                </div>
                <div class="w-50 pl5 flex">
                    <div class="chart-label">y</div>
                    <div class="chart-control">
                        <input type="number" class="chart-number" min="0" placeholder="[0-1]" ` + modelName + `="y" />
                    </div>
                </div>
            </div>`);
      chartBody.append(xyContainer);
      const xy2Container = $(`<div class="chart-item flex">
                <div class="w-50 pr5 flex">
                    <div class="chart-label">x2</div>
                    <div class="chart-control">
                        <input type="number" class="chart-number" min="0"  placeholder="[0-1]" ` + modelName + `="x2" />
                    </div>
                </div>
                <div class="w-50 pl5 flex">
                    <div class="chart-label">y2</div>
                    <div class="chart-control">
                        <input type="number" class="chart-number" min="0" placeholder="[0-1]" ` + modelName + `="y2" />
                    </div>
                </div>
            </div>`);
      chartBody.append(xy2Container);

      if (!item.colorStops) {
        item.colorStops = [];
      }
      const colorStops = new ColorStops(this.context,true);
      colorStops.initPanel(chartBody, item.colorStops, callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "linearColor-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "线性渐变"
  }
}