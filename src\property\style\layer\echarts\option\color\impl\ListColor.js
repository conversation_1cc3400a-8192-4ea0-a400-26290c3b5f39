import Color from "./Color";
import EchartsStyle from "../../../EchartsStyle";
export default class ListColor extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  afterAddItem (itemContainer, item, index, callback) {
    const model = new Color(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("颜色[" + (index + 1) + "]配置");
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "颜色列表"
  }
}