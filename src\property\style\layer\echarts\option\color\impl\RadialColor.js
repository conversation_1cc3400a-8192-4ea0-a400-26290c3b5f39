import ColorStops from "./ColorStops";
import EchartsStyle from "../../../EchartsStyle";
export default class RadialColor extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const globalContainer = $(`<div class="chart-item flex">
        <div class="flex">
          <span class="chart-span">是否全局</span>
          <label class="chart-switch">
              <input type="checkbox" class="chart-checkbox" `+ modelName + `="global">
              <div class="slider round"></div>
          </label>
        </div>
      </div>`);
      chartBody.append(globalContainer);

      const xyContainer = $(`<div class="chart-item flex">
          <div class="w-50 pr5 flex">
              <div class="chart-label">x</div>
              <div class="chart-control">
                  <input type="number" class="chart-number" min="0"  placeholder="[0-1]" ` + modelName + `="x" />
              </div>
          </div>
          <div class="w-50 pl5 flex">
              <div class="chart-label">y</div>
              <div class="chart-control">
                  <input type="number" class="chart-number" min="0" placeholder="[0-1]" ` + modelName + `="y" />
              </div>
          </div>
      </div>`);
      chartBody.append(xyContainer);
      const xy2Container = $(`<div class="chart-item flex">
          <div class="w-50 pr5 flex">
              <div class="chart-label">r</div>
              <div class="chart-control">
                  <input type="number" class="chart-number" min="0"  placeholder="[0-1]" ` + modelName + `="r" />
              </div>
          </div>
          <div class="w-50 pl5 flex">
              
          </div>
      </div>`);
      chartBody.append(xy2Container);

      if (!item.colorStops) {
        item.colorStops = [];
      }
      const colorStops = new ColorStops(this.context,true);
      colorStops.initPanel(chartBody, item.colorStops, callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "radialColor-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "径向渐变"
  }
}