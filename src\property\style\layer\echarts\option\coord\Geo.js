// import Tooltip from "../Tooltip";
import ItemStyle from "../style/ItemStyle";
import GapStyle from "../style/common/GapStyle";
import Label from "../style/Label";
import Emphasis from "../style/Emphasis";
import EchartsStyle from "../../EchartsStyle";
export default class Geo extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);

       //间距
       const gapStyle=new GapStyle(this.context);
       gapStyle.initPanel(chartBody,item,callback);
       
       if (!item["label"]) {
         item["label"] = {};
       }
       const label = new Label(this.context);
       label.initPanel(chartBody, item["label"], callback);
       
       if (!item["itemStyle"]) {
         item["itemStyle"] = {};
       }
       const itemStyle = new ItemStyle(this.context);
       itemStyle.initPanel(chartBody, item["itemStyle"], callback);
       itemStyle.setIsGeo(true)
 
       if (!item["emphasis"]) {
         item["emphasis"] = {};
       }
       const emphasis = new Emphasis(this.context);
       emphasis.initPanel(chartBody, item["emphasis"], callback);
 

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "geo-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "地理坐标系"
  }
}