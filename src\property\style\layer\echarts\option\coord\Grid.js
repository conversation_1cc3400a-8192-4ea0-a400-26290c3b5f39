import Tooltip from "../Tooltip";
import BorderStyle from "../style/common/BorderStyle";
import ShadowStyle from "../style/common/ShadowStyle";
import GapStyle from "../style/common/GapStyle";
import ColorStyle from "../color/ColorStyle";
// import CommonStyle from "../style/common/CommonStyle";
import EchartsStyle from "../../EchartsStyle";
export default class Grid extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);
      // this.loadBackgroundColor(chartBody, modelName);
      this.loadWidth(chartBody, modelName);
      this.loadHeight(chartBody, modelName);
      //间距
      const gapStyle = new GapStyle(this.context);
      gapStyle.initPanel(chartBody, item, callback);
      //边框
      const borderStyle = new BorderStyle(this.context);
      borderStyle.initPanel(chartBody, item, callback);
      //阴影
      const shadowStyle = new ShadowStyle(this.context);
      shadowStyle.initPanel(chartBody, item, callback);

      //颜色
      if(!item["backgroundColor"]){
        item["backgroundColor"] ={};
      }
      const backgroundColorStyle = new ColorStyle(this.context);
      backgroundColorStyle.initPanel(chartBody,item["backgroundColor"],callback);
      backgroundColorStyle.refreshTitle("背景色");

      if (!item["tooltip"]) {
        item["tooltip"] = {};
      }
      const tooltip = new Tooltip(this.context);
      tooltip.initPanel(chartBody, item["tooltip"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "grid-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "直角坐标系"
  }
}