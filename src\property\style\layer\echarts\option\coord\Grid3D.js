import GapStyle from "../style/common/GapStyle";
import EchartsStyle from "../../EchartsStyle";
import AxisLine from "../style/AxisLine";
import AxisLabel from "../style/AxisLabel";
import AxisTick from "../style/AxisTick";
import SplitLine from "../style/SplitLine";
import SplitArea from "../style/SplitArea";
import AxisPointer from "../AxisPointer";
import ViewControl from "./ViewControl";
export default class Grid3D extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);
      this.loadWidth(chartBody, modelName);
      this.loadHeight(chartBody, modelName);
      const boxWidthContainer = $(`<div class="chart-item flex">
      <div class="chart-label">三维宽度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="三维宽度" ` + modelName + `="boxWidth" />
        </div>
      </div>`);
      chartBody.append(boxWidthContainer);
      const boxHeightContainer = $(`<div class="chart-item flex">
      <div class="chart-label">三维高度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="三维高度" ` + modelName + `="boxHeigth" />
        </div>
      </div>`);
      chartBody.append(boxHeightContainer);
      const boxDepthContainer = $(`<div class="chart-item flex">
      <div class="chart-label">三维深度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="三维深度" ` + modelName + `="boxDepth" />
        </div>
      </div>`);
      chartBody.append(boxDepthContainer);
      //间距
      const gapStyle = new GapStyle(this.context);
      gapStyle.initPanel(chartBody, item, callback);

      if (!item["axisLine"]) {
        item["axisLine"] = {};
      }
      const axisLine = new AxisLine(this.context);
      axisLine.initPanel(chartBody, item["axisLine"], callback);

      if (!item["axisLabel"]) {
        item["axisLabel"] = {};
      }
      const axisLabel = new AxisLabel(this.context);
      axisLabel.initPanel(chartBody, item["axisLabel"], callback);

      if (!item["axisTick"]) {
        item["axisTick"] = {};
      }
      const axisTick = new AxisTick(this.context);
      axisTick.initPanel(chartBody, item["axisTick"], callback);

      if (!item["splitLine"]) {
        item["splitLine"] = {};
      }
      const splitLine = new SplitLine(this.context);
      splitLine.initPanel(chartBody, item["splitLine"], callback);

      if (!item["splitArea"]) {
        item["splitArea"] = {};
      }
      const splitArea = new SplitArea(this.context);
      splitArea.initPanel(chartBody, item["splitArea"], callback);

      if (!item["axisPointer"]) {
        item["axisPointer"] ={};
      }
      const axisPointer = new AxisPointer(this.context);
      axisPointer.initPanel(chartBody,item["axisPointer"],callback);

      if (!item["viewControl"]) {
        item["viewControl"] ={};
      }
      const viewControlStyle = new ViewControl(this.context);
      viewControlStyle.initPanel(chartBody,item["viewControl"],callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "grid3d-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "3D直角坐标系"
  }
}