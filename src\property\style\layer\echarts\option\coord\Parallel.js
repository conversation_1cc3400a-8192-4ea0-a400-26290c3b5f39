import GapStyle from "../style/common/GapStyle";
// import CommonStyle from "../style/common/CommonStyle";
import EchartsStyle from "../../EchartsStyle";
export default class Parallel extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const axisExpandableContainer = $(`<div class="chart-item flex">
          <div class="flex">
            <span class="chart-span">是否展开折叠</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="axisExpandable">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(axisExpandableContainer);

      const layoutContainer = $(`<div class="chart-item flex">
          <div class="chart-label">布局方式</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="layout">
                  <option value="">---请选择---</option>
                  <option value="horizontal">水平排布</option>
                  <option value="vertical">竖直排布</option>
              </select>
          </div>
        </div>`);
      chartBody.append(layoutContainer);

      const axisExpandCountContainer = $(`<div class="chart-item flex">
          <div class="chart-label">轴展开数</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="轴展开数" ` + modelName + `="axisExpandCount" />
          </div>
        </div>`);
      chartBody.append(axisExpandCountContainer);

      const axisExpandWidthContainer = $(`<div class="chart-item flex">
          <div class="chart-label">轴间距</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="轴间距" ` + modelName + `="axisExpandWidth" />
          </div>
        </div>`);
      chartBody.append(axisExpandWidthContainer);

      //通用
      // const commonStyle=new CommonStyle(this.context);
      // commonStyle.initPanel(chartBody,item,callback);
      // commonStyle.loadStyle(["width","height"]);
      this.loadWidth(chartBody,modelName);
      this.loadHeight(chartBody,modelName);
       //间距
       const gapStyle=new GapStyle(this.context);
       gapStyle.initPanel(chartBody,item,callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "parallel-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "平行坐标系"
  }
}