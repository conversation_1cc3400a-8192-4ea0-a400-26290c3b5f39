import Tooltip from "../Tooltip";
import EchartsStyle from "../../EchartsStyle";
export default class Polar extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const centerContainer = $(`<div class="chart-item flex">
        <div class="chart-label">圆心</div>
        <div class="chart-control">
            <input type="text" class="chart-text" min="0" max="" placeholder="50%,50%" ` + modelName + `="center" />
        </div>
      </div>`);
      chartBody.append(centerContainer);
      const radiusCountContainer = $(`<div class="chart-item flex">
          <div class="chart-label">半径</div>
          <div class="chart-control">
          <input type="text" class="chart-text" min="0" max="" placeholder="40%,50%" ` + modelName + `="radius" />
          </div>
        </div>`);
      chartBody.append(radiusCountContainer);

      if (!item["tooltip"]) {
        item["tooltip"] = {};
      }
      const tooltip = new Tooltip(this.context);
      tooltip.initPanel(chartBody, item["tooltip"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "polar-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "极坐标系"
  }
}