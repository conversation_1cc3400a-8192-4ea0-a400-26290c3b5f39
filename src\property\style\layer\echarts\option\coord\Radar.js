import AxisLine from "../style/AxisLine";
import AxisLabel from "../style/AxisLabel";
import AxisTick from "../style/AxisTick";
import SplitArea from "../style/SplitArea";
import SplitLine from "../style/SplitLine";
import EchartsStyle from "../../EchartsStyle";
export default class Radar extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const scaleContainer = $(`<div class="chart-item flex">
        <div class="flex">
          <span class="chart-span">是否脱离 0 值比例</span>
          <label class="chart-switch">
              <input type="checkbox" class="chart-checkbox" `+ modelName + `="scale">
              <div class="slider round"></div>
          </label>
        </div>`);
      chartBody.append(scaleContainer);

      const silentContainer = $(`<div class="chart-item flex">
        <div class="flex">
          <span class="chart-span">是否静态无法交互</span>
          <label class="chart-switch">
              <input type="checkbox" class="chart-checkbox" `+ modelName + `="silent">
              <div class="slider round"></div>
          </label>
        </div>`);
      chartBody.append(silentContainer);

      const startAngleContainer = $(`<div class="chart-item flex">
        <div class="chart-label">起始角度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="起始角度" ` + modelName + `="startAngle" />
          </div>
        </div>`);
      chartBody.append(startAngleContainer);
      
      const nameGapContainer = $(`<div class="chart-item flex">
          <div class="chart-label">轴线间距</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="名称与轴线间距" ` + modelName + `="nameGap" />
          </div>
        </div>`);
      chartBody.append(nameGapContainer);

      const splitNumberContainer = $(`<div class="chart-item flex">
          <div class="chart-label">分割段数</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="分割段数" ` + modelName + `="splitNumber" />
          </div>
        </div>`);
      chartBody.append(splitNumberContainer);

      if (!item["axisLine"]) {
        item["axisLine"] = {};
      }
      const axisLine = new AxisLine(this.context);
      axisLine.initPanel(chartBody, item["axisLine"], callback);
      
      if (!item["axisLabel"]) {
        item["axisLabel"] = {};
      }
      const axisLabel = new AxisLabel(this.context);
      axisLabel.initPanel(chartBody, item["axisLabel"], callback);

      if (!item["axisTick"]) {
        item["axisTick"] = {};
      }
      const axisTick = new AxisTick(this.context);
      axisTick.initPanel(chartBody, item["axisTick"], callback);

      if (!item["splitArea"]) {
        item["splitArea"] = {};
      }
      const splitArea = new SplitArea(this.context);
      splitArea.initPanel(chartBody, item["splitArea"], callback);

      if (!item["splitLine"]) {
        item["splitLine"] = {};
      }
      const splitLine = new SplitLine(this.context);
      splitLine.initPanel(chartBody, item["splitLine"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "radar-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "雷达坐标系"
  }
}