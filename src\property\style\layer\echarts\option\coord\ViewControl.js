import EchartsStyle from "../../EchartsStyle";

export default class ViewControl extends EchartsStyle{
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // projection 投影方式，默认为透视投影'perspective'，也支持设置为正交投影'orthographic'。
      const projectionContainer = $(`<div class="chart-item flex">
          <div class="chart-label">投影方式</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="projection">
                  <option value="">--请选择--</option>
                  <option value="perspective">透视投影</option>
                  <option value="orthographic">正交投影</option>
              </select>
          </div>
      </div>`);
      chartBody.append(projectionContainer);
      // autoRotate 是否开启视角绕物体的自动旋转查看。
      const autoRotateContainer = $(`<div class="chart-item flex">
        <div class="flex">
          <span class="chart-span">是否自动旋转</span>
          <label class="chart-switch">
              <input type="checkbox" class="chart-checkbox" `+ modelName + `="autoRotate">
              <div class="slider round"></div>
          </label>
        </div>
      </div>`);
      chartBody.append(autoRotateContainer);
      // autoRotateDirection 物体自转的方向 cw顺时针 ccw逆时针
      const autoRotateDirectionContainer = $(`<div class="chart-item flex">
          <div class="chart-label">自转方向</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="projection">
                  <option value="">--请选择--</option>
                  <option value="cw">顺时针</option>
                  <option value="ccw">逆时针</option>
              </select>
          </div>
      </div>`);
      chartBody.append(autoRotateDirectionContainer);
      // autoRotateSpeed 物体自转的速度。单位为角度 / 秒，默认为10 ，也就是36秒转一圈。
      const autoRotateSpeedContainer = $(`<div class="chart-item flex">
        <div class="chart-label">自转速度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="自转速度" ` + modelName + `="autoRotateSpeed" />
          </div>
        </div>`);
      chartBody.append(autoRotateSpeedContainer);
      // autoRotateAfterStill 在鼠标静止操作后恢复自动旋转的时间间隔。
      const autoRotateAfterStillContainer = $(`<div class="chart-item flex">
        <div class="chart-label">恢复自转间隔</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="恢复自转间隔" ` + modelName + `="autoRotateAfterStill" />
          </div>
        </div>`);
      chartBody.append(autoRotateAfterStillContainer);
      // alpha x轴倾斜角度
      const alphaContainer = $(`<div class="chart-item flex">
        <div class="chart-label">x轴倾斜角度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="x轴倾斜角度" ` + modelName + `="alpha" />
          </div>
        </div>`);
      chartBody.append(alphaContainer);
      // beta y轴倾斜角度
      const betaContainer = $(`<div class="chart-item flex">
        <div class="chart-label">y轴倾斜角度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="y轴倾斜角度" ` + modelName + `="beta" />
          </div>
        </div>`);
      chartBody.append(betaContainer);
      // minAlpha x轴倾斜角度
      const minAlphaContainer = $(`<div class="chart-item flex">
        <div class="chart-label">x轴最小倾斜角度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="x轴最小倾斜角度" ` + modelName + `="minAlpha" />
          </div>
        </div>`);
      chartBody.append(minAlphaContainer);
      // maxAlpha y轴倾斜角度
      const maxAlphaContainer = $(`<div class="chart-item flex">
        <div class="chart-label">x轴最大倾斜角度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="x轴最大倾斜角度" ` + modelName + `="maxAlpha" />
          </div>
        </div>`);
      chartBody.append(maxAlphaContainer);
      // minBeta y轴最小倾斜角度
      const minBetaContainer = $(`<div class="chart-item flex">
        <div class="chart-label">y轴最小倾斜角度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="y轴最小倾斜角度" ` + modelName + `="minBeta" />
          </div>
        </div>`);
      chartBody.append(minBetaContainer);
      // maxBeta y轴最大倾斜角度
      const maxBetaContainer = $(`<div class="chart-item flex">
        <div class="chart-label">y轴最大倾斜角度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="y轴最大倾斜角度" ` + modelName + `="maxBeta" />
          </div>
        </div>`);
      chartBody.append(maxBetaContainer);
      // distance 默认视角距离主体的距离
      const distanceContainer = $(`<div class="chart-item flex">
        <div class="chart-label">视角距离</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="视角距离" ` + modelName + `="distance" />
          </div>
        </div>`);
      chartBody.append(distanceContainer);
      // minDistance 视角通过鼠标控制能拉近到主体的最小距离
      const minDistanceContainer = $(`<div class="chart-item flex">
        <div class="chart-label">视角距离</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="最小视角距离" ` + modelName + `="minDistance" />
          </div>
        </div>`);
      chartBody.append(minDistanceContainer);
      // maxDistance 视角通过鼠标控制能拉远到主体的最大距离
      const maxDistanceContainer = $(`<div class="chart-item flex">
        <div class="chart-label">视角距离</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="最大视角距离" ` + modelName + `="maxDistance" />
          </div>
        </div>`);
      chartBody.append(maxDistanceContainer);
      // orthographicSize 正交投影的大小。在 projection 为'orthographic'的时候有效。
      const orthographicSizeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">正交投影大小</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="正交投影大小" ` + modelName + `="orthographicSize" />
          </div>
        </div>`);
      chartBody.append(orthographicSizeContainer);
      // minOrthographicSize 正交投影缩放的最小值。在 projection 为'orthographic'的时候有效。
      const minOrthographicSizeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">最小正交投影大小</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="最小正交投影大小" ` + modelName + `="minOrthographicSize" />
          </div>
        </div>`);
      chartBody.append(minOrthographicSizeContainer);
      // maxOrthographicSize 正交投影缩放的最大值。在 projection 为'orthographic'的时候有效。
      const maxOrthographicSizeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">最大正交投影大小</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="最大正交投影大小" ` + modelName + `="maxOrthographicSize" />
          </div>
        </div>`);
      chartBody.append(maxOrthographicSizeContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "grid3d-view-control-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "视图控制"
  }
}