import SymbolStyle from "../style/common/SymbolStyle";

import Label from "../style/Label";
import ItemStyle from "../style/ItemStyle";
import BackgroundStyle from "../style/BackgroundStyle";

import EchartsStyle from "../../EchartsStyle";
export default class BarPictorSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const roundCapAndWidthContainer = $(`<div class="chart-item flex">
                <div class="w-50 pr5 flex">
                    <span class="chart-span">是否圆弧</span>
                    <label class="chart-switch">
                        <input type="checkbox" class="chart-checkbox" `+ modelName + `="roundCap">
                        <div class="slider round"></div>
                    </label>
                </div>
                <div class="w-50 pl5 flex">
                    <span class="chart-span">是否显示最值</span>
                    <label class="chart-switch">
                        <input type="checkbox" class="chart-checkbox" `+ modelName + `="showMaximum">
                        <div class="slider round"></div>
                    </label>
                </div>
            </div>`);
      chartBody.append(roundCapAndWidthContainer);

      const showContainer = $(`<div class="chart-item flex">
                <div class="w-50 pr5 flex">
                    <span class="chart-span">是否显示背景色</span>
                    <label class="chart-switch">
                        <input type="checkbox" class="chart-checkbox" `+ modelName + `="showBackground">
                        <div class="slider round"></div>
                    </label>
                </div>
            </div>`);
      chartBody.append(showContainer);

      const typeZlevelContainer = $(`<div class="chart-item flex">
            <div class="w-50 pr5 flex">
                <div class="chart-label">类型</div>
                <div class="chart-control">
                    <select class="chart-select" `+ modelName + `="type" >
                            <option value="">请选择</option>
                            <option value="bar">bar</option>
                            <option value="pictorialBar">pictorialBar</option>
                    </select>
                </div>
            </div>
            <div class="w-50 pl5 flex">
                <div class="chart-label">z轴层级</div>
                    <div class="chart-control">
                        <input type="number" class="chart-number"  `+ modelName + `="zlevel" />
                    </div>
            </div>
        </div>`);
      chartBody.append(typeZlevelContainer);

      const barAndCategoryGapContainer = $(`<div class="chart-item flex">
            <div class="w-50 pr5 flex">
                <div class="chart-label">同系间距</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="同一系之间的距离" `+ modelName + `="barCategoryGap"  />
                </div>
            </div>
            <div class="w-50 pl5 flex">
                <div class="chart-label">异系间距</div>
                <div class="chart-control">
                    <input type="text" class="chart-text" placeholder="不同系之间的距离" `+ modelName + `="barGap"  />
                </div>
            </div>
        </div>`);
      chartBody.append(barAndCategoryGapContainer);

      const xAxisIndexYAxisIndexContainer = $(`<div class="chart-item flex">
            <div class="w-50 pr5 flex">
                <div class="chart-label">X轴指数</div>
                <div class="chart-control">
                    <input type="number" class="chart-number" `+ modelName + `="xAxisIndex"  />
                </div>
            </div>
            <div class="w-50 pl5 flex">
                <div class="chart-label">Y轴指数</div>
                <div class="chart-control">
                    <input type="number" class="chart-number" `+ modelName + `="yAxisIndex"  />
                </div>
            </div>
        </div>`);
      chartBody.append(xAxisIndexYAxisIndexContainer);

      const showBackgroundContainer = $(`<div class="chart-item flex">
            <div class="w-50 pr5 flex">
                <div class="chart-label">柱状宽度</div>
                <div class="chart-control">
                    <input type="text" class="chart-text"  `+ modelName + `="barWidth"  />
                </div>
            </div>
            <div class="w-50 pl5 flex">
            <div class="chart-label">最大宽度</div>
                <div class="chart-control">
                    <input type="text" class="chart-text"  `+ modelName + `="barMaxWidth"  />
                </div>
                
            </div>
        </div>`);
      chartBody.append(showBackgroundContainer);

      const ZContainer = $(`<div class="chart-item flex">
                <div class="w-50 pl5 flex">
                    <div class="chart-label">z轴值</div>
                    <div class="chart-control">
                        <input type="text" class="chart-text"  `+ modelName + `="z"  />
                    </div>
                </div>
            </div>`);
      chartBody.append(ZContainer);

      const symbolStyle = new SymbolStyle(this.context);
      symbolStyle.initPanel(chartBody, item, callback);

      if (!item.label) {
        item.label = {};
      }
      const label = new Label(this.context);
      label.initPanel(chartBody, item.label, callback);

      if (!item.itemStyle) {
        item.itemStyle = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item.itemStyle, callback);

      if (!item.backgroundStyle) {
        item.backgroundStyle = {};
      }
      const backgroundStyle = new BackgroundStyle(this.context);
      backgroundStyle.initPanel(chartBody, item.backgroundStyle, callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "barPictor-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "柱状象形";
  }
}