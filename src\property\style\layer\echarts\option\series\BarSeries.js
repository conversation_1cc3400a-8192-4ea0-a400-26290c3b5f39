import Tooltip from "../Tooltip";
import Label from "../style/Label";
import LabelLine from "../style/LabelLine";
import ItemStyle from "../style/ItemStyle";
import BackgroundStyle from "../style/BackgroundStyle";

import EchartsStyle from "../../EchartsStyle";
export default class BarSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const showContainer = $(`<div class="chart-item flex">
          <div class="w-50 flex">
              <span class="chart-span">是否显示背景色</span>
              <label class="chart-switch">
                  <input type="checkbox" class="chart-checkbox" `+ modelName + `="showBackground">
                  <div class="slider round"></div>
              </label>
          </div>
      </div>`);
      chartBody.append(showContainer);
      // 数据堆叠
      const stackContainer = $(`<div class="chart-item flex">
        <div class="chart-label">数据堆叠</div>
        <div class="chart-control">
            <input type="text" class="chart-text" min="0" max="" placeholder="数据堆叠" ` + modelName + `="stack" />
        </div>
      </div>`);
      chartBody.append(stackContainer);

      // 柱条宽度
      const barWidthContainer = $(`<div class="chart-item flex">
        <div class="chart-label">柱条宽度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="柱条宽度" ` + modelName + `="barWidth" />
        </div>
      </div>`);
      chartBody.append(barWidthContainer);

      //同系间距
      const barCategoryGapContainer = $(`<div class="chart-item flex">
        <div class="chart-label">同系间距</div>
        <div class="chart-control">
            <input type="text" class="chart-text" min="0" max="" placeholder="-100%为重叠" ` + modelName + `="barCategoryGap" />
        </div>
      </div>`);
      chartBody.append(barCategoryGapContainer);

       // 间距
       const barGapContainer = $(`<div class="chart-item flex">
        <div class="chart-label">柱间距离</div>
        <div class="chart-control">
            <input type="text" class="chart-text" min="0" max="" placeholder="50" ` + modelName + `="barGap" />
        </div>
      </div>`);
      chartBody.append(barGapContainer);

      // 层级
      const zContainer = $(`<div class="chart-item flex">
        <div class="chart-label">图形顺序</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="1" ` + modelName + `="z" />
        </div>
      </div>`);
      chartBody.append(zContainer);

      // if (!item["tooltip"]) {
      //   item["tooltip"] = {};
      // }
      // const tooltip = new Tooltip(this.context);
      // tooltip.initPanel(chartBody, item["tooltip"], callback);

      if (!item["label"]) {
        item["label"] = {};
      }
      const label = new Label(this.context);
      label.initPanel(chartBody, item["label"], callback);

      // if (!item["labelLine"]) {
      //   item["labelLine"] = {};
      // }
      // const labelLine = new LabelLine(this.context);
      // labelLine.initPanel(chartBody, item["labelLine"], callback);

      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      if (!item.backgroundStyle) {
        item.backgroundStyle = {};
      }
      const backgroundStyle = new BackgroundStyle(this.context);
      backgroundStyle.initPanel(chartBody, item.backgroundStyle, callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "barSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "柱状图";
  }
}