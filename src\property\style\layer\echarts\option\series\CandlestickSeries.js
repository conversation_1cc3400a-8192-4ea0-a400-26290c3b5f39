import Tooltip from "../Tooltip";
import ItemStyle from "../style/ItemStyle";

import DataList from "./data/DataList";

import EchartsStyle from "../../EchartsStyle";
export default class CandlestickSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadName(chartBody, modelName);

      // 最大宽度
      const barMaxWidthContainer = $(`<div class="chart-item flex">
       <div class="chart-label">最大宽度</div>
       <div class="chart-control">
           <input type="number" class="chart-number" min="0" max="" placeholder="最大宽度" ` + modelName + `="barMaxWidth" />
       </div>
     </div>`);
      chartBody.append(barMaxWidthContainer);

      // 最小宽度
      const barMinWidthContainer = $(`<div class="chart-item flex">
       <div class="chart-label">最小宽度</div>
       <div class="chart-control">
           <input type="number" class="chart-number" min="0" max="" placeholder="最小宽度" ` + modelName + `="barMinWidth" />
       </div>
     </div>`);
      chartBody.append(barMinWidthContainer);

      if (!item["tooltip"]) {
        item["tooltip"] = {};
      }
      const tooltip = new Tooltip(this.context);
      tooltip.initPanel(chartBody, item["tooltip"], callback);

      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      if (!item["data"]) {
        item["data"] = [];
      }
      const datas = new DataList(this.context);
      datas.initPanel(chartBody, item["data"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "candlestickSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "序列-K线图"
  }
}