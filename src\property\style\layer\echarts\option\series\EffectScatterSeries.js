import GapStyle from "../style/common/GapStyle";
import Tooltip from "../Tooltip";
import Label from "../style/Label";
import LabelLine from "../style/LabelLine";
import ItemStyle from "../style/ItemStyle";

import EchartsStyle from "../../EchartsStyle";
export default class EffectScatterSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //选择坐标系
      // const  coordinateSystemContainer = $(`<div class="chart-item flex">
      //     <div class="chart-label">选择坐标系</div>
      //     <div class="chart-control">
      //         <select class="chart-select" `+ modelName + `="coordinateSystem">
      //             <option value="">---请选择---</option>
      //             <option value="cartesian2d">二维的直角坐标系</option>
      //             <option value="polar">极坐标系</option>
      //             <option value="geo">地理坐标系</option>
      //             <option value="calendar">日历坐标系</option>
      //         </select>
      //     </div>
      //   </div>`);
      // chartBody.append(coordinateSystemContainer);

      //间距
      const gapStyle=new GapStyle(this.context);
      gapStyle.initPanel(chartBody,item,callback);

      if (!item["tooltip"]) {
        item["tooltip"] = {};
      }
      const tooltip = new Tooltip(this.context);
      tooltip.initPanel(chartBody, item["tooltip"], callback);

      if (!item["label"]) {
        item["label"] = {};
      }
      const label = new Label(this.context);
      label.initPanel(chartBody, item["label"], callback);

      if (!item["labelLine"]) {
        item["labelLine"] = {};
      }
      const labelLine = new LabelLine(this.context);
      labelLine.initPanel(chartBody, item["labelLine"], callback);

      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "effectScatterSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "涟漪散点"
  }
}