import Tooltip from "../Tooltip";
import ItemStyle from "../style/ItemStyle";
import AxisLine from "../style/AxisLine";
import AxisTick from "../style/AxisTick";
import AxisLabel from "../style/AxisLabel";

import DataList from "./data/DataList";

import EchartsStyle from "../../EchartsStyle";
export default class GaugeSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadName(chartBody, modelName);
      //起始角度
      const startAngleContainer = $(`<div class="chart-item flex">
      <div class="chart-label">起始角度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="起始角度" ` + modelName + `="startAngle" />
        </div>
      </div>`);
      chartBody.append(startAngleContainer);
      //结束角度
      const endAngleContainer = $(`<div class="chart-item flex">
      <div class="chart-label">结束角度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="结束角度" ` + modelName + `="endAngle" />
        </div>
      </div>`);
      chartBody.append(endAngleContainer);
      //最小值
      const minContainer = $(`<div class="chart-item flex">
      <div class="chart-label">最小值</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="最小值" ` + modelName + `="min" />
        </div>
      </div>`);
      chartBody.append(minContainer);
      //最大值
      const maxContainer = $(`<div class="chart-item flex">
      <div class="chart-label">最大值</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="最大值" ` + modelName + `="max" />
        </div>
      </div>`);
      chartBody.append(maxContainer);

      if (!item["tooltip"]) {
        item["tooltip"] = {};
      }
      const tooltip = new Tooltip(this.context);
      tooltip.initPanel(chartBody, item["tooltip"], callback);

      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      if (!item["axisLine"]) {
        item["axisLine"] = {};
      }
      const axisLine = new AxisLine(this.context);
      axisLine.initPanel(chartBody, item["axisLine"], callback);

      if (!item["axisLabel"]) {
        item["axisLabel"] = {};
      }
      const axisLabel = new AxisLabel(this.context);
      axisLabel.initPanel(chartBody, item["axisLabel"], callback);

      if (!item["axisTick"]) {
        item["axisTick"] = {};
      }
      const axisTick = new AxisTick(this.context);
      axisTick.initPanel(chartBody, item["axisTick"], callback);

      if (!item["data"]) {
        item["data"] = [];
      }
      const datas = new DataList(this.context);
      datas.initPanel(chartBody, item["data"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "gaugeSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "序列-仪表盘"
  }
}