import GapStyle from "../style/common/GapStyle";
import Tooltip from "../Tooltip";
import Label from "../style/Label";
import LineStyle from "../style/LineStyle";
import ItemStyle from "../style/ItemStyle";

import DataList from "./data/DataList";

import EchartsStyle from "../../EchartsStyle";
export default class GraphSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadName(chartBody, modelName);
      this.loadWidth(chartBody, modelName);
      this.loadHeight(chartBody, modelName);

      //布局
      const layoutContainer = $(`<div class="chart-item flex">
          <div class="chart-label">布局</div>
            <div class="chart-control">
                <select class="chart-select" `+ modelName + `="layout">
                    <option value="">--请选择--</option>
                    <option value="none">无</option>
                    <option value="circular">环形布局</option>
                    <option value="force">力引导布局</option>
                </select>
            </div>
        </div>`);
      chartBody.append(layoutContainer);

      //间距
      const gapStyle = new GapStyle(this.context);
      gapStyle.initPanel(chartBody, item, callback);

      if (!item["tooltip"]) {
        item["tooltip"] = {};
      }
      const tooltip = new Tooltip(this.context);
      tooltip.initPanel(chartBody, item["tooltip"], callback);

      if (!item["label"]) {
        item["label"] = {};
      }
      const label = new Label(this.context);
      label.initPanel(chartBody, item["label"], callback);

      if (!item["lineStyle"]) {
        item["lineStyle"] = {};
      }
      const lineStyle = new LineStyle(this.context);
      lineStyle.initPanel(chartBody, item["lineStyle"], callback);

      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      if (!item["data"]) {
        item["data"] = [];
      }
      const datas = new DataList(this.context);
      datas.initPanel(chartBody, item["data"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "graphSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "序列-关系图"
  }
}