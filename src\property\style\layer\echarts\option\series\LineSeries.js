import SymbolStyle from "../style/common/SymbolStyle";

import Tooltip from "../Tooltip";
import Label from "../style/Label";
import LabelLine from "../style/LabelLine";
import LineStyle from "../style/LineStyle";
import ItemStyle from "../style/ItemStyle";
import AreaStyle from "../style/AreaStyle";

import EchartsStyle from "../../EchartsStyle";
export default class LineSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const smoothContainer = $(`<div class="chart-item flex">
          <div class="flex">
            <span class="chart-span">是否平滑</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="smooth">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(smoothContainer);

      //通用
      this.loadName(chartBody, modelName);

      // 数据堆叠
      const stackContainer = $(`<div class="chart-item flex">
        <div class="chart-label">数据堆叠</div>
        <div class="chart-control">
            <input type="text" class="chart-text" min="0" max="" placeholder="数据堆叠" ` + modelName + `="stack" />
        </div>
      </div>`);
      chartBody.append(stackContainer);

     

      const symbolStyle = new SymbolStyle(this.context);
      symbolStyle.initPanel(chartBody,item,callback);

      // if (!item["tooltip"]) {
      //   item["tooltip"] = {};
      // }
      // const tooltip = new Tooltip(this.context);
      // tooltip.initPanel(chartBody, item["tooltip"], callback);

      if (!item["label"]) {
        item["label"] = {};
      }
      const label = new Label(this.context);
      label.initPanel(chartBody, item["label"], callback);

      // if (!item["labelLine"]) {
      //   item["labelLine"] = {};
      // }
      // const labelLine = new LabelLine(this.context);
      // labelLine.initPanel(chartBody, item["labelLine"], callback);
      
      if (!item["lineStyle"]) {
        item["lineStyle"] = {};
      }
      const lineStyle = new LineStyle(this.context);
      lineStyle.initPanel(chartBody, item["lineStyle"], callback);
      
      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      if (!item["areaStyle"]) {
        item["areaStyle"] = {};
      }
      const areaStyle = new AreaStyle(this.context);
      areaStyle.initPanel(chartBody, item["areaStyle"], callback);

      

      
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "lineSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "折线/面积图"
  }
}