import Label from "../style/Label";
import ItemStyle from "../style/ItemStyle";
import BackgroundStyle from "../style/BackgroundStyle";
import Outline from "../style/liquid/Outline";

import DataList from "./data/DataList";

import EchartsStyle from "../../EchartsStyle";
export default class LiquidSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //标签样式
      if (!item["label"]) {
        item["label"] = {};
      }
      const label = new Label(this.context);
      label.initPanel(chartBody, item["label"], callback);
      //Item样式
      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);
      //背景样式
      if (!item["backgroundStyle"]) {
        item["backgroundStyle"] = {};
      }
      const backgroundStyle = new BackgroundStyle(this.context);
      backgroundStyle.initPanel(chartBody, item["backgroundStyle"], callback);
      //OutLine
      if (!item["outline"]) {
        item["outline"] = {};
      }
      const outline = new Outline(this.context);
      outline.initPanel(chartBody, item["outline"], callback);

      if (!item["data"]) {
        item["data"] = [];
      }
      const datas = new DataList(this.context);
      datas.initPanel(chartBody, item["data"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "liquidSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "序列-水晶球"
  }
}