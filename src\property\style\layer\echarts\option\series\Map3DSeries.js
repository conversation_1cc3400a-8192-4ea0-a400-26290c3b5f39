import Tooltip from "../Tooltip";
import Label from "../style/Label";
import ItemStyle from "../style/ItemStyle";

import EchartsStyle from "../../EchartsStyle";
import Emphasis from "../style/Emphasis";
import ViewControl from "../coord/ViewControl";
import GapStyle from "../style/common/GapStyle";
export default class Map3DSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      this.loadName(chartBody, modelName)
      this.loadWidth(chartBody, modelName);
      this.loadHeight(chartBody, modelName);
      const boxWidthContainer = $(`<div class="chart-item flex">
      <div class="chart-label">三维宽度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="三维宽度" ` + modelName + `="boxWidth" />
        </div>
      </div>`);
      chartBody.append(boxWidthContainer);
      const boxHeightContainer = $(`<div class="chart-item flex">
      <div class="chart-label">三维高度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="三维高度" ` + modelName + `="boxHeigth" />
        </div>
      </div>`);
      chartBody.append(boxHeightContainer);
      const boxDepthContainer = $(`<div class="chart-item flex">
      <div class="chart-label">三维深度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="三维深度" ` + modelName + `="boxDepth" />
        </div>
      </div>`);
      chartBody.append(boxDepthContainer);

      // regionHeight 模型的高度
      const regionHeightContainer = $(`<div class="chart-item flex">
      <div class="chart-label">模型高度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="模型高度" ` + modelName + `="regionHeight" />
        </div>
      </div>`);
      chartBody.append(regionHeightContainer);

      if (!item["label"]) {
        item["label"] = {};
      }
      const label = new Label(this.context);
      label.initPanel(chartBody, item["label"], callback);

      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);
      itemStyle.setIsGeo(true)

      if (!item["emphasis"]) {
        item["emphasis"] = {};
      }
      const emphasis = new Emphasis(this.context);
      emphasis.initPanel(chartBody, item["emphasis"], callback);

      if (!item["viewControl"]) {
        item["viewControl"] ={};
      }
      const viewControlStyle = new ViewControl(this.context);
      viewControlStyle.initPanel(chartBody,item["viewControl"],callback);

      //间距
      const gapStyle=new GapStyle(this.context);
      gapStyle.initPanel(chartBody,item,callback);


      if (!item["tooltip"]) {
        item["tooltip"] = {};
      }
      const tooltip = new Tooltip(this.context);
      tooltip.initPanel(chartBody, item["tooltip"], callback);
      

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "mapSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "3D地图"
  }
}