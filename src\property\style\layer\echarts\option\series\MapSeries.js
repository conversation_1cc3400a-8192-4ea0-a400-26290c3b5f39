import Tooltip from "../Tooltip";
import Label from "../style/Label";
import LabelLine from "../style/LabelLine";
import ItemStyle from "../style/ItemStyle";

import EchartsStyle from "../../EchartsStyle";
export default class MapSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //模式
      const  selectedModeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">模式</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="selectedMode">
                  <option value="">---请选择---</option>
                  <option value="multiple">多选</option>
                  <option value="single">单选</option>
              </select>
          </div>
        </div>`);
      chartBody.append(selectedModeContainer);

      
      if (!item["tooltip"]) {
        item["tooltip"] = {};
      }
      const tooltip = new Tooltip(this.context);
      tooltip.initPanel(chartBody, item["tooltip"], callback);

      if (!item["label"]) {
        item["label"] = {};
      }
      const label = new Label(this.context);
      label.initPanel(chartBody, item["label"], callback);

      if (!item["labelLine"]) {
        item["labelLine"] = {};
      }
      const labelLine = new LabelLine(this.context);
      labelLine.initPanel(chartBody, item["labelLine"], callback);

      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);
      

      

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "mapSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "地图"
  }
}