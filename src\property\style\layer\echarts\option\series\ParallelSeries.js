import Tooltip from "../Tooltip";
import LineStyle from "../style/LineStyle";

import DataList from "./data/DataList";

import EchartsStyle from "../../EchartsStyle";
export default class ParallelSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const smoothContainer = $(`<div class="chart-item flex">
          <div class="flex">
            <span class="chart-span">是否平滑曲线</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="smooth">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(smoothContainer);

      //通用
      this.loadName(chartBody, modelName);

      //平行坐标
      const parallelIndexContainer = $(`<div class="chart-item flex">
      <div class="chart-label">平行坐标</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="索引" ` + modelName + `="parallelIndex" />
        </div>
      </div>`);
      chartBody.append(parallelIndexContainer);

      if (!item["tooltip"]) {
        item["tooltip"] = {};
      }
      const tooltip = new Tooltip(this.context);
      tooltip.initPanel(chartBody, item["tooltip"], callback);

      if (!item["lineStyle"]) {
        item["lineStyle"] = {};
      }
      const lineStyle = new LineStyle(this.context);
      lineStyle.initPanel(chartBody, item["lineStyle"], callback);

      if (!item["data"]) {
        item["data"] = [];
      }
      const datas = new DataList(this.context);
      datas.initPanel(chartBody, item["data"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "parallelSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "序列-平行坐标系";
  }
}