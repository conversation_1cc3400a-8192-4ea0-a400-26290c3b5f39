import GapStyle from "../style/common/GapStyle";
import Tooltip from "../Tooltip";
import Label from "../style/Label";
import LabelLine from "../style/LabelLine";
import ItemStyle from "../style/ItemStyle";

import DataList from "./data/DataList";

import EchartsStyle from "../../EchartsStyle";
export default class PieSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadName(chartBody, modelName);
      this.loadWidth(chartBody, modelName);
      this.loadHeight(chartBody, modelName);

      // 展示模式
      const roseTypeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">展示模式</div>
        <div class="chart-control">
            <select class="chart-select" `+ modelName + `="roseType">
                <option value="radius">百分百展示</option>
                <option value="area">对半展示</option>
            </select>
        </div>
      </div>`);
      chartBody.append(roseTypeContainer);

      // 最小角度
      const minAngleContainer = $(`<div class="chart-item flex">
        <div class="chart-label">最小角度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="0 ~ 360" ` + modelName + `="minAngle" />
        </div>
      </div>`);
      chartBody.append(minAngleContainer);

      // 饼图的半径
      const radiusContainer = $(`<div class="chart-item flex">
        <div class="chart-label">半径</div>
        <div class="chart-control">
            <input type="text" class="chart-text" min="0" max="" placeholder="0, 75%" ` + modelName + `="radius" />
        </div>
      </div>`);
      chartBody.append(radiusContainer);

      // 坐标
      const centerContainer = $(`<div class="chart-item flex">
        <div class="chart-label">坐标</div>
        <div class="chart-control">
            <input type="text" class="chart-text" min="0" max="" placeholder="50%, 50%" ` + modelName + `="center" />
        </div>
      </div>`);
      chartBody.append(centerContainer);


      //间距
      const gapStyle = new GapStyle(this.context);
      gapStyle.initPanel(chartBody, item, callback);

      // if (!item["tooltip"]) {
      //   item["tooltip"] = {};
      // }
      // const tooltip = new Tooltip(this.context);
      // tooltip.initPanel(chartBody, item["tooltip"], callback);

      if (!item["label"]) {
        item["label"] = {};
      }
      const label = new Label(this.context);
      label.initPanel(chartBody, item["label"], callback);

      if (!item["labelLine"]) {
        item["labelLine"] = {};
      }
      const labelLine = new LabelLine(this.context);
      labelLine.initPanel(chartBody, item["labelLine"], callback);

      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      if (!item["data"]) {
        item["data"] = [];
      }
      const datas = new DataList(this.context);
      datas.initPanel(chartBody, item["data"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "pieSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "序列-饼图"
  }
}