import Tooltip from "../Tooltip";
import Label from "../style/Label";
import LineStyle from "../style/LineStyle";
import ItemStyle from "../style/ItemStyle";
import AreaStyle from "../style/AreaStyle";

import DataList from "./data/DataList";

import EchartsStyle from "../../EchartsStyle";
export default class RadarSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadName(chartBody, modelName);

      //雷达图下标
      const radarIndexContainer = $(`<div class="chart-item flex">
      <div class="chart-label">雷达索引</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="雷达索引" ` + modelName + `="radarIndex" />
        </div>
      </div>`);
      chartBody.append(radarIndexContainer);

      if (!item["tooltip"]) {
        item["tooltip"] = {};
      }
      const tooltip = new Tooltip(this.context);
      tooltip.initPanel(chartBody, item["tooltip"], callback);

      if (!item["label"]) {
        item["label"] = {};
      }
      const label = new Label(this.context);
      label.initPanel(chartBody, item["label"], callback);

      if (!item["lineStyle"]) {
        item["lineStyle"] = {};
      }
      const lineStyle = new LineStyle(this.context);
      lineStyle.initPanel(chartBody, item["lineStyle"], callback);

      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      if (!item["areaaStyle"]) {
        item["areaaStyle"] = {};
      }
      const areaaStyle = new AreaStyle(this.context);
      areaaStyle.initPanel(chartBody, item["areaaStyle"], callback);

      if (!item["data"]) {
        item["data"] = [];
      }
      const datas = new DataList(this.context);
      datas.initPanel(chartBody, item["data"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "radarSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "序列-雷达图"
  }
}