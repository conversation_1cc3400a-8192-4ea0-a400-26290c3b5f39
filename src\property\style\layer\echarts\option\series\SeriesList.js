import SeriesVal from './SeriesVal'//数值
import BarPictorSeries from './BarPictorSeries';//柱状象形
import LineSeries from './LineSeries'//折线图
import BarSeries from './BarSeries'//柱状图
import PieSeries from './PieSeries'//饼图
import ScatterSeries from './ScatterSeries'//气泡(散点)
import RadarSeries from './/RadarSeries'//雷达
import GaugeSeries from './GaugeSeries'//仪表盘
import FunnelSeries from './FunnelSeries'//漏斗图
import SankeySeries from './SankeySeries'//桑基图
import GraphSeries from './GraphSeries'//关系图
import BoxplotSeries from './BoxplotSeries'//盒须图
import CandlestickSeries from './CandlestickSeries'//K线图
import LinesSeries from './LinesSeries'//路径图
import ParallelSeries from './ParallelSeries'//平行坐标系
import MapSeries from './MapSeries';//地图
import HeatmapSeries from './HeatmapSeries';//热力图
import EffectScatterSeries from './EffectScatterSeries';//涟漪散点
import PictorialBarSeries from './PictorialBarSeries';//象形柱图
import LiquidSeries from './LiquidSeries';
import EchartsStyle from "../../EchartsStyle";
import Map3DSeries from "./Map3DSeries";
export default class SeriesList extends EchartsStyle {
  constructor(context, type, isOpen) {
    super(context, true, isOpen);
    this.type = type || 'val'
  }
  setOptions (options) {
    this.options = options;
  }

  afterAddItem (itemContainer, item, index, callback) {
    if (this.type) {
      if(this.type!="barPictor"){
        item["type"] = this.type;
      }
      const model = this.buildSeries(this.context, this.type);
      model.initPanel(itemContainer, item, callback);
      model.refreshId(index);
      model.refreshTitle("序列[" + (index + 1) + "]配置");
      if (this.options && this.options.length) {
        model.setOptions(this.options);
      }
    }
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "序列列表"
  }

  buildSeries (context, type) {
    if (type) {
      let series
      switch (type) {
        case 'val':
          series = new SeriesVal(context);
          break
        case 'barPictor':
          series = new BarPictorSeries(context);
          break;
        case 'line':
          series = new LineSeries(context);
          break
        case 'bar':
          series = new BarSeries(context);
          break
        case 'pie':
          series = new PieSeries(context);
          break
        case 'scatter':
          series = new ScatterSeries(context);
          break
        case 'radar':
          series = new RadarSeries(context);
          break
        case 'gauge':
          series = new GaugeSeries(context);
          break
        case 'funnel':
          series = new FunnelSeries(context);
          break
        case 'sankey':
          series = new SankeySeries(context);
          break
        case 'graph':
          series = new GraphSeries(context);
          break
        case 'boxplot':
          series = new BoxplotSeries(context);
          break
        case 'candlestick':
          series = new CandlestickSeries(context);
          break
        case 'lines':
          series = new LinesSeries(context);
          break
        case 'parallel':
          series = new ParallelSeries(context);
          break
        case 'map':
          series = new MapSeries(context);
          break
        case 'map3D':
          series = new Map3DSeries(context);
          break
        case 'effectScatter':
          series = new EffectScatterSeries(context);
          break
        case 'heatmap':
          series = new HeatmapSeries(context);
          break
        case 'pictorialBar':
          series = new PictorialBarSeries(context);
          break;
        case 'liquidFill':
          series = new LiquidSeries(context);
          break;
        default:
          console.error('未配置类型[' + type + ']序列')
          break
      }
      return series
    }
  }
}