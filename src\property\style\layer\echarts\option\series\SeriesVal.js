
import EchartsStyle from "../../EchartsStyle";
export default class SeriesVal extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  setOptions (options) {
    this.options = options;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const lineContainer = $(`<div class="chart-item flex">
      <div class="chart-label">值</div>
      <div class="chart-control">
        <select class="chart-select" ` + modelName + `="value" ></select>
      </div>
    </div>`);
      chartBody.append(lineContainer);

      const lineSelector = $(lineContainer).find(".chart-select");
      this.refreshOption(lineSelector, this.options);

      this.refreshModel(item, null);
      this.bindModel(item, callback);
    }
  }
  refreshOption (selecterContainer, list) {
    $(selecterContainer).empty();
    selecterContainer.prepend("<option value=''>---请选择---</option>");//添加第一个option值
    if (list && list.length > 0) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        selecterContainer.append("<option value='" + item.text + "'>" + item.text + "</option>");
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "val-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "序列-值"
  }
}