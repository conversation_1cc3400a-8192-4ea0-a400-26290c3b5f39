import Tooltip from "../Tooltip";
import ItemStyle from "../style/ItemStyle";

import DataList from "./data/DataList";

import EchartsStyle from "../../EchartsStyle";
export default class WordSeries extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const legendHoverLinkContainer = $(`<div class="chart-item flex">
          <div class="flex">
            <span class="chart-span">是否启用图例</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="legendHoverLink">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(legendHoverLinkContainer);

      const hoverAnimationContainer = $(`<div class="chart-item flex">
          <div class="flex">
            <span class="chart-span">是否开启动画</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="hoverAnimation">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(hoverAnimationContainer);

      //通用
      this.loadName(chartBody, modelName);

      if (!item["tooltip"]) {
        item["tooltip"] = {};
      }
      const tooltip = new Tooltip(this.context);
      tooltip.initPanel(chartBody, item["tooltip"], callback);

      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      if (!item["data"]) {
        item["data"] = [];
      }
      const datas = new DataList(this.context);
      datas.initPanel(chartBody, item["data"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "wordSeries-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "序列-词云"
  }
}