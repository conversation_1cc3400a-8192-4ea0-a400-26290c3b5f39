import Label from "../../style/Label";
import LabelLine from "../../style/LabelLine";
import ItemStyle from "../../style/ItemStyle";
import AreaStyle from "../../style/AreaStyle";
import Emphasis from "../../style/Emphasis";
import EchartsStyle from "../../../EchartsStyle";
export default class Data extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      if (!item["label"]) {
        item["label"] = {};
      }
      const label = new Label(this.context);
      label.initPanel(chartBody, item["label"], callback);

      if (!item["labelLine"]) {
        item["labelLine"] = {};
      }
      const labelLine = new LabelLine(this.context);
      labelLine.initPanel(chartBody, item["labelLine"], callback);

      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      if (!item["areaStyle"]) {
        item["areaStyle"] = {};
      }
      const areaStyle = new AreaStyle(this.context);
      areaStyle.initPanel(chartBody, item["areaStyle"], callback);
      
      if (!item["emphasis"]) {
        item["emphasis"] = {};
      }
      const emphasis = new Emphasis(this.context);
      emphasis.initPanel(chartBody, item["emphasis"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "data-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "数值"
  }
}