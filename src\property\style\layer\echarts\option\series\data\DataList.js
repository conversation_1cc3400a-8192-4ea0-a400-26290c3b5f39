import Data from "./Data";
import EchartsStyle from "../../../EchartsStyle";
export default class DataList extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  afterAddItem (itemContainer, item, index, callback) {
      const model = new Data(this.context);
      model.initPanel(itemContainer, item, callback);
      model.refreshId(index);
      model.refreshTitle("数值[" + (index+1) + "]配置");
    // }
    
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "序列-数据"
  }
}
