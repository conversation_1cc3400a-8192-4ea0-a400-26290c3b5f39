import ImageStyle from "../../../style/ImageStyle";
import ColorStyle from "../color/ColorStyle";
import EchartsStyle from "../../EchartsStyle";
export default class AreaColor extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const typeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="type">
                  <option value="">---请选择---</option>
                  <option value="color">颜色</option>
                  <option value="image">图片</option>
              </select>
          </div>
        </div>`);
      chartBody.append(typeContainer);
      this.colorContainer= $(`<div></div>`);
      this.imageContainer= $(`<div></div>`);
      chartBody.append(this.colorContainer);
      chartBody.append(this.imageContainer);
      //颜色
      if(!item["geoColor"]){
        item["geoColor"] ={};
      }
      const colorStyle = new ColorStyle(this.context);
      colorStyle.initPanel(this.colorContainer,item["geoColor"],callback);

      if(!item["geoImage"]){
        item["geoImage"] ={};
      }
      const imageStyle = new ImageStyle(this.context);
      imageStyle.initPanel(this.imageContainer, item["geoImage"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    if(key && key==="type"){
      if(value && value==="color"){
        $(this.colorContainer).show();
        $(this.imageContainer).hide();
      }else if(value && value==="image"){
        $(this.colorContainer).hide();
        $(this.imageContainer).show();
      }else{
        $(this.colorContainer).hide();
        $(this.imageContainer).hide();
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "areaColor-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "区域颜色"
  }
}