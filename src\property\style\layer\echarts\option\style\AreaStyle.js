import ShadowStyle from "./common/ShadowStyle";
import ColorStyle from "../color/ColorStyle";
import EchartsStyle from "../../EchartsStyle";
export default class AreaStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      // this.loadColor(chartBody,modelName);
      this.loadOpacity(chartBody,modelName);

       //颜色
      if(!item["color"]){
        item["color"] ={};
      }
      const colorStyle = new ColorStyle(this.context);
      colorStyle.initPanel(chartBody,item["color"],callback);
      //阴影
      const shadowStyle=new ShadowStyle(this.context);
      shadowStyle.initPanel(chartBody,item,callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "areaStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "区域样式"
  }
}