import FontStyle from "./common/FontStyle";
import BorderStyle from "./common/BorderStyle";
import ShadowStyle from "./common/ShadowStyle";
import TextBorderStyle from "./common/TextBorderStyle";
import TextShadowStyle from "./common/TextShadowStyle";
import ColorStyle from "../color/ColorStyle";
import EchartsStyle from "../../EchartsStyle";
export default class AxisLabel extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      

      const insideContainer = $(`<div class="chart-item flex">
          <div class="flex">
            <span class="chart-span">是否朝内</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="inside">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(insideContainer);

      const showMinLabelContainer = $(`<div class="chart-item flex">
          <div class="flex">
            <span class="chart-span">显示最小 tick </span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="showMinLabel">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(showMinLabelContainer);

      const showMaxLabelLabelContainer = $(`<div class="chart-item flex">
          <div class="flex">
            <span class="chart-span">显示最大 tick </span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="showMaxLabel">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(showMaxLabelLabelContainer);

      const hideOverlapLabelContainer = $(`<div class="chart-item flex">
          <div class="flex">
            <span class="chart-span">隐藏重叠标签</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="hideOverlap">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(hideOverlapLabelContainer);
      
      //通用
      this.loadShow(chartBody, modelName);
      // this.loadColor(chartBody, modelName);
      // this.loadBackgroundColor(chartBody, modelName);
      this.loadWidth(chartBody, modelName);
      this.loadHeight(chartBody, modelName);
      this.loadPadding(chartBody, modelName);
      this.loadMargin(chartBody, modelName);
      //旋转角度
      const rotateContainer = $(`<div class="chart-item flex">
          <div class="chart-label">旋转角度</div>
            <div class="chart-control">
                <input type="number" class="chart-number" min="0" max="" placeholder="角度值" ` + modelName + `="rotate" />
            </div>
          </div>`);
      chartBody.append(rotateContainer);

      const overflowContainer = $(`<div class="chart-item flex">
          <div class="chart-label">截断/换行</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="overflow">
                  <option value="none">无</option>
                  <option value="truncate">截断</option>
                  <option value="break">换行</option>
              </select>
          </div>
        </div>`);
      chartBody.append(overflowContainer);

      this.loadFormatter(chartBody, modelName);

       //颜色
      if(!item["color"]){
        item["color"] ={};
      }
      const colorStyle = new ColorStyle(this.context);
      colorStyle.initPanel(chartBody,item["color"],callback);

      //颜色
      if(!item["backgroundColor"]){
        item["backgroundColor"] ={};
      }
      const backgroundColorStyle = new ColorStyle(this.context);
      backgroundColorStyle.initPanel(chartBody,item["backgroundColor"],callback);
      backgroundColorStyle.refreshTitle("背景色");
      
      //字体
      const fontStyle=new FontStyle(this.context);
      fontStyle.initPanel(chartBody,item,callback);
      //边框
      const borderStyle=new BorderStyle(this.context);
      borderStyle.initPanel(chartBody,item,callback);
      //阴影
      const shadowStyle=new ShadowStyle(this.context);
      shadowStyle.initPanel(chartBody,item,callback);
      //文本边框
      const textBorderStyle=new TextBorderStyle(this.context);
      textBorderStyle.initPanel(chartBody,item,callback);
      //文本阴影
      const textShadowStyle=new TextShadowStyle(this.context);
      textShadowStyle.initPanel(chartBody,item,callback);
       

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "axisLabel-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "轴标签"
  }
}