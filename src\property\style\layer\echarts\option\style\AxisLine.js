import LineStyle from "./LineStyle";
import EchartsStyle from "../../EchartsStyle";
export default class AxisLine extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);

      const symbolContainer = $(`<div class="chart-item flex">
          <div class="chart-label">轴线箭头</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="symbol">
                  <option value="">---请选择---</option>
                  <option value="arrow">箭头</option>
                  <option value="none">不显示箭头</option>
              </select>
          </div>
      </div>`);
      chartBody.append(symbolContainer);

      const onZeroAxisIndexContainer = $(`<div class="chart-item flex">
        <div class="chart-label">指定轴</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="指定轴的 0 刻度" ` + modelName + `="onZeroAxisIndex" />
          </div>
        </div>`);
      chartBody.append(onZeroAxisIndexContainer);

      if (!item["lineStyle"]) {
        item["lineStyle"] = {};
      }
      const lineStyle = new LineStyle(this.context);
      lineStyle.initPanel(chartBody, item["lineStyle"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "axisLine-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "轴线"
  }
}