import LineStyle from "./LineStyle";
import EchartsStyle from "../../EchartsStyle";
export default class AxisTick extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const insideContainer = $(`<div class="chart-item flex">
          <div class="flex">
            <span class="chart-span">刻度是否朝内</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="inside">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(insideContainer);

      const alignWithLabelContainer = $(`<div class="chart-item flex">
          <div class="flex">
            <span class="chart-span">刻度线和标签对齐</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="alignWithLabel">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(alignWithLabelContainer);
      //通用
      this.loadShow(chartBody, modelName);
      this.loadLength(chartBody, modelName);
      //线样式
      if (!item["lineStyle"]) {
        item["lineStyle"] = {};
      }
      const lineStyle = new LineStyle(this.context);
      lineStyle.initPanel(chartBody, item["lineStyle"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "axisTick-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "轴刻度"
  }
}