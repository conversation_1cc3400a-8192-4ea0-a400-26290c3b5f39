import ShadowStyle from "./common/ShadowStyle";
import BorderStyle from "./common/BorderStyle";
import ColorStyle from "../color/ColorStyle";
import AreaColor from "./AreaColor";
import EchartsStyle from "../../EchartsStyle";
export default class ItemStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  setIsGeo (isGeo) {
    this.isgeo = isGeo;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      
      //通用
      // this.loadColor(chartBody,modelName);
      this.loadOpacity(chartBody,modelName);
      if (this.isgeo) {
        //区域颜色
        const areaColor = new AreaColor(this.context);
        areaColor.initPanel(chartBody,item,callback);
      }

      //颜色
      if(!item["color"]){
        item["color"] ={};
      }
      const colorStyle = new ColorStyle(this.context);
      colorStyle.initPanel(chartBody,item["color"],callback);

      //边框
      const borderStyle=new BorderStyle(this.context);
      borderStyle.initPanel(chartBody,item,callback);
      //阴影
      const shadowStyle=new ShadowStyle(this.context);
      shadowStyle.initPanel(chartBody,item,callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "itemStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "项样式"
  }
}