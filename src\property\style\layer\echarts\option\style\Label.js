import FontStyle from "./common/FontStyle";
import BorderStyle from "./common/BorderStyle";
import ShadowStyle from "./common/ShadowStyle";
import TextShadowStyle from "./common/TextShadowStyle";
import TextBorderStyle from "./common/TextBorderStyle";
import ColorStyle from "../color/ColorStyle";
import EchartsStyle from "../../EchartsStyle";
export default class Label extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);
      // this.loadColor(chartBody, modelName);
      this.loadWidth(chartBody, modelName);
      this.loadHeight(chartBody, modelName);
      this.loadPadding(chartBody, modelName);
      this.loadFormatter(chartBody, modelName);

      const positionContainer = $(`<div class="chart-item flex">
        <div class="chart-label">位置</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="position">
                  <option value="">---请选择---</option>
                  <option value="top">顶部</option>
                  <option value="left">居左</option>
                  <option value="right">居右</option>
                  <option value="buttom">底部</option>
                  <option value="inner">内部</option>

                  <option value="inside">内中</option>
                  <option value="insideTop">内顶</option>
                  <option value="insideLeft">内左</option>
                  <option value="insideRight">内右</option>
                  <option value="insideBottom">内底</option>
                  <option value="insideTopLeft">内顶左</option>
                  <option value="insideTopRight">内顶右</option>
                  <option value="insideBottomLeft">内底左</option>
                  <option value="insideBottomRight">内底右</option>
              </select>
          </div>
        </div>`);
      chartBody.append(positionContainer);

      const distanceContainer = $(`<div class="chart-item flex">
        <div class="chart-label">距离</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="距离图形" ` + modelName + `="distance" />
        </div>
      </div>`);
      chartBody.append(distanceContainer);

      const rotateContainer = $(`<div class="chart-item flex">
        <div class="chart-label">标签旋转</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="标签旋转" ` + modelName + `="rotate" />
        </div>
      </div>`);
      chartBody.append(rotateContainer);

       //颜色
       if(!item["color"]){
        item["color"] ={};
      }
      const colorStyle = new ColorStyle(this.context);
      colorStyle.initPanel(chartBody,item["color"],callback);

      //字体
      const fontStyle = new FontStyle(this.context);
      fontStyle.initPanel(chartBody, item, callback);
      //边框
      const borderStyle = new BorderStyle(this.context);
      borderStyle.initPanel(chartBody, item, callback);
      //阴影
      const shadowStyle = new ShadowStyle(this.context);
      shadowStyle.initPanel(chartBody, item, callback);
      //文本边框
      const textBorderStyle = new TextBorderStyle(this.context);
      textBorderStyle.initPanel(chartBody, item, callback);
      //文本阴影
      const textShadowStyle = new TextShadowStyle(this.context);
      textShadowStyle.initPanel(chartBody, item, callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "label-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "标签"
  }
}