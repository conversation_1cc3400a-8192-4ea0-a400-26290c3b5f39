import LineStyle from "./LineStyle";
import EchartsStyle from "../../EchartsStyle";
export default class LabelLine extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);
      this.loadLength(chartBody, modelName);
      //线样式
      if (!item["lineStyle"]) {
        item["lineStyle"] = {};
      }
      const lineStyle = new LineStyle(this.context);
      lineStyle.initPanel(chartBody, item["lineStyle"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "labelLine-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "标签引导线"
  }
}