import ShadowStyle from "./common/ShadowStyle";
import ColorStyle from "../color/ColorStyle";
import EchartsStyle from "../../EchartsStyle";
export default class LineStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      // this.loadShow(chartBody, modelName);
      // this.loadColor(chartBody,modelName);
      this.loadWidth(chartBody,modelName);
      this.loadOpacity(chartBody,modelName);
      
      const typeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="type">
                  <option value="">---请选择---</option>
                  <option value="solid">实线</option>
                  <option value="dashed">虚线</option>
                  <option value="dotted">点缀</option>
              </select>
          </div>
        </div>`);
      chartBody.append(typeContainer);

      const capContainer = $(`<div class="chart-item flex">
          <div class="chart-label">绘制方式</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="cap">
                  <option value="">---请选择---</option>
                  <option value="butt">方形</option>
                  <option value="round">圆形</option>
                  <option value="square">矩形</option>
              </select>
          </div>
        </div>`);
      chartBody.append(capContainer);

       //颜色
      if(!item["color"]){
        item["color"] ={};
      }
      const colorStyle = new ColorStyle(this.context);
      colorStyle.initPanel(chartBody,item["color"],callback);
      
      //阴影
      const shadowStyle=new ShadowStyle(this.context);
      shadowStyle.initPanel(chartBody,item,callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "lineStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "线样式"
  }
}