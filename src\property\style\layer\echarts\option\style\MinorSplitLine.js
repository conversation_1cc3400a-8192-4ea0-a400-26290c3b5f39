import LineStyle from "./LineStyle";
import EchartsStyle from "../../EchartsStyle";
export default class MinorSplitLine extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);

      if (!item["lineStyle"]) {
        item["lineStyle"] = {};
      }
      const lineStyle = new LineStyle(this.context);
      lineStyle.initPanel(chartBody, item["lineStyle"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "minorSplitLine-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "次分割线"
  }
}