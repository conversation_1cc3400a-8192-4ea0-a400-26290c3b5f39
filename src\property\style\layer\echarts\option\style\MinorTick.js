import LineStyle from "./LineStyle";
import EchartsStyle from "../../EchartsStyle";
export default class MinorTick extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);

      const splitNumberContainer = $(`<div class="chart-item flex">
        <div class="chart-label">分割数</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="分割数" ` + modelName + `="splitNumber" />
        </div>
      </div>`);
      chartBody.append(splitNumberContainer);

      const lengthContainer = $(`<div class="chart-item flex">
          <div class="chart-label">长度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max=""  placeholder="长度" ` + modelName + `="length" />
          </div>
        </div>`);
      chartBody.append(lengthContainer);

      if (!item["lineStyle"]) {
        item["lineStyle"] = {};
      }
      const lineStyle = new LineStyle(this.context);
      lineStyle.initPanel(chartBody, item["lineStyle"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "minorTick-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "次刻度线"
  }
}