import AreaStyle from "./AreaStyle";
import EchartsStyle from "../../EchartsStyle";
export default class SplitArea extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);

      if (!item["areaStyle"]) {
        item["areaStyle"] = {};
      }
      const areaStyle = new AreaStyle(this.context);
      areaStyle.initPanel(chartBody, item["areaStyle"], callback);
      
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "splitArea-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "分隔区域"
  }
}