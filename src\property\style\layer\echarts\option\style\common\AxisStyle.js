import EchartsStyle from "../../../EchartsStyle";
export default class AxisStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const minContainer = $(`<div class="chart-item flex">
        <div class="chart-label">最小值</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="最小值" ` + modelName + `="min" />
          </div>
        </div>`);
      chartBody.append(minContainer);

      const maxContainer = $(`<div class="chart-item flex">
        <div class="chart-label">最大值</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="最大值" ` + modelName + `="max" />
          </div>
        </div>`);
      chartBody.append(maxContainer);

      const splitNumberContainer = $(`<div class="chart-item flex">
        <div class="chart-label">分割段数</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="分割段数" ` + modelName + `="splitNumber" />
          </div>
        </div>`);
      chartBody.append(splitNumberContainer);

      const intervalContainer = $(`<div class="chart-item flex">
        <div class="chart-label">分割间隔</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="分割间隔" ` + modelName + `="interval" />
          </div>
        </div>`);
      chartBody.append(intervalContainer);

      const minIntervalContainer = $(`<div class="chart-item flex">
        <div class="chart-label">最小间隔</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="最小间隔" ` + modelName + `="minInterval" />
          </div>
        </div>`);
      chartBody.append(minIntervalContainer);

      const maxIntervalContainer = $(`<div class="chart-item flex">
        <div class="chart-label">最大间隔</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="最大间隔" ` + modelName + `="maxInterval" />
          </div>
        </div>`);
      chartBody.append(maxIntervalContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "axisStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "轴样式"
  }
}