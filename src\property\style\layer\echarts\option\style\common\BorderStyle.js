import EchartsStyle from "../../../EchartsStyle";
export default class BorderStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //边框颜色
      const borderColorContainer = $(`<div class="chart-item flex">
      <div class="chart-label">边框颜色</div>
        <div class="chart-control">
          <input type="text" class="chart-color" ` + modelName + `="borderColor" />
        </div>
      </div>`);
      chartBody.append(borderColorContainer);
      //边框宽度
      const borderWidthContainer = $(`<div class="chart-item flex">
      <div class="chart-label">边框宽度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="宽度" ` + modelName + `="borderWidth" />
        </div>
      </div>`);
      chartBody.append(borderWidthContainer);
      //边框圆角
      const borderRadiusContainer = $(`<div class="chart-item flex">
      <div class="chart-label">圆角半径</div>
        <div class="chart-control">
            <input type="text" class="chart-text"  placeholder="5,5,0,0" ` + modelName + `="borderRadius" />
        </div>
      </div>`);
      chartBody.append(borderRadiusContainer);
      //描边类型
      const borderTypeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">描边类型</div>
        <div class="chart-control">
            <select class="chart-select" `+ modelName + `="borderType">
                <option value="">---请选择---</option>
                <option value="solid">实线</option>
                <option value="dashed">虚线</option>
                <option value="dotted">点缀</option>
            </select>
        </div>
        </div>`);
      chartBody.append(borderTypeContainer);
      //虚线偏移
      const borderDashOffsetContainer = $(`<div class="chart-item flex">
          <div class="chart-label">偏移量</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="虚线" ` + modelName + `="borderDashOffset" />
          </div>
        </div>`);
      chartBody.append(borderDashOffsetContainer);
      
      // const borderMiterLimitContainer = $(`<div class="chart-item flex">
      //     <div class="chart-label">限制比例</div>
      //     <div class="chart-control">
      //         <input type="number" class="chart-number" min="0" max="" placeholder="斜接面限制比例" ` + modelName + `="shadowBlur" />
      //     </div>
      //   </div>`);
      // chartBody.append(borderMiterLimitContainer);

      // const borderCapContainer = $(`<div class="chart-item flex">
      //     <div class="chart-label">绘制方式</div>
      //     <div class="chart-control">
      //         <select class="chart-select" `+ modelName + `="borderCap">
      //             <option value="">---请选择---</option>
      //             <option value="butt">方形</option>
      //             <option value="round">圆形</option>
      //             <option value="square">形状</option>
      //         </select>
      //     </div>
      //   </div>`);
      // chartBody.append(borderCapContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "borderStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "边框样式"
  }
}