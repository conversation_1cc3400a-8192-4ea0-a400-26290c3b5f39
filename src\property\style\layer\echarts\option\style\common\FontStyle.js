import EchartsStyle from "../../../EchartsStyle";
export default class FontStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //字体大小
      const fontSizeContainer = $(
        `<div class="chart-item flex">
          <div class="chart-label">大小</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="字体大小" ` +
          modelName +
          `="fontSize" />
          </div>
      </div>`
      );
      chartBody.append(fontSizeContainer);
      //字体行高
      const lineHeightContainer = $(
        `<div class="chart-item flex">
            <div class="chart-label">行高</div>
            <div class="chart-control">
                <input type="number" class="chart-number" min="0" max="" placeholder="行高" ` +
          modelName +
          `="lineHeight" />
            </div>
        </div>`
      );
      chartBody.append(lineHeightContainer);
      //字体风格
      const fontStyleContainer = $(
        `<div class="chart-item flex">
            <div class="chart-label">风格</div>
            <div class="chart-control">
                <select class="chart-select" ` +
          modelName +
          `="fontStyle">
                    <option value="">---请选择---</option>
                    <option value="normal">正常</option>
                    <option value="italic">斜体字</option>
                    <option value="oblique">倾斜</option>
                </select>
            </div>
        </div>`
      );
      chartBody.append(fontStyleContainer);
      //字体粗细
      const fontWeightContainer = $(
        `<div class="chart-item flex">
            <div class="chart-label">粗细</div>
            <div class="chart-control">
                <select class="chart-select" ` +
          modelName +
          `="fontWeight">
                    <option value="">---请选择---</option>
                    <option value="normal">正常</option>
                    <option value="bold">黑体</option>
                    <option value="bolder">加粗</option>
                    <option value="lighter">加亮</option>
                </select>
            </div>
        </div>`
      );
      chartBody.append(fontWeightContainer);

      const alignContainer = $(
        `<div class="chart-item flex">
          <div class="chart-label">水平对齐</div>
          <div class="chart-control">
              <select class="chart-select" ` +
          modelName +
          `="align">
                  <option value="">---请选择---</option>
                  <option value="left">居左</option>
                  <option value="center">居中</option>
                  <option value="right">居右</option>
              </select>
          </div>
        </div>`
      );
      chartBody.append(alignContainer);

      const verticalAlignContainer = $(
        `<div class="chart-item flex">
          <div class="chart-label">垂直对齐</div>
          <div class="chart-control">
              <select class="chart-select" ` +
          modelName +
          `="verticalAlign">
                  <option value="">---请选择---</option>
                  <option value="top">顶部</option>
                  <option value="middle">居中</option>
                  <option value="bottom">底部</option>
              </select>
          </div>
        </div>`
      );
      chartBody.append(verticalAlignContainer);
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {}
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "fontStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "字体样式";
  }
}
