import EchartsStyle from "../../../EchartsStyle";
export default class GapStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const leftContainer = $(`<div class="chart-item flex">
      <div class="chart-label">左间距</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="左间距" ` + modelName + `="left" />
        </div>
      </div>`);
      chartBody.append(leftContainer);
      const topContainer = $(`<div class="chart-item flex">
      <div class="chart-label">上间距</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="上间距" ` + modelName + `="top" />
        </div>
      </div>`);
      chartBody.append(topContainer);
      const rightContainer = $(`<div class="chart-item flex">
      <div class="chart-label">右间距</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="右间距" ` + modelName + `="right" />
        </div>
      </div>`);
      chartBody.append(rightContainer);
      const bottomContainer = $(`<div class="chart-item flex">
      <div class="chart-label">下间距</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="下间距" ` + modelName + `="bottom" />
        </div>
      </div>`);
      chartBody.append(bottomContainer);
      
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "gapStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "间距样式"
  }
}