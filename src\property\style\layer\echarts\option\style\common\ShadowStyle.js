import EchartsStyle from "../../../EchartsStyle";
export default class ShadowStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      const shadowColorContainer = $(`<div class="chart-item flex">
      <div class="chart-label">阴影颜色</div>
        <div class="chart-control">
            <input type="text" class="chart-color" ` + modelName + `="shadowColor" />
        </div>
      </div>`);
      chartBody.append(shadowColorContainer);

      const shadowBlurContainer = $(`<div class="chart-item flex">
      <div class="chart-label">阴影长度</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="阴影长度" ` + modelName + `="shadowBlur" />
        </div>
      </div>`);
      chartBody.append(shadowBlurContainer);

      const shadowOffsetXContainer = $(`<div class="chart-item flex">
      <div class="chart-label">阴影 X</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="偏移距离" ` + modelName + `="shadowOffsetX" />
        </div>
      </div>`);
      chartBody.append(shadowOffsetXContainer);

      const shadowOffsetYContainer = $(`<div class="chart-item flex">
      <div class="chart-label">阴影 Y</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="偏移距离" ` + modelName + `="shadowOffsetY" />
        </div>
      </div>`);
      chartBody.append(shadowOffsetYContainer);
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "shadowStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "阴影样式"
  }
}