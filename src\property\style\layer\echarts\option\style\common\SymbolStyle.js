import EchartsStyle from "../../../EchartsStyle";
export default class SymbolStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //是否显示Symbol
      const showSymbolContainer = $(`<div class="chart-item">
          <div class="flex">
            <span class="chart-span">是否显示</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="showSymbol">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(showSymbolContainer);
      //是否显示Symbol
      const isCropContainer = $(`<div class="chart-item">
          <div class="flex">
            <span class="chart-span">是否剪裁图形</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="symbolClip">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(isCropContainer);
      //元素是否重复
      const symbolRepeatContainer = $(`<div class="chart-item">
          <div class="flex">
            <span class="chart-span">图形是否重复</span>
            <label class="chart-switch">
                <input type="checkbox" class="chart-checkbox" `+ modelName + `="symbolRepeat">
                <div class="slider round"></div>
            </label>
          </div>
        </div>`);
      chartBody.append(symbolRepeatContainer);
      
      const symbolPositionBarMaxWidthContainer = $(`<div class="chart-item">
        <div class="flex-1 flex">
            <div class="chart-label">标记形状</div>
            <div class="chart-control">
                <select class="chart-select" `+ modelName + `="symbol" >
                        <option value="">请选择</option>
                        <option value="circle">圆</option>
                        <option value="rect">矩形</option>
                        <option value="roundRect">圆角矩形</option>
                        <option value="triangle">三角形</option>
                        <option value="diamond">菱形</option>
                        <option value="pin">大头针</option>
                        <option value="arrow">箭头</option>
                        <option value="none">无</option>
                </select>
            </div>
        </div>
        </div>`);
      chartBody.append(symbolPositionBarMaxWidthContainer);
      const positionBarMaxWidthContainer = $(`<div class="chart-item">
            <div class="flex-1 flex">
                <div class="chart-label">图形定位</div>
                <div class="chart-control">
                    <select class="chart-select" `+ modelName + `="symbolPosition" >
                            <option value="">请选择</option>
                            <option value="start">起点</option>
                            <option value="end">末尾</option>
                            <option value="center">中间</option>
                    </select>
                </div>
            </div>
        </div>`);
      chartBody.append(positionBarMaxWidthContainer);
      const symbolOffsetContainer = $(`<div class="chart-item flex">
                <div class="flex-1 flex">
                    <div class="chart-label">符号偏移</div>
                    <div class="chart-control">
                        <input type="text"  class="chart-text"  `+ modelName + `="symbolOffset" />
                    </div>
                </div>
            </div>`);
      chartBody.append(symbolOffsetContainer);
      const symbolSizeContainer = $(`<div class="chart-item flex">
                <div class="flex-1 flex">
                    <div class="chart-label">符号大小</div>
                    <div class="chart-control">
                        <input type="text" class="chart-text"  `+ modelName + `="symbolSize"  />
                    </div>
                </div>
            </div>`);
      chartBody.append(symbolSizeContainer);
      const symbolRepeatSymbolMarginContainer = $(`<div class="chart-item flex">
              <div class="flex-1 flex">
                    <div class="chart-label">图形间隔</div>
                    <div class="chart-control">
                        <input type="text" class="chart-text"  `+ modelName + `="symbolMargin"  />
                    </div>
                </div>
            </div>`);
      chartBody.append(symbolRepeatSymbolMarginContainer);

        //     const symbolRepeatSymbolMarginContainer = $(`<div class="chart-item flex">
        // <div class="w-50 pl5">
        //         <div class="chart-label">图形重复</div>
        //         <div class="chart-control">
        //             <select class="chart-select" `+ modelName + `="symbolRepeat" >
        //                     <option value="">请选择</option>
        //                     <option value="fixed">固定</option>
        //                     <option value="false">重复</option>
        //                     <option value="true">不重复</option>
        //             </select>
        //         </div>
        //     </div>
        //     <div class="w-50 pl5">
        //         <div class="chart-label">图形间隔</div>
        //         <div class="chart-control">
        //             <input type="text" class="chart-text"  `+ modelName + `="symbolMargin"  />
        //         </div>
        //     </div>
        // </div>`);
        //     chartBody.append(symbolRepeatSymbolMarginContainer);

      // // 图形类型
      // const symbolContainer = $(`<div class="chart-item flex">
      //   <div class="chart-label">图形类型</div>
      //   <div class="chart-control">
      //       <select class="chart-select" `+ modelName + `="symbol">
      //           <option value="none">无</option>
      //           <option value="circle">圆形</option>
      //           <option value="rect">矩形</option>
      //           <option value="triangle">三角形</option>
      //           <option value="diamond">菱形</option>
      //           <option value="pin">钉形</option>
      //           <option value="arrow">箭头</option>
      //           <option value="roundRect">圆形矩形</option>
      //       </select>
      //   </div>
      // </div>`);
      // chartBody.append(symbolContainer);

      // // 图形大小
      // const symbolSizeContainer = $(`<div class="chart-item flex">
      //   <div class="chart-label">图形大小</div>
      //   <div class="chart-control">
      //       <input type="text" class="chart-text" min="0" max="" placeholder="[20, 10]" ` + modelName + `="symbolSize" />
      //   </div>
      // </div>`);
      // chartBody.append(symbolSizeContainer);

      // // 两边间隔
      // const symbolMarginContainer = $(`<div class="chart-item flex">
      //   <div class="chart-label">柱块间隔</div>
      //   <div class="chart-control">
      //       <input type="number" class="chart-number" min="0" max="" placeholder="5" ` + modelName + `="symbolMargin" />
      //   </div>
      // </div>`);
      // chartBody.append(symbolMarginContainer);

      // // 图形定位
      // const symbolPositionContainer = $(`<div class="chart-item flex">
      //   <div class="chart-label">图形定位</div>
      //   <div class="chart-control">
      //       <select class="chart-select" `+ modelName + `="symbolPosition">
      //           <option value="start">开始</option>
      //           <option value="end">结束</option>
      //           <option value="center">居中</option>
      //       </select>
      //   </div>
      // </div>`);
      // chartBody.append(symbolPositionContainer);

      // // 图形偏移
      // const symbolOffsetContainer = $(`<div class="chart-item flex">
      //   <div class="chart-label">图形偏移</div>
      //   <div class="chart-control">
      //       <input type="text" class="chart-text" min="0" max="" placeholder="[0, '-50%']" ` + modelName + `="symbolOffset" />
      //   </div>
      // </div>`);
      // chartBody.append(symbolOffsetContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "symbol-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "象形"
  }
}