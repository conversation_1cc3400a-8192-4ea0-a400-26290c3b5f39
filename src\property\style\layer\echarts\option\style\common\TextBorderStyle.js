import EchartsStyle from "../../../EchartsStyle";
export default class TextShadowStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        //描边颜色
       const textBorderColorContainer = $(`<div class="chart-item flex">
       <div class="chart-label">描边颜色</div>
         <div class="chart-control">
           <input type="text" class="chart-color" ` + modelName + `="textBorderColor" />
         </div>
       </div>`);
       chartBody.append(textBorderColorContainer);
      //描边宽度
      const textBorderWidthContainer = $(`<div class="chart-item flex">
        <div class="chart-label">描边宽度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="描边宽度" ` + modelName + `="textBorderWidth" />
          </div>
        </div>`);
      chartBody.append(textBorderWidthContainer);
      //描边类型
      const textBorderTypeContainer = $(`<div class="chart-item flex">
          <div class="chart-label ">描边类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="textBorderType">
                  <option value="">---请选择---</option>
                  <option value="solid">实线</option>
                  <option value="dashed">虚线</option>
                  <option value="dotted">点缀</option>
              </select>
          </div>
        </div>`);
      chartBody.append(textBorderTypeContainer);
      //虚线偏移量
      const textBorderDashOffsetContainer = $(`<div class="chart-item flex">
        <div class="chart-label">偏移量</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="虚线" ` + modelName + `="textBorderDashOffset" />
        </div>
      </div>`);
      chartBody.append(textBorderDashOffsetContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "textShadowStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "文本边框"
  }
}