import EchartsStyle from "../../../EchartsStyle";
export default class TextShadowStyle extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //阴影颜色
      const textShadowColorContainer = $(`<div class="chart-item flex">
      <div class="chart-label">阴影颜色</div>
        <div class="chart-control">
          <input type="text" class="chart-color" ` + modelName + `="textShadowColor" />
        </div>
      </div>`);
      chartBody.append(textShadowColorContainer);
      //阴影长度
      const textShadowBlurContainer = $(`<div class="chart-item flex">
        <div class="chart-label">阴影长度</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="阴影长度" ` + modelName + `="textShadowBlur" />
          </div>
        </div>`);
      chartBody.append(textShadowBlurContainer);
      //阴影 X
      const textShadowOffsetXContainer = $(`<div class="chart-item flex">
        <div class="chart-label">阴影 X</div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="偏移量" ` + modelName + `="textShadowOffsetX" />
          </div>
        </div>`);
      chartBody.append(textShadowOffsetXContainer);
      //阴影 Y
      const textShadowOffsetYContainer = $(`<div class="chart-item flex">
        <div class="chart-label">阴影 Y </div>
          <div class="chart-control">
              <input type="number" class="chart-number" min="0" max="" placeholder="偏移量" ` + modelName + `="textShadowOffsetY" />
          </div>
        </div>`);
      chartBody.append(textShadowOffsetYContainer);
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "textShadowStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "文本阴影"
  }
}