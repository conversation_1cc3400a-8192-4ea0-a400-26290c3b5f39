import Brush from "./tool/Brush";
import DataView from "./tool/DataView";
import DataZoom from "./tool/DataZoom";
import MagicType from "./tool/MagicType";
import Restore from "./tool/Restore";
import SaveAsImage from "./tool/SaveAsImage";
import EchartsStyle from "../../../EchartsStyle";
export default class Feature extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      if (!item.brush) {
        item.brush = { show: false };
      }
      const brush = new Brush(this.context);
      brush.initPanel(chartBody, item.brush, callback);

      if (!item.dataView) {
        item.dataView = { show: false };
      }
      const dataView = new DataView(this.context);
      dataView.initPanel(chartBody, item.dataView, callback);

      if (!item.dataZoom) {
        item.dataZoom = { show: false };
      }
      const dataZoom = new DataZoom(this.context);
      dataZoom.initPanel(chartBody, item.dataZoom, callback);

      if (!item.magicType) {
        item.magicType = { show: false };
      }
      const magicType = new MagicType(this.context);
      magicType.initPanel(chartBody, item.magicType, callback);

      if (!item.restore) {
        item.restore = { show: false };
      }
      const restore = new Restore(this.context);
      restore.initPanel(chartBody, item.restore, callback);

      if (!item.saveAsImage) {
        item.saveAsImage = { show: false };
      }
      const saveAsImage = new SaveAsImage(this.context);
      saveAsImage.initPanel(chartBody, item.saveAsImage, callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "feature-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "配置项"
  }
}