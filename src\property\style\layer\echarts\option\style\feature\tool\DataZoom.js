import EchartsStyle from "../../../../EchartsStyle";
export default class DataZoom extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "dataZoom-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "数据区域缩放"
  }
}