import EchartsStyle from "../../../../EchartsStyle";
export default class MagicType extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);

      const overflow_ellipsisContainer = $(`<div class="chart-item flex">
      <div class="chart-label">类型</div>
      <div class="chart-control">
          <input type="text" class="chart-text" placeholder="0,0,0,0" ` + modelName + `="type" />
      </div>
    </div>`);
      chartBody.append(overflow_ellipsisContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "magicType-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "动态类型切换"
  }
}