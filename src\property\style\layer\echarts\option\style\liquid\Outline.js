import ItemStyle from "../ItemStyle";
import EchartsStyle from "../../../EchartsStyle";
export default class Outline extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //通用
      this.loadShow(chartBody, modelName);

      //边框间距
      const borderWidthContainer = $(`<div class="chart-item flex">
      <div class="chart-label">边框间距</div>
        <div class="chart-control">
            <input type="number" class="chart-number"  `+ modelName + `="borderDistance"  />
        </div>
      </div>`);
      chartBody.append(borderWidthContainer);

      //项样式
      if (!item["itemStyle"]) {
        item["itemStyle"] = {};
      }
      const itemStyle = new ItemStyle(this.context);
      itemStyle.initPanel(chartBody, item["itemStyle"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "outline-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "外线配置"
  }
}