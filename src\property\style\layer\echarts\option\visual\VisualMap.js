import TextStyle from "../TextStyle";
import GapStyle from "../style/common/GapStyle";
import VisualRange from "./VisualRange";
import VisualPieces from "./VisualPieces";

import EchartsStyle from "../../EchartsStyle";
export default class VisualMap extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const showContainer = $(`<div class="chart-item flex">
        <div class="w-50 flex">
          <span class="chart-span">是否显示标签</span>
          <label class="chart-switch">
              <input type="checkbox" class="chart-checkbox" `+ modelName + `="showLabel">
              <div class="slider round"></div>
          </label>
        </div>
      </div>`);
      chartBody.append(showContainer);
      //通用
      this.loadShow(chartBody, modelName);
      // this.loadPadding(chartBody, modelName);
      // this.loadBackgroundColor(chartBody, modelName);
      // this.loadFormatter(chartBody, modelName);

      const typeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="type">
                  <option value="">---请选择---</option>
                  <option value="piecewise">分段型</option>
                  <option value="continuous">连续型</option>
              </select>
          </div>
        </div>`);
      chartBody.append(typeContainer);

      const itemWidthContainer = $(`<div class="chart-item flex">
      <div class="chart-label">最小值</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="最小值" ` + modelName + `="itemWidth" />
        </div>
      </div>`);
      chartBody.append(itemWidthContainer);

      const itemHeightContainer = $(`<div class="chart-item flex">
      <div class="chart-label">最大值</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="最大值" ` + modelName + `="itemHeight" />
        </div>
      </div>`);
      chartBody.append(itemHeightContainer);

      const orientContainer = $(`<div class="chart-item flex">
          <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="orient">
                  <option value="">---请选择---</option>
                    <option value="horizontal">水平</option>
                    <option value="vertical">垂直</option>
              </select>
          </div>
        </div>`);
      chartBody.append(orientContainer);

      const alignContainer = $(`<div class="chart-item flex">
          <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="align">
                  <option value=>请选择</option>
                  <option value="auto">自动</option>
                  <option value="left">左边</option>
                  <option value="right">右边</option>
                  <option value="bottom">底部</option>
              </select>
          </div>
        </div>`);
      chartBody.append(alignContainer);

      //间距
      const gapStyle = new GapStyle(this.context);
      gapStyle.initPanel(chartBody, item, callback);

      if (!item["textStyle"]) {
        item["textStyle"] = {};
      }
      const textStyle = new TextStyle(this.context);
      textStyle.initPanel(chartBody, item["textStyle"], callback);

      // this.visualInRangeContainer = $(`<div class="visual-in-range"></div>`);
      // chartBody.append(this.visualInRangeContainer);

      // this.visualPiecesContainer = $(`<div class="visual-pieces"></div>`);
      // chartBody.append(this.visualPiecesContainer);

      if(!item.inRange){
        item.inRange={};
      }
      const visualRange = new VisualRange(this.context);
      visualRange.initPanel(chartBody,item.inRange,callback);

      if(!item.pieces){
        item.pieces=[];
      }
      const visualPieces = new VisualPieces(this.context);
      visualPieces.initPanel(chartBody,item.pieces,callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "visualMap-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "视觉映射组件"
  }
}