import ColorStyle from "../color/ColorStyle";
import EchartsStyle from "../../EchartsStyle";
export default class VisualPieceItem extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const minContainer = $(`<div class="chart-item flex">
      <div class="chart-label">最小值</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="最小值" ` + modelName + `="min" />
        </div>
      </div>`);
      chartBody.append(minContainer);

      const maxContainer = $(`<div class="chart-item flex">
      <div class="chart-label">最大值</div>
        <div class="chart-control">
            <input type="number" class="chart-number" min="0" max="" placeholder="最大值" ` + modelName + `="max" />
        </div>
      </div>`);
      chartBody.append(maxContainer);

      const labelContainer = $(`<div class="chart-item flex">
      <div class="chart-label">文本</div>
        <div class="chart-control">
            <input type="text" class="chart-text" min="0" max="" placeholder="视觉图例文本" ` + modelName + `="label" />
        </div>
      </div>`);
      chartBody.append(labelContainer);

      const symbolTypeContainer = $(`<div class="chart-item flex">
        <div class="chart-label">标记类型</div>
        <div class="chart-control">
            <select class="chart-select" `+modelName+`="symbol" >
                <option value="none">默认</option>
                <option value="circle">圆点</option>
                <option value="rect">矩形</option>
                <option value="roundRect">圆矩形</option>
                <option value="triangle">三角形</option>
                <option value="diamond">钻石</option>
                <option value="pin">大头针</option>
                <option value="arrow">箭头</option> 
            </select>
        </div>
      </div>`);
      chartBody.append(symbolTypeContainer);

       //颜色
      if(!item["color"]){
        item["color"] ={};
      }
      const colorStyle = new ColorStyle(this.context);
      colorStyle.initPanel(chartBody,item["color"],callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "visualPieceItem-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "分段视觉"
  }
}