import VisualPieceItem from "./VisualPieceItem";
import EchartsStyle from "../../EchartsStyle";
export default class VisualPieces extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  afterAddItem (itemContainer, item, index, callback) {
    const model = new VisualPieceItem(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("分段[" + (index + 1) + "]视觉");
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "分段视觉"
  }
}
