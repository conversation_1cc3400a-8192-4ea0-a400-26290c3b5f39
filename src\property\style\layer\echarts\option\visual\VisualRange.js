import ColorStyle from "../color/ColorStyle";
import EchartsStyle from "../../EchartsStyle";
export default class VisualRange extends EchartsStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        const symbolTypeContainer = $(`<div class="chart-item flex">
            <div class="chart-label">标记类型</div>
            <div class="chart-control">
                <select class="chart-select" `+modelName+`="symbol" >
                    <option value="none">默认</option>
                    <option value="circle">圆点</option>
                    <option value="rect">矩形</option>
                    <option value="roundRect">圆矩形</option>
                    <option value="triangle">三角形</option>
                    <option value="diamond">钻石</option>
                    <option value="pin">大头针</option>
                    <option value="arrow">箭头</option> 
                </select>
            </div>
        </div>`);
        chartBody.append(symbolTypeContainer);

        const symbolSizeContainer = $(`<div class="chart-item flex">
            <div class="chart-label">标记宽高</div>
            <div class="chart-control">
                <input type="text" placeholder="范围值[0-1]"  class="chart-number" `+modelName+`="symbolSize" />
            </div>
        </div>`);
        chartBody.append(symbolSizeContainer);

        const colorAlphaContainer = $(`<div class="chart-item flex">
            <div class="chart-label">透明度</div>
            <div class="chart-control">
                <input type="text" placeholder="范围值[0-1]" class="chart-number" `+modelName+`="colorAlpha" />
            </div>
        </div>`);
        chartBody.append(colorAlphaContainer);

        const colorHueContainer = $(`<div class="chart-item flex">
            <div class="chart-label">色调</div>
            <div class="chart-control">
                <input type="text" placeholder="范围值[0-360]" class="chart-number" `+modelName+`="colorHue" />
            </div>
        </div>`);
        chartBody.append(colorHueContainer);

        const colorLightnessContainer = $(`<div class="chart-item flex">
            <div class="chart-label">明暗度</div>
            <div class="chart-control">
                <input type="text" placeholder="范围值[0-1]" class="chart-number" `+modelName+`="colorLightness" />
            </div>
        </div>`);
        chartBody.append(colorLightnessContainer);

         //颜色
        if(!item["color"]){
            item["color"] ={};
        }
        const colorStyle = new ColorStyle(this.context);
        colorStyle.initPanel(chartBody,item["color"],callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "visualRange-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "连续视觉"
  }
}