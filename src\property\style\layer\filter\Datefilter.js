import CardStyle from "../../CardStyle";
export default class Datefilter extends CardStyle {
  constructor(context, isOpen) {
    super(context, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //是否自动关闭  是否显示今天按钮
      const autocloseContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否自动关闭</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="autoclose">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
      chartBody.append(autocloseContainer);
      const todayBtnContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否显示今天</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="todayBtn">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
      chartBody.append(todayBtnContainer);
      //是否高亮当前日期  是否箭头导航
      const todayHighlightContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否高亮日期</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="todayHighlight">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
      chartBody.append(todayHighlightContainer);
      const keyboardNavigationContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否方向键</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="keyboardNavigation">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
      chartBody.append(keyboardNavigationContainer);

      //是否校验日期格式 是否禁止未来时间选择
      const forceParseContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">是否校验</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="forceParse">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
      chartBody.append(forceParseContainer);

      //禁用未来时间
      const futureDayContainer = $(`<div class="chart-item flex">
            <div class="w-50 flex">
                <span class="chart-span">禁用结束时间</span>
                <label class="chart-switch">
                    <input type="checkbox" class="chart-checkbox" `+ modelName + `="realDate.futureDay">
                    <div class="slider round"></div>
                </label>
            </div>
        </div>`);
      chartBody.append(futureDayContainer);

      const startViewContainer = $(`<div class="chart-item flex"> 
            <div class="chart-label">显示视图</div>              
            <div class="chart-control">
                <select class="chart-select" ` + modelName + `="startView">
                    <option value="">--请选择--</option>
                    <option value="0">小时视图</option>
                    <option value="1">日期视图</option>
                    <option value="2">月视图</option>
                    <option value="3">年视图</option>
                    <option value="4">十年视图</option>
                </select>
            </div>
        </div>`);
      chartBody.append(startViewContainer);
      // const maxViewContainer = $(`<div class="chart-item flex"> 
      //       <div class="chart-label">最大视图</div>              
      //       <div class="chart-control">
      //           <select class="chart-select" ` + modelName + `="maxView">
      //               <option value="">--请选择--</option>
      //               <option value="0">小时视图</option>
      //               <option value="1">日期视图</option>
      //               <option value="2">月视图</option>
      //               <option value="3">年视图</option>
      //               <option value="4">十年视图</option>
      //           </select>
      //       </div>
      //   </div>`);
      // chartBody.append(maxViewContainer);
      const minViewContainer = $(`<div class="chart-item flex"> 
            <div class="chart-label">最小视图</div>              
            <div class="chart-control">
                <select class="chart-select" ` + modelName + `="minView">
                    <option value="">--请选择--</option>
                    <option value="0">小时视图</option>
                    <option value="1">日期视图</option>
                    <option value="2">月视图</option>
                    <option value="3">年视图</option>
                    <option value="4">十年视图</option>
                </select>
            </div>
        </div>`);
      chartBody.append(minViewContainer);
      // 开始周 和 样式主题
      const weekStartContainer = $(`<div class="chart-item flex">
            <div class="chart-label">开始周</div>              
            <div class="chart-control">
                <select class="chart-select" ` + modelName + `="weekStart">
                    <option value="">--请选择--</option>
                    <option value="0">星期天</option>
                    <option value="1">星期一</option>
                    <option value="2">星期二</option>
                    <option value="3">星期三</option>
                    <option value="4">星期四</option>
                    <option value="5">星期五</option>
                    <option value="6">星期六</option>
                </select>
            </div>
        </div>`);
      chartBody.append(weekStartContainer);

      //初始日期
      // const initDateContainer = $(`<div class="chart-item flex">  
      //     <div class="chart-label">初始日期</div>                    
      //     <div class="chart-control">
      //         <input type="text" class="chart-text" ` + modelName + `="initDate" />
      //     </div>
      // </div>`);
      // chartBody.append(initDateContainer);
      //开始日期
      const startDateContainer = $(`<div class="chart-item flex">  
            <div class="chart-label">最小日期</div>                    
            <div class="chart-control">
                <input type="text" class="chart-text" ` + modelName + `="startDate" />
            </div>
        </div>`);
      chartBody.append(startDateContainer);

      //结束日期
      this.endDateContainer = $(`<div class="chart-item flex">  
            <div class="chart-label">最大日期</div>                    
            <div class="chart-control">
                <input type="text" class="chart-text" ` + modelName + `="endDate" />
            </div>
        </div>`);
      chartBody.append(this.endDateContainer);

      //显示格式
      const showFormatContainer = $(`<div class="chart-item flex">
            <div class="chart-label">显示格式</div>
            <div class="chart-control">
                <input type="text" class="chart-text" placeholder="yyyy-mm-dd hh:ii:ss"  ` + modelName + `="format" />
            </div>
        </div>`);
      chartBody.append(showFormatContainer);
      //输入日期格式
      const inputFormatContainer = $(`<div class="chart-item flex">
            <div class="chart-label">输入格式</div>
            <div class="chart-control">
                <input type="text" class="chart-text" placeholder=""yyyy-mm-dd hh:ii:ss" ` + modelName + `="realDate.inputFormat" />
            </div>
        </div>`);
      chartBody.append(inputFormatContainer);
      //输出日期格式
      const outputFormatContainer = $(`<div class="chart-item flex">
            <div class="chart-label">输出格式</div>
            <div class="chart-control">
                <input type="text" class="chart-text" placeholder="填入输出日期格式:yyyy-MM-dd" ` + modelName + `="realDate.outputFormat" />
            </div>
        </div>`);
      chartBody.append(outputFormatContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    if (key && key === "define.futureDay") {
      if (value) {
        this.endDateContainer.hide();
        this.item["endDate"] = "";
      } else {
        this.endDateContainer.show();
      }
    }

    if(value && key && key === "startView"){
      this.item["startView"] =  parseInt(value);
    }
    if(value && key && key === "minView") {
      this.item["minView"] = parseInt(value);
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "datefilter-model";
  }

  /**
* 描述:标题信息
* @returns {string}
*/
  getTitle () {
    return "日期"
  }
}