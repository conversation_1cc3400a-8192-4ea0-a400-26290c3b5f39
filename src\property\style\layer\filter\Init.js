import TreeStyle from "../../TreeStyle";
export default class Init extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  setDatasets (datasets) {
    this.datasets = datasets;
  }
  setParams (params) {
    this.params = params;
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      const keyContainer = $(`<div class="chart-item flex">
          <div class="chart-label">键(映射)</div>
          <div class="chart-control">
              <input type="text" disabled readonly class="chart-text" ` + modelName + `="key" />
          </div>
      </div>`);
      chartBody.append(keyContainer);

      const typeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" ` + modelName + `="type" >
                  <option value="">---请选择---</option>
                  <option value="dataset">绑定数据集</option>
                  <option value="param">绑定参数</option>
              </select>
          </div>
      </div>`);
      chartBody.append(typeContainer);

      this.datasetContainer = $(`<div class="chart-item flex">
          <div class="chart-label">数据集</div>
          <div class="chart-control">
              <select class="chart-select" ` + modelName + `="datasetId" ></select>
          </div>
      </div>`);
      chartBody.append(this.datasetContainer);
      this.datasetSelector = $(this.datasetContainer).find(".chart-select");

      this.sheetContainer = $(`<div class="chart-item flex">
          <div class="chart-label">数据表</div>
          <div class="chart-control">
              <select class="chart-select" ` + modelName + `="datasetSheetId" ></select>
          </div>
      </div>`);
      chartBody.append(this.sheetContainer);
      this.sheetSelector = $(this.sheetContainer).find(".chart-select");

      this.codeContainer = $(`<div class="chart-item flex">
          <div class="chart-label">属性</div>
          <div class="chart-control">
              <select class="chart-select" ` + modelName + `="code" ></select>
          </div>
      </div>`);
      chartBody.append(this.codeContainer);
      this.codeSelector = $(this.codeContainer).find(".chart-select");


      this.valueContainer = $(`<div class="chart-item flex">
          <div class="chart-label">表达式</div>
          <div class="chart-control">
              <input type="text"   class="chart-text" ` + modelName + `="value" />
          </div>
      </div>`);
      chartBody.append(this.valueContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    if (key && key === "type") {
      if (value === "param") {
        this.codeContainer.show();
        this.valueContainer.hide();
        this.datasetContainer.hide();
        this.sheetContainer.hide();
        this.refreshOption(this.codeSelector, this.params);
      } else if (value === "dataset") {
        this.datasetContainer.show();
        this.codeContainer.hide();
        this.valueContainer.hide();
        this.refreshOption(this.datasetSelector, this.datasets);
      } else {
        this.codeContainer.hide();
        this.valueContainer.show();
      }
    }
    const item = this.item;
    if (item.type) {
      if (item.type === "dataset" && item.datasetId && key !== 'code') {
        const dataset = this.context.getDataSetById(item.datasetId);
        if (dataset) {
          const property = dataset.property;
          const columns = property.columns;
          const isModel = property.type === "model";
          const isFile = property.type === "file";

          if (isModel && value) {
            this.codeContainer.show();
            this.sheetContainer.hide();
            const column = dataset.getModelColumns();
            this.refreshOptionArray(this.codeSelector, column);
          } else if (isFile) {
            const file = property.file;
            if (file && columns && columns.length) {
              if (file.fileType === "xlsx" || file.fileType === "csv") {
                // 当选择数据集时，初始化数据表
                if (key !== 'datasetSheetId' && value) {
                  this.sheetContainer.show();
                  this.codeContainer.hide();
                  this.refreshOption(this.sheetSelector, columns, "id", "name");
                } else {
                  this.codeContainer.show();
                  const sheet = columns.find(c => c.id === value);
                  if (sheet) {
                    this.refreshOption(this.codeSelector, sheet.columns);
                  }
                }
              }
              if (file.fileType === 'json' && value) {
                this.codeContainer.show();
                this.sheetContainer.hide();
                this.refreshOptionArray(this.codeSelector, columns);
              }
            }
          }
        }
      }
    }
  }
  refreshOptionArray (selectContainer, list) {
    if (selectContainer) {
      $(selectContainer).empty();
      selectContainer.prepend("<option value=''>--请选择--</option>");//添加第一个option值
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          selectContainer.append("<option value='" + item + "'>" + item + "</option>");
        }
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "init-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "初始值"
  }
}