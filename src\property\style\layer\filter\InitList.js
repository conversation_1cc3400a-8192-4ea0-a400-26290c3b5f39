import Init from "./Init";
import TreeStyle from "../../TreeStyle";
export default class InitList extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  setDatasets(datasets) {
    this.datasets = datasets;
  }
  setParams(params) {
    this.params = params;
  }
  afterAddItem(itemContainer, item, index, callback) {
    const model = new Init(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("初始值[" + (item.key) + "]");
    model.setDatasets(this.datasets)
    model.setParams(this.params);
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "初始值列表"
  }
}