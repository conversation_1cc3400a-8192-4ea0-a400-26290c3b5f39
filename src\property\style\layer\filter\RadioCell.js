
import BgStyle from "../style/BgStyle";
import BorderStyle from "../style/BorderStyle";
import FontStyle from "../style/FontStyle";
import RadioSelected from "./RadioSelected";
import TreeStyle from "../../TreeStyle";
export default class RadioCell extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //内外间距
      const paddingContainer = $(`<div class="chart-item flex">
            <div class="chart-label">内间距</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  placeholder="5px 5px 5px 5px" ` + modelName + `="padding"  />
            </div>
        </div>`);
      chartBody.append(paddingContainer);

      const marginContainer = $(`<div class="chart-item flex">
            <div class="chart-label">外间距</div>
            <div class="chart-control">
                <input type="text" class="chart-text"  placeholder="5px 5px 5px 5px"  ` + modelName + `="margin"  />
            </div>
        </div>`);
      chartBody.append(marginContainer);

      if (!item["bg"]) {
        item["bg"] = {};
      }
      const bgStyle = new BgStyle(this.context);
      bgStyle.initPanel(chartBody, item["bg"], callback);

      if (!item["font"]) {
        item["font"] = {};
      }
      const fontStyle = new FontStyle(this.context);
      fontStyle.initPanel(chartBody, item["font"], callback);

      if (!item["border"]) {
        item["border"] = {};
      }
      const borderStyle = new BorderStyle(this.context);
      borderStyle.initPanel(chartBody, item["border"], callback);

      if (!item["selected"]) {
        item["selected"] = {};
      }
      const radioSelected = new RadioSelected(this.context);
      radioSelected.initPanel(chartBody, item["selected"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "radioCell-model";
  }

  /**
* 描述:标题信息
* @returns {string}
*/
  getTitle () {
    return "单元格"
  }
}