
import BorderStyle from "../style/BorderStyle";
import BgStyle from "../style/BgStyle";
import FontStyle from "../style/FontStyle";
import TreeStyle from "../../TreeStyle";
export default class SelectStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }

  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //字体
      if (!item["font"]) {
        item["font"] = {};
      }
      const fontStyle = new FontStyle(this.context);
      fontStyle.initPanel(chartBody, item["font"], callback);

      if (!item["bg"]) {
        item["bg"] = {};
      }
      const bgStyle = new BgStyle(this.context);
      bgStyle.initPanel(chartBody, item["bg"], callback);

      if (!item["border"]) {
        item["border"] = {};
      }
      const borderStyle = new BorderStyle(this.context);
      borderStyle.initPanel(chartBody, item["border"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "selectStyle-model";
  }

  /**
* 描述:标题信息
* @returns {string}
*/
  getTitle () {
    return "多选"
  }
}