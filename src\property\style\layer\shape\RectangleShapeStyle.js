import BorderStyle from "../style/BorderStyle";
import BgStyle from "../style/BgStyle";
import TreeStyle from "../../TreeStyle";
export default class RectangleShapeStyle extends TreeStyle{
    constructor(context, isOpen) {
        super(context, false, isOpen);
        }

refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        // //公共
        // const  commonStyle = new CommonStyle(this.context);
        // commonStyle.initPanel(chartBody, item, callback);
        
        if(!item["bg"]){
          item["bg"]={};
        }
        const bgStyle = new BgStyle(this.context);
        bgStyle.initPanel(chartBody,item["bg"],callback);

        if(!item["border"]){
          item["border"]={};
        }
        const borderStyle = new BorderStyle(this.context);
        borderStyle.initPanel(chartBody,item["border"],callback);
        
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
}
    refreshEvent(key, value) {

    }
    /**
     *
     * @returns {string}
     */
    getModelName() {
        return "rectangleShape-model";
    }

     /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "长方形"
  }
}