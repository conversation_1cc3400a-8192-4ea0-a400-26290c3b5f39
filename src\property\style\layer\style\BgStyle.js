import TreeStyle from "../../TreeStyle";
import ImageStyle from "./ImageStyle";
import ColorStyle from "./color/ColorStyle";
export default class BgStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const typeContainer = $(`<div class="chart-item mt10">
          <div class="chart-label">类型</div>
          <div class="chart-control flex-1">
              <select class="chart-select" `+ modelName + `="bgType">
                  <option value="">---请选择---</option>
                  <option value="color">颜色</option>
                  <option value="image">图片</option>
              </select>
          </div>
          </div>`);
      chartBody.append(typeContainer);
      //颜色
      this.colorContainer = $(`<div class="bg-color mb10"></div>`);
      chartBody.append(this.colorContainer);
      //图片
      this.imageContainer = $(`<div class="bg-image mb10"></div>`);
      chartBody.append(this.imageContainer);

      if (!item["color"]) {
        item["color"] = {}
      }
      const colorStyle = new ColorStyle(this.context);
      colorStyle.initPanel(this.colorContainer, item["color"], callback);

      if (!item["image"]) {
        item["image"] = { type: "2" }
      }
      const imageStyle = new ImageStyle(this.context);
      imageStyle.initPanel(this.imageContainer, item["image"], callback);

      //透明度
      const opacityContainer = $(`<div class="chart-item">
          <div class="chart-label">透明度</div>
          <div class="chart-control">
              <input type="number" class="chart-number"  placeholder="" ` + modelName + `="opacity" />
          </div>
      </div>`);
      chartBody.append(opacityContainer);
      
      //模糊度
      const filterContainer = $(`<div class="chart-item">
          <div class="chart-label">模糊度</div>
          <div class="chart-control">
              <input type="text" class="chart-text"  placeholder="blur(5px)" ` + modelName + `="filter" />
          </div>
      </div>`);
      chartBody.append(filterContainer);

      //毛玻璃
      const backdropfilterContainer = $(`<div class="chart-item">
          <div class="chart-label">毛玻璃</div>
          <div class="chart-control">
              <input type="text" class="chart-text"  placeholder="blur(5px)" ` + modelName + `="backdropfilter" />
          </div>
      </div>`);
      chartBody.append(backdropfilterContainer);

      //旋转角度
      const rotateContainer = $(`<div class="chart-item">
          <div class="chart-label">旋转角度</div>
          <div class="chart-control">
              <input type="number" class="chart-number"  placeholder="" ` + modelName + `="rotate" />
          </div>
      </div>`);
      chartBody.append(rotateContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    if (key && key == "bgType") {
      if (value === "color") {
        this.colorContainer.show();
        this.imageContainer.hide();
      } else if (value === "image") {
        this.colorContainer.hide();
        this.imageContainer.show();
      } else {
        this.colorContainer.hide();
        this.imageContainer.hide();
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "bg-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "背景"
  }
}