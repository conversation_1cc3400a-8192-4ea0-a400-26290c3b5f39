import TreeStyle from "../../TreeStyle";
import ImageStyle from "./ImageStyle";
import BoxShadowStyle from "./BoxShadowStyle";
export default class BorderStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const typeContainer = $(`<div class="chart-item">
          <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="bdType">
                  <option value="">---请选择---</option>
                  <option value="color">颜色</option>
                  <option value="image">图片</option>
              </select>
          </div>
          </div>`);
      chartBody.append(typeContainer);
      //颜色
      this.colorContainer = $(`<div class="bd-color mb10"></div>`);
      chartBody.append(this.colorContainer);
      const borderColorContainer = $(`<div class="chart-item">
          <div class="chart-label">颜色</div>
          <div class="chart-control" style="padding: 0">
              <input type="text" class="chart-color" ` + modelName + `="borderColor" />
          </div>
      </div>`);
      this.colorContainer.append(borderColorContainer);
      //图片
      this.imageContainer = $(`<div class="bd-image mb10"></div>`);
      chartBody.append(this.imageContainer);

      if (!item["image"]) {
        item["image"] = { type: "3" }
      }
      const imageStyle = new ImageStyle(this.context);
      imageStyle.initPanel(this.imageContainer, item["image"], callback);


      const borderWidthContainer = $(`<div class="chart-item">
          <div class="chart-label">宽度</div>
          <div class="chart-control">
              <input type="text" class="chart-text"  placeholder="1px 1px 1px 1px" ` + modelName + `="borderWidth" />
          </div>
        </div>`);
      chartBody.append(borderWidthContainer);

      const borderSliceContainer = $(`<div class="chart-item">
          <div class="chart-label">裁剪</div>
          <div class="chart-control">
              <input type="text" class="chart-text"  placeholder="1 1 1 1" ` + modelName + `="borderSlice" />
          </div>
        </div>`);
      chartBody.append(borderSliceContainer);
      //边框圆角
      const borderRadiusContainer = $(`<div class="chart-item">
          <div class="chart-label">圆角</div>
          <div class="chart-control">
              <input type="text" class="chart-text"  placeholder="1px 1px 1px 1px" ` + modelName + `="borderRadius" />
          </div>
        </div>`);
      chartBody.append(borderRadiusContainer);
      //边框类型 
      const borderStyleContainer = $(`<div class="chart-item">
          <div class="chart-label">样式</div>
          <div class="chart-control">
              <select class="chart-select" ` + modelName + `="borderStyle" >
                  <option value="">--请选择--</option>
                  <option value="solid">实线边框</option>
                  <option value="dashed">虚线边框</option>
                  <option value="dotted">点线边框</option>
                  <option value="double">双线边框</option>
                  <option value="inset">3D嵌入式边框</option>
                  <option value="outset">3D突出式边框</option>
                  <option value="groove">3D沟槽边框</option>
                  <option value="ridge">3D脊边框</option>
              </select>
          </div>
        </div>`);
      chartBody.append(borderStyleContainer);

      if (!item["boxShadow"]) {
        item["boxShadow"] = {};
      }
      const boxShadowStyle = new BoxShadowStyle(this.context);
      boxShadowStyle.initPanel(chartBody, item["boxShadow"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    if (key && key == "bdType") {
      if (value === "color") {
        this.colorContainer.show();
        this.imageContainer.hide();
      } else if (value === "image") {
        this.colorContainer.hide();
        this.imageContainer.show();
      } else {
        this.colorContainer.hide();
        this.imageContainer.hide();
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "border-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "边框属性"
  }
}