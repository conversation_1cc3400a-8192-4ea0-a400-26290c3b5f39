import TreeStyle from "../../TreeStyle";
export default class BoxShadowStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      // offsetX 和 offsetY 指定阴影的水平和垂直偏移量。
      // blur 指定模糊距离。
      // spread指定扩散距离。
      // color 指定阴影的颜色。
      // inset 关键字（可选）指定内阴影而不是外阴影。

      //X偏移 和 Y偏移
      const hShadowContainer = $(`<div class="chart-item">
        <div class="chart-label">X偏移</div>
        <div class="chart-control">
            <input type="number" class="chart-number"  min="0" max="" placeholder="" ` + modelName + `="hShadow" />
        </div>
      </div>`);
      chartBody.append(hShadowContainer);

      const vShadowContainer = $(`<div class="chart-item">
        <div class="chart-label">Y偏移</div>
        <div class="chart-control">
            <input type="number" class="chart-number"  min="0" max="" placeholder="" ` + modelName + `="vShadow" />
        </div>
      </div>`);
      chartBody.append(vShadowContainer);

      //模糊
      const blurContainer = $(`<div class="chart-item">
        <div class="chart-label">模糊距离</div>
        <div class="chart-control">
            <input type="number" class="chart-number"  min="0" max="" placeholder="" ` + modelName + `="blur" />
        </div>
      </div>`);
      chartBody.append(blurContainer);
      //颜色
      const colorConctainer = $(`<div class="chart-item">
        <div class="chart-label">颜色</div>
        <div class="chart-control" style="padding: 0">
            <input type="text" class="chart-color"  ` + modelName + `="shadowColor" />
        </div>
      </div>`);
      chartBody.append(colorConctainer);

      const spreadContainer = $(`<div class="chart-item">
        <div class="chart-label">扩散距离</div>
        <div class="chart-control">
            <input type="text" class="chart-text"  min="0" max="" placeholder="" ` + modelName + `="spread" />
        </div>
      </div>`);
      chartBody.append(spreadContainer);

      const shadowTypeContainer = $(`<div class="chart-item">
        <div class="chart-label">内外</div>
        <div class="chart-control">
            <select class="chart-select" ` + modelName + `="shadowType" >
                <option value="">--请选择--</option>
                <option value="inset">内阴影</option>
                <option value="outset">外阴影</option>
            </select>
        </div>
      </div>`);
      chartBody.append(shadowTypeContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "boxShadow-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "阴影"
  }
}