import TreeStyle from "../../TreeStyle";
export default class BgStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //格式
      const dateformaeContainer = $(
        `<div class="chart-item">
          <div class="chart-label">格式</div>
          <div class="chart-control">
              <input type="text" class="chart-number"  placeholder="" ` +
          modelName +
          `="dateFormat" />
          </div>
      </div>`
      );
      chartBody.append(dateformaeContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {
    // if (key && key == "bgType") {
    //   if (value === "color") {
    //     this.colorContainer.show();
    //     this.imageContainer.hide();
    //   } else if (value === "image") {
    //     this.colorContainer.hide();
    //     this.imageContainer.show();
    //   } else {
    //     this.colorContainer.hide();
    //     this.imageContainer.hide();
    //   }
    // }
  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "dateformat-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "时间格式";
  }
}
