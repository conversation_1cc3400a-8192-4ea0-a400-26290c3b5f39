import TreeStyle from "../../TreeStyle";
export default class FlexStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel(chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
        //类型
        const flexContainer = $(`<div class="chart-item flex">
            <div class="chart-label">类型</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + ` = "display" >
                    <option value="">--请选择--</option>
                    <option value="flex">Flex布局</option>
                    <option value="inline-flex">Inline-Flex布局</option>
                </select>
            </div>
        </div>`);
        chartBody.append(flexContainer);

        //方向
        const flexAndDirectionContainer = $(`<div class="chart-item flex">
            <div class="chart-label">方向</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + ` = "flexDirection" >
                    <option value="">--请选择--</option>
                    <option value="row">水平方向，起点左端</option>
                    <option value="row-reverse">水平方向，起点右端</option>
                    <option value="column">垂直方向，起点上沿</option>
                    <option value="column-reverse">垂直方向，起点下沿</option>
                </select>
            </div>
        </div>`);
        chartBody.append(flexAndDirectionContainer);

        //水平对齐 垂直对齐
        const justifyContentContainer = $(`<div class="chart-item flex">
            <div class="chart-label">水平</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + ` = "justifyContent" >
                    <option value="">--请选择--</option>
                    <option value="flex-start">左对齐</option>
                    <option value="flex-end">右对齐</option>
                    <option value="center">居中</option>
                    <option value="space-between">两端对齐</option>
                    <option value="space-around">两端间隙一半</option>
                </select>
            </div>
        </div>`);
        chartBody.append(justifyContentContainer);
        const alignItemsContainer = $(`<div class="chart-item flex">
            <div class="chart-label">垂直</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + ` = "alignItems" >
                    <option value="">--请选择--</option>
                    <option value="flex-start">顶部</option>
                    <option value="flex-end">底部</option>
                    <option value="center">居中</option>
                    <option value="baseline">首行基线</option>
                    <option value="stretch">占满</option>
                </select>
            </div>
        </div>`);
        chartBody.append(alignItemsContainer);

        //换行配置
        const flexWrapContainer = $(`<div class="chart-item flex">
            <div class="chart-label">换行配置</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + ` = "flexWrap" >
                    <option value="">--请选择--</option>
                    <option value="nowrap">不换行</option>
                    <option value="wrap">换行，首行上</option>
                    <option value="wrap-reverse">换行，首行下</option>
                </select>
            </div>
        </div>`);
        chartBody.append(flexWrapContainer);
        
        //多轴对齐
        const alignContentContainer = $(`<div class="chart-item flex">
            <div class="chart-label">多轴对齐</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + ` = "alignContent" >
                    <option value="">--请选择--</option>
                    <option value="flex-start">起点对齐</option>
                    <option value="flex-end">终点对齐</option>
                    <option value="center">中点对齐</option>
                    <option value="space-between">两端对齐</option>
                    <option value="space-around">间隔相等</option>
                    <option value="stretch">占满</option>
                </select>
            </div>
        </div>`);
        chartBody.append(alignContentContainer);
      
        this.refreshModel(item);
        this.bindModel(item, callback);
    }
  }
  refreshEvent(key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName() {
    return "flex-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle() {
    return "布局"
  }
}