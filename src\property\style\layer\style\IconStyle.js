import { getIcons } from "../../../../utils/StyleUtil";
import TreeStyle from "../../TreeStyle";
export default class IconStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //  isClazz:true,
      //   family:"lsd-font",//家族(序列) fd-font fd-flag  &#xe8e0;
      //   prefix:"lsd-icon-",
      //   clazz:"lsd-icon-auto",
      this.refreshModel(item);
      this.bindModel(item, callback);
      //类型
      const listContainer = $(`<div class="lsd-icon-table"></div>`);
      chartBody.append(listContainer);
      // const iconMap = require('../../../../assets/font/lsd-font.json');
      const iconMap = getIcons();
      if (iconMap) {
        for (let key in iconMap) {
          const value = iconMap[key];
          this.refreshItem(listContainer, item, callback, key, value);
        }
      }

    }
  }
  refreshItem (listContainer, item, callback, key, value) {
    const itemContainer = $(`<div class="lsd-icon-item lsd-font ` + key + `"></div>`);
    listContainer.append(itemContainer);
    const self = this;
    $(itemContainer).on("click", function () {
      item["clazz"] = key;
      // item["text"] = "&#x"+value;
      self.refreshChecked(listContainer, key);
      if (callback) {
        callback();
      }
    });
  }
  refreshChecked (listContainer, icon) {
    if (listContainer && icon) {
      //清理选中项
      $(listContainer).find(".lsd-icon-item").each(function (index, element) {
        $(element).removeClass("lsd-icon-selected");
      });
      //添加选中项
      $(listContainer).find("." + icon).each(function (index, element) {
        $(element).addClass("lsd-icon-selected");
      });
    }
  }


  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "icon-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "图标"
  }
}