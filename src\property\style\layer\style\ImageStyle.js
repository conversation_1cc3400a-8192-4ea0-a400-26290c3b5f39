import ImageDialog from "../../../../dialog/ImageDialog";
import TreeStyle from "../../TreeStyle";
export default class ImageStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const imageContainer = $(`<div class="chart-item">
        <div class="chart-label ">图片</div>
        <div class="chart-control">
            <div class="chart-img">
              <img class="chart-img-img" title="选择"  ` + modelName + `="imageUrl" alt="选择图片"  />
              <div class="chart-img-close"><i class="lsd-font lsd-icon-close"></i></div>
            </div>
        </div>
      </div>`);
      chartBody.append(imageContainer);
      const imageSelector = imageContainer.find(".chart-img-img");
      const imageClose = imageContainer.find(".chart-img-close");
      const self = this;
      $(imageSelector).on("click", function (event) {
        const dialog = new ImageDialog(self.context);
        dialog.open(function (result) {
          if (result) {
            $(imageSelector).attr("src", result.fileUrl);
            item["id"] = result["id"];
          }
          if (callback) {
            callback();
          }
        });
      });
      //删除
      $(imageClose).on("click", function (event) {
        $(imageSelector).attr("src", "");
        if (item["id"]) {
          delete item["id"];
        }
        if (item["imageUrl"]) {
          delete item["imageUrl"];
        }
        if (callback) {
          callback();
        }
      });
      //获取素材
      if (item.id) {
        const image = this.context.getMaterialById(item.id);
        if (image) {
          const imageUrl = image.getUrl();
          $(imageSelector).attr("src", imageUrl);
        }
      }
      //双向绑定（兼容老数据）
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "image-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "图片"
  }
}