import TreeStyle from "../../TreeStyle";
import <PERSON><PERSON> from "lottie-web";
import ImageDialog from "../../../../dialog/ImageDialog";
export default class SvgAnimationSelectorStyle extends TreeStyle {
  constructor(context) {
    super(context, false, false);
    this.lottie = null;
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      const imageContainer = $(`<div class="chart-item">
        <div class="chart-label ">SVG素材</div>
        <div class="chart-control">
            <div class="chart-img">
              <div class="chart-svg-preview">选择SVG素材</div>
              <div class="chart-img-close"><i class="lsd-font lsd-icon-close"></i></div>
            </div>
        </div>
      </div>`);
      chartBody.append(imageContainer);
      const svgAnimationSelector = imageContainer.find(".chart-svg-preview");
      const imageClose = imageContainer.find(".chart-img-close");
      const self = this;
      $(svgAnimationSelector).on("click", (event) => {
        const dialog = new ImageDialog(self.context, { type: "7" });
        dialog.open((result) => {
          if (result) {
            if (this.lottie) {
              this.lottie.destroy()
            }
            if (svgAnimationSelector && svgAnimationSelector.length && result.fileUrl) {
              this.lottie = Lottie.loadAnimation({
                path: result.fileUrl,
                autoplay: true,
                loop: true,
                container: svgAnimationSelector.get(0)
              })
              svgAnimationSelector.text('')
              item['path'] = result.fileUrl;
            }
            item["id"] = result["id"];
          }
          if (callback) {
            callback();
          }
        });
      });
      //删除
      $(imageClose).on("click", (event) => {
        if (this.lottie) {
          this.lottie.destroy()
          svgAnimationSelector && svgAnimationSelector.text('选择SVG素材')
        }
        if (item["id"]) {
          delete item["id"];
        }
        if (item["path"]) {
          item["path"] = '';
          item["animationData"] = '';
        }
        if (callback) {
          callback();
        }
      });
      //获取素材
      if (item.path) {
        this.lottie = Lottie.loadAnimation({
          path: item.path,
          autoplay: true,
          loop: true,
          container: svgAnimationSelector.get(0)
        })
        svgAnimationSelector.text('')
      }
      //双向绑定（兼容老数据）
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "svg-animation-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "SVG素材"
  }
}