import TreeStyle from "../../TreeStyle";
export default class TextShadowStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      //X偏移 和 Y偏移
      const hShadowContainer = $(`<div class="chart-item">
        <div class="chart-label">X偏移</div>
        <div class="chart-control">
            <input type="number" class="chart-number"  min="0" max="" placeholder="" ` + modelName + `="hShadow" />
        </div>
      </div>`);
      chartBody.append(hShadowContainer);

      const vShadowContainer = $(`<div class="chart-item">
        <div class="chart-label">Y偏移</div>
        <div class="chart-control">
            <input type="number" class="chart-number"  min="0" max="" placeholder="" ` + modelName + `="vShadow" />
        </div>
      </div>`);
      chartBody.append(vShadowContainer);

      //模糊
      const blurContainer = $(`<div class="chart-item">
        <div class="chart-label">模糊</div>
        <div class="chart-control">
            <input type="number" class="chart-number"  min="0" max="" placeholder="" ` + modelName + `="blur" />
        </div>
      </div>`);
      chartBody.append(blurContainer);
      //颜色
      const colorConctainer = $(`<div class="chart-item">
        <div class="chart-label">颜色</div>
        <div class="chart-control" style="padding: 0">
            <input type="text" class="chart-color"  ` + modelName + `="shadowColor" />
        </div>
      </div>`);
      chartBody.append(colorConctainer);

      // const shadowCountContainer = $(`<div class="chart-item flex">
      //   <div class="chart-label">层数</div>
      //   <div class="chart-control">
      //       <input type="number" class="chart-number"  min="0" max="" placeholder="" ` + modelName + `="shadowCount" />
      //   </div>
      // </div>`);
      // chartBody.append(shadowCountContainer);
      
      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "textShadow-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "文本阴影"
  }
}