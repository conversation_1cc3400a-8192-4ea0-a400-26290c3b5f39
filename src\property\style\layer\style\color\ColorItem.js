import TreeStyle from "../../../TreeStyle";
export default class ColorItem extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //颜色
      const backgroundColorContainer = $(`<div class="chart-item flex">
        <div class="chart-label">颜色</div>
          <div class="chart-control">
            <input type="text" class="chart-color" ` + modelName + `="color" />
          </div>
      </div>`);
      chartBody.append(backgroundColorContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "colorItem-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "渐变色"
  }
}