import GradientStyle from "./gradient/GradientStyle";
import ColorList from "./ColorList";
import TreeStyle from "../../../TreeStyle";
export default class ColorStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  //color:{type:"颜色"/渐变,color,gradient:{type:"线性"/"径向",}}
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {

      const typeContainer = $(`<div class="chart-item">
          <div class="chart-label">类型</div>
          <div class="chart-control">
              <select class="chart-select" `+ modelName + `="type">
                  <option value="">---请选择---</option>
                  <option value="color">颜色</option>
                  <option value="gradient">渐变色</option>
              </select>
          </div>
          </div>`);
      chartBody.append(typeContainer);

      this.colorContainer = $(`<div class="chart-item">
        <div class="chart-label">颜色</div>
          <div class="chart-control" style="padding: 0">
            <input type="text" class="chart-color" ` + modelName + `="color" />
          </div>
      </div>`);
      chartBody.append(this.colorContainer);

      //渐变色
      this.gradientContainer = $(`<div class="gradient-color mb10"></div>`);
      chartBody.append(this.gradientContainer);
      if (!item["gradient"]) {
        item["gradient"] = {};
      }
      const gradientStyle = new GradientStyle(this.context);
      gradientStyle.initPanel(this.gradientContainer, item["gradient"], callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    if (key && key == "type") {
      if (value === "color") {
        this.colorContainer.show();
        this.gradientContainer.hide();
      } else if (value === "gradient") {
        this.colorContainer.hide();
        this.gradientContainer.show();
      } else {
        this.colorContainer.hide();
        this.gradientContainer.hide();
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "color-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "颜色"
  }
}