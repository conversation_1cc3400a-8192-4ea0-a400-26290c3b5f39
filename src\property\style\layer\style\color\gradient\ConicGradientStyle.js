import GradientColorList from "./GradientColorList";
import TreeStyle from "../../../../TreeStyle";
export default class ConicGradientStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      if (!item["colors"]) {
        item["colors"] = [];
      }
      const gradientColorList = new GradientColorList(this.context);
      gradientColorList.initPanel(chartBody,item["colors"],callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "conicGradientStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "圆锥渐变"
  }
}