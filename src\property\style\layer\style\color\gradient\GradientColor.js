import TreeStyle from "../../../../TreeStyle";
export default class GradientColor extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //颜色
      const backgroundColorContainer = $(`<div class="chart-item">
          <div class="chart-label">颜色</div>
          <div class="chart-control flex-1" style="padding: 0">
            <input type="text" class="chart-color" ` + modelName + `="color" />
          </div>
      </div>`);
      chartBody.append(backgroundColorContainer);

      //百分比
      const borderWidthContainer = $(`<div class="chart-item">
        <div class="chart-label">百分比</div>
          <div class="chart-control flex-1">
          <input type="number" class="chart-number" min="0" max="100" placeholder="50" ` + modelName + `="percent" />
          </div>
      </div>`);
      chartBody.append(borderWidthContainer);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "gradientColor-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "颜色"
  }
}