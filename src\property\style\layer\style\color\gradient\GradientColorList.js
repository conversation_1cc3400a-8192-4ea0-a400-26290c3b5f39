import GradientColor from "./GradientColor";
import TreeStyle from "../../../../TreeStyle";
export default class GradientColorList extends TreeStyle {
  constructor(context, isOpen) {
    super(context, true, isOpen);
  }
  afterAddItem (itemContainer, item, index, callback) {
    const model = new GradientColor(this.context);
    model.initPanel(itemContainer, item, callback);
    model.refreshId(index);
    model.refreshTitle("颜色[" + (index + 1) + "]配置");
  }
  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "颜色列表"
  }
}