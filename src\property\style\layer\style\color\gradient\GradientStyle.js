import LineGradientStyle from "./LineGradientStyle";
import RadialGradientStyle from "./RadialGradientStyle";
import ConicGradientStyle from "./ConicGradientStyle";
import TreeStyle from "../../../../TreeStyle";
export default class GradientStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //函数
      const gradientFnContainer = $(`<div class="chart-item">
          <div class="chart-label">函数</div>
          <div class="chart-control">
                <select class="chart-select" ` + modelName + `="gradientFn" >
                    <option value="">--请选择--</option>
                    <option value="linear">线性渐变</option>
                    <option value="radial">径向渐变</option>
                    <option value="conic">圆锥渐变</option>
                    <option value="repeating-conic">迭代圆锥渐变</option>
                    <option value="repeating-linear">迭代线性渐变</option>
                    <option value="repeating-radial">迭代径向渐变</option>
                </select>
          </div>
        </div>`);
      chartBody.append(gradientFnContainer);

      //线性渐变
      this.linearContainer = $(`<div class="linear-body"></div>`);
      chartBody.append(this.linearContainer);
      const lineGradientStyle = new LineGradientStyle(this.context);
      lineGradientStyle.initPanel(this.linearContainer, item, callback);

     //径向渐变
     this.radialContainer = $(`<div class="radial-body"></div>`);
     chartBody.append(this.radialContainer);
     const radialGradientStyle = new RadialGradientStyle(this.context);
     radialGradientStyle.initPanel(this.radialContainer, item, callback);

     //圆锥渐变
     this.conicContainer = $(`<div class="conic-body"></div>`);
     chartBody.append(this.conicContainer);
     const conicGradientStyle = new ConicGradientStyle(this.context);
     conicGradientStyle.initPanel(this.conicContainer, item, callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {
    if (key && key == "gradientFn") {
      if (value === "linear") {
        this.linearContainer.show();
        this.radialContainer.hide();
        this.conicContainer.hide();
      } else if (value === "radial") {
        this.linearContainer.hide();
        this.radialContainer.show();
        this.conicContainer.hide();
      } else if (value === "conic") {
        this.linearContainer.hide();
        this.radialContainer.hide();
        this.conicContainer.show();
      }else{
        this.linearContainer.hide();
        this.radialContainer.hide();
        this.conicContainer.hide();
      }
    }
  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "gradientStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "渐变色"
  }
}