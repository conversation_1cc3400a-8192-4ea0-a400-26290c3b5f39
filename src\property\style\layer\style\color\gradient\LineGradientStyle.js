import GradientColorList from "./GradientColorList";
import TreeStyle from "../../../../TreeStyle";
export default class LineGradientStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //角度
      const borderWidthContainer = $(`<div class="chart-item">
      <div class="chart-label">角度</div>
        <div class="chart-control">
        <input type="number" class="chart-number" placeholder="渐变的角度" ` + modelName + `="gradientAngle" />
        </div>
      </div>`);
      chartBody.append(borderWidthContainer);

      //结束定位
      const gradientAtPositionContainer = $(`<div class="chart-item">
          <div class="chart-label">结束定位</div>
          <div class="chart-control">
            <select class="chart-select" ` + modelName + `="gradientAtPosition" >
                <option value="">--请选择--</option>
                <option value="top">上</option>
                <option value="bottom">下</option>
                <option value="center">中</option>
                <option value="left">左</option>
                <option value="right">右</option>
            </select>
          </div>
        </div>`);
      chartBody.append(gradientAtPositionContainer);

      if (!item["colors"]) {
        item["colors"] = [];
      }
      const gradientColorList = new GradientColorList(this.context);
      gradientColorList.initPanel(chartBody,item["colors"],callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "lineGradientStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "线性渐变"
  }
}