import GradientColorList from "./GradientColorList";
import TreeStyle from "../../../../TreeStyle";
export default class RadialGradientStyle extends TreeStyle {
  constructor(context, isOpen) {
    super(context, false, isOpen);
  }
  refreshPanel (chartBody, modelName, item, callback) {
    if (chartBody && modelName && item) {
      //形状
      const radialShapeContainer = $(`<div class="chart-item flex">
            <div class="chart-label">形状</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + `="gradientRadialShape" >
                    <option value="">--请选择--</option>
                    <option value="ellipse">椭圆形</option>
                    <option value="circle">圆形</option>
                </select>
            </div>
        </div>`);
        chartBody.append(radialShapeContainer);

        //径向大小
        const radialSizeContainer = $(`<div class="chart-item flex">
            <div class="chart-label">径向大小</div>
            <div class="chart-control">
                <select class="chart-select" ` + modelName + `="gradientRadialSize" >
                    <option value="">--请选择--</option>
                    <option value="farthest-corner">最远的角</option>
                    <option value="farthest-side">最近的角</option>
                    <option value="closest-side">最近的边</option>
                    <option value="closest-corner">最远的边</option>
                </select>
            </div>
        </div>`);
        chartBody.append(radialSizeContainer);

      if (!item["colors"]) {
        item["colors"] = [];
      }
      const gradientColorList = new GradientColorList(this.context);
      gradientColorList.initPanel(chartBody,item["colors"],callback);

      this.refreshModel(item);
      this.bindModel(item, callback);
    }
  }
  refreshEvent (key, value) {

  }
  /**
   *
   * @returns {string}
   */
  getModelName () {
    return "radialGradientStyle-model";
  }

  /**
   * 描述:标题信息
   * @returns {string}
   */
  getTitle () {
    return "径向渐变"
  }
}