import Expression from "../utils/Expression";
import DataModelService from "./DataModelService";
export default class BindQueryHandler {
  constructor(context, bind) {
    this.context = context;
    this.bind = bind;
    this.$$isLoading=false;
    this.expression = new Expression(context);
  }

  /**######################[数据查询]######################## */
  queryData (callback, relationParams) {
    // const bind = this.property.bind;
    const bind = this.bind;
    if (bind && bind.id) {
      //模型参数
      const modelParams = this.parseModelParams(relationParams);
      //是否开始查询
      this.$$isLoading = true;
      //查询参数
      const self = this;
      const params = { id: bind.id, params: modelParams };
      const dataModelService = new DataModelService(this.context);
      dataModelService.queryData(params, function (result) {
        if (!(result && result.data)) {
          console.error('接口空数据', result)
        }
        callback && callback(result);
        self.$$isLoading = false;
      }, function (error) {
        self.$$isLoading = false;
        callback && callback();
        console.error("查询失败!");
      });
    } else {
      this.$$isLoading = false;
      if (callback) callback();
    }
  }
  /**######################[数据预览]######################## */
  queryPreview (callback) {
    // const bind = this.property.bind;
    const bind = this.bind;
    if (bind && bind.id) {
      //模型参数
      const modelParams = this.parseModelParams();
      //是否开始查询
      this.$$isLoading = true;
      //查询参数
      const self = this;
      const params = { id: bind.id, params: modelParams };
      const dataModelService = new DataModelService(this.context);
      dataModelService.queryPreview(params, function (result) {
        if (!(result && result.data)) {
          console.error('接口空数据', result)
        }
        callback && callback(result);
        self.$$isLoading = false;
      }, function (error) {
        callback && callback();
        self.$$isLoading = false;
        console.error("查询失败!");
      });
    } else {
      this.$$isLoading = false;
      if (callback) callback();
    }
  }

  /**######################[数据预览]######################## */
  /**
   * 解析模型参数
   * @param {*} relationParams 联动参数
   * @returns 
   */
  parseModelParams (relationParams) {
    const data = {};
    const bind = this.bind;
    //参数数据
    const paramData = this.context.getParamPropertyMap();
    //绑定参数
    if (bind.params && bind.params.length) {
      for (let i = 0; i < bind.params.length; i++) {
        const param = bind.params[i];
        const key = param.name;
        const expr = param.defValue;
        param["value"] = this.expression.computeExpr(expr, paramData);
        //处理联动参数
        if (key && relationParams && relationParams[key]) {
          param["value"] = relationParams[key];
        }
        data[key] = param["value"];
      }
    }
    return data;
  }
}