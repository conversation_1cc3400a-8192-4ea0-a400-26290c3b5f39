import Service from "./Service"

export default class DataModelService extends Service {
  constructor(context) {
    super(context);
  }
  getServer () {
    return this.getServerByKey("datamodel");
  }
  /**
   * 描述:保存
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  save (data, success, fail) {
    const url = this.getServer() + "/publics/dataModel/save";
    this.postRequest(url, data, success, fail);
  }
  /**
   * 描述:保存-批量
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  saveBatch (data, success, fail) {
    const url = this.getServer() + "/publics/dataModel/saveBatch";
    this.postRequest(url, data, success, fail);
  }
  /**
   * 描述:查询根据Id
   * @param success
   * @param fail
   */
  queryById (data, success, fail) {
    const url = this.getServer() + "/publics/dataModel/queryById";
    this.getRequest(url, data, success, fail);
  }
  /**
   * 描述:查询list
   * @param success
   * @param fail
   */
  queryList (data, success, fail) {
    const url = this.getServer() + "/publics/dataModel/queryList";
    this.postRequest(url, data, success, fail);
  }
  /**
   * 描述:查询Page
   * @param success
   * @param fail
   */
  queryPage (data, success, fail) {
    const url = this.getServer() + "/publics/dataModel/queryPage";
    this.getRequest(url, data, success, fail);
  }
  /**
   * 描述:查询模型查询
   * @param success
   * @param fail
   */
  queryData (data, success, fail) {
    const url = this.getServer() + "/publics/dataModel/queryModelData";
    this.postRequest(url, data, success, fail);
  }
  /**
   * 描述:数据模型预览
   * @param success
   * @param fail
   */
  queryPreview (data, success, fail) {
    const url = this.getServer() + "/publics/dataModel/queryPreview";
    this.postRequest(url, data, success, fail);
  }

  /**
   * 描述:查询模型参数
   * @param success
   * @param fail
   */
  queryByModelId (data, success, fail) {
    const url = this.getServer() + "/publics/dataModelParam/queryByModelId";
    this.getRequest(url, data, success, fail);
  }

  /**
   * 查询所有模型参数
   * @param data
   * @param success
   * @param fail
   */
  queryAllByModelId (data, success, fail) {
    const url = this.getServer() + "/publics/dataModel/queryAllByModelId";
    this.getRequest(url, data, success, fail);
  }

}