import Service from "./Service"

export default class DictService extends Service {
  constructor(context) {
    super(context);
  }
  getServer () {
    return this.getServerByKey("oauth");
  }
  /**
   * 描述:保存
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  save (data, success, fail) {
    const url = this.getServer() + "/publics/dictType/save";
    this.postRequest(url, data, success, fail);
  }
  /**
   * 描述:保存-批量
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  saveBatch (data, success, fail) {
    const url = this.getServer() + "/publics/dictType/saveBatch";
    this.postRequest(url, data, success, fail);
  }
  /**
   * 描述:查询根据Id
   * @param success
   * @param fail
   */
  queryById (data, success, fail) {
    const url = this.getServer() + "/publics/dictType/queryById";
    this.getRequest(url, data, success, fail);
  }
  /**
   * 描述:查询list
   * @param success
   * @param fail
   */
  queryList (data, success, fail) {
    const url = this.getServer() + "/publics/dictType/queryList";
    this.getRequest(url, data, success, fail);
  }
  /**
   * 描述:查询分页
   * @param success
   * @param fail
   */
  queryPage (data, success, fail) {
    const url = this.getServer() + "/publics/dictType/queryPage";
    this.getRequest(url, data, success, fail);
  }
  /**
   * 描述:查询数据-根据key
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  queryDataByKey (data, success, fail) {
    const url = this.getServer() + "/publics/dict/getDictByNodeKey";
    this.getRequest(url, data, success, fail);
  }
}