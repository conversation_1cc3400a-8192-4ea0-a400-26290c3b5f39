import Service from "./Service"

export default class DirLabelService extends Service {
    constructor(context) {
        super(context);
    }
    getServer() {
        return this.getServerByKey("lsd");
    }
    /**
     * 描述:保存
     * @param {*} data 
     * @param {*} success 
     * @param {*} fail 
     */
    save(data, success, fail) {
        const url = this.getServer() + "/lsd/lsdDirLabel/save";
        this.postRequest(url, data, success, fail);
    }
    /**
     * 描述:保存-批量
     * @param {*} data 
     * @param {*} success 
     * @param {*} fail 
     */
    saveBatch(data, success, fail) {
        const url = this.getServer() + "/lsd/lsdDirLabel/saveBatch";
        this.postRequest(url, data, success, fail);
    }
    /**
     * 描述:查询根据Id
     * @param success
     * @param fail
     */
    queryById(data, success, fail) {
        const url = this.getServer() + "/lsd/lsdDirLabel/queryById";
        this.getRequest(url, data, success, fail);
    }
    /**
     * 描述:查询list
     * @param success
     * @param fail
     */
    queryList(data, success, fail) {
        const url = this.getServer() + "/lsd/lsdDirLabel/queryList";
        this.getRequest(url, data, success, fail);
    }
    /**
     * 描述:查询Page
     * @param success
     * @param fail
     */
    queryPage(data, success, fail) {
        const url = this.getServer() + "/lsd/lsdDirLabel/queryPage";
        this.getRequest(url, data, success, fail);
    }

    /**
   * 描述:目录标签查询
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
    queryListPath(data, success, fail) {
        const url = this.getServer() + "/lsd/vo/lsdDirLabel/queryList";
        this.getRequest(url, data, success, fail);
    }
}