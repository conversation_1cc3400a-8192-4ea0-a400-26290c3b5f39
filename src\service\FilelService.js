import Service from "./Service"

export default class DataModelService extends Service {
  constructor(context) {
    super(context);
  }
  getServer () {
    return this.getServerByKey("lsd");
  }

  /**
   * 描述:查询文件数据
   * @param success
   * @param fail
   */
  queryFileData (data, success, fail) {
    const url = this.getServer() + "/lsd/lsdScreenFileData/queryById";
    this.getRequest(url, data, success, fail);
  }

}