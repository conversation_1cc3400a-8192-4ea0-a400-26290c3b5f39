import Service from "./Service"

export default class MaterialService extends Service {
  constructor(context) {
    super(context);
  }
  getServer () {
    return this.getServerByKey("lsd");
  }
  /**
   * 描述:保存
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  save (data, success, fail) {
    const url = this.getServer() + "/lsd/lsdMaterial/save";
    this.postRequest(url, data, success, fail);
  }
  /**
   * 描述:保存-批量
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  saveBatch (data, success, fail) {
    const url = this.getServer() + "/lsd/lsdMaterial/saveBatch";
    this.postRequest(url, data, success, fail);
  }
  /**
   * 描述:查询根据Id
   * @param success
   * @param fail
   */
  queryById (data, success, fail) {
    const url = this.getServer() + "/lsd/lsdMaterial/queryById";
    this.getRequest(url, data, success, fail);
  }
  /**
   * 描述:查询list
   * @param success
   * @param fail
   */
  queryList (data, success, fail) {
    const url = this.getServer() + "/lsd/lsdMaterial/queryList";
    this.getRequest(url, data, success, fail);
  }
  /**
   * 描述:查询Page
   * @param success
   * @param fail
   */
  queryPage (data, success, fail) {
    const url = this.getServer() + "/lsd/lsdMaterial/queryPage";
    this.getRequest(url, data, success, fail);
  }
  /**
   * 描述:查询lis根据大屏Id
   * @param success
   * @param fail
   */
  queryListByScreenId (data, success, fail) {
    const url = this.getServer() + "/lsd/vo/lsdMaterial/queryListByScreenId";
    this.getRequest(url, data, success, fail);
  }
}