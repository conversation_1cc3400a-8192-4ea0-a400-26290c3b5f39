import Service from "./Service"

export default class ResourcesService extends Service {
  constructor(context) {
    super(context);
  }
  getServer () {
    return this.getServerByKey("oauth");
  }

  /**
   * 获取资源
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  queryAuthResources (data, success, fail) {
    const url = this.getServer() + "/publics/resources/queryAuthResources";
    this.postRequest(url, data, success, fail);
  }
  /**
   * 获取资源目录
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  queryAuthResourcesDir (data, success, fail) {
    const url = this.getServer() + "/publics/resources/queryAuthResourcesDir";
    this.postRequest(url, data, success, fail);
  }

  /**
   * 获取链接
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  generateLink (data, success, fail) {
    const url = this.getServer() + "/publics/ucShareLinkExamples/generateLink";
    this.postRequest(url, data, success, fail);
  }

  /**
   * 获取token
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  getAccessVerifyToken(data,success,fail){
    const url = this.getServer() + "/publics/ucShareLinkExamples/getAccessVerifyToken";
    this.getRequest(url, data, success, fail);
  }

  /**
   * 保存
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  save(data,success,fail){
    const url = this.getServer() + "/publics/ucShareLinkExamples/save";
    this.postRequest(url, data, success, fail);
  }
}