import Service from "./Service"

export default class ScreenService extends Service {
  constructor(context) {
    super(context);
  }
  getServer () {
    return this.getServerByKey("lsd");
  }
  /**
   * 描述:保存
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  save (data, success, fail) {
    const url = this.getServer() + "/lsd/lsdScreen/save";
    this.postRequest(url, data, success, fail);
  }
  /**
   * 描述:保存-批量
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  saveBatch (data, success, fail) {
    const url = this.getServer() + "/lsd/lsdScreen/saveBatch";
    this.postRequest(url, data, success, fail);
  }
  /**
   * 描述:查询根据Id
   * @param success
   * @param fail
   */
  queryById (data, success, fail) {
    const url = this.getServer() + "/lsd/lsdScreen/queryById";
    this.getRequest(url, data, success, fail);
  }
  /**
   * 描述:查询list
   * @param success
   * @param fail
   */
  queryList (data, success, fail) {
    const url = this.getServer() + "/lsd/lsdScreen/queryList";
    this.getRequest(url, data, success, fail);
  }
  /**
   * 描述:查询Page
   * @param success
   * @param fail
   */
  queryPage (data, success, fail) {
    const url = this.getServer() + "/lsd/lsdScreen/queryPage";
    this.postRequest(url, data, success, fail);
  }
  /**
   * 描述:保存-关联
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  saveScreen (data, success, fail) {
    const url = this.getServer() + "/lsd/vo/lsdScreen/save";
    this.postRequest(url, data, success, fail);
  }

  /**
   * 描述:查询-关联
   * @param {*} data 
   * @param {*} success 
   * @param {*} fail 
   */
  queryScreen (data, success, fail) {
    const url = this.getServer() + "/lsd/vo/lsdScreen/queryById";
    this.getRequest(url, data, success, fail);
  }
}