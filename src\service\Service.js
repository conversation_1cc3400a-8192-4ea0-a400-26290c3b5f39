import EncryptUtil from "../utils/EncryptUtil";
import { getToken } from "../utils/StorageUtil";
import { getRandom } from "../utils/Util";
import { alert, warn, error } from "../assets/element/jquery/msg/MsgUtil";
import { start, end, setMsg } from "../assets/element/jquery/load/LoadUtil";
import globalLoadingManager from "../utils/GlobalLoadingManager";
export default class Service {
  constructor(context) {
    this.context = context;
  }
  getServerByKey(key) {
    if (this.context) {
      const host = this.context.getHost();
      if (key && host && host[key]) {
        return host[key];
      } else {
        error("查询服务失败![类型:" + key + "不存在]");
      }
    } else {
      error("查询服务失败![上下文不存在]");
    }
  }
  /**
   * 描述:加密
   * @param {*} url
   * @param {*} data
   * @param {*} success
   * @param {*} fail
   * @param {*} dataType
   */
  postEncrypt(url, data, success, fail, dataType, options = {}) {
    const token = getToken();
    if (url && token) {
      // 检查全局loading配置
      const globalLoadingEnabled = this.context
        ? this.context.isGlobalLoadingEnabled()
        : true;
      const globalConfig = this.context
        ? this.context.getGlobalLoadingConfig()
        : {};

      // 默认显示loading，除非全局禁用或明确设置为false
      const showLoading = globalLoadingEnabled && options.showLoading !== false;
      const loadingMessage =
        options.loadingMessage ||
        globalConfig.defaultMessages?.postEncrypt ||
        "加密提交中...";
      const loadingPriority = options.loadingPriority || 0;

      let requestId = null;

      if (showLoading) {
        requestId = globalLoadingManager.startRequest(
          loadingMessage,
          loadingPriority
        );
      }

      //加密-获取密钥
      const encrynum = getRandom(6);
      //加密-数据加密
      const encryptUtil = new EncryptUtil();
      const encryptData = encryptUtil.encrypt(encrynum, JSON.stringify(data));

      $.ajax({
        url: url,
        data: encryptData,
        type: "POST",
        dataType: dataType || "json",
        contentType: "application/json;charset=utf-8",
        encry: true,
        beforeSend: function (request) {
          //Header-添加token
          request.setRequestHeader(token.key, token.value);
          //Header-添加密钥
          request.setRequestHeader("encry", encrynum);
        },
        complete: function (xhr, status) {},
        success: function (result, status, xhr) {
          if (showLoading && requestId) {
            globalLoadingManager.endRequest(requestId);
          }

          let data = result;
          const textStatus = status;
          let encrynum = xhr.getResponseHeader("encry"); //获取密钥
          if (encrynum) {
            const encryptUtil = new EncryptUtil();
            let decryptData = encryptUtil.decrypt(encrynum, data);
            data = JSON.parse(decryptData);
          }
          if (success) {
            success(data, textStatus);
          }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
          if (showLoading && requestId) {
            globalLoadingManager.endRequest(requestId);
          }

          if (fail) {
            fail(XMLHttpRequest, textStatus, errorThrown);
          } else {
            error("请求服务失败!");
          }
        },
      });
    } else {
      error("请在配置中配置服务器Host和应用编码");
    }
  }
  /**
   * 描述:解密
   * @param {*} url
   * @param {*} data
   * @param {*} success
   * @param {*} fail
   * @param {*} dataType
   */
  getDecrypt(url, data, success, fail, dataType, options = {}) {
    const token = getToken();
    if (url && token) {
      // 检查全局loading配置
      const globalLoadingEnabled = this.context
        ? this.context.isGlobalLoadingEnabled()
        : true;
      const globalConfig = this.context
        ? this.context.getGlobalLoadingConfig()
        : {};

      // 默认显示loading，除非全局禁用或明确设置为false
      const showLoading = globalLoadingEnabled && options.showLoading !== false;
      const loadingMessage =
        options.loadingMessage ||
        globalConfig.defaultMessages?.getDecrypt ||
        "解密加载中...";
      const loadingPriority = options.loadingPriority || 0;

      let requestId = null;

      if (showLoading) {
        requestId = globalLoadingManager.startRequest(
          loadingMessage,
          loadingPriority
        );
      }

      $.ajax({
        url: url,
        data: data,
        type: "GET",
        dataType: dataType || "json",
        contentType: "application/json;charset=utf-8",
        beforeSend: function (request) {
          request.setRequestHeader(token.key, token.value);
        },
        complete: function (xhr, status) {},
        success: function (result, status, xhr) {
          if (showLoading && requestId) {
            globalLoadingManager.endRequest(requestId);
          }

          let data = result;
          const textStatus = status;
          let encrynum = xhr.getResponseHeader("encry"); //获取密钥
          if (encrynum) {
            const encryptUtil = new EncryptUtil();
            let decryptData = encryptUtil.decrypt(encrynum, data);
            data = JSON.parse(decryptData);
          }
          if (success) {
            success(data, textStatus);
          }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
          if (showLoading && requestId) {
            globalLoadingManager.endRequest(requestId);
          }

          if (fail) {
            fail(XMLHttpRequest, textStatus, errorThrown);
          } else {
            error("请求服务失败!");
          }
        },
      });
    } else {
      error("请在配置中配置服务器Host和应用编码");
    }
  }

  /**
   * 描述:查询数据 包括callback 和 error
   * @param callback
   * @param error
   */
  getRequest(url, data, success, fail, dataType, options = {}) {
    const token = getToken();
    if (url && token) {
      // 检查全局loading配置
      const globalLoadingEnabled = this.context
        ? this.context.isGlobalLoadingEnabled()
        : true;
      const globalConfig = this.context
        ? this.context.getGlobalLoadingConfig()
        : {};

      // 默认显示loading，除非全局禁用或明确设置为false
      const showLoading = globalLoadingEnabled && options.showLoading !== false;
      const loadingMessage =
        options.loadingMessage ||
        globalConfig.defaultMessages?.get ||
        "加载中...";
      const loadingPriority = options.loadingPriority || 0;

      let requestId = null;

      if (showLoading) {
        requestId = globalLoadingManager.startRequest(
          loadingMessage,
          loadingPriority
        );
      }

      $.ajax({
        url: url,
        data: data,
        type: "GET",
        dataType: dataType || "json",
        contentType: "application/json;charset=utf-8",
        beforeSend: function (request) {
          request.setRequestHeader(token.key, token.value);
        },
        success: function (data, textStatus) {
          if (showLoading && requestId) {
            globalLoadingManager.endRequest(requestId);
          }
          if (success) {
            success(data, textStatus);
          }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
          if (showLoading && requestId) {
            globalLoadingManager.endRequest(requestId);
          }
          if (fail) {
            fail(XMLHttpRequest, textStatus, errorThrown);
          } else {
            if (
              XMLHttpRequest &&
              XMLHttpRequest.responseJSON &&
              XMLHttpRequest.responseJSON.msg
            ) {
              error("请求服务失败![" + XMLHttpRequest.responseJSON.msg + "]");
            } else {
              error("请求服务失败!");
            }
          }
        },
      });
    } else {
      error("请在配置中配置服务器Host和应用编码");
    }
  }

  /**
   * 描述:同步查询数据 包括callback 和 error
   * @param callback
   * @param error
   */
  getSyncRequest(url, data, dataType) {
    let result;
    const token = getToken();
    if (url && token) {
      $.ajax({
        url: url,
        data: data,
        type: "GET",
        async: false,
        dataType: dataType || "json",
        contentType: "application/json;charset=utf-8",
        beforeSend: function (request) {
          request.setRequestHeader(token.key, token.value);
        },
        success: function (data, textStatus) {
          // end(loadingContainer);
          result = data;
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
          if (
            XMLHttpRequest &&
            XMLHttpRequest.responseJSON &&
            XMLHttpRequest.responseJSON.msg
          ) {
            error("请求服务失败![" + XMLHttpRequest.responseJSON.msg + "]");
          } else {
            error("请求服务失败!");
          }
        },
      });
    } else {
      error("请在配置中配置服务器Host和应用编码");
    }
    return result;
  }

  /**
   * 描述:查询数据 包括callback 和 error
   * @param callback
   * @param error
   */
  postRequest(url, data, success, fail, dataType, options = {}) {
    const token = getToken();
    if (url && token) {
      // 检查全局loading配置
      const globalLoadingEnabled = this.context
        ? this.context.isGlobalLoadingEnabled()
        : true;
      const globalConfig = this.context
        ? this.context.getGlobalLoadingConfig()
        : {};

      // 默认显示loading，除非全局禁用或明确设置为false
      const showLoading = globalLoadingEnabled && options.showLoading !== false;
      const loadingMessage =
        options.loadingMessage ||
        globalConfig.defaultMessages?.post ||
        "提交中...";
      const loadingPriority = options.loadingPriority || 0;

      let requestId = null;

      if (showLoading) {
        requestId = globalLoadingManager.startRequest(
          loadingMessage,
          loadingPriority
        );
      }

      $.ajax({
        url: url,
        data: JSON.stringify(data),
        type: "POST",
        dataType: dataType || "json",
        contentType: "application/json;charset=utf-8",
        beforeSend: function (request) {
          request.setRequestHeader(token.key, token.value);
        },
        success: function (data, textStatus) {
          if (showLoading && requestId) {
            globalLoadingManager.endRequest(requestId);
          }
          if (success) {
            success(data, textStatus);
          }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
          if (showLoading && requestId) {
            globalLoadingManager.endRequest(requestId);
          }
          if (fail) {
            fail(XMLHttpRequest, textStatus, errorThrown);
          } else {
            if (
              XMLHttpRequest &&
              XMLHttpRequest.responseJSON &&
              XMLHttpRequest.responseJSON.msg
            ) {
              error("请求服务失败![" + XMLHttpRequest.responseJSON.msg + "]");
            } else {
              error("请求服务失败!");
            }
          }
        },
      });
    } else {
      error("请在配置中配置服务器Host和应用编码");
    }
  }
  /**
   *
   * @param {*} url
   * @param {*} data
   * @param {*} success
   * @param {*} fail
   */
  downLoad(url, data, success, fail) {
    const token = getToken();
    if (url && data && token) {
      let xhr = new XMLHttpRequest();
      //
      if (data["isToken"]) {
        xhr.setRequestHeader(token.key, token.value);
      }
      //
      if (data["method"]) {
        xhr.open(data["method"], url);
      } else {
        xhr.open("get", url);
      }
      //响应类型
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (xhr.readyState == 4) {
          if (xhr.status == 200) {
            // 返回200
            const blob = this.response;
            const reader = new FileReader();
            reader.readAsDataURL(blob); // 转换为base64，可以直接放入a表情href
            reader.onload = function (e) {
              const fullName = data["fileName"] + "." + data["fileExt"];
              const blob = new Blob([xhr.response], { type: data["fileExt"] });
              const blobFileUrl = URL.createObjectURL(blob);
              const downLoadAContainer = $(
                `<a target="_blank" download="` + fullName + `"></a>`
              );
              downLoadAContainer.attr("href", blobFileUrl);
              $("body").append(downLoadAContainer);
              if (downLoadAContainer[0]) {
                downLoadAContainer[0].click();
              }
              downLoadAContainer.remove();
              if (success) {
                success();
              }
            };
          } else {
            fail();
          }
        }
      };
      xhr.send();
    }
  }
}
