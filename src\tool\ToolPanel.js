import ConfigDialog from "../dialog/ConfigDialog";
import TemplDialog from "../dialog/TemplDialog";
import Submit from "../Submit";
import "./css/tool.css";
import { isEmptyObj } from "../utils/Util";
import MessageCenterDialog from "../dialog/MessageCenterDialog";
import {registryMessageCenter} from "../utils/ListenerUtil";
export default class ToolPanel {
  constructor(context) {
    this.context = context;
    this.designer = context.getDesigner();
  }
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="tool-container"></div>`);
      parentContainer.append(this.container);
    }
  }
  /**
   * 描述:刷新面板
   * 
   */
  refreshPanel () {
    if (this.container) {
      this.container.empty();
      const mainContainer = $(`<div class="tool-wrap">
        <div class="tool-left"><div>可视化大屏</div></div>
        <div class="tool-middle"></div>
        <div class="tool-right"></div>
      </div>`);
      this.container.append(mainContainer);
      const toolRightContainer = $(mainContainer).find(".tool-right");
      this.addSave(toolRightContainer);
      this.addPreview(toolRightContainer);
      this.addConfig(toolRightContainer);
      this.addTempl(toolRightContainer);
      this.addMessageCenter(toolRightContainer);

      const toolMiddleContainer = $(mainContainer).find(".tool-middle");
      this.addUndo(toolMiddleContainer);
      this.addRedo(toolMiddleContainer);
      this.addDelete(toolMiddleContainer);
      this.addLockOrUnlock(toolMiddleContainer);
      this.addShowOrHide(toolMiddleContainer);
      this.addCopy(toolMiddleContainer);
      this.addVerticalTop(toolMiddleContainer);
      this.addVerticalMiddle(toolMiddleContainer);
      this.addVerticalBottom(toolMiddleContainer);
      this.addHorizontalLeft(toolMiddleContainer);
      this.addHorizontalMiddle(toolMiddleContainer);
      this.addHorizontalRight(toolMiddleContainer);
    }
  }
  /**
   * 刷新顶部工具栏
   * @param layer 图层
   */
  refreshLinkPanel (layer) {
    const lockIcon = $(this.container).find('.flag-lock');
    const hideIcon = $(this.container).find('.flag-hide');
    const undoIcon = $(this.container).find('.flag-undo');
    const redoIcon = $(this.container).find('.flag-redo');
    if (layer && !isEmptyObj(layer)) {
      // 选中了图层，再判断图层锁定状态
      if (layer.isLock) {
        lockIcon.removeClass("icon-jiesuo").removeClass('icon-suoding').addClass("icon-suoding");
      } else {
        lockIcon.removeClass("icon-jiesuo").removeClass('icon-suoding').addClass("icon-jiesuo");
      }
      // 选中了图层，再判断图层显隐状态
      if (layer.property.isHide) {
        hideIcon.removeClass("icon-chakan-on").removeClass('icon-chakan-off').addClass("icon-chakan-off");
      } else {
        hideIcon.removeClass("icon-chakan-on").removeClass('icon-chakan-off').addClass("icon-chakan-on");
      }
    } else {
      // 空图层，恢复初始图标
      lockIcon.removeClass("icon-jiesuo").removeClass('icon-suoding').addClass("icon-jiesuo");
      hideIcon.removeClass("icon-chakan-on").removeClass('icon-chakan-off').addClass("icon-chakan-on");
    }
    const undos = this.context.getUndos();
    if (undos.length > 0) {
      undoIcon.removeClass('opacity50')
    } else {
      undoIcon.addClass('opacity50')
    }
    const redos = this.context.getRedos();
    if (redos.length > 0) {
      redoIcon.removeClass('opacity50')
    } else {
      redoIcon.addClass('opacity50')
    }
  }
  buildClickItem (parentContainer, iconName, text, callback) {
    if (parentContainer && text) {
      const container = $(`<div class="tool-icon ft-font ${iconName}" data-tooltip="${text}"></div>`);
      parentContainer.append(container);
      $(container).on("click", function (event) {
        // console.info("点击:["+text+"]");
        if (callback) {
          callback(event);
        }
      });
    }
  }

  //##################中间按钮################
  /**
   * 撤销
   * @param container 容器
   */
  addUndo (container) {
    if (container) {
      this.buildClickItem(container, "flag-undo icon-chexiao1 opacity50", "撤销", (event) => {
        this.designer.undoLayer();
      });
    }
  }
  /**
   * 重做
   * @param container 容器
   */
  addRedo (container) {
    if (container) {
      this.buildClickItem(container, "flag-redo icon-quxiaocaozuo opacity50", "重做", (event) => {
        this.designer.redoLayer();
      });
    }
  }
  /**
   * 删除
   * @param container 容器
   */
  addDelete (container) {
    if (container) {
      this.buildClickItem(container, "icon-shanchu2", "删除", event => {
        // 获取当前选择的图层
        const layer = this.context.getLayer();
        if (!isEmptyObj(layer)) {
          // 刷新图层图表状态
          if (this.designer) {
            // 删除图层
            this.designer.delLayer(layer);
            // 联动菜单
            this.refreshLink(layer);
          }
        }
      });
    }
  }
  /**
   * 解锁or锁定
   * @param container 容器
   */
  addLockOrUnlock (container) {
    if (container) {
      this.buildClickItem(container, "flag-lock icon-jiesuo", "锁定", event => {
        // 获取当前选择的图层
        const layer = this.context.getLayer();
        if (layer && !isEmptyObj(layer)) {
          layer.isLock = !layer.isLock;

          if (layer.isLock) {
            $(container).find('.flag-lock').addClass("icon-suoding").removeClass("icon-jiesuo");
          } else {
            $(container).find('.flag-lock').addClass("icon-jiesuo").removeClass("icon-suoding");
          }
          layer.refreshLock();
          this.refreshLink(layer);
        }
      });
    }
  }
  /**
   * 查看or隐藏
   * @param container 容器
   */
  addShowOrHide (container) {
    if (container) {
      this.buildClickItem(container, "flag-hide icon-chakan-on", "隐藏", event => {
        // 获取当前选择的图层
        const layer = this.context.getLayer();
        if (layer && !isEmptyObj(layer)) {
          layer.property.isHide = !layer.property.isHide;
          if (layer.property.isHide) {
            $(container).find('.flag-hide').addClass("icon-chakan-off").removeClass("icon-chakan-on");
          } else {
            $(container).find('.flag-hide').addClass("icon-chakan-on").removeClass("icon-chakan-off");
          }
          layer.refreshHide();
          this.refreshLink(layer);
        }
      });
    }
  }
  /**
   * 复制
   * @param container 容器
   */
  addCopy (container) {
    if (container) {
      this.buildClickItem(container, "icon-jianlizuhe", "复制", event => {
        const layer = this.context.getLayer();
        if (layer && !isEmptyObj(layer) && this.designer) {
          this.designer.localCopyLayer(layer)
        }
      });
    }
  }
  /**
   * 垂直方向顶端对齐
   * @param container 容器
   */
  addVerticalTop (container) {
    if (container) {
      this.buildClickItem(container, "icon-dingduiqi", "顶端对齐", () => {
        this.designer.alignLayers('top');
      });
    }
  }
  /**
   * 垂直居中
   * @param container 容器
   */
  addVerticalMiddle (container) {
    if (container) {
      this.buildClickItem(container, "icon-shuipingduiqi", "垂直居中", () => {
        this.designer.alignLayers('middle');
      });
    }
  }
  /**
   * 垂直方向底端对齐
   * @param container 容器
   */
  addVerticalBottom (container) {
    if (container) {
      this.buildClickItem(container, "icon-diduiqi", "底端对齐", () => {
        this.designer.alignLayers('bottom');
      });
    }
  }
  /**
   * 水平方左对齐
   * @param container 容器
   */
  addHorizontalLeft (container) {
    if (container) {
      this.buildClickItem(container, "icon-zuoduiqi", "左对齐", () => {
        this.designer.alignLayers('left');
      });
    }
  }
  /**
   * 水平居中
   * @param container 容器
   */
  addHorizontalMiddle (container) {
    if (container) {
      this.buildClickItem(container, "icon-chuizhiduiqi", "水平居中", () => {
        this.designer.alignLayers('center');
      });
    }
  }
  /**
   * 水平方向右对齐
   * @param container 容器
   */
  addHorizontalRight (container) {
    if (container) {
      this.buildClickItem(container, "icon-youduiqi", "右对齐", () => {
        this.designer.alignLayers('right');
      });
    }
  }
  refreshLink (layer) {
    if (this.designer) {
      this.designer.linkByTool(layer);
    }
  }

  //###################右边#####################
  addSave (container) {
    if (container) {
      const self = this;
      this.buildClickItem(container, "icon-baocun", "保存", function (event) {
        const submit = Submit.getInstance(self.context);
        submit.save();
      });
    }
  }
  addPreview (container) {
    if (container) {
      const self = this;
      this.buildClickItem(container, "icon-yulanchakan", "预览", function (event) {
        const submit = Submit.getInstance(self.context);
        submit.preview();
      });
    }
  }
  addConfig (container) {
    if (container) {
      const self = this;
      this.buildClickItem(container, "icon-peizhi", "配置", function (event) {
        const dialog = new ConfigDialog(self.context);
        dialog.open(function (result) {
          if (self.designer) {
            self.designer.refreshScale();
            self.designer.refreshBackground();
          }
        });
      });
    }
  }
  addTempl (container) {
    if (container) {
      const self = this;
      this.buildClickItem(container, "icon-shengchengmoban", "生成模板", function (event) {
        //弹框
        const dialog = new TemplDialog(self.context);
        dialog.open(function (result) { });
      });
    }
  }
  addMessageCenter (container) {
    if (container) {
      this.buildClickItem(container, "icon-xiaoxizhongxin", "消息中心", (event) => {
        const dialog = new MessageCenterDialog(this.context);
        dialog.open(() => {
          registryMessageCenter(this.context)
        });
      });
    }
  }
}