import ShareDialog from "../dialog/ShareDialog";
import Submit from "../Submit";
export default class ToolPreview {
  constructor(context) {
    this.context = context;
    // const tool=context.getTool();
    this.property = {};
  }
  initProperty (property) {
    // 递归赋值
    $.extend(true, this.property, property);
  }
  refreshProperty () {
    const container = this.container;
    const property = this.property;
    if (container && property) {
      const drawerContainer = $(container).find(".preview-tool-drawer");
      $(drawerContainer).css(property);
      if (property.btnStyle) {
        const btnContainer = $(container).find(".preview-tool-btn");
        $(btnContainer).css(property.btnStyle);
      }
    }
  }
  initPanel (parentContainer) {
    if (parentContainer) {
      this.container = $(`<div class="preview-tool-container">
          <div class="preview-tool-drawer">
              <div class="preview-tool-icon preview-tool-icon-switch ft-font icon-zuozuo" switch="close"></div>
              <div class="preview-tool-body"></div>
              <div class="preview-tool-icon preview-tool-icon-close ft-font icon-guanbi"></div>
          </div>
      </div>`);
      parentContainer.append(this.container);
      this.drawerContainer = $(this.container).find(".preview-tool-drawer");
      this.bodyContainer = $(this.container).find(".preview-tool-body");


      const iconSwitchContainer = $(this.container).find(".preview-tool-icon-switch");
      const iconCloseContainer = $(this.container).find(".preview-tool-icon-close");

      this.refreshOpen(iconSwitchContainer);
      // this.refreshWidth(drawerContainer);

      const self = this;
      $(iconSwitchContainer).on("click", function (event) {
        self.isOpen = !self.isOpen;
        self.refreshOpen(iconSwitchContainer);
        self.refreshWidth(self.drawerContainer);
      });
      // 清空容器，彻底关闭，刷新页面才会出来
      $(iconCloseContainer).on("click", function (event) {
        parentContainer.empty();
      });
    }
  }
  /**
   * 描述:刷新面板
   * 
   */
  refreshPanel () {
    // 1 渲染
    const property = this.property;
    if (property && property.isShow) {
      if (this.bodyContainer) {
        this.bodyContainer.empty();
        //第三种(对象)
        const dataMap = property.dataMap;
        if (dataMap) {
          for (let key in dataMap) {
            const data = dataMap[key];
            if (data.isShow && key === "share") {
              const self=this;
              this.buildClickItem(this.bodyContainer, data, function (event) {
                const submit = Submit.getInstance(self.context);
                console.info("点击:share");
                const dialog = new ShareDialog(self.context);
                dialog.open(function(result){
                  submit.saveShare(result);
                });
              });
            }
            if (data.isShow && key === "warn") {
              this.buildClickItem(this.bodyContainer, data, function (event) {
                console.info("点击:warn");
              });
            }
          }
        }
      }
      // 2 样式
      this.refreshProperty();
      this.modelWidth = $(this.bodyContainer).outerWidth();
      this.refreshWidth(this.drawerContainer);
    } else {
      if (this.container) {
        this.container.empty();
      }
    }
  }
  refreshOpen (iconSwitchContainer) {
    if (iconSwitchContainer) {
      if (this.isOpen) {
        $(iconSwitchContainer).attr({ switch: 'open' })
      } else {
        $(iconSwitchContainer).attr({ switch: 'close' })
      }
    }
  }
  refreshWidth (drawerContainer) {
    if (drawerContainer) {
      // const propWidth = this.property.width || 200;
      const propRight = this.modelWidth + 112;
      if (this.isOpen) {
        $(drawerContainer).css("right", 1);
      } else {
        $(drawerContainer).css("right", -(propRight - 64));
      }
    }
  }
  buildClickItem (parentContainer, item, callback) {
    if (parentContainer && item) {
      const container = $(`<div class="preview-tool-btn">
                            <i class="preview-tool-btn-icon ft-font" >${item.icon}</i>
                            <div class="preview-tool-btn-tooltip">${item.name}</div>
                          </div>`);
      parentContainer.append(container);
      // 根据工具条顶部的距离和高度动态设置tooltip的top
      container.find('.preview-tool-btn-tooltip').css({
        top: (Number(this.property.top) + Number(this.drawerContainer.outerHeight()) + 5) + 'px',
      })
      $(container).on("click", function (event) {
        // console.info("点击:[" + text + "]");
        if (callback) {
          callback(event);
        }
      });
    }
  }
}