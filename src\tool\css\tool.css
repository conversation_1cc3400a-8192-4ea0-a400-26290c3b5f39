.tool-wrap {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 40px;
}

.tool-wrap .tool-icon {
  position: relative;
  width: 32px;
  height: 32px;
  border-radius: 4px 4px 4px 4px;
  font-size: 16px;
  color: #D9D9D9;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
  cursor: pointer;
}


.tool-icon::after {
  content: attr(data-tooltip);
  padding: 12px;
  white-space: nowrap;
  position: fixed;
  top: 50px;
  z-index: 999999;
  background: rgba(29, 30, 31, 0.8);
  border-radius: 4px 4px 4px 4px;
  font-size: 12px;
  color: #FFFFFF;
  line-height: 16px;
  visibility: hidden;
}

.tool-icon:hover::after {
  visibility: visible;
  opacity: 1;
}

.tool-wrap .tool-icon:hover {
  background: #409EFF;
}


.tool-wrap .tool-left {
  font-size: 16px;
  color: #E5EAF3;
}

.tool-wrap .tool-middle {
  display: flex;
  margin-left: 128px;
}


/* 顶部右边配置 */
.tool-right {
  display: flex;
  flex-direction: row-reverse
}


.tool-right .tool-item {
  margin: 0px 10px;
}

.tool-right .tool-item .tool-font {
  font-size: 14px;
  cursor: pointer;
}

.preview-tool-container {
  /* position: relative; */
  position: absolute;
}

.preview-tool-drawer {
  /* 抽屉固定在页面窗口上 */
  position: fixed;
  right: 0;
  height: 48px;
  padding: 8px 16px;
  transition: right 0.3s ease-out;
  white-space: nowrap;
  display: flex;
  align-items: center;
  border-radius: 50px 0 0 50px;
}

.preview-tool-icon {
  height: 100%;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px 4px 4px 4px;
  color: #cfd3dc;
  font-size: 10px;
  cursor: pointer;
}
.preview-tool-icon-switch[switch='open'] {
  transform: rotateZ(180deg);
}
.preview-tool-icon-switch[switch='close'] {
  transform: rotateZ(0deg);
}

.preview-tool-body {
  display: flex;
  align-items: center;
  height: 100%;
  margin-right: 16px;
}

.preview-tool-btn {
  position: relative;
  width: 32px;
  height: 32px;
  margin-left: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px 4px 4px 4px;
  cursor: pointer;
  user-select: none;
}
.preview-tool-btn .preview-tool-btn-tooltip {
  max-width: 100px;
  padding: 12px;
  position: fixed;
  z-index: 999999;
  background: #1D1E1F;
  border-radius: 4px 4px 4px 4px;
  font-size: 12px;
  color: #FFFFFF;
  line-height: 17px;
  visibility: hidden;
  word-break: break-all;
  display: inline-block;
  white-space: break-spaces;
}
.preview-tool-btn:hover .preview-tool-btn-tooltip {
  visibility: visible;
}

.preview-tool-icon:hover,
.preview-tool-btn:hover {
  background: #409EFF;
  color: #ffffff;
}

.preview-tool-btn .preview-tool-btn-icon {
  font-size: 16px;
  color: #cfd3dc;
}