import {toBgStyle, toBorderStyle, toFontStyle} from "./StyleUtil";

/**
 * 描述:获取浏览器当前支持的前缀
 * 如：'', '-webkit-', '-moz-', '-o-', '-ms-'
 * @returns {string}
 */
export function getVendorPrefix() {
    if (!isSupported()) {
        const style = document.body.style;
        const prefixes = getPrefixes();
        for (let i = 0; i < prefixes.length; i++) {
            if (style[prefixes[i] + 'AnimationName'] !== undefined) {
                return '-' + prefixes[i].toLowerCase() + '-';
            }
        }
    }
    return '';
}

/**
 * 描述:是否支持动画
 * @returns {boolean}
 */
export function isSupported() {
    const style = document.body.style;
    if (style.animationName !== undefined) {
        return true;
    }
    return false;
}

/**
 * 描述：获取所有浏览器前缀
 * ['', '-webkit-', '-moz-', '-o-', '-ms-'];
 * @returns {string}
 */
export function getWebPrefixes() {
    let webPrefixes;
    let prefixes = getPrefixes();
    for (let i = 0; i < prefixes.length; i++) {
        if(!webPrefixes)
            webPrefixes=[];
        if (prefixes[i])
            webPrefixes.push("-" + prefixes[i].toLowerCase() + "-");
        else
            webPrefixes.push(prefixes[i]);
    }
    return webPrefixes;
}

/**
 * 描述:新增动画<style>标签
 * @param context
 * @param property
 */
export function addKeyFrameTag(context, property) {
    if (!property.id) {
        console.error("添加keyFrame标签失败，动画ID不存在");
        return;
    }
    const id = `${property.id}`;
    const name = property.name;
    const keyFrames = property.frames;
    if (!name || !keyFrames || !keyFrames.length) {
        console.error("添加keyFrame标签失败，名称或者动画正不存在", keyFrames);
        return false;
    }
    //获取所有样式信息
    let keyFrameListStr = "";
    for (let i = 0; i < keyFrames.length; i++) {
        let keyFrameItem = keyFrames[i];
        const keyFrameStr = toKeyFrameStr(context, keyFrameItem);
        if (keyFrameStr) {
            keyFrameListStr += keyFrameStr + " ";
        }
    }
    if (keyFrameListStr) {
        const vendorPrefix = getVendorPrefix();
        const keyFrameStyleHtml= `@` + vendorPrefix + `keyframes ${id} {${keyFrameListStr}} `;
        const styleSheet = $(`<style>`+keyFrameStyleHtml+`</style>`);
        styleSheet.attr({type: "text/css",id: id,"class": "keyframe-style"});
        styleSheet.appendTo("head");
        return styleSheet;
    }
}

/**
 * 检查并删除动画标签
 * @param property 动画属性
 */
export function checkAndRemoveKeyFrameTag(property) {
    if (property && property.id) {
        const styles = document.head.getElementsByClassName('keyframe-style');
        for (let i = 0; i < styles.length; i++) {
            if (styles[i].id === `${property.id}`) {
                styles[i].remove();
                break;
            }
        }
    }
}

/**
 * 获取浏览器的前缀
 * @returns {string[]}
 */
function getPrefixes() {
    return ['', 'Webkit', 'Moz', 'O', 'ms'];
}


/**
 * 解析动画的帧样式
 * @param context
 * @param keyFrameItem
 * selector 帧进度 0
 *      帧的进度信息
 * 一般样式
 *      top: 407 left: 632 height: 90 width: 90
 * opacity [0-1] 透明度
 *      0.5
 * transition-timing-function 过渡函数
 *   cubic-bezier(0.215, 0.61, 0.355, 1);//曲线
 * transform-origin 基点位置
 *   top center; center bottom;  center;
 * visibility 是否可见
 *   visible;  hidden;
 *
 * @keyframes shakeX {
      from,
      to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
      }
      80% {
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0);
      }
      90% { -webkit-transform: translate3d(-10px, 0, 0); transform: translate3d(-10px, 0, 0);}
    }
 */
function toKeyFrameStr(context, keyFrameItem) {
    if (!keyFrameItem || (!keyFrameItem["frameIndex"] && 0 !== keyFrameItem["frameIndex"])) {
        console.error("解析keyFrameItem异常,为空或者帧进度没填写", keyFrameItem)
        return;
    }
    let keyFrameStr = "";

    // 1. 处理位置配置
    if (keyFrameItem['position']) {
        const position = keyFrameItem['position'];
        if (position["width"] || 0===position["width"]) {
            keyFrameStr += `width:${position["width"]};`;
        }
        if (position["height"] || 0===position["height"]) {
            keyFrameStr += `height:${position["height"]};`;
        }
        if (position["top"] || 0===position["top"]) {
            keyFrameStr += `top:${position["top"]};`;
        }
        if (position["right"] || 0===position["right"]) {
            keyFrameStr += `right:${position["right"]};`;
        }
        if (position["bottom"] || 0===position["bottom"]) {
            keyFrameStr += `bottom:${position["bottom"]};`;
        }
        if (position["left"] || 0===position["left"]) {
            keyFrameStr += `left:${position["left"]};`;
        }
        if (position["margin"] || 0===position["margin"]) {
            keyFrameStr += `margin:${position["margin"]};`;
        }
        if (position["padding"] || 0===position["padding"]) {
            keyFrameStr += `padding:${position["padding"]};`;
        }
    }

    // 2. 处理其他配置
    // 透明度
    if (keyFrameItem["opacity"] || 0===keyFrameItem["opacity"]) {
        keyFrameStr += `opacity:${Number(keyFrameItem["opacity"]) / 100};`;
    }

    // 文字属性
    const fontStyle = toFontStyle(keyFrameItem.font);
    for (const key in fontStyle) {
        if (fontStyle[key]) {
            keyFrameStr += `${key}:${fontStyle[key]};`;
        }
    }

    // 背景属性
    const bgStyle = toBgStyle(context, keyFrameItem.bg);
    for (const key in bgStyle) {
        if (bgStyle[key]) {
            keyFrameStr += `${key}:${bgStyle[key]};`;
        }
    }

    // 边框属性
    const borderStyle = toBorderStyle(context, keyFrameItem.border);
    for (const key in borderStyle) {
        if (borderStyle[key]) {
            keyFrameStr += `${key}:${borderStyle[key]};`;
        }
    }

    //3. 设置transform属性
    if (keyFrameItem.transform) {
        const webPrefixes = getWebPrefixes();

        const keyFrameTransformValue = toTransformValueStr(keyFrameItem.transform);
        if (keyFrameTransformValue) {
            for (let i = 0; i < webPrefixes.length; i++) {
                keyFrameStr += webPrefixes[i] + "transform :" + keyFrameTransformValue + ";";
            }
        }
        // 动画基点
        if (keyFrameItem.transform["transformOrigin"]) {
            keyFrameStr += 'transform-origin' + ":" + keyFrameItem.transform["transformOrigin"] + ";";
        }
    }

    //4. 组装全过程
    if (keyFrameStr) {
        let percent;
        if (Number(keyFrameItem["frameIndex"]) === 0) {
            percent = "from";
        } else if (Number(keyFrameItem["frameIndex"]) === 100) {
            percent = "to";
        } else {
            percent = keyFrameItem["frameIndex"] + "%";
        }
        return `${percent} { ${keyFrameStr} }`;
    }
}
/**
 * 根据属性解析成transform值
 * @param transformItem
 * @returns {string}
 */
function toTransformValueStr(transformItem) {
    let transformValue = "";
    //设置移动
    if (transformItem["translateX"] || 0===transformItem["translateX"])
        transformValue += "translateX(" + transformItem["translateX"] + "px) ";
    if (transformItem["translateY"] || 0===transformItem["translateY"])
        transformValue += "translateY(" + transformItem["translateY"] + "px) ";
    if (transformItem["translateZ"] || 0===transformItem["translateZ"])
        transformValue += "translateZ(" + transformItem["translateZ"] + "px) ";
    //设置缩放
    if (transformItem["scaleX"] || 0===transformItem["scaleX"])
        transformValue += "scaleX(" + transformItem["scaleX"] + ") ";
    if (transformItem["scaleY"] || 0===transformItem["scaleY"])
        transformValue += "scaleY(" + transformItem["scaleY"] + ") ";
    if (transformItem["scaleZ"] || 0===transformItem["scaleZ"])
        transformValue += "scaleZ(" + transformItem["scaleZ"] + ") ";
    //设置旋转
    if (transformItem["rotate"] || 0===transformItem["rotate"]) //兼容之前的
        transformValue += "rotate(" + transformItem["rotateZ"] + "deg) ";

    if (transformItem["rotateX"] || 0===transformItem["rotateX"])
        transformValue += "rotateX(" + transformItem["rotateX"] + "deg) ";
    if (transformItem["rotateY"] || 0===transformItem["rotateY"])
        transformValue += "rotateY(" + transformItem["rotateY"] + "deg) ";
    if (transformItem["rotateZ"] || 0===transformItem["rotateZ"] )
        transformValue += "rotateZ(" + transformItem["rotateZ"] + "deg) ";
    //设置倾斜 skew
    if (transformItem["skewX"] || 0===transformItem["skewX"])
        transformValue += "skewX(" + transformItem["skewX"] + "deg) ";
    if (transformItem["skewY"] || 0===transformItem["skewY"])
        transformValue += "skewY(" + transformItem["skewY"] + "deg) ";
    if (transformItem["skewZ"] || 0===transformItem["skewZ"])
        transformValue += "skewZ(" + transformItem["skewZ"] + "deg) ";
    return transformValue;
}