import Expression from "./Expression" //表达式解析
import { isNum, isStr } from "./Util";

export function computeToResultMap (conditionItem, paramItem, dataItem) {
  let resultMap;
  if (conditionItem.funList && conditionItem.funList.length) {
    for (let i = 0; i < conditionItem.funList.length; i++) {
      const funItem = conditionItem.funList[i];
      if (funItem) {
        let computeResult;
        if (funItem.type && "param" === funItem.type) {
          computeResult = compute(funItem, paramItem);//参数
        } else if (funItem.type && "bind" === funItem.type) {
          computeResult = compute(funItem, dataItem);//数据
        }

        //存入结果
        if (!resultMap) {
          resultMap = {};
        }
        const andor = funItem.andor;
        if (andor && "or" === andor) {
          if (!resultMap["or"]) {
            resultMap["or"] = [computeResult];
          } else {
            resultMap["or"].push(computeResult);
          }
        } else {
          if (!resultMap["and"]) {
            resultMap["and"] = [computeResult];
          } else {
            resultMap["and"].push(computeResult);
          }
        }
      }
    }
  }
  return resultMap;
}
/**
 * 描述:判断
 * @param map
 * @returns {boolean}
 */
export function judge (map) {
  if (!map) {
    return false;
  }
  //计算 and
  let andFlag = true;
  if (map && map["and"] && map["and"].length) {
    for (let i = 0; i < map["and"].length; i++) {
      const flag = map["and"][i];
      if (!flag) {
        andFlag = false;
      }
    }
  }
  //计算 or
  let orFlag = false;
  if (map && map["or"] && map["or"].length) {
    for (let i = 0; i < map["or"].length; i++) {
      const flag = map["or"][i];
      if (flag) {
        orFlag = true;
      }
    }
  }
  //判断
  if (andFlag || orFlag) {
    return true;
  } else {
    return false;
  }
}
/**
 * 计算条件属性
 * export function conditionCompute(conditionItem,mappingDataItem){
 */
export function compute (conditionItem, mappingDataItem) {
  //1.获取字段属性值
  if (!conditionItem.column || !conditionItem.opt || !conditionItem.expr) {
    console.warn("未选择字段或操作符或未填写表达值!");
    return false;
  } else {
    const optEnum = getOptEnum();
    if (mappingDataItem) {
      const columnValue = getColumnValue(conditionItem, mappingDataItem);
      conditionItem.columnValue = columnValue;
      const exprValue = getExprValue(conditionItem, mappingDataItem);
      conditionItem.exprValue = columnValue;
      //console.info("条件属性处理，字段值["+columnValue+"] 表达式值["+exprValue+"]",conditionItem);
      if ((columnValue || 0 === columnValue || "0" === columnValue) && (exprValue || 0 === exprValue || "0" === exprValue)) {
        let exprList;
        let str;
        switch (conditionItem.opt) {
          case optEnum.More:
            return (getNumberValue(columnValue) > getNumberValue(exprValue));
          case optEnum.MoreEqual:
            return (getNumberValue(columnValue) >= getNumberValue(exprValue));
          case optEnum.Less:
            return (getNumberValue(columnValue) < getNumberValue(exprValue));
          case optEnum.LessEqual:
            return (getNumberValue(columnValue) <= getNumberValue(exprValue));
          case optEnum.Equal:
            return (getNumberValue(columnValue) == getNumberValue(exprValue));
          case optEnum.NotEqual:
            return (columnValue != exprValue);
          case optEnum.Start:
            // return exprValue.startsWith(columnValue);
            return columnValue.startsWith(exprValue);
          case optEnum.End:
            // return exprValue.endsWith(columnValue);
            return columnValue.endsWith(exprValue);
          case optEnum.Like:
            if (isStr(columnValue)) {
              str = columnValue;
            } else {
              str = columnValue.toString();
            }
            if (str) {
              if (str.indexOf(exprValue) != -1) {
                return true;
              } else {
                return false;
              }
            }
          case optEnum.NotLike:
            if (isStr(columnValue)) {
              str = columnValue;
            } else {
              str = columnValue.toString();
            }
            if (str) {
              if (str.indexOf(exprValue) != -1) {
                return false;
              } else {
                return true;
              }
            }
          case optEnum.In:
            if (exprValue.indexOf(",") != -1) {
              exprList = exprValue.split(",");
            } else {
              exprList = [exprValue];
            }
            if (exprList.indexOf(columnValue) != -1) {
              return true;
            } else {
              return false;
            }
          case optEnum.NotIn:
            if (exprValue.indexOf(",") != -1) {
              exprList = exprValue.split(",");
            } else {
              exprList = [exprValue];
            }
            if (exprList.indexOf(columnValue) != -1) {
              return false;
            } else {
              return true;
            }
          default:
            return false;
        }
        return false;
      } else {
        return false;
      }
    } else {
      console.warn("未选匹配到绑定数据信息!");
      return false;
    }
  }
}


function getNumberValue (value) {
  var regu = "^[0-9]+\.?[0-9]*$";

  var re = new RegExp(regu);

  if (re.test(value)) {
    return parseFloat(value);
  } else {
    return value;
  }
}
/**
 * 获取表达式值
 * @param conditionItem
 * @param dataItem
 */
function getExprValue (conditionItem, dataItem) {
  if (conditionItem.expr) {
    const expression = new Expression();
    return expression.computeExpr(conditionItem.expr, dataItem);
  }
}
/**
 * 描述:获取左值
 * @param conditionItem
 * @param dataList
 */
function getColumnValue (conditionItem, dataItem) {
  let columnValue;
  if (!conditionItem || !dataItem) {
    console.warn("条件属性未配置或者未配置数据绑定!");
  }
  if (conditionItem.column) {
    if (dataItem[conditionItem.column] || 0 === dataItem[conditionItem.column] || "0" === dataItem[conditionItem.column]) {
      columnValue = dataItem[conditionItem.column];
    } else {
      console.warn("数据绑定结果不存在字段或为空[" + conditionItem.column + "]!", dataItem);
    }
  } else {
    console.warn("条件属性未配置字段信息!");
  }
  return columnValue;
}
/**
 * 描述：获取条件操作类型枚举
 * @returns {Readonly<{More: symbol, NotEqual: symbol, Like: symbol, Equal: symbol, Start: symbol, MoreEqual: symbol, LessEqual: symbol, End: symbol, NotLike: symbol, Less: symbol}>}
 */
function getOptEnum () {
  return Object.freeze({
    More: "More",//大于
    MoreEqual: "MoreEqual",//大于等于
    Less: "Less",//小于
    LessEqual: "LessEqual",//小于等于
    Equal: "Equal", //等于
    NotEqual: "NotEqual", //不等于
    Start: "Start",//开头
    End: "End",//结束
    Like: "Like",//包含
    NotLike: "NotLike",//不包含
    In: "In",//不包含
    NotIn: "NotIn",//不包含
  })
}