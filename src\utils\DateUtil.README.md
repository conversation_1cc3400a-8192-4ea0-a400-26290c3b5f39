# DateUtil 日期格式化工具类

一个类似dayjs的轻量级日期格式化工具类，专为LSD-Designer项目设计。

## 特性

- 🚀 轻量级，无外部依赖
- 📅 支持多种日期格式化模式
- 🔧 链式调用API
- ⏰ 支持时间计算和操作
- 🌍 支持12/24小时制
- 📱 兼容现代浏览器

## 安装和导入

```javascript
// 直接导入
import DateUtil from './utils/DateUtil.js';

// 或从Util.js中导入
import { DateUtil } from './utils/Util.js';
```

## 基础用法

### 格式化当前时间

```javascript
// 默认格式 YYYY-MM-DD HH:mm:ss
DateUtil.format(); // "2024-01-15 14:30:25"

// 自定义格式
DateUtil.format('YYYY-MM-DD'); // "2024-01-15"
DateUtil.format('HH:mm:ss'); // "14:30:25"
DateUtil.format('YYYY年MM月DD日'); // "2024年01月15日"
```

### 格式化指定时间

```javascript
const date = new Date('2024-01-15 14:30:25');

DateUtil.formatDate(date); // "2024-01-15 14:30:25"
DateUtil.formatDate(date, 'YYYY-MM-DD'); // "2024-01-15"
DateUtil.formatDate(date, 'hh:mm:ss A'); // "02:30:25 PM"
```

### 创建DateUtil实例

```javascript
const dateUtil = DateUtil.create('2024-01-15');
console.log(dateUtil.format('YYYY年MM月DD日')); // "2024年01月15日"
```

## 格式化标记

| 标记 | 描述 | 示例 |
|------|------|------|
| `YYYY` | 四位年份 | 2024 |
| `YY` | 两位年份 | 24 |
| `MM` | 两位月份 | 01-12 |
| `M` | 月份 | 1-12 |
| `DD` | 两位日期 | 01-31 |
| `D` | 日期 | 1-31 |
| `HH` | 24小时制小时 | 00-23 |
| `H` | 24小时制小时 | 0-23 |
| `hh` | 12小时制小时 | 01-12 |
| `h` | 12小时制小时 | 1-12 |
| `mm` | 分钟 | 00-59 |
| `m` | 分钟 | 0-59 |
| `ss` | 秒 | 00-59 |
| `s` | 秒 | 0-59 |
| `SSS` | 毫秒 | 000-999 |
| `A` | 上午/下午 | AM/PM |
| `a` | 上午/下午 | am/pm |

## 预定义格式

```javascript
const formats = DateUtil.formats;

// 使用预定义格式
DateUtil.format(formats.DATE); // "2024-01-15"
DateUtil.format(formats.TIME); // "14:30:25"
DateUtil.format(formats.DATETIME); // "2024-01-15 14:30:25"
DateUtil.format(formats.DATE_CN); // "2024年01月15日"
DateUtil.format(formats.TIME_12); // "02:30:25 PM"
```

### 可用的预定义格式

- `DATE`: 'YYYY-MM-DD'
- `TIME`: 'HH:mm:ss'
- `DATETIME`: 'YYYY-MM-DD HH:mm:ss'
- `DATETIME_SHORT`: 'YYYY-MM-DD HH:mm'
- `DATE_CN`: 'YYYY年MM月DD日'
- `TIME_12`: 'hh:mm:ss A'
- `TIMESTAMP`: 'YYYY-MM-DD HH:mm:ss.SSS'

## 时间操作

### 添加时间

```javascript
const date = DateUtil.create('2024-01-15 12:00:00');

date.add(1, 'year').format(); // "2025-01-15 12:00:00"
date.add(3, 'month').format(); // "2024-04-15 12:00:00"
date.add(7, 'day').format(); // "2024-01-22 12:00:00"
date.add(2, 'hour').format(); // "2024-01-15 14:00:00"
date.add(30, 'minute').format(); // "2024-01-15 12:30:00"
```

### 减少时间

```javascript
const date = DateUtil.create('2024-01-15 12:00:00');

date.subtract(1, 'year').format(); // "2023-01-15 12:00:00"
date.subtract(2, 'hour').format(); // "2024-01-15 10:00:00"
```

### 支持的时间单位

- `year` - 年
- `month` - 月
- `day` - 日
- `hour` - 小时
- `minute` - 分钟
- `second` - 秒
- `millisecond` - 毫秒

## 在LSD-Designer中的应用场景

### 1. 图层组件中显示时间

```javascript
// 在TextLayer中显示当前时间
const textLayer = {
  text: '当前时间: ' + DateUtil.format('YYYY-MM-DD HH:mm:ss')
};

// 在NumLayer中显示时间戳
const numLayer = {
  value: DateUtil.format('YYYYMMDDHHmmss')
};
```

### 2. 数据查询参数

```javascript
// 构建查询参数
const queryParams = {
  startTime: DateUtil.format('YYYY-MM-DD 00:00:00'),
  endTime: DateUtil.format('YYYY-MM-DD 23:59:59')
};
```

### 3. 图表时间轴

```javascript
// ECharts时间轴数据
const chartData = [];
for (let i = 0; i < 7; i++) {
  const date = DateUtil.create().subtract(i, 'day');
  chartData.push({
    date: date.format('MM-DD'),
    value: Math.random() * 100
  });
}
```

### 4. 属性面板配置

```javascript
// 时间格式选择器
const timeFormatOptions = [
  { label: '标准格式', value: 'YYYY-MM-DD HH:mm:ss' },
  { label: '中文格式', value: 'YYYY年MM月DD日' },
  { label: '12小时制', value: 'hh:mm:ss A' }
];
```

## API参考

### 静态方法

- `DateUtil.format(format?)` - 格式化当前时间
- `DateUtil.formatDate(date, format?)` - 格式化指定时间
- `DateUtil.create(date?)` - 创建DateUtil实例

### 实例方法

- `format(format?)` - 格式化时间
- `add(value, unit)` - 添加时间
- `subtract(value, unit)` - 减少时间
- `isValid()` - 检查日期是否有效
- `year()` - 获取年份
- `month()` - 获取月份
- `day()` - 获取日期
- `hour()` - 获取小时
- `minute()` - 获取分钟
- `second()` - 获取秒
- `toDate()` - 转换为Date对象

## 浏览器兼容性

- Chrome 51+
- Firefox 54+
- Safari 10+
- Edge 15+
- IE 不支持 (使用了ES6语法)

## 测试

打开 `test-dateutil.html` 文件在浏览器中查看测试结果。

## 许可证

MIT License
