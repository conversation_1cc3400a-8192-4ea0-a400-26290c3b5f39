/**
 * DateUtil 使用示例
 * 展示如何在LSD-Designer项目中使用日期格式化工具类
 */

import DateUtil from './DateUtil.js';
// 或者从Util.js中导入
// import { DateUtil } from './Util.js';

/**
 * 基础使用示例
 */
export function basicUsageExamples() {
  console.log('=== DateUtil 基础使用示例 ===');
  
  // 1. 格式化当前时间
  console.log('当前时间:', DateUtil.format());
  console.log('当前日期:', DateUtil.format('YYYY-MM-DD'));
  console.log('当前时间(12小时制):', DateUtil.format('hh:mm:ss A'));
  
  // 2. 格式化指定时间
  const specificDate = new Date('2024-01-15 14:30:25');
  console.log('指定时间:', DateUtil.formatDate(specificDate));
  console.log('指定日期:', DateUtil.formatDate(specificDate, 'YYYY年MM月DD日'));
  
  // 3. 使用预定义格式
  console.log('预定义格式:');
  console.log('  DATE:', DateUtil.formatDate(specificDate, DateUtil.formats.DATE));
  console.log('  TIME:', DateUtil.formatDate(specificDate, DateUtil.formats.TIME));
  console.log('  DATETIME:', DateUtil.formatDate(specificDate, DateUtil.formats.DATETIME));
  console.log('  DATE_CN:', DateUtil.formatDate(specificDate, DateUtil.formats.DATE_CN));
  console.log('  TIME_12:', DateUtil.formatDate(specificDate, DateUtil.formats.TIME_12));
}

/**
 * 在图层组件中使用的示例
 * 模拟在TextLayer或NumLayer中显示格式化时间
 */
export function layerUsageExample() {
  console.log('=== 图层组件中的使用示例 ===');
  
  // 模拟图层属性配置
  const layerProperty = {
    text: '当前时间: {currentTime}',
    dateFormat: 'YYYY-MM-DD HH:mm:ss',
    autoUpdate: true
  };
  
  // 替换文本中的时间占位符
  function updateLayerText(property) {
    let text = property.text;
    if (text.includes('{currentTime}')) {
      const formattedTime = DateUtil.format(property.dateFormat);
      text = text.replace('{currentTime}', formattedTime);
    }
    return text;
  }
  
  console.log('图层显示文本:', updateLayerText(layerProperty));
  
  // 不同格式的示例
  const formats = [
    'YYYY-MM-DD',
    'HH:mm:ss',
    'YYYY年MM月DD日',
    'hh:mm A',
    'MM/DD/YYYY'
  ];
  
  formats.forEach(format => {
    const property = { ...layerProperty, dateFormat: format };
    console.log(`格式 ${format}:`, updateLayerText(property));
  });
}

/**
 * 在数据绑定中使用的示例
 * 模拟在数据查询或处理中格式化时间参数
 */
export function dataBindingExample() {
  console.log('=== 数据绑定中的使用示例 ===');
  
  // 模拟数据查询参数
  function buildQueryParams(startDate, endDate) {
    return {
      startTime: DateUtil.formatDate(startDate, 'YYYY-MM-DD 00:00:00'),
      endTime: DateUtil.formatDate(endDate, 'YYYY-MM-DD 23:59:59'),
      queryDate: DateUtil.format('YYYY-MM-DD'),
      timestamp: DateUtil.format('YYYY-MM-DD HH:mm:ss')
    };
  }
  
  const start = new Date('2024-01-01');
  const end = new Date('2024-01-31');
  const params = buildQueryParams(start, end);
  
  console.log('查询参数:', params);
  
  // 模拟API请求URL构建
  function buildApiUrl(baseUrl, params) {
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    return `${baseUrl}?${queryString}`;
  }
  
  const apiUrl = buildApiUrl('/api/data', params);
  console.log('API URL:', apiUrl);
}

/**
 * 在报表和图表中使用的示例
 * 模拟在ECharts图表中格式化时间轴
 */
export function chartUsageExample() {
  console.log('=== 图表中的使用示例 ===');
  
  // 生成时间序列数据
  function generateTimeSeriesData(days = 7) {
    const data = [];
    const baseDate = DateUtil.create().subtract(days, 'day');
    
    for (let i = 0; i < days; i++) {
      const date = baseDate.add(i, 'day');
      data.push({
        date: date.format('YYYY-MM-DD'),
        dateDisplay: date.format('MM月DD日'),
        value: Math.floor(Math.random() * 100)
      });
    }
    
    return data;
  }
  
  const chartData = generateTimeSeriesData();
  console.log('图表数据:', chartData);
  
  // 模拟ECharts配置
  const chartOption = {
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.dateDisplay)
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: chartData.map(item => item.value),
      type: 'line'
    }],
    title: {
      text: `数据趋势 (${DateUtil.format('YYYY年MM月')})`
    }
  };
  
  console.log('ECharts配置:', chartOption);
}

/**
 * 时间计算和操作示例
 */
export function timeCalculationExample() {
  console.log('=== 时间计算示例 ===');
  
  const now = DateUtil.create();
  
  // 计算相对时间
  console.log('当前时间:', now.format());
  console.log('1小时前:', now.subtract(1, 'hour').format());
  console.log('1天后:', now.add(1, 'day').format());
  console.log('下个月:', now.add(1, 'month').format('YYYY年MM月DD日'));
  console.log('去年:', now.subtract(1, 'year').format('YYYY年'));
  
  // 生成时间范围
  function getTimeRange(type) {
    const now = DateUtil.create();
    let start, end;
    
    switch (type) {
      case 'today':
        start = now.format('YYYY-MM-DD 00:00:00');
        end = now.format('YYYY-MM-DD 23:59:59');
        break;
      case 'thisWeek':
        start = now.subtract(now.date.getDay(), 'day').format('YYYY-MM-DD 00:00:00');
        end = now.add(6 - now.date.getDay(), 'day').format('YYYY-MM-DD 23:59:59');
        break;
      case 'thisMonth':
        start = DateUtil.create(new Date(now.date.getFullYear(), now.date.getMonth(), 1)).format('YYYY-MM-DD 00:00:00');
        end = DateUtil.create(new Date(now.date.getFullYear(), now.date.getMonth() + 1, 0)).format('YYYY-MM-DD 23:59:59');
        break;
      case 'last7Days':
        start = now.subtract(7, 'day').format('YYYY-MM-DD 00:00:00');
        end = now.format('YYYY-MM-DD 23:59:59');
        break;
    }
    
    return { start, end };
  }
  
  console.log('今天:', getTimeRange('today'));
  console.log('本周:', getTimeRange('thisWeek'));
  console.log('本月:', getTimeRange('thisMonth'));
  console.log('最近7天:', getTimeRange('last7Days'));
}

/**
 * 在属性面板中使用的示例
 * 模拟在PropertyPanel中配置时间格式
 */
export function propertyPanelExample() {
  console.log('=== 属性面板中的使用示例 ===');
  
  // 模拟时间格式选项
  const timeFormatOptions = [
    { label: '标准格式', value: 'YYYY-MM-DD HH:mm:ss' },
    { label: '日期', value: 'YYYY-MM-DD' },
    { label: '时间', value: 'HH:mm:ss' },
    { label: '中文日期', value: 'YYYY年MM月DD日' },
    { label: '12小时制', value: 'hh:mm:ss A' },
    { label: '简短格式', value: 'MM/DD HH:mm' },
    { label: '时间戳', value: 'YYYY-MM-DD HH:mm:ss.SSS' }
  ];
  
  // 预览不同格式的效果
  const previewDate = new Date();
  console.log('格式预览:');
  timeFormatOptions.forEach(option => {
    const formatted = DateUtil.formatDate(previewDate, option.value);
    console.log(`  ${option.label}: ${formatted}`);
  });
  
  // 模拟保存配置
  function saveTimeFormatConfig(layerId, format) {
    const config = {
      layerId,
      timeFormat: format,
      preview: DateUtil.format(format),
      updatedAt: DateUtil.format()
    };
    console.log('保存配置:', config);
    return config;
  }
  
  saveTimeFormatConfig('layer_001', 'YYYY年MM月DD日 HH:mm');
}

// 运行所有示例（在开发环境中可以调用）
export function runAllExamples() {
  basicUsageExamples();
  layerUsageExample();
  dataBindingExample();
  chartUsageExample();
  timeCalculationExample();
  propertyPanelExample();
}

// 如果直接运行此文件，执行所有示例
if (typeof window === 'undefined') {
  // Node.js环境
  runAllExamples();
}
