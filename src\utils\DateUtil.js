/**
 * 日期格式化工具类
 * 类似dayjs的简化版本，支持常用的日期格式化功能
 * 
 * 支持的格式化标记：
 * YYYY - 四位年份 (2024)
 * YY   - 两位年份 (24)
 * MM   - 两位月份 (01-12)
 * M    - 月份 (1-12)
 * DD   - 两位日期 (01-31)
 * D    - 日期 (1-31)
 * HH   - 24小时制小时 (00-23)
 * H    - 24小时制小时 (0-23)
 * hh   - 12小时制小时 (01-12)
 * h    - 12小时制小时 (1-12)
 * mm   - 分钟 (00-59)
 * m    - 分钟 (0-59)
 * ss   - 秒 (00-59)
 * s    - 秒 (0-59)
 * SSS  - 毫秒 (000-999)
 * A    - 上午/下午 (AM/PM)
 * a    - 上午/下午 (am/pm)
 * 
 * 使用示例：
 * DateUtil.format() // 默认格式 YYYY-MM-DD HH:mm:ss
 * DateUtil.format('YYYY-MM-DD') // 2024-01-15
 * DateUtil.format('YYYY年MM月DD日') // 2024年01月15日
 * DateUtil.format('HH:mm:ss') // 14:30:25
 * DateUtil.format('hh:mm:ss A') // 02:30:25 PM
 */

class DateUtil {
  constructor(date) {
    this.date = date ? new Date(date) : new Date();
  }

  /**
   * 静态方法：创建DateUtil实例
   * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
   * @returns {DateUtil}
   */
  static create(date) {
    return new DateUtil(date);
  }

  /**
   * 静态方法：格式化当前日期
   * @param {string} format - 格式化字符串，默认 'YYYY-MM-DD HH:mm:ss'
   * @returns {string}
   */
  static format(format = 'YYYY-MM-DD HH:mm:ss') {
    return new DateUtil().format(format);
  }

  /**
   * 静态方法：格式化指定日期
   * @param {Date|string|number} date - 日期
   * @param {string} format - 格式化字符串
   * @returns {string}
   */
  static formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    return new DateUtil(date).format(format);
  }

  /**
   * 格式化日期
   * @param {string} format - 格式化字符串
   * @returns {string}
   */
  format(format = 'YYYY-MM-DD HH:mm:ss') {
    if (!this.isValid()) {
      return 'Invalid Date';
    }

    const year = this.date.getFullYear();
    const month = this.date.getMonth() + 1;
    const day = this.date.getDate();
    const hour = this.date.getHours();
    const minute = this.date.getMinutes();
    const second = this.date.getSeconds();
    const millisecond = this.date.getMilliseconds();

    // 12小时制转换
    const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const ampmLower = ampm.toLowerCase();

    // 格式化映射
    const formatMap = {
      'YYYY': year.toString(),
      'YY': year.toString().slice(-2),
      'MM': this.padZero(month, 2),
      'M': month.toString(),
      'DD': this.padZero(day, 2),
      'D': day.toString(),
      'HH': this.padZero(hour, 2),
      'H': hour.toString(),
      'hh': this.padZero(hour12, 2),
      'h': hour12.toString(),
      'mm': this.padZero(minute, 2),
      'm': minute.toString(),
      'ss': this.padZero(second, 2),
      's': second.toString(),
      'SSS': this.padZero(millisecond, 3),
      'A': ampm,
      'a': ampmLower
    };

    // 按长度排序，避免短标记被长标记包含的问题
    const sortedKeys = Object.keys(formatMap).sort((a, b) => b.length - a.length);
    
    let result = format;
    sortedKeys.forEach(key => {
      result = result.replace(new RegExp(key, 'g'), formatMap[key]);
    });

    return result;
  }

  /**
   * 检查日期是否有效
   * @returns {boolean}
   */
  isValid() {
    return this.date instanceof Date && !isNaN(this.date.getTime());
  }

  /**
   * 获取年份
   * @returns {number}
   */
  year() {
    return this.date.getFullYear();
  }

  /**
   * 获取月份 (1-12)
   * @returns {number}
   */
  month() {
    return this.date.getMonth() + 1;
  }

  /**
   * 获取日期 (1-31)
   * @returns {number}
   */
  day() {
    return this.date.getDate();
  }

  /**
   * 获取小时 (0-23)
   * @returns {number}
   */
  hour() {
    return this.date.getHours();
  }

  /**
   * 获取分钟 (0-59)
   * @returns {number}
   */
  minute() {
    return this.date.getMinutes();
  }

  /**
   * 获取秒 (0-59)
   * @returns {number}
   */
  second() {
    return this.date.getSeconds();
  }

  /**
   * 获取毫秒 (0-999)
   * @returns {number}
   */
  millisecond() {
    return this.date.getMilliseconds();
  }

  /**
   * 获取星期几 (0-6, 0表示周日)
   * @returns {number}
   */
  weekday() {
    return this.date.getDay();
  }

  /**
   * 获取时间戳
   * @returns {number}
   */
  timestamp() {
    return this.date.getTime();
  }

  /**
   * 添加时间
   * @param {number} value - 数值
   * @param {string} unit - 单位 (year, month, day, hour, minute, second, millisecond)
   * @returns {DateUtil}
   */
  add(value, unit) {
    const newDate = new Date(this.date);
    
    switch (unit) {
      case 'year':
        newDate.setFullYear(newDate.getFullYear() + value);
        break;
      case 'month':
        newDate.setMonth(newDate.getMonth() + value);
        break;
      case 'day':
        newDate.setDate(newDate.getDate() + value);
        break;
      case 'hour':
        newDate.setHours(newDate.getHours() + value);
        break;
      case 'minute':
        newDate.setMinutes(newDate.getMinutes() + value);
        break;
      case 'second':
        newDate.setSeconds(newDate.getSeconds() + value);
        break;
      case 'millisecond':
        newDate.setMilliseconds(newDate.getMilliseconds() + value);
        break;
    }
    
    return new DateUtil(newDate);
  }

  /**
   * 减少时间
   * @param {number} value - 数值
   * @param {string} unit - 单位
   * @returns {DateUtil}
   */
  subtract(value, unit) {
    return this.add(-value, unit);
  }

  /**
   * 获取原始Date对象
   * @returns {Date}
   */
  toDate() {
    return new Date(this.date);
  }

  /**
   * 转换为ISO字符串
   * @returns {string}
   */
  toISOString() {
    return this.date.toISOString();
  }

  /**
   * 数字补零
   * @param {number} num - 数字
   * @param {number} length - 长度
   * @returns {string}
   */
  padZero(num, length) {
    return num.toString().padStart(length, '0');
  }

  /**
   * 获取常用格式的快捷方法
   */
  static get formats() {
    return {
      DATE: 'YYYY-MM-DD',
      TIME: 'HH:mm:ss',
      DATETIME: 'YYYY-MM-DD HH:mm:ss',
      DATETIME_SHORT: 'YYYY-MM-DD HH:mm',
      DATE_CN: 'YYYY年MM月DD日',
      TIME_12: 'hh:mm:ss A',
      TIMESTAMP: 'YYYY-MM-DD HH:mm:ss.SSS'
    };
  }
}

export default DateUtil;

// 导出常用的静态方法，方便直接使用
export const format = DateUtil.format;
export const formatDate = DateUtil.formatDate;
export const create = DateUtil.create;
export const formats = DateUtil.formats;
