const _EncodeKeyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

/**
 * 描述:判断是否是base64编码
 * @param str
 * @returns {boolean}
 */
/*export function isBase64(str){
    const exgBase64 = new RegExp('^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$');
    if(!str){
        return false;
    }
    return exgBase64.test(str);
}*/
export function isBase64(str) {
    let  chunkSize = 65536
    // 基础检查
    if (typeof str !== 'string' || !str) return false;

    // 快速长度检查
    const len = str.length;
    if (len % 4 !== 0) return false;

    // 计算填充符数量
    let paddingCount = 0;
    if (len >= 1 && str[len - 1] === '=') paddingCount++;
    if (len >= 2 && str[len - 2] === '=') paddingCount++;

    // 验证填充符数量合法性
    if (paddingCount > 2) return false;

    // 创建无填充的正则表达式
    const base64Regex = /^[A-Za-z0-9+/]+$/;

    // 对超长字符串进行分块验证
    for (let i = 0; i < len; i += chunkSize) {
        const chunk = str.slice(i, i + chunkSize);

        // 最后一块需要特殊处理填充符
        if (i + chunkSize >= len) {
            const cleanChunk = paddingCount > 0
                ? chunk.slice(0, -paddingCount)
                : chunk;

            if (!base64Regex.test(cleanChunk)) {
                return false;
            }
        } else {
            // 中间块必须完全符合无填充Base64字符集
            if (!base64Regex.test(chunk)) {
                return false;
            }
        }
    }
    return true;
}

/**
 * base64编码
 * @param input
 * @returns {string}
 */
export function encodeBase64(input) {
    let output = "";
    let chr1, chr2, chr3, enc1, enc2, enc3, enc4;
    let i = 0;
    input = encodeUtf8(input);
    while (i < input.length) {
        chr1 = input.charCodeAt(i++);
        chr2 = input.charCodeAt(i++);
        chr3 = input.charCodeAt(i++);
        enc1 = chr1 >> 2;
        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
        enc4 = chr3 & 63;
        if (isNaN(chr2)) {
            enc3 = enc4 = 64;
        } else if (isNaN(chr3)) {
            enc4 = 64;
        }
        output = output +
            _EncodeKeyStr.charAt(enc1) + _EncodeKeyStr.charAt(enc2) +
            _EncodeKeyStr.charAt(enc3) + _EncodeKeyStr.charAt(enc4);
    }
    return output;
}

/**
 * 描述:base64 解码
 * @param input
 * @returns {string}
 */
export function decodeBase64(input) {
    // 预处理：移除非法字符和换行符
    const cleanStr = input.replace(/[^\w\d+/=]/g, "").replace(/[\n\r]/g, "");

    // 分块解码（每1MB为一块）
    const chunkSize = 1024 * 1024 * 3/4; // Base64每3字节→4字符
    const buffer = [];

    for (let i = 0; i < cleanStr.length; i += chunkSize) {
        const chunk = cleanStr.slice(i, i + chunkSize);
        // 补足4的倍数（必需对齐）
        const paddedChunk = chunk.padEnd(Math.ceil(chunk.length/4)*4, "=");
        const binaryStr = atob(paddedChunk);

        // 字节数据存储优化
        const uintArray = new Uint8Array(binaryStr.length);
        for (let j = 0; j < binaryStr.length; j++) {
            uintArray[j] = binaryStr.charCodeAt(j);
        }
        buffer.push(uintArray);
    }

    // 合并结果并转为UTF-8
    const merged = new Uint8Array(buffer.reduce((acc, cur) => acc + cur.length, 0));
    let offset = 0;
    buffer.forEach(arr => {
        merged.set(arr, offset);
        offset += arr.length;
    });
    return new TextDecoder("utf-8").decode(merged);




    // let output = "";
    // let chr1, chr2, chr3;
    // let enc1, enc2, enc3, enc4;
    // let i = 0;
    // input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
    // while (i < input.length) {
    //     enc1 = _EncodeKeyStr.indexOf(input.charAt(i++));
    //     enc2 = _EncodeKeyStr.indexOf(input.charAt(i++));
    //     enc3 = _EncodeKeyStr.indexOf(input.charAt(i++));
    //     enc4 = _EncodeKeyStr.indexOf(input.charAt(i++));
    //     chr1 = (enc1 << 2) | (enc2 >> 4);
    //     chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
    //     chr3 = ((enc3 & 3) << 6) | enc4;
    //     output = output + String.fromCharCode(chr1);
    //     if (enc3 != 64) {
    //         output = output + String.fromCharCode(chr2);
    //     }
    //     if (enc4 != 64) {
    //         output = output + String.fromCharCode(chr3);
    //     }
    // }
}

// private method for UTF-8 encoding
function  encodeUtf8(string) {
    string = string.replace(/\r\n/g,"\n");
    var utftext = "";
    for (var n = 0; n < string.length; n++) {
        var c = string.charCodeAt(n);
        if (c < 128) {
            utftext += String.fromCharCode(c);
        } else if((c > 127) && (c < 2048)) {
            utftext += String.fromCharCode((c >> 6) | 192);
            utftext += String.fromCharCode((c & 63) | 128);
        } else {
            utftext += String.fromCharCode((c >> 12) | 224);
            utftext += String.fromCharCode(((c >> 6) & 63) | 128);
            utftext += String.fromCharCode((c & 63) | 128);
        }

    }
    return utftext;
}

// private method for UTF-8 decoding
function  decodeUtf8(utftext) {
    let string = "";
    let i = 0;
    let c=0 ,c1 = 0 ,c2 = 0;
    while ( i < utftext.length ) {
        c = utftext.charCodeAt(i);
        if (c < 128) {
            string += String.fromCharCode(c);
            i++;
        } else if((c > 191) && (c < 224)) {
            c2 = utftext.charCodeAt(i+1);
            string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
            i += 2;
        } else {
            c2 = utftext.charCodeAt(i+1);
            let c3 = utftext.charCodeAt(i+2);
            string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
            i += 3;
        }
    }
    return string;
}