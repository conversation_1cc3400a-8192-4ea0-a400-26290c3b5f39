import { getUrlParams, isEmptyObj } from "./Util";
/**
 * 描述:表达式工具
 * 用于计算表达式的值
 * 主要包括了 + - * / %等算法
 * ${param} 从全局参数中取相关值，或者从绑定返回数据data中取相关参数值
 * #{gloal}从全局提供的一些特殊值取得相关值
 * 比如:  token day month year week 等
 */
export default class Expression {
  constructor(context) {
    this.context = context;
  }
  parseExpr (item) {//用到的参数expr,type,linkId
    const context = this.context;
    let value;
    if (item && item.expr) {
      // if ("param" === item.type) {
      //   const dataItem = context.getParamPropertyMap();
      //   value = this.computeExpr(item.expr, dataItem);
      // } else if ("bind" === item.type && item.bindId) {
      //   const bindModel = context.getBindModelById(item.bindId);
      //   if (bindModel) {
      //     const dataItem = bindModel.getDataItem();
      //     value = this.computeExpr(item.expr, dataItem);
      //   }
      // } else 
      if ("url" === item.type) {
        const dataItem = getUrlParams();
        value = this.computeExpr(item.expr, dataItem);
      } else if ("sys" === item.type) {
        value = this.computeExpr(item.expr);
      } else {
        value = item.expr;
      }
    }
    item.value = value;
    return value;
  }
  /**
   * 描述:计算表达式
   * @param expr
   * @param data
   * @returns {*}
   */
  computeExpr (expr, data) {
    let value;
    const paramKeyList = this.getParamReg(expr);
    if (paramKeyList && paramKeyList.length) {
      for (let i = 0; i < paramKeyList.length; i++) {
        const paramKey = paramKeyList[i];
        if (isEmptyObj(data)) {
          data = this.getSystemParams();
        }
        let paramValue = data[paramKey];
        if (!paramValue) {
          if (paramValue === "0" || paramValue === 0) {
            paramValue = "0";
          } else {
            paramValue = "";
          }
        }
        if (typeof (paramValue) === 'object') {
          expr = expr.replace("${" + paramKey + "}", JSON.stringify(paramValue));
          value = JSON.parse(expr);
        } else {
          value = expr.replace("${" + paramKey + "}", paramValue);
        }
      }
    }else{
      value=expr;
    }
    return value;
  }
  /**
   * 获取定义的全局参数
   */
  getSystemParams () {
    const date = new Date();
    const sysParams = {
      year: date.getFullYear(), //获取完整的年份(4位)
      month: this.appendzero(date.getMonth() + 1), //获取当前月份(0-11,0代表1月)
      years: date.getFullYear() + this.appendzero(date.getMonth() + 1),
      day: date.getDate(), //获取当前日(1-31)
      weak: date.getDay(), //获取当前星期X(0-6,0代表星期天)
      time: date.getTime(), //获取当前时间(从1970.1.1开始的毫秒数)
      hour: date.getHours(), //获取当前小时数(0-23)
      minutes: date.getMinutes(), //获取当前分钟数(0-59)
      second: date.getSeconds(),//获取当前秒数(0-59)
      milli: date.getMilliseconds(),//获取当前毫秒数(0-999)
      locale: date.toLocaleDateString() //获取当前日期
    }
    console.info("========系统默认参数============", sysParams)
    return sysParams;
  }
  /**
   * 补零
   */
  appendzero (obj) {
    if (obj < 10) return "0" + "" + obj;
    else return obj;
  }
  /**
   * 获取表达式中使用到的所有参数
   * @param str
   * @returns {Array}
   */
  getParamReg (str) {
    var reg = /\$\{(.+?)\}/g
    var list = []
    var result = null
    do {
      result = reg.exec(str)
      result && list.push(result[1])
    } while (result)
    return list
  }
  /**
   * 描述:获取表达式中使用的所有全局默认值
   * @param str
   * @returns {Array}
   */
  getSystemReg (str) {
    var reg = /\#\{(.+?)\}/g
    var list = []
    var result = null
    do {
      result = reg.exec(str)
      result && list.push(result[1])
    } while (result)
    return list
  }

}
