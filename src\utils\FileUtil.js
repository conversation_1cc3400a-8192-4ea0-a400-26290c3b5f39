import {formatDate, isArray, isObject, uuid} from "./Util";
import {error} from "../assets/element/jquery/msg/MsgUtil";

export function readerXlsx (file, callback) {
  if (!(file && checkFileType(file, 'xlsx'))) {
    error('文件不存在或文件类型不匹配')
    callback && callback(null)
  }
  const reader = new FileReader();
  reader.onloadend = () => {
    const workbook = new ExcelJS.Workbook();
    workbook.xlsx.load(Buffer.from(reader.result)).then((w) => {
      console.log('w', w)
      const result = {
        name: file.name,
        fileType: 'xlsx',
        datas: [],
      }
      w.eachSheet(function(worksheet, sheetId) {
        if (worksheet.state === 'visible') {
          const sheet = {
            id: uuid(),
            name: worksheet.name,
            columns: [],
            datas: [],
          }
          worksheet.eachRow((row, index) => {
            // 有值&不隐藏
            if (row.hasValues && !row.hidden) {
              // 表头
              if (index === 1) {
                row.eachCell((cell, cellIndex) => {
                  sheet.columns.push(cell.value)
                })
              }
              // 表数据
              if (index > 1) {
                const rowData = {}
                row.eachCell((cell, cellIndex) => {
                  // 日期
                  if (cell.type === 4) {
                    rowData[sheet.columns[cellIndex - 1]] = formatDate(cell.value, 'yyyy-MM-dd HH:mm:ss');
                  } else {
                    rowData[sheet.columns[cellIndex - 1]] = cell.value
                  }
                })
                sheet.datas.push(rowData)
              }
            }
          })
          result.datas.push(sheet)
        }
      });
      callback && callback({
        type: 'file',
        name: file.name,
        file: result,
      })
    })
  }
  reader.readAsArrayBuffer(file);
}

export function readerCsv (file, callback) {
  if (!(file && checkFileType(file, 'csv'))) {
    error('文件不存在或文件类型不匹配')
    callback && callback(null)
  }
  const reader = new FileReader();
  reader.onloadend = (e) => {
    const csvData = e.target.result; // CSV 文件内容字符串
    console.log('csvData', csvData)
    const result = {
      fileType: "xlsx",
      name: file.name,
      datas: [],
    }
    if (csvData) {
      const sheet = {
        id: uuid(),
        name: 'csv sheet',
        columns: [],
        datas: [],
      }
      // 按行分割
      const rows = csvData.split('\n');
      rows.forEach((row, index) => {
        const values = row.trim().split(','); // 按逗号分割列
        if (index === 0) {
          values.forEach((value, index) => {
            sheet.columns.push(value)
          })
        }
        if (index > 0 && values.length === sheet.columns.length) {
          const rowData = {}
          values.forEach((value, index) => {
            rowData[sheet.columns[index]] = value
          })
          sheet.datas.push(rowData)
        }
      });
      result.datas.push(sheet)
    }
    callback && callback({
      name: file.name,
      type: 'file',
      file: result,
    })
    // const csvData = e.target.result; // CSV 文件内容字符串
    // const property = {
    //   name: file.name,
    //   isOpen: false,
    //   type: 'xlsx',
    //   sheets: [],
    // }
    // if (csvData) {
    //   const sheet = {
    //     name: 'csv data',
    //     columns: [],
    //     data: [],
    //   }
    //   // 按行分割
    //   const rows = csvData.split('\n');
    //   rows.forEach((row, index) => {
    //     const values = row.trim().split(','); // 按逗号分割列
    //     if (index === 0) {
    //       values.forEach((value, index) => {
    //         sheet.columns.push(value)
    //       })
    //     }
    //     if (index > 0 && values.length === sheet.columns.length) {
    //       const rowData = {}
    //       console.log('values', values)
    //       values.forEach((value, index) => {
    //         rowData[sheet.columns[index]] = value
    //       })
    //       sheet.data.push(rowData)
    //     }
    //   });
    //   property.sheets.push(sheet)
    // }
    // callback && callback(property)
  }
  reader.readAsText(file, 'UTF-8');
}

export function readerJson (file, callback) {
  if (!(file && checkFileType(file, 'json'))) {
    error('文件不存在或文件类型不匹配')
    callback && callback(null)
  }
  const reader = new FileReader();
  reader.onloadend = (e) => {
    let result;
    if (e.target.result && e.target.result.trim()) {
      try {
        const data = JSON.parse(e.target.result)
        console.log('json data', data)
        if (isObject(data)) {
          /**
           * 处理对象数组
           * {
           *   xxx: [
           *     {
           *       a: 1,
           *       b: 2,
           *       ...
           *     },
           *     ...
           *   ]
           * }
           */
          const keys = Object.keys(data);
          const firstArray = data[keys[0]];
          if (isArray(firstArray)) {
            result = {
              id: uuid(),
              fileType: "json",
              datas: firstArray,
              columns: firstArray[0] && Object.keys(firstArray[0]) || [],
            }
          } else {
            error('JSON文件第一个属性不是数组')
          }
        } else if (isArray(data)) {
          /**
           * 处理数组
           * [
           *    {
           *       a: 1,
           *       b: 2,
           *       ...
           *    },
           *    ...
           * ]
           */
          result = {
            id: uuid(),
            fileType: "json",
            datas: data,
            columns: data[0] && Object.keys(data[0]) || [],
          }
        } else {
          error('请上传正确的JSON文件')
        }
      } catch (e) {
        error('JSON文件解析异常:'+JSON.stringify(e))
        console.error(e)
      }
    }
    callback && callback({
      name: file.name,
      type: 'file',
      file: result,
    })
  }
  reader.readAsText(file, 'UTF-8');
}

function checkFileType (file, type) {
  if (!file) {
    return false
  }
  const fileName = file.name;
  const fileType = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
  return fileType === type.toLowerCase();
}