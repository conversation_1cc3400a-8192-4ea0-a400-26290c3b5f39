# GlobalLoadingManager 全局Loading管理器

一个专为LSD-Designer项目设计的全局loading管理器，支持多个并发请求时只显示一个loading，避免loading重叠和闪烁问题。

## 🎯 核心特性

- ✅ **请求计数机制** - 只有当所有请求完成时才隐藏loading
- ✅ **防抖机制** - 避免快速请求导致的loading闪烁
- ✅ **超时保护** - 防止loading一直显示不消失
- ✅ **消息优先级** - 支持重要请求优先显示消息
- ✅ **最小显示时间** - 避免loading一闪而过的体验问题
- ✅ **状态监控** - 提供详细的状态信息用于调试

## 📦 安装和导入

```javascript
// 导入管理器实例
import globalLoadingManager from './utils/GlobalLoadingManager';

// 或导入便捷方法
import { startLoading, endLoading, resetLoading } from './utils/GlobalLoadingManager';
```

## 🚀 基础用法

### 1. 简单使用

```javascript
// 开始loading
const requestId = startLoading('加载中...');

// 模拟异步操作
setTimeout(() => {
    // 结束loading
    endLoading(requestId);
}, 2000);
```

### 2. 自定义消息和优先级

```javascript
// 高优先级消息
const requestId = startLoading('正在加载重要数据...', 10);

// 低优先级消息
const requestId2 = startLoading('后台数据同步中...', 1);
```

### 3. 多个并发请求

```javascript
// 同时发起多个请求，只会显示一个loading
const req1 = startLoading('加载图表数据...', 1);
const req2 = startLoading('加载用户信息...', 2); // 更高优先级，会显示这个消息
const req3 = startLoading('加载配置...', 0);

// 按不同时间完成
setTimeout(() => endLoading(req1), 1000);
setTimeout(() => endLoading(req3), 1500);
setTimeout(() => endLoading(req2), 2000); // 最后一个完成时loading才消失
```

## 🔧 在Service中集成

Service.js已经集成了全局loading管理器，支持以下所有请求方法：

- `getRequest()` - 普通GET请求
- `postRequest()` - 普通POST请求
- `postEncrypt()` - 加密POST请求
- `getDecrypt()` - 解密GET请求

### 1. 默认使用（自动显示loading）

```javascript
const service = new Service(context);

// 普通GET请求 - 自动显示"加载中..."
service.getRequest('/api/data', {},
    (data) => console.log('成功:', data),
    (error) => console.error('失败:', error)
);

// 普通POST请求 - 自动显示"提交中..."
service.postRequest('/api/save', data,
    (result) => console.log('保存成功:', result)
);

// 加密POST请求 - 自动显示"加密提交中..."
service.postEncrypt('/api/secure-save', sensitiveData,
    (result) => console.log('加密保存成功:', result)
);

// 解密GET请求 - 自动显示"解密加载中..."
service.getDecrypt('/api/secure-data', { userId: 123 },
    (data) => console.log('解密数据:', data)
);
```

### 2. 自定义loading选项

```javascript
// GET请求 - 自定义消息和优先级
service.getRequest('/api/important-data', {},
    (data) => console.log('数据:', data),
    null,
    'json',
    {
        loadingMessage: '正在加载重要数据...',
        loadingPriority: 10
    }
);

// POST请求 - 自定义loading
service.postRequest('/api/save', data,
    (result) => console.log('保存成功:', result),
    null,
    'json',
    {
        loadingMessage: '正在保存您的数据...',
        loadingPriority: 5
    }
);

// 加密POST请求 - 自定义loading
service.postEncrypt('/api/secure-save', sensitiveData,
    (result) => console.log('加密保存成功:', result),
    null,
    'json',
    {
        loadingMessage: '正在安全保存数据...',
        loadingPriority: 8
    }
);

// 解密GET请求 - 自定义loading
service.getDecrypt('/api/secure-data', { userId: 123 },
    (data) => console.log('解密数据:', data),
    null,
    'json',
    {
        loadingMessage: '正在获取安全数据...',
        loadingPriority: 7
    }
);

// 禁用loading的后台请求
service.getRequest('/api/background-data', {},
    (data) => console.log('后台数据:', data),
    null,
    'json',
    {
        showLoading: false
    }
);
```

### 3. 加密方法特殊说明

加密方法 `postEncrypt` 和 `getDecrypt` 具有以下特点：

- **自动加密解密**: 数据传输过程中自动进行加密解密处理
- **安全性更高**: 适用于敏感数据的传输
- **默认消息**:
  - `postEncrypt` 默认显示 "加密提交中..."
  - `getDecrypt` 默认显示 "解密加载中..."
- **使用场景**: 用户登录、敏感配置、重要业务数据等

```javascript
// 用户登录 - 使用加密POST
service.postEncrypt('/api/login', {
    username: 'admin',
    password: 'password123'
},
    (result) => {
        console.log('登录成功:', result);
        // 处理登录成功逻辑
    },
    (error) => {
        console.error('登录失败:', error);
    },
    'json',
    {
        loadingMessage: '正在验证用户信息...',
        loadingPriority: 10
    }
);

// 获取敏感配置 - 使用解密GET
service.getDecrypt('/api/sensitive-config', { moduleId: 'dashboard' },
    (config) => {
        console.log('配置获取成功:', config);
        // 应用配置
    },
    null,
    'json',
    {
        loadingMessage: '正在获取系统配置...',
        loadingPriority: 8
    }
);
```

## 🎨 在图层组件中使用

### 1. 图层数据刷新

```javascript
class MyLayer extends Layer {
    refreshData() {
        const requestId = startLoading(`正在刷新${this.property.name}数据...`, 1);
        
        // 调用数据服务
        this.dataService.getData()
            .then(data => {
                // 处理数据
                this.renderData(data);
            })
            .catch(error => {
                console.error('数据加载失败:', error);
            })
            .finally(() => {
                endLoading(requestId);
            });
    }
}
```

### 2. 属性面板中使用

```javascript
// 加载数据集选项
function loadDataSetOptions() {
    const requestId = startLoading('加载数据集选项...', 2);
    
    return fetch('/api/datasets')
        .then(response => response.json())
        .then(data => {
            endLoading(requestId);
            return data;
        })
        .catch(error => {
            endLoading(requestId);
            throw error;
        });
}
```

## ⚙️ 配置选项

```javascript
// 更新配置
globalLoadingManager.updateConfig({
    showDelay: 100,     // 显示延迟(ms) - 防止闪烁
    hideDelay: 200,     // 隐藏延迟(ms) - 用户体验
    timeout: 30000,     // 超时时间(ms) - 30秒
    minShowTime: 500    // 最小显示时间(ms) - 避免一闪而过
});
```

## 📊 状态监控

```javascript
// 获取当前状态
const status = globalLoadingManager.getStatus();
console.log('状态:', status);
// 输出: { requestCount: 2, isVisible: true, currentMessage: "加载中...", messageQueueLength: 2 }

// 实时监控
const monitor = setInterval(() => {
    const status = globalLoadingManager.getStatus();
    console.log('Loading状态:', status);
    
    if (status.requestCount === 0) {
        clearInterval(monitor);
    }
}, 1000);
```

## 🚨 错误处理

### 1. 强制重置

```javascript
// 当出现异常情况时，强制重置所有状态
resetLoading();
// 或
globalLoadingManager.forceReset();
```

### 2. 超时保护

管理器内置了30秒超时保护，如果loading显示超过30秒会自动隐藏并重置状态。

### 3. 异常请求处理

```javascript
try {
    const requestId = startLoading('处理中...');
    
    // 可能出现异常的操作
    await riskyOperation();
    
    endLoading(requestId);
} catch (error) {
    // 确保在异常情况下也结束loading
    endLoading(requestId);
    console.error('操作失败:', error);
}
```

## 🎪 高级用法

### 1. 消息队列和优先级

```javascript
// 低优先级后台任务
const bgTask = startLoading('后台同步中...', 0);

// 用户操作，高优先级
const userAction = startLoading('正在保存...', 5);

// 紧急任务，最高优先级
const urgentTask = startLoading('紧急处理中！', 10);

// 界面会显示"紧急处理中！"，因为它优先级最高
```

### 2. 链式操作

```javascript
async function complexOperation() {
    // 第一步
    const step1 = startLoading('步骤1: 验证数据...', 1);
    await validateData();
    endLoading(step1);
    
    // 第二步
    const step2 = startLoading('步骤2: 处理数据...', 1);
    await processData();
    endLoading(step2);
    
    // 第三步
    const step3 = startLoading('步骤3: 保存结果...', 1);
    await saveResult();
    endLoading(step3);
}
```

## 🧪 测试

打开 `test-global-loading.html` 文件在浏览器中查看各种场景的测试效果：

- 单个请求测试
- 并发请求测试
- 优先级消息测试
- 超时保护测试
- 状态监控测试

## 🔍 调试技巧

1. **开启控制台日志**: 管理器会输出详细的操作日志
2. **状态监控**: 使用 `getStatus()` 方法查看当前状态
3. **测试页面**: 使用提供的测试页面验证各种场景

## 📝 最佳实践

1. **总是配对使用**: 每个 `startLoading()` 都要有对应的 `endLoading()`
2. **异常处理**: 在 try-catch 中确保 loading 被正确结束
3. **合理设置优先级**: 用户直接操作使用高优先级，后台任务使用低优先级
4. **避免过度使用**: 对于很快完成的操作（<200ms）考虑不显示loading
5. **自定义消息**: 为用户提供有意义的加载提示信息

## 🔗 相关文件

- `src/utils/GlobalLoadingManager.js` - 核心管理器
- `src/utils/GlobalLoadingManager.example.js` - 使用示例
- `src/service/Service.js` - 已集成的服务类
- `test-global-loading.html` - 测试页面

## 📄 许可证

MIT License
