/**
 * GlobalLoadingManager 使用示例
 * 展示如何在LSD-Designer项目中使用全局loading管理器
 */

import globalLoadingManager, {
  startLoading,
  endLoading,
  resetLoading,
} from "./GlobalLoadingManager";
import Service from "../service/Service";

/**
 * 示例1: 基础使用 - 单个请求
 */
export function basicUsageExample() {
  console.log("=== 基础使用示例 ===");

  // 方式1: 直接使用便捷方法
  const requestId = startLoading("加载用户数据...");

  // 模拟异步操作
  setTimeout(() => {
    endLoading(requestId);
    console.log("请求完成");
  }, 2000);

  // 方式2: 使用管理器实例
  const requestId2 = globalLoadingManager.startRequest("加载配置数据...", 1);
  setTimeout(() => {
    globalLoadingManager.endRequest(requestId2);
  }, 1500);
}

/**
 * 示例2: 多个并发请求
 */
export function concurrentRequestsExample() {
  console.log("=== 并发请求示例 ===");

  // 同时发起多个请求
  const req1 = startLoading("加载图表数据...", 1);
  const req2 = startLoading("加载用户信息...", 2); // 更高优先级
  const req3 = startLoading("加载配置...", 0);

  // 模拟不同的完成时间
  setTimeout(() => endLoading(req1), 1000);
  setTimeout(() => endLoading(req3), 1500);
  setTimeout(() => endLoading(req2), 2000); // 最后完成的请求

  console.log("发起了3个并发请求，只会显示一个loading");
}

/**
 * 示例3: 在Service中使用
 */
export function serviceUsageExample(context) {
  console.log("=== Service中使用示例 ===");

  const service = new Service(context);

  // 1. 普通GET请求 - 使用默认loading
  service.getRequest(
    "/api/data",
    {},
    (data) => {
      console.log("数据加载成功:", data);
    },
    (error) => {
      console.error("数据加载失败:", error);
    }
  );

  // 2. 普通POST请求 - 自定义loading消息和优先级
  service.postRequest(
    "/api/save",
    { name: "测试数据" },
    (result) => {
      console.log("保存成功:", result);
    },
    (error) => {
      console.error("保存失败:", error);
    },
    "json",
    {
      loadingMessage: "正在保存数据...",
      loadingPriority: 5,
    }
  );

  // 3. 加密POST请求 - 使用postEncrypt
  service.postEncrypt(
    "/api/secure-save",
    {
      sensitiveData: "敏感信息",
      userId: 12345,
    },
    (result) => {
      console.log("加密保存成功:", result);
    },
    (error) => {
      console.error("加密保存失败:", error);
    },
    "json",
    {
      loadingMessage: "正在安全保存数据...",
      loadingPriority: 8,
    }
  );

  // 4. 解密GET请求 - 使用getDecrypt
  service.getDecrypt(
    "/api/secure-data",
    { userId: 12345 },
    (data) => {
      console.log("解密数据获取成功:", data);
    },
    (error) => {
      console.error("解密数据获取失败:", error);
    },
    "json",
    {
      loadingMessage: "正在获取安全数据...",
      loadingPriority: 7,
    }
  );

  // 5. 禁用loading的后台请求
  service.getRequest(
    "/api/background-data",
    {},
    (data) => {
      console.log("后台数据加载成功:", data);
    },
    null,
    "json",
    {
      showLoading: false,
    }
  );

  // 6. 高优先级紧急请求
  service.getRequest(
    "/api/urgent-data",
    {},
    (data) => {
      console.log("紧急数据加载成功:", data);
    },
    (error) => {
      console.error("紧急数据加载失败:", error);
    },
    "json",
    {
      loadingMessage: "紧急数据处理中，请稍候...",
      loadingPriority: 10,
    }
  );
}

/**
 * 示例4: 在图层数据刷新中使用
 */
export function layerDataRefreshExample() {
  console.log("=== 图层数据刷新示例 ===");

  // 模拟图层数据刷新
  class MockLayer {
    constructor(name) {
      this.name = name;
    }

    refreshData() {
      const requestId = startLoading(`正在刷新${this.name}数据...`, 1);

      // 模拟API请求
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (Math.random() > 0.2) {
            endLoading(requestId);
            resolve(`${this.name}数据刷新成功`);
          } else {
            endLoading(requestId);
            reject(`${this.name}数据刷新失败`);
          }
        }, Math.random() * 2000 + 500);
      });
    }
  }

  // 创建多个图层并同时刷新数据
  const layers = [
    new MockLayer("图表A"),
    new MockLayer("图表B"),
    new MockLayer("地图C"),
  ];

  // 同时刷新所有图层数据
  Promise.allSettled(layers.map((layer) => layer.refreshData())).then(
    (results) => {
      results.forEach((result, index) => {
        if (result.status === "fulfilled") {
          console.log(result.value);
        } else {
          console.error(`图层${index + 1}刷新失败:`, result.reason);
        }
      });
    }
  );
}

/**
 * 示例5: 错误处理和超时保护
 */
export function errorHandlingExample() {
  console.log("=== 错误处理示例 ===");

  // 模拟请求异常，导致loading一直显示
  const requestId = startLoading("模拟异常请求...");

  // 故意不调用endLoading，模拟请求异常
  console.log("请求ID:", requestId, "- 故意不结束，测试超时保护");

  // 5秒后强制重置（正常情况下会有30秒超时保护）
  setTimeout(() => {
    console.log("手动重置loading");
    resetLoading();
  }, 5000);
}

/**
 * 示例6: 配置自定义参数
 */
export function customConfigExample() {
  console.log("=== 自定义配置示例 ===");

  // 更新配置
  globalLoadingManager.updateConfig({
    showDelay: 50, // 减少显示延迟
    hideDelay: 100, // 减少隐藏延迟
    timeout: 10000, // 10秒超时
    minShowTime: 300, // 最小显示300ms
  });

  const requestId = startLoading("快速请求测试...");

  setTimeout(() => {
    endLoading(requestId);
    console.log("快速请求完成");
  }, 100);
}

/**
 * 示例7: 状态监控
 */
export function statusMonitoringExample() {
  console.log("=== 状态监控示例 ===");

  // 启动状态监控
  const monitor = setInterval(() => {
    const status = globalLoadingManager.getStatus();
    console.log("Loading状态:", status);

    if (status.requestCount === 0) {
      clearInterval(monitor);
      console.log("所有请求已完成，停止监控");
    }
  }, 500);

  // 发起一些测试请求
  const req1 = startLoading("监控测试请求1...");
  const req2 = startLoading("监控测试请求2...");

  setTimeout(() => endLoading(req1), 1000);
  setTimeout(() => endLoading(req2), 2000);
}

/**
 * 示例8: 在组件属性面板中使用
 */
export function propertyPanelExample() {
  console.log("=== 属性面板使用示例 ===");

  // 模拟属性面板中的数据加载
  function loadDataSetOptions() {
    const requestId = startLoading("加载数据集选项...", 2);

    return new Promise((resolve) => {
      setTimeout(() => {
        endLoading(requestId);
        resolve([
          { id: 1, name: "数据集1" },
          { id: 2, name: "数据集2" },
          { id: 3, name: "数据集3" },
        ]);
      }, 1000);
    });
  }

  function loadParamOptions() {
    const requestId = startLoading("加载参数选项...", 1);

    return new Promise((resolve) => {
      setTimeout(() => {
        endLoading(requestId);
        resolve([
          { key: "param1", name: "参数1" },
          { key: "param2", name: "参数2" },
        ]);
      }, 800);
    });
  }

  // 同时加载多种选项
  Promise.all([loadDataSetOptions(), loadParamOptions()]).then(
    ([datasets, params]) => {
      console.log("数据集选项:", datasets);
      console.log("参数选项:", params);
    }
  );
}

/**
 * 运行所有示例
 */
export function runAllExamples(context) {
  console.log("开始运行GlobalLoadingManager示例...");

  // 依次运行示例（有延迟避免冲突）
  basicUsageExample();

  setTimeout(() => concurrentRequestsExample(), 3000);

  setTimeout(() => {
    if (context) {
      serviceUsageExample(context);
    }
  }, 6000);

  setTimeout(() => layerDataRefreshExample(), 9000);

  setTimeout(() => errorHandlingExample(), 12000);

  setTimeout(() => customConfigExample(), 18000);

  setTimeout(() => statusMonitoringExample(), 21000);

  setTimeout(() => propertyPanelExample(), 25000);
}

// 如果直接运行此文件，执行所有示例
if (typeof window !== "undefined") {
  // 浏览器环境
  window.runGlobalLoadingExamples = runAllExamples;
  console.log("可以调用 window.runGlobalLoadingExamples() 来运行示例");
}
