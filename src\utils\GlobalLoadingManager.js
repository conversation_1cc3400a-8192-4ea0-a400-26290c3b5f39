/**
 * 全局Loading管理器
 * 支持多个并发请求时只显示一个loading
 *
 * 功能特性：
 * 1. 请求计数器机制 - 只有当所有请求完成时才隐藏loading
 * 2. 防抖机制 - 避免loading闪烁
 * 3. 超时保护 - 防止loading一直显示
 * 4. 自定义消息 - 支持显示不同的加载消息
 * 5. 优先级机制 - 支持重要请求优先显示消息
 */

import Loading from "../assets/element/jquery/load/Loading";

class GlobalLoadingManager {
  constructor() {
    this.requestCount = 0; // 当前活跃请求数量
    this.loadingInstance = null; // loading实例
    this.loadingContainer = null; // loading容器
    this.isVisible = false; // loading是否可见
    this.showTimer = null; // 显示延迟定时器
    this.hideTimer = null; // 隐藏延迟定时器
    this.timeoutTimer = null; // 超时定时器
    this.currentMessage = "加载中..."; // 当前显示的消息
    this.messageQueue = []; // 消息队列

    // 配置参数
    this.config = {
      showDelay: 100, // 显示延迟(ms) - 防止快速请求导致的闪烁
      hideDelay: 200, // 隐藏延迟(ms) - 让用户看到加载完成的反馈
      timeout: 30000, // 超时时间(ms) - 30秒后强制隐藏
      minShowTime: 500, // 最小显示时间(ms) - 避免loading一闪而过
    };

    this.showStartTime = null; // loading开始显示的时间
  }

  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!GlobalLoadingManager.instance) {
      GlobalLoadingManager.instance = new GlobalLoadingManager();
    }
    return GlobalLoadingManager.instance;
  }

  /**
   * 开始请求 - 增加请求计数并显示loading
   * @param {string} message - 可选的加载消息
   * @param {number} priority - 消息优先级 (数字越大优先级越高)
   * @returns {string} 请求ID，用于结束请求时标识
   */
  startRequest(message = "加载中...", priority = 0) {
    const requestId = this.generateRequestId();

    // 增加请求计数
    this.requestCount++;

    // 添加消息到队列
    this.addMessageToQueue(message, priority, requestId);

    // 显示loading
    this.showLoading();

    console.log(
      `[GlobalLoading] 开始请求 ${requestId}, 当前请求数: ${this.requestCount}`
    );

    return requestId;
  }

  /**
   * 结束请求 - 减少请求计数，如果没有活跃请求则隐藏loading
   * @param {string} requestId - 请求ID
   */
  endRequest(requestId) {
    if (this.requestCount > 0) {
      this.requestCount--;

      // 从消息队列中移除对应的消息
      this.removeMessageFromQueue(requestId);

      console.log(
        `[GlobalLoading] 结束请求 ${requestId}, 剩余请求数: ${this.requestCount}`
      );

      // 如果没有活跃请求，隐藏loading
      if (this.requestCount === 0) {
        this.hideLoading();
      } else {
        // 更新显示消息
        this.updateMessage();
      }
    }
  }

  /**
   * 强制重置 - 清除所有请求计数和定时器
   */
  forceReset() {
    console.log("[GlobalLoading] 强制重置");

    this.requestCount = 0;
    this.messageQueue = [];
    this.clearAllTimers();
    this.hideLoadingImmediately();
  }

  /**
   * 显示loading
   */
  showLoading() {
    // 清除隐藏定时器
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = null;
    }

    // 如果已经可见，只更新消息
    if (this.isVisible) {
      this.updateMessage();
      return;
    }

    // 设置显示延迟，避免快速请求导致的闪烁
    if (!this.showTimer) {
      this.showTimer = setTimeout(() => {
        this.showLoadingImmediately();
        this.showTimer = null;
      }, this.config.showDelay);
    }
  }

  /**
   * 立即显示loading
   */
  showLoadingImmediately() {
    if (!this.isVisible) {
      this.loadingInstance = Loading.getInstance();
      this.loadingContainer = this.loadingInstance.addModel();
      this.isVisible = true;
      this.showStartTime = Date.now();

      // 设置超时保护
      this.setTimeoutProtection();

      console.log("[GlobalLoading] Loading显示");
    }

    this.updateMessage();
  }

  /**
   * 隐藏loading
   */
  hideLoading() {
    // 清除显示定时器
    if (this.showTimer) {
      clearTimeout(this.showTimer);
      this.showTimer = null;
    }

    // 如果loading未显示，直接返回
    if (!this.isVisible) {
      return;
    }

    // 计算已显示时间
    const showDuration = Date.now() - (this.showStartTime || 0);
    const remainingMinTime = Math.max(
      0,
      this.config.minShowTime - showDuration
    );

    // 设置隐藏延迟，确保最小显示时间
    const hideDelay = Math.max(this.config.hideDelay, remainingMinTime);

    this.hideTimer = setTimeout(() => {
      this.hideLoadingImmediately();
      this.hideTimer = null;
    }, hideDelay);
  }

  /**
   * 立即隐藏loading
   */
  hideLoadingImmediately() {
    if (this.isVisible && this.loadingInstance) {
      this.loadingInstance.delModel(this.loadingContainer);
      this.isVisible = false;
      this.loadingContainer = null;
      this.showStartTime = null;

      console.log("[GlobalLoading] Loading隐藏");
    }

    this.clearTimeoutProtection();
  }

  /**
   * 添加消息到队列
   */
  addMessageToQueue(message, priority, requestId) {
    this.messageQueue.push({
      message,
      priority,
      requestId,
      timestamp: Date.now(),
    });

    // 按优先级排序
    this.messageQueue.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 从队列中移除消息
   */
  removeMessageFromQueue(requestId) {
    this.messageQueue = this.messageQueue.filter(
      (item) => item.requestId !== requestId
    );
  }

  /**
   * 更新显示消息
   */
  updateMessage() {
    if (this.messageQueue.length > 0) {
      const highestPriorityMessage = this.messageQueue[0].message;
      if (this.currentMessage !== highestPriorityMessage) {
        this.currentMessage = highestPriorityMessage;

        if (this.isVisible && this.loadingInstance) {
          this.loadingInstance.addText(
            this.loadingContainer,
            this.currentMessage
          );
        }
      }
    }
  }

  /**
   * 设置超时保护
   */
  setTimeoutProtection() {
    this.clearTimeoutProtection();

    this.timeoutTimer = setTimeout(() => {
      console.warn("[GlobalLoading] Loading超时，强制隐藏");
      this.forceReset();
    }, this.config.timeout);
  }

  /**
   * 清除超时保护
   */
  clearTimeoutProtection() {
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer);
      this.timeoutTimer = null;
    }
  }

  /**
   * 清除所有定时器
   */
  clearAllTimers() {
    if (this.showTimer) {
      clearTimeout(this.showTimer);
      this.showTimer = null;
    }

    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = null;
    }

    this.clearTimeoutProtection();
  }

  /**
   * 生成请求ID
   */
  generateRequestId() {
    return "req_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取当前状态信息
   */
  getStatus() {
    return {
      requestCount: this.requestCount,
      isVisible: this.isVisible,
      currentMessage: this.currentMessage,
      messageQueueLength: this.messageQueue.length,
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    this.config = Object.assign(this.config, newConfig);
  }
}

// 导出单例实例
const globalLoadingManager = GlobalLoadingManager.getInstance();

export default globalLoadingManager;

// 导出便捷方法
export const startLoading = (message, priority) =>
  globalLoadingManager.startRequest(message, priority);
export const endLoading = (requestId) =>
  globalLoadingManager.endRequest(requestId);
export const resetLoading = () => globalLoadingManager.forceReset();
export const getLoadingStatus = () => globalLoadingManager.getStatus();
