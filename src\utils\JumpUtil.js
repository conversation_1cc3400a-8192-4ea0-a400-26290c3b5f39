import { getToken } from "./StorageUtil";
import Context from "../Context";
import Service from "../service/Service";
/**
 * 描述:跳转处理
 * @param jumpItem
 *     target:
 *           CURRENT 当前页面跳转
 *           SERVICE 点击服务处理
 *           WINDOW  弹出新窗口
 *     params:
 *
 * @param layerData
 */
export function jumpTo (jumpItem, layerData) {
  if (jumpItem && jumpItem["isOpen"] && jumpItem["url"]) {
    const context = Context.getInstance();
    const paramMap = computeParamMap(jumpItem["params"], layerData);
    const paramGetUrl = computeGetParamUrl(paramMap, jumpItem["isToken"]);
    const getUrl = jumpItem["url"] + "?" + paramGetUrl;

    if (!context.getIsDesign()) {
      const target = jumpItem["target"];
      const download = jumpItem["download"];
      const isToken = jumpItem["isToken"];
      if (target) {
        if (target === "CURRENT") { //本页跳转
          window.location.href = getUrl;

        } else if (target === "WINDOW") {//新窗口
          if(getUrl.includes("https") || getUrl.includes("http")){
            window.open(getUrl,"_blank");
          }else{
            window.open('//'+getUrl);
          }

        } else if (target === "DOWNLOAD") { //下载
          if (download) {
            download["isToken"] = isToken;
            const baseService = new Service(context);
            baseService.downLoad(getUrl, download);
          }
        } else if (target === "DIALOG") {//弹框
          console.warn("处理dialog弹窗方式!");
        }
      }
    }
  }
}

/**
 * 描述:获取get的参数类型
 * @param isToken
 * @param paramMap
 * @returns {string}
 */
function computeGetParamUrl (paramMap, isToken) {
  let paramBuffer;
  if (paramMap) {
    let isFirstParam = true;
    for (let key in paramMap) {
      if (isFirstParam) {
        paramBuffer = key + "=" + paramMap[key];
        isFirstParam = false;
      } else {
        paramBuffer += "&" + key + "=" + paramMap[key];
      }
    }
  }
  if (isToken) {
    const token = getToken();
    if (paramBuffer) {
      paramBuffer += "&" + token.key + "=" + token.value;
    } else {
      paramBuffer = token.key + "=" + token.value
    }
  }
  // if (isToken) {
  //   const tokenModel = getToken();
  //   if (paramBuffer) {
  //     paramBuffer += "&token=" + tokenModel["token"];
  //   } else {
  //     paramBuffer = "token=" + tokenModel["token"];
  //   }
  // }
  return paramBuffer;
}
/**
 * 描述:计算参数获得参数map对象
 * @param jumpParams
 * @param layerData
 * @returns {{}|boolean}
 */
function computeParamMap (jumpParams, layerData) {
  let paramMap;
  if (jumpParams && jumpParams.length) {
    const context = Context.getInstance();
    for (let i = 0; i < jumpParams.length; i++) {
      const paramItem = jumpParams[i];
      if (!paramItem || !paramItem["type"]) {
        console.warn("跳转参数必须配置类型!", jumpParams);
        return false;
      }
      if (!paramItem || !paramItem["code"]) {
        console.warn("跳转参数必须配置编码!", jumpParams);
        return false;
      }
      if (!paramItem || (!paramItem["key"] && !paramItem["expr"])) {
        console.warn("跳转参数必须配置属性!", jumpParams);
        return false;
      }
      let itemValue;
      if (paramItem["type"] === "LAYER") {
        if (layerData && layerData[paramItem["key"]]) {
          itemValue = layerData[paramItem["key"]];
        }
      }
      if (paramItem["type"] === "PARAM") {
        itemValue = context.getParamValue(paramItem["key"]);
      }
      if (paramItem["type"] === "EXPR") {
        itemValue = paramItem["expr"];
      }
      if (!paramMap) {
        paramMap = {};
      }
      paramMap[paramItem["code"]] = itemValue;
    }
  }
  return paramMap;
}
