/**
 * 触发Iframe
 * @param {*} iframeContainer 
 * @param {*} callback 
 */
export function triggerIframe (iframeContainer, callback) {
  if (iframeContainer) {
    const document = iframeContainer[0].contentDocument;
    if (document && document.readyState === 'complete') {//完成
      // console.info("iframe已经加载完毕!");
      if (callback) {
        callback();
      }
    } else {
      // console.info("iframe正在加载中!");
      $(iframeContainer).on('load', function () {
        if (callback) {
          callback();
        }
      });
    }
  }
}
/**
 * 消息：发送者
 * @param {*} iframeContainer 
 * @param {*} data {type:"tempLSD",data:{}}
 */
export function senderMessage (iframeContainer, params, path) {
  if (iframeContainer && params) {
    const url = path || window.location.origin;
    const iframeWindow = iframeContainer[0].contentWindow;
    if (iframeWindow) {
      iframeWindow.postMessage(params, url);
    }
  }
}
/**
 * 消息：接收者
 * @param {*} callback 
 */
export function receiverMessage (callback) {
  window.addEventListener('message', function (event) {
    // console.log("从" + event.origin + "收到消息： ", event.data);
    if (event && event.data) {
      const data = event.data;
      let params;
      if (data["map3D"]) {
        const json = data["map3D"]["item"];
        if (json) {
          params = { type: "map3D", data: json };
        }
      }
      if (data["subLSD"]) {
        const json = data["subLSD"]["paramMap"];
        if (json) {
          params = { type: "subLSD", data: json };
        }
      }
      if (data["tempLSD"]) {
        const json = data["tempLSD"]["data"];
        if (json) {
          params = { type: "tempLSD", data: json };
        }
      }
      if (params) {
        if (callback) {
          callback(params);
        }
      }
    }
  });
}

/**
 * 注册消息中心监听
 */
export const registryMessageCenter = (() => {
  let currentListener = null; // 保存当前的监听器

  return function (context) {
    // 是否存在有效的消息
    if (context) {
      // 移除之前的监听器
      if (currentListener) {
        window.removeEventListener('message', currentListener);
      }

      // 创建新的监听器并绑定
      currentListener = function (event) {
        messageListener(context, event);
      };

      // 添加新的监听器
      window.addEventListener('message', currentListener);
      console.log('registry');
    }
  };
})()

function messageListener (context, event) {
  if (event && event.data) {
    try {
      const data = JSON.parse(event.data);
      console.log('data', data)
      if (data.hasOwnProperty('type')) {
        const messageCenters = context.getMessageCenters();
        console.log('messageCenters', messageCenters)
        if (messageCenters && messageCenters.length && messageCenters.some(item => item.messageName)) {
          for (let i = 0; i < messageCenters.length; i++) {
            const msg = messageCenters[i];
            if (msg.messageName === data.type) {
              // 检查条件字段是否存在
              if (data.data && msg.condition) {
                // 解析条件字符串,例如 exp(a==1 && b==2)。获取表达式 a==1 && b==2
                const condition = parseCondition(msg.condition);
                // 动态执行条件判断
                const conditionMet = evaluateCondition(condition, data.data);
                if (!conditionMet) {
                  console.log(`Condition not met: ${msg.condition}`);
                  continue; // 跳过当前消息处理
                }
              }
              // 处理消息
              if (msg.links && msg.links.length) {
                for (let j = 0; j < msg.links.length; j++) {
                  const link = msg.links[j];
                  if (link.param) {
                    context.setParamValue(link.param, link.constValue)
                    console.log('set param success')

                    // 获取与当前参数有关联得图层
                    const layerMap = context.getLayerMap();
                    if (layerMap) {
                      for (let key in layerMap) {
                        const layer = layerMap[key];
                        if (layer && layer.isHaveParam && layer.isHaveParam(link.param)) {
                          layer.refreshData();
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    } catch (e) {
      console.error(e)
    }
  }
}

/**
 * 解析条件字符串，提取表达式内容
 * @param {string} conditionStr 条件字符串，例如 "exp(a==1 && b==2)"
 * @returns {string} 提取后的表达式，例如 "a==1 && b==2"
 */
function parseCondition(conditionStr) {
  const match = conditionStr.match(/^exp\((.*)\)$/);
  return match ? match[1] : null;
}

/**
 * 动态执行条件表达式
 * @param {string} condition 条件表达式，例如 "a==1 && b==2"
 * @param {object} body 数据对象，例如 { a: 1, b: 2 }
 * @returns {boolean} 条件是否满足
 */
function evaluateCondition(condition, body) {
  try {
    // 构造一个新的函数环境，手动绑定变量
    const keys = Object.keys(body); // 提取 body 的键
    const values = Object.values(body); // 提取 body 的值

    // 将每个键绑定为局部变量
    let contextScript = '';
    for (let i = 0; i < keys.length; i++) {
      contextScript += `var ${keys[i]} = ${JSON.stringify(values[i])};\n`;
    }

    // 在新环境中执行条件表达式
    const script = `${contextScript}return (${condition});`;
    const fn = new Function(script);
    return fn();
  } catch (e) {
    console.error('Failed to evaluate condition:', condition, e);
    return false;
  }
}