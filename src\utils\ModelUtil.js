import { jsonPath } from "./JsonPathUtil";

export function getPathes(expr) {
  var subX = [];
  const pathesStr = expr.replace(/[\["](\??\(.*?\))[\]"]/g, function($0, $1) {
      return "[#" + (subX.push($1) - 1) + "]";
  }).replace(/"?\."?|\["?/g, ";").replace(/;;;|;;/g, ";..;").replace(/;$|"?]|"$/g, "").replace(/#([0-9]+)/g, function($0, $1) {
      return subX[$1];
  });
  if (pathesStr.indexOf(";") !== -1) {
      return pathesStr.split(";");
  } else {
      return [pathesStr];
  }
}

/**
 * 获取元素的值
 * @param target
 * @returns {jQuery}
 */
export function getElementValue(ui) {
    const nodeName = $(ui).prop("nodeName");
    //1.如果是单选框 或者
    if (nodeName.toUpperCase() === "INPUT" && ($(ui).prop("type") === "checkbox")) {
        return $(ui).prop("checked");
    }
    if (nodeName.toUpperCase() === "INPUT" && ($(ui).prop("type") === "number")) {
        let value = $(ui).val();
        if(value){
            if(value.indexOf(".") === -1){
                value = parseInt(value)
            }else{
                value = parseFloat(value)
            }
        }
        return value;
    }else{
        return $(ui).val();
    }
}
/**
 * 初始化相关值
 * @param constainer
 * @param property
 */
export function refreshModel(constainer,property, callback,attrModel) {
    if(!attrModel){
        attrModel="v-model";
    }
    $(constainer).find("["+attrModel+"]").each(function(i, ui) {
        const modelName = $(ui).attr(attrModel);
        let modelInitValue;
        const jsonPathExpr = "$." + modelName;
        const pathArr = jsonPath(property, jsonPathExpr);
        if (pathArr && pathArr.length) {
            modelInitValue = pathArr[0];
            const nodeName = $(ui).prop("nodeName");
            if (nodeName.toUpperCase() === "INPUT") {
                const type = $(ui).prop("type");
                if (type && type === "checkbox") {
                    $(ui).prop("checked", modelInitValue);
                } else if (type === "radio") {
                    if($(ui).val() && modelInitValue && $(ui).val()===modelInitValue){
                        $(ui).prop("checked", true);
                    }
                } else {
                    $(ui).val(modelInitValue);
                }
            } else if (nodeName.toUpperCase() === "SELECT") {
                $(ui).val(modelInitValue);
            }else if (nodeName.toUpperCase() === "IMG") {
                $(ui).attr("src", modelInitValue);
            }else {
                $(ui).text(modelInitValue);
            }
        }
        if (callback && $.isFunction(callback)) {
            callback.call(ui, modelName, modelInitValue);
        }
    });
}
/**
 * 描述:绑定事件，用于绑定容器内相关Element对象（change click 等操作）与属性
 * @param event
 * @param constainer
 * @param property
 * @param callback
 */
export function bindModel(constainer, property, callback,attrModel) {
    if(!attrModel){
        attrModel="v-model";
    }
    $(constainer).find("["+attrModel+"]").each(function(index, ui) {
        let event = "keyup change";
        const nodeName = $(ui).prop("nodeName");
        if (nodeName.toUpperCase() === "INPUT" && ($(ui).prop("type") === "checkbox" || $(ui).prop("type") === "radio")) {
            event = "change";
        }
        if (nodeName.toUpperCase() === "SELECT") {
            event = "change";
        }
        $(ui).on(event, function() {
            const modelName = $(ui).attr(attrModel);
            const modelValue = getElementValue(ui);
            const pathes = getPathes(modelName);
            let oldValue;
            if (pathes instanceof Array) { //数组说明是多重路径的数据设置
                let subProperty = property;
                //根据属性路径数组，找到需要赋值的属性
                for (let i = 0; i < pathes.length - 1; i++) {
                    if (!subProperty[pathes[i]]) {
                        subProperty[pathes[i]] = {};
                    }
                    subProperty = subProperty[pathes[i]];
                }
                if (subProperty) {
                    oldValue=subProperty[pathes[pathes.length - 1]];
                    if (typeof (modelValue) === "undefined" || modelValue === null || modelValue ==="" || modelValue ==="undefined") {
                        delete subProperty[pathes[pathes.length - 1]];
                    }else{
                        subProperty[pathes[pathes.length - 1]] = modelValue;
                    }
                    /*if(modelValue){
                        subProperty[pathes[pathes.length - 1]] = modelValue;
                    }else{
                        delete subProperty[pathes[pathes.length - 1]];
                    }*/
                }
            } else { //直接设置相关信息
                oldValue=property[modelName];
                if (typeof (modelValue) === "undefined" || modelValue === null || modelValue ==="" || modelValue ==="undefined") {
                    delete property[modelName];
                }else{
                    property[modelName] = modelValue;
                }
            }
            if (callback && $.isFunction(callback)) {
                callback.call(event, modelName, modelValue,oldValue);
            }
        });
    });
}


