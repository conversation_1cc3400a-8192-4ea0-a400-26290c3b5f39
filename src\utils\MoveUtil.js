/**
 * 1.使用数组splice方法交换位置实现上移、下移功能
 * 2.使用数组splice、unshift、push方法实现置顶、置底功能
 * splice(index, num,item1,...,itemN):
 * index：添加/删除元素的位置
 * num：要删除元素的数量
 * item1,...,itemN：向数组添加新的元素
 */

export function moveUp (arr, index) {
  if (arr && index - 1 >= 0) {
    swap(arr, index, (index - 1))
  } else {
    console.warn('素材已经置顶，无法上移！')
  }
}
export function moveDown (arr, index) {
  if (index + 1 < arr.length) {
    swap(arr, index, index + 1)
  } else {
    console.warn('素材已经置底，无法下移！');
  }
}
export function moveTop (arr, index) {
  if (index - 1 >= 0) {
    // 将要置顶的元素存储后删除
    const temp = arr.splice(index, 1)[0]
    // 将元素unshift到数组第一位
    arr.unshift(temp)
  } else {
    console.warn('素材已经置顶！');
  }
}
export function moveBottom (arr, index) {
  if (index + 1 < arr.length) {
    // 将要置底的元素存储后删除
    const temp = arr.splice(index, 1)[0]
    // 将元素push到数组最后一位
    arr.push(temp)
  } else {
    console.warn('素材已经置底！');
  }
}
function swap (arr, indexAdd, indexDel) {
  arr[indexAdd] = arr.splice(indexDel, 1, arr[indexAdd])[0]
  return arr
}