import { isArray, isEmptyObject, isObj, isObject, jsonClone } from "./Util";

// 数组删除操作
const ArrayDelMethods = ["pop", "shift", "splice"];
// 数组新增操作
const ArrayAddMethods = ["push", "unshift"];
// 需要跳过图层内部属性 (内部chart宽高和外部的冲突了，不管改了哪个都会重复触发set)
const SkipChartProps = ["width", "height"];
// 需要跳过的属性  (每次拖入新图层都会刷新所有图层的zIndex)
const SkipProps = ["zIndex"];
// 需要防抖的属性
const DebounceProps = ["top", "left", "width", "height"];

/**
 * 创建一个代理对象
 * @param source 对象源
 * @param callback
 * @param root
 * @returns {*|object|boolean} 结果
 */
export function createProxy(source, callback, root = source) {
  if (source === null) {
    return source;
  }
  if (!isObj(source)) {
    return source;
  }

  let timer;
  let oldValue;

  // 递归代理嵌套对象
  for (const key in source) {
    // 对象和数组都通过
    if (isObj(source)) {
      source[key] = createProxy(source[key], callback, root);
    }
  }
  // 创建代理对象
  return new Proxy(source, {
    get(target, prop, receiver) {
      // 处理数组
      if (isArray(target)) {
        // 数组删除动作
        if (ArrayDelMethods.includes(prop)) {
          oldValue = jsonClone(root);
          return function () {
            const applyResult = Reflect.apply(
              Array.prototype[prop],
              target,
              arguments
            );
            callback && callback(oldValue, jsonClone(root));
            return applyResult;
          };
        }
        // 数组增加动作
        if (ArrayAddMethods.includes(prop)) {
          oldValue = jsonClone(root);
          return function () {
            if (isObject(arguments[0])) {
              arguments[0] = createProxy(arguments[0], callback, root);
              callback && callback(oldValue, jsonClone(root));
              return Reflect.apply(Array.prototype[prop], target, arguments);
            }
            // 往数组中添加数组，理论上这个项目不会进入这里
            // if (isArray(arguments[0])) {
            //   arguments[0] = createProxy(arguments[0], callback, root);
            //   return Reflect.apply(Array.prototype[prop], target, arguments);
            // }
            const applyResult = Reflect.apply(
              Array.prototype[prop],
              target,
              arguments
            );
            callback && callback(oldValue, jsonClone(root));
            return applyResult;
          };
        }
      }
      return Reflect.get(target, prop, receiver);
    },
    set: (target, prop, newValue, receiver) => {
      // 单独处理文本图层
      if (root.type === "TextLayer" && !target.hasOwnProperty("id")) {
        if (prop === "height" || prop === "width" || prop === "fontSize") {
          return Reflect.set(target, prop, newValue, receiver);
        }
      }
      // 单独处理文本图层
      if (root.type === "DateLayer" && !target.hasOwnProperty("id")) {
        if (prop === "height" || prop === "width" || prop === "fontSize") {
          return Reflect.set(target, prop, newValue, receiver);
        }
      }
      // 单独处理数字翻牌
      if (root.type === "NumLayer" && !target.hasOwnProperty("id")) {
        if (prop === "height" || prop === "width" || prop === "fontSize") {
          return Reflect.set(target, prop, newValue, receiver);
        }
      }

      // 需要跳过的属性
      if (SkipProps.includes(prop)) {
        return Reflect.set(target, prop, newValue, receiver);
      }
      // 图层的chart也有宽高属性，外层已经设置并触发代理，这里直接跳过
      if (SkipChartProps.includes(prop) && !target.hasOwnProperty("id")) {
        return Reflect.set(target, prop, newValue, receiver);
      }
      // 防抖属性，xy宽高值可以通过拖动来赋值，触发太频繁，最开始的旧值会被覆盖
      if (DebounceProps.includes(prop)) {
        // 只赋值一次旧值
        if (!oldValue) {
          oldValue = jsonClone(root);
        }
      } else {
        oldValue = jsonClone(root);
      }

      // 处理新值数组
      if (isArray(newValue) && target[prop] === undefined) {
        newValue = createProxy(newValue, callback, root);
        callback && callback(oldValue, jsonClone(root));
        return Reflect.set(target, prop, newValue, receiver);
      }
      // 处理新值对象
      if (isObject(newValue) && target[prop] === undefined) {
        newValue = createProxy(newValue, callback, root);
        return Reflect.set(target, prop, newValue, receiver);
      }

      // 旧值和新值都是空对象，不处理
      if (isEmptyObject(target[prop]) && isEmptyObject(newValue)) {
        return Reflect.set(target, prop, newValue, receiver);
      }

      const isDiff = target[prop] !== newValue;
      // 尝试更新属性值
      const success = Reflect.set(target, prop, newValue, receiver);

      if (success && isDiff) {
        // 修改了最外层的xy轴，需要防抖，防止触发太多次旧值被覆盖
        if (DebounceProps.includes(prop) && target.hasOwnProperty("id")) {
          // 简易防抖
          clearTimeout(timer);
          timer = setTimeout(() => {
            // 返回旧值和新值
            callback && callback(oldValue, jsonClone(root));
            // 清空旧值
            oldValue = null;
          }, 200); // 移动图层如果暂定了200毫秒，则会记录一次撤销操作
        } else {
          callback && callback(oldValue, jsonClone(root));
          oldValue = null;
        }
      }
      return success;
    },

    deleteProperty: (target, prop) => {
      const oldValue = jsonClone(root); // 获取根对象的快照
      const success = Reflect.deleteProperty(target, prop);
      if (success) {
        // 执行回调，传入旧值和更新后的对象
        callback && callback(oldValue, jsonClone(root));
      }
      return success;
    },
  });
}
