const LOCAL_STORAGE_TOKEN_MODEL = "$$tokenModel";
const LOCAL_STORAGE_SHARE_MODEL = "shareToken";
const LOCAL_STORAGE_LOCAL_COPY = "$$localCopy";
const LOCAL_STORAGE_LOCAL_CACHE = "$$localCache";
const LOCAL_STORAGE_LOCAL_YSTOKEN = "$$localYsToken";

/**
 * 获取
 * @returns 
 */
export function getToken () {
  const tokenModel = getTokenModel();
  const shareModel = getShareModel();
  if (tokenModel && tokenModel["token"]) {
    return { key: "token", value: tokenModel["token"] };
  } else if (shareModel) {
    return { key: "shareToken", value: shareModel };
  } else {
    console.error("token获取失败![token令牌/share令牌]");
  }
}
/**
 * token
 */
export function setToken (token) {
  const tokenModel = { "token": token }
  setTokenModel(tokenModel);
}

/**
 * 获取
 * @returns
 */
export function getYsToken () {
  const token = getSession(LOCAL_STORAGE_LOCAL_YSTOKEN);
  if (token) {
    return token;
  }
  return "";
}
/**
 * token
 */
export function setYsToken (token) {
  setSession(LOCAL_STORAGE_LOCAL_YSTOKEN, token)
}
/**
 * 令牌token
 * @param {*} tokenModel 
 */
export function setTokenModel (tokenModel) {
  // setStorage(LOCAL_STORAGE_TOKEN_MODEL, tokenModel);
  setSession(LOCAL_STORAGE_TOKEN_MODEL, tokenModel);
}
export function getTokenModel () {
  // return getStorage(LOCAL_STORAGE_TOKEN_MODEL);
  return getSession(LOCAL_STORAGE_TOKEN_MODEL);
}
/**
 * 分享token
 * @returns 
 */
export function setShareModel (shareToken) {
  // setStorage(LOCAL_STORAGE_SHARE_MODEL, shareToken);
  setSession(LOCAL_STORAGE_SHARE_MODEL, shareToken);
}
export function getShareModel () {
  // return getStorage(LOCAL_STORAGE_SHARE_MODEL);
  return getSession(LOCAL_STORAGE_SHARE_MODEL);
}
/**
 * 本地拷贝
 */
export function setLocalCopy (jsons) {
  setStorage(LOCAL_STORAGE_LOCAL_COPY, jsons);
}
export function getLocalCopy () {
  return getStorage(LOCAL_STORAGE_LOCAL_COPY);
}
/**
 * 本地缓存
 * @param {*} jsons 
 */
export function setLocalCache (jsons) {
  setStorage(LOCAL_STORAGE_LOCAL_CACHE, jsons);
}
export function getLocalCache () {
  return getStorage(LOCAL_STORAGE_LOCAL_CACHE);
}
/**
 * 本地缓存
 * @param {*} key 
 * @returns 
 */
function getStorage (key) {
  if (key) {
    const value = localStorage.getItem(key);
    if (typeof value === 'string') {
      try {
        return JSON.parse(value) || {};
      } catch (error) {
        return value;
      }
    }
  }
}
function setStorage (key, json) {
  if (key && json) {
    const value = JSON.stringify(json);
    localStorage.setItem(key, value);
  }
}

/**
 * session缓存
 * @param {*} key 
 * @returns 
 */
function getSession (key) {
  if (key) {
    const value = sessionStorage.getItem(key);
    if (typeof value === 'string') {
      try {
        return JSON.parse(value) || {};
      } catch (error) {
        return value;
      }
    }
  }
}
function setSession (key, json) {
  if (key && json) {
    const value = JSON.stringify(json);
    sessionStorage.setItem(key, value);
  }
}