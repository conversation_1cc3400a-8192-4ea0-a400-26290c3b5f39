import { isEmpty } from "./Util";
/**
 * 描述：获取所有浏览器前缀
 * ['', '-webkit-', '-moz-', '-o-', '-ms-'];
 * @returns {string}
 */
export function getPrefixes (name) {
  let list;
  let array = ['', '-webkit-', '-moz-', '-o-', '-ms-'];
  // const array=getWebPrefixes();
  if (name) {
    for (let i = 0; i < array.length; i++) {
      const prefix = array[i].toLowerCase();
      const label = prefix + name;
      if (!list) {
        list = [];
      }
      list.push(label);
    }
  } else {
    list = array;
  }
  return list;
}
/**
 * 修改css样式信息
 * @param constainer
 * @param property
 * @param callback
 */
export function refreshCss (container, property, callback) {
  if (container) {
    for (const cssName in property) {
      $(container).css(cssName, property[cssName]);
    }
    const styleText = $(container).attr("style");
    if (styleText) {
      const resetStyles = [];
      // 重新设置样式，消除unset属性带来的副作用
      const styleProps = styleText.split(";");
      for (let i = 0; i < styleProps.length; i++) {
        const styleProp = styleProps[i];
        if (styleProp && styleProp.indexOf('unset') === -1) {
          resetStyles.push(styleProp);
        }
      }
      if (resetStyles.length) {
        $(container).attr("style", resetStyles.join(";"));
      }
    }
    if (callback && $.isFunction(callback)) {
      callback.call(event, container, property);
    }
  }
}
export function refreshScale (context, container) {
  if (context && container) {
    //比例
    const scale = context.getScale();
    //宽高
    const width = context.getWidth();
    const height = context.getHeight();
    if (container) {
      $(container).css("width", width);
      $(container).css("height", height);
      $(container).css("transform", "scale(" + scale + ")");
    }
  }
}
export function refreshBackground (context, container) {
  if (context && container) {
    const background = context.getBackground();
    const style = toBgStyle(context, background);
    refreshCss(container, style);
    // //1.加载
    // if (background && background["bgType"]) {
    //   //1.1图片
    //   if (background["bgType"] === "image") {
    //     const material = context.getMaterialById(background["materialId"]);
    //     if (material && material["property"]) {
    //       $(container).css("background-image", "url(" + material["property"]["fileUrl"] + ")");
    //       $(container).css("background-repeat", "no-repeat");
    //       $(container).css("background-size", "100% 100%");
    //     }
    //   }
    //   //1.2颜色
    //   if (background["bgType"] === "color") {
    //     $(container).css("background-color", background["color"]);
    //   }
    // }
    // //2.透明度
    // if (background && background["opacity"]) {
    //   $(container).css("opacity", (background["opacity"] / 100));
    // }
    // //3.模糊度
    // if (background && background["blur"]) {
    //   const list = getPrefixes("filter");
    //   if (list && list.length) {
    //     for (let i = 0; i < list.length; i++) {
    //       const item = list[i];
    //       $(container).css(item, "blur(" + background["blur"] + "px)");
    //     }
    //   }
    //   // $(container).css("-webkit-filter", "blur(" + background["blur"] + "px)");
    //   // $(container).css("-moz-filter", "blur(" + background["blur"] + "px)");
    //   // $(container).css("-o-filter", "blur(" + background["blur"] + "px)");
    //   // $(container).css("-ms-filter", "blur(" + background["blur"] + "px)");
    //   // $(container).css("filter", "blur(" + background["blur"] + "px)");
    // }
  }
}


export function toStyle (context, json, filters) {
  const style = {};
  let tempJson;
  if (json) {
    const webPrefixes = getPrefixes();
    //处理透明色
    if (!isEmpty(json.opacity) && webPrefixes) {
      for (let i = 0; i < webPrefixes.length; i++) {
        style[webPrefixes[i] + "opacity"] = json.opacity;
      }
    }
    //过滤
    const copyJson = JSON.parse(JSON.stringify(json));
    if (filters && filters.length) {
      for (let i = 0; i < filters.length; i++) {
        const filter = filters[i];
        tempJson[filter] = copyJson[filter];
      }
    } else {
      tempJson = copyJson;
    }
    //滚动条
    if (tempJson.isOverFlow) {
      style["overflow"] = "auto";
    } else {
      style["overflow"] = "";
    }
    //转换
    if (tempJson.display) {
      style["display"] = tempJson.display;
    }
    if (tempJson.isOverFlow) {
      style["overflow"] = "hidden";
    } else {
      style["overflow"] = "visible";
    }
    if (!isEmpty(tempJson.padding)) {
      style["padding"] = tempJson.padding;
    } else {
      style["padding"] = "unset";
    }
    if (!isEmpty(tempJson.margin)) {
      style["margin"] = tempJson.margin;
    } else {
      style["margin"] = "unset";
    }
    if (!isEmpty(tempJson.width)) {
      style["width"] = tempJson.width;
    }
    if (!isEmpty(tempJson.height)) {
      style["height"] = tempJson.height;
    }
    if (!isEmpty(tempJson.lineHeight)) {
      style["line-height"] = tempJson.lineHeight;
    }
    if (tempJson.fontSize) {
      style["fontSize"] = tempJson.fontSize;
    }
    const fontStyle = toFontStyle(tempJson.font || {});
    $.extend(style, fontStyle);
    if (tempJson.border) {
      const borderStyle = toBorderStyle(context, tempJson.border);
      $.extend(style, borderStyle);
    }
    if (tempJson.bg) {
      const bgStyle = toBgStyle(context, tempJson.bg);
      $.extend(style, bgStyle);
    }
    const animateStyle = toAnimateStyle(tempJson.animate || {});
    $.extend(style, animateStyle);
    // if (tempJson.group) {
    //   const groupStyle = toGroupStyle(tempJson.group);
    //   $.extend(style, groupStyle);
    // }
  }
  return style;
}

/**
 * 字体样式
 * @param {*} font 
 * @returns 
 */
export function toFontStyle (font) {
  const style = {};
  if (font) {
    if (font.fontFamily)
      style["font-family"] = font.fontFamily;
    if (font.fontStyle)
      style["font-style"] = font.fontStyle;
    if (font.fontSize)
      style["font-size"] = font.fontSize;
    if (font.letterSpacing) {
      style["letter-spacing"] = font.letterSpacing;
    } else {
      style["letter-spacing"] = 'inherit'
    }
    if (font.fontWeight) {
      style["font-weight"] = font.fontWeight;
    } else {
      style["font-weight"] = 'inherit';
    }
    if (font.textAlign) {
      style["text-align"] = font.textAlign;
    } else {
      style["text-align"] = 'unset';
    }
    if (font.verticalAlign) {
      style["vertical-align"] = font.verticalAlign;
    } else {
      style["vertical-align"] = 'unset';
    }
    if (font.verticalAlign) {
      style["display"] = "table-cell";
    }
    if (font.textIndent) {
      style["text-indent"] = font.textIndent;
    } else {
      style["text-indent"] = 'unset';
    }
    if (font.lineHeight)
      style["line-height"] = font.lineHeight + "px";
    if (font.textDecoration) {
      style["text-decoration"] = font.textDecoration;
    } else {
      style["text-decoration"] = 'unset';
    }
    if (font.isEllipsis) {
      style["overflow"] = "hidden";
      style["text-overflow"] = "ellipsis";
      style["white-space"] = "nowrap";
    } else {
      // delete style["overflow"];
      // delete style["text-overflow"];
      // delete style["white-space"];
      style["overflow"] = "";
      style["text-overflow"] = "";
      style["white-space"] = "";
    }
    //处理渐变颜色
    if (font.gradient && font.color00 && font.color01) {
      const linearGradient = (font.gradient + "," + font.color00 + "," + font.color01);
      // console.log('linearGradient :>> ', linearGradient);
      style["background-image"] = "linear-gradient(" + linearGradient + ")";
      //style["background-image"]="-webkit-linear-gradient("+linearGradient+")";
      style["background-clip"] = "text";
      style["text-fill-color"] = "transparent";
    } else {
      if (font.color) {
        style["color"] = font.color;
      } else {
        style["color"] = 'unset';
      }
    }
    //处理边框阴影style h-shadow v-shadow blur spread color inset
    const textShadowStr = toTextShadow(font.textShadow);
    if (textShadowStr) {
      style["text-shadow"] = textShadowStr;
    } else {
      style["text-shadow"] = 'unset'
    }
  }
  return style;
}
/**
 * 背景属性样式
 * @param {*} context 
 * @param {*} bg 
 * @returns 
 */
export function toBgStyle (context, bg) {
  const style = {};
  if (bg) {
    if (bg.bgType) {
      if (bg.bgType === "color") {
        //颜色
        const colorStr = toColorStyle(bg.color);
        style["background"] = colorStr || 'unset';
      } else if (bg.bgType === "image") {
        //图片
        const fileUrl = toImageStyle(context, bg.image);
        if (fileUrl) {
          style["background-image"] = "url(" + fileUrl + ") ";
          style["background-repeat"] = "no-repeat";
          style["background-size"] = "100% 100%";
        } else {
          style["background-image"] = "none";
        }
      }
    }
    //2.透明度
    if (bg.opacity) {
      style["opacity"] = (bg.opacity / 100);
    } else {
      style["opacity"] = 'unset'
    }
    //3.模糊度
    if (bg && bg["blur"]) {
      const list = getPrefixes("filter");
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          style[item] = "blur(" + bg["blur"] + "px)";
        }
      }
    }
    //4.设置图片旋转角度
    if (bg.rotate) {
      style["transform"] = "rotate(" + bg.rotate + "deg)";
      style["-ms-transform"] = "rotate(" + bg.rotate + "deg)";
    }
    //5.毛玻璃(其他配置)
    if (bg.backdropfilter) {
      style["backdrop-filter"] = bg.backdropfilter;
    }
    //6.模糊度(其他配置)
    if (bg.filter) {
      style["filter"] = bg.filter;
    }
  }
  return style;
}
/**
 * 边框属性样式
 * @param {*} context 
 * @param {*} border 
 * @returns 
 */
export function toBorderStyle (context, border) {
  const style = {};
  if (border) {
    //素材
    if (border.bdType && border.bdType === "image") {
      //获取图片地址
      const fileUrl = toImageStyle(context, border.image);
      style["border-image-source"] = "url(" + fileUrl + ")";
    }
    //颜色
    if (border.bdType && border.bdType === "color") {
      style["border-image-source"] = "none";
      if (border["borderColor"]) {
        style["border-color"] = border["borderColor"];
      } else {
        style["border-color"] = 'unset';
      }
    }
    //样式style
    if (border["borderStyle"]) {
      style["border-style"] = border["borderStyle"];
    } else {
      style["border-style"] = 'unset';
    }
    //裁剪
    if (border["borderSlice"]) {
      style["border-image-slice"] = border["borderSlice"];
    } else {
      style["border-image-slice"] = 'unset';
    }
    //宽度
    if (border["borderWidth"]) {
      style["border-width"] = border["borderWidth"];
    } else {
      style["border-width"] = 'unset';
    }
    //圆角
    if (border["borderRadius"] || 0 === border["borderRadius"]) {
      style["border-radius"] = border["borderRadius"];
      style["-moz-border-radius"] = border["borderRadius"];
      style["-webkit-border-radius"] = border["borderRadius"];
    } else {
      style["border-radius"] = 'unset';
      style["-moz-border-radius"] = 'unset';
      style["-webkit-border-radius"] = 'unset';
    }
    //处理边框阴影style h-shadow v-shadow blur spread color inset
    // $.extend(true, style, toBoxShadow(border));
    if (border["boxShadow"]) {
      $.extend(true, style, toBoxShadow(border["boxShadow"]));
    }
  }
  return style;
}

export function toFlexStyle (flex) {
  const style = {};
  if (flex) {
    // const style = {}
    if (flex["display"]) {
      style["display"] = flex["display"];
    }
    if (flex["flexDirection"]) {
      style["flex-direction"] = flex["flexDirection"];
    }
    if (flex["flexWrap"]) {
      style["flex-wrap"] = flex["flexWrap"];
    }
    if (flex["justifyContent"]) {
      style["justify-content"] = flex["justifyContent"];
    }
    if (flex["alignItems"]) {
      style["align-items"] = flex["alignItems"];
    }
    if (flex["alignContent"]) {
      style["align-content"] = flex["alignContent"];
    }
  }
  return style;
}
/**
 * 描述：获取素材相关信息
 */
export function toImageStyle (context, image) {
  let imageUrl;
  if (context && image) {
    if (image["id"]) {
      const material = context.getMaterialById(image["id"]);
      if (material) {
        imageUrl = material.getUrl();
      }
    } else if (image["imageUrl"]) {
      imageUrl = image["imageUrl"];
    } else {
      imageUrl = image["fileUrl"];
    }
  }
  return imageUrl;
}
/**
 * 描述：获取颜色
 */
export function toColorStyle (color) {
  let expr;
  if (color) {
    if (typeof color === 'string' && !color["type"]) {
      expr = color;
    } else {
      if (color["type"] && color["type"] === 'gradient') {
        //渐变色对象
        const gradient = color["gradient"];
        if (gradient) {
          const colors = gradient["colors"];
          const gradientFn = gradient["gradientFn"];
          const gradientAtPosition = gradient["gradientAtPosition"];
          //颜色列表处理
          let colorStr = "";
          if (colors && colors.length) {
            for (let i = 0; i < colors.length; i++) {
              const colorItem = colors[i];
              if (colorItem["color"] && colorItem["percent"]) {

                if (gradientFn && gradientFn === "linear") {
                  if (i === colors.length - 1) {
                    colorStr = colorStr + colorItem["color"] + " " + colorItem["percent"] + "%";
                  } else {
                    colorStr = colorStr + colorItem["color"] + " " + colorItem["percent"] + "%, ";
                  }
                } else {
                  if (i === colors.length - 1) {
                    colorStr = colorStr + colorItem["color"] + " ";
                  } else {
                    colorStr = colorStr + colorItem["color"] + ", ";
                  }
                }

              } else if (colorItem["color"]) {
                if (i === colors.length - 1) {
                  colorStr = colorStr + colorItem["color"] + " ";
                } else {
                  colorStr = colorStr + colorItem["color"] + ", ";
                }
              }
            }
          }
          //结束位置处理
          let post;
          if ("top" === gradientAtPosition) {
            post = "to top";
          } else if ("bottom" === gradientAtPosition) {
            post = "to bottom";
          } else if ("left" === gradientAtPosition) {
            post = "to left";
          } else if ("right" === gradientAtPosition) {
            post = "to right";
          }

          //渐变角度处理
          let angle;
          if (gradient["gradientAngle"]) {
            angle = gradient["gradientAngle"] + "deg";
          }

          //渐变色拼接颜色
          if (gradientFn) {
            if (gradientFn === "linear") {
              if (post && angle && colorStr) {
                expr = "linear-gradient(" + post + ", " + colorStr + ")";
              } else if (post && colorStr) {
                expr = "linear-gradient(" + post + ", " + colorStr + ")";
              } else if (angle && colorStr) {
                expr = "linear-gradient(" + angle + ", " + colorStr + ")";
              }
            } else if (gradientFn === "radial") {
              if (gradient["gradientRadialShape"] && colorStr) {
                expr = "radial-gradient(" + gradient["gradientRadialShape"] + ", " + colorStr + ")";
              }
            } else if (gradientFn === "conic") {
              if (colorStr) {
                expr = "conic-gradient(" + colorStr + ")";
              }
            }
          }

        }
      } else {
        if (color["color"]) {
          expr = color["color"];
        }
      }
    }
  }
  return expr;
}
/**
 * 描述:获取阴影相关样式
 * @param shadow
 */
export function toBoxShadow (shadow) {
  if (shadow) {
    const hShadow = (shadow["hShadow"] || 0) + "px";
    const vShadow = (shadow["vShadow"] || 0) + "px";
    const blur = (shadow["blur"] || 0) + "px";
    let boxShadow
    if (shadow["shadowColor"]) {
      boxShadow = hShadow + " " + vShadow + " " + blur + " " + shadow["shadowColor"];
    }
    if (shadow["spread"]) {
      const spread = (shadow["spread"] || 0) + "px";
      boxShadow = hShadow + " " + vShadow + " " + blur + " " + spread + " " + shadow["shadowColor"];
    }
    if (shadow["shadowType"]) {
      boxShadow += " " + shadow["shadowType"];
    }
    return {
      "box-shadow": boxShadow,
      "-moz-box-shadow": boxShadow,
      "-webkit-box-shadow": boxShadow
    }
  }
}

/**
 * 描述:获取文本阴影相关样式
 * @param shadow
 */
export function toTextShadow (shadow) {
  let buf = "";
  if (shadow && shadow["shadowColor"]) {
    const hShadow = (shadow["hShadow"] || 0) + "px";
    const vShadow = (shadow["vShadow"] || 0) + "px";
    let count = (shadow["shadowCount"] || 1);
    for (let i = 0; i < count; i++) {
      let blur = ((shadow["blur"] || 0) * (i + 1)) + "px";
      buf += hShadow + " " + vShadow + " " + blur + " " + shadow["shadowColor"];
      if (i < (count - 1)) {
        buf += ",";
      }
    }
  }
  return buf;
}

/**
 * 
 * @param {*} group 
 * @returns 
 */
export function toGroupStyle (group) {
  const style = {};
  if (group) {
    if (group.isEllipsis) {
      style["overflow"] = "auto";
    } else {
      delete style["overflow"];
    }
  }
  return style;
}

/**
 * 动画样式
 * @param {*} animate
 * @returns 
 */
export function toAnimateStyle (animate) {
  let style = {};
  
  if (animate) { 
    const isAnimate = animate["isAnimate"] && animate["name"];
    const webPrefixes = getPrefixes();
    for (let i = 0; i < webPrefixes.length; i++) {
      //配置动画名称
      style[webPrefixes[i] + "animation-name"] = isAnimate ? animate["name"] : 'unset';
      //配置动画运行时长
      const duration = (animate["duration"] || 2000) + "ms";
      style[webPrefixes[i] + "animation-duration"] = isAnimate ? duration : 'unset';
      //配置动画速度曲线
      if (animate["timingFunction"]) {
        style[webPrefixes[i] + "animation-timing-function"] = isAnimate ? animate["timingFunction"] : 'unset';
      }
      //配置动画延迟时长
      if (animate["delay"]) {
        style[webPrefixes[i] + "animation-delay"] = isAnimate ? animate["delay"] + "ms" : 'unset';
      }
      //配置动画方向
      if (animate["direction"]) {
        style[webPrefixes[i] + "animation-direction"] = isAnimate ? animate["direction"] : 'unset';
      }
      //配置动画显示模式
      if (animate["fillMode"]) {
        style[webPrefixes[i] + "animation-fill-mode"] = isAnimate ? animate["fillMode"] : 'unset';
      }
      //配置动画播放次数
      if (animate["isInfinite"]) {
        style[webPrefixes[i] + "animation-iteration-count"] = "infinite";
      } else {
        style[webPrefixes[i] + "animation-iteration-count"] = animate["iterationCount"] || "1";
      }
    }
  }
  //console.info("转换动画样式",animate,style);
  return style;
}
export function toTransformStyle (transform) {
  let style;
  if(transform){
    const transformValueStr = toTransformValueStr(transform);
    const webPrefixes = getPrefixes("transform");
    if (transformValueStr && webPrefixes && webPrefixes.length) {
      for (let i = 0; i < webPrefixes.length; i++) {
        const key = webPrefixes[i];
        if (!style) {
          style = {};
        }
        style[key] = transformValueStr;
      }
    }
  }
  return style;
}

function toTransformValueStr (item) {
  let value = "";
  if(item){
    //移动 translateX
    if(!isEmpty(item["translateX"])){
      let translateX
      if(item["translateX"]===0){
        translateX="translateX(0) ";
      }else{
        translateX="translateX(" + item["translateX"] + "px) ";
      }
      value+=translateX;
    }
    //移动 translateY
    if(!isEmpty(item["translateY"])){
      let translateY
      if(item["translateY"]===0){
        translateY="translateY(0) ";
      }else{
        translateY="translateY(" + item["translateY"] + "px) ";
      }
      value+=translateY;
    }
    //移动 translateZ
    if(!isEmpty(item["translateZ"])){
      let translateZ
      if(item["translateZ"]===0){
        translateZ="translateZ(0) ";
      }else{
        translateZ="translateZ(" + item["translateZ"] + "px) ";
      }
      value+=translateZ;
    }


    //缩放 scaleX
    if(!isEmpty(item["scaleX"])){
      let scaleX
      if(item["scaleX"]===0){
        scaleX="scaleX(0) ";
      }else{
        scaleX="scaleX(" + item["scaleX"] + ") ";
      }
      value+=scaleX;
    }
    //缩放 scaleY
    if(!isEmpty(item["scaleY"])){
      let scaleY
      if(item["scaleY"]===0){
        scaleY="scaleY(0) ";
      }else{
        scaleY="scaleY(" + item["scaleY"] + ") ";
      }
      value+=scaleY;
    }
    //缩放 scaleZ
    if(!isEmpty(item["scaleZ"])){
      let scaleZ
      if(item["scaleZ"]===0){
        scaleZ="scaleZ(0) ";
      }else{
        scaleZ="scaleZ(" + item["scaleZ"] + ") ";
      }
      value+=scaleZ;
    }

    //旋转 rotateX
    if(!isEmpty(item["rotateX"])){
      let rotateX
      if(item["rotateX"]===0){
        rotateX="rotateX(0) ";
      }else{
        rotateX="rotateX(" + item["rotateX"] + "deg) ";
      }
      value+=rotateX;
    }
    //旋转 rotateY
    if(!isEmpty(item["rotateY"])){
      let rotateY
      if(item["rotateY"]===0){
        rotateY="rotateY(0) ";
      }else{
        rotateY="rotateY(" + item["rotateY"] + "deg) ";
      }
      value+=rotateY;
    }
    //旋转 rotateZ
    if(!isEmpty(item["rotateZ"])){
      let rotateZ
      if(item["rotateZ"]===0){
        rotateZ="rotateZ(0) ";
      }else{
        rotateZ="rotateZ(" + item["rotateZ"] + "deg) ";
      }
      value+=rotateZ;
    }


    //倾斜 skewX
    if(!isEmpty(item["skewX"])){
      let skewX
      if(item["skewX"]===0){
        skewX="skewX(0) ";
      }else{
        skewX="skewX(" + item["skewX"] + "deg) ";
      }
      value+=skewX;
    }
    //倾斜 skewY
    if(!isEmpty(item["skewY"])){
      let skewY
      if(item["skewY"]===0){
        skewY="skewY(0) ";
      }else{
        skewY="skewY(" + item["skewY"] + "deg) ";
      }
      value+=skewY;
    }
    //倾斜 skewZ
    if(!isEmpty(item["skewZ"])){
      let skewZ
      if(item["skewZ"]===0){
        skewZ="skewZ(0) ";
      }else{
        skewZ="skewZ(" + item["skewZ"] + "deg) ";
      }
      value+=skewZ;
    }
  }
  return value;
}
/**
 * 描述:动画数组
 * @returns *[]}
 */
export function getDefaultAnimates () {
  return {
    "A-S5WA7OWIK6EY":"散点1",
    "A-DG2E62MP0ESX":"散点2",
    "A-U1TDE31QCWAQ":"散点3",
    "bounce": "弹跳",
    "bounceMin": "弹跳_轻",
    "rotate": "旋转",
    "flash": "闪现",
    "pulse": "跳动",
    "rubberBand": "伸缩",
    "shakeX": "摇动_水平",
    "shakeY": "摇动_垂直",
    "headShake": "摇头",
    "swing": "秋千",
    "tada": "多田",
    "wobble": "摇晃",
    "jello": "凝胶物",
    "heartBeat": "心跳",
    "backInDown": "退入_下",
    "backInLeft": "退入_左",
    "backInRight": "退入_右",
    "backInUp": "退入_上",
    "backOutDown": "退出_下",
    "backOutLeft": "退出_左",
    "backOutRight": "退出_右",
    "backOutUp": "退出_上",
    "bounceIn": "弹入",
    "bounceInDown": "弹入_下",
    "bounceInLeft": "弹入_左",
    "bounceInRight": "弹入_右",
    "bounceInUp": "弹入_上",
    "bounceOut": "弹出",
    "bounceOutDown": "弹出_下",
    "bounceOutLeft": "弹出_左",
    "bounceOutRight": "弹出_右",
    "bounceOutUp": "弹出_上",
    "fadeIn": "淡入",
    "fadeInDown": "淡入_下",
    "fadeInDownBig": "淡入_下方",
    "fadeInLeft": "淡入_左",
    "fadeInLeftBig": "淡入_左方",
    "fadeInRight": "淡入_右",
    "fadeInRightBig": "淡入_右方",
    "fadeInUp": "淡入_上",
    "fadeInUpBig": "淡入_上方",
    "fadeInTopLeft": "淡入_左上",
    "fadeInTopRight": "淡入_右上",
    "fadeInBottomLeft": "淡入_左下",
    "fadeInBottomRight": "淡入_右下",
    "fadeOut": "淡出",
    "fadeOutDown": "淡出_下",
    "fadeOutDownBig": "淡出_下方",
    "fadeOutLeft": "淡出_左",
    "fadeOutLeftBig": "淡出_左方",
    "fadeOutRight": "淡出_右",
    "fadeOutRightBig": "淡出_右方",
    "fadeOutUp": "淡出_上",
    "fadeOutUpBig": "淡出_上方",
    "fadeOutTopLeft": "淡出_左上",
    "fadeOutTopRight": "淡出_右上",
    "fadeOutBottomRight": "淡出_左下",
    "fadeOutBottomLeft": "淡出_右下",
    "flip": "翻转",
    "flipX": "翻转_水平",
    "flipY": "翻转_垂直",
    "flipInX": "翻入_水平",
    "flipInY": "翻入_垂直",
    "flipOutX": "翻出_水平",
    "flipOutY": "翻出_垂直",
    "lightSpeedInRight": "线入_右",
    "lightSpeedInLeft": "线入_左",
    "lightSpeedOutRight": "线出_右",
    "lightSpeedOutLeft": "线出_左",
    "rotateIn": "旋入",
    "rotateInDownLeft": "旋入_左下",
    "rotateInDownRight": "旋入_右下",
    "rotateInUpLeft": "旋入_左上",
    "rotateInUpRight": "旋入_右上",
    "rotateOut": "旋出",
    "rotateOutDownLeft": "旋出_左下",
    "rotateOutDownRight": "旋出_右下",
    "rotateOutUpLeft": "旋出_左上",
    "rotateOutUpRight": "旋出_右上",
    "hinge": "转折",
    "rollIn": "旋转入",
    "rollOut": "旋转出",
    "zoomIn": "缩放入",
    "zoomInDown": "缩放入_下",
    "zoomInLeft": "缩放入_左",
    "zoomInRight": "缩放入_右",
    "zoomInUp": "缩放入_上",
    "zoomOut": "缩放出",
    "zoomOutDown": "缩放出_下",
    "zoomOutLeft": "缩放出_左",
    "zoomOutRight": "缩放出_右",
    "zoomOutUp": "缩放出_上",
    "slideInDown": "滑动入_下",
    "slideInLeft": "滑动入_左",
    "slideInRight": "滑动入_右",
    "slideInUp": "滑动入_上",
    "slideOutDown": "滑动出_下",
    "slideOutLeft": "滑动出_左",
    "slideOutRight": "滑动出_右",
    "slideOutUp": "滑动出_上"
  };
}

/**
 * 描述:获取字体对象
 * @returns {*}
 */
export function getFontFamily () {
  return {
    "宋体": "宋体",
    "仿宋": "仿宋",
    "黑体": "黑体",
    "楷体": "楷体",
    "微软雅黑": "微软雅黑",
    "Arial": "Arial",
    "Impact": "Impact",
    "Times New Roman": "Times New Roman",
    "Comic Sans MS": "Comic Sans MS",
    "Courier New": "Courier New",
    "calculator": "数字1",
    "chintzy": "数字2",
    "digital": "数字3",
    "shadow": "数字4",
    "fakehope": "数字5",
    "time": "数字6",
    "pennstat": "数字7",
    "PangMen": "庞门正道"
  }
}
/**
 * 图标
 * @returns
 */
export function getIcons () {
  return require('../assets/font/lsd-font.json');
}