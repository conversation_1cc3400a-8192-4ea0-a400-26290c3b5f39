/**
 * 撤销
 */
export function undoLayer (context) {
  if (!context) {
    return
  }
  const undos = context.getUndos();
  const designer = context.getDesigner();
  if (undos.length > 0) {
    const undo = undos.pop();
    if (undo.layer && undo.prevAction) {
      // 赋值未代理的撤销属性
      undo.layer.property = JSON.parse(JSON.stringify(undo.prevAction));
      // 重新代理
      undo.layer.refreshProxy();
      undo.layer.refreshLayer()
      setTimeout(() => {
        designer && designer.linkByLayer(undo.layer);
      }, 100)
      // 记录到重做
      context.addRedo(undo);
    }
  }
}

/**
 * 重做
 */
export function redoLayer (context) {
  if (!context) {
    return
  }
  const redos = context.getRedos();
  const designer = context.getDesigner();
  if (redos.length > 0) {
    const redo = redos.pop();
    if (redo.layer && redo.action) {
      // 赋值未代理的重做属性
      redo.layer.property = JSON.parse(JSON.stringify(redo.action));
      // 重新代理
      redo.layer.refreshProxy();
      redo.layer.refreshLayer()
      setTimeout(() => {
        designer && designer.linkByLayer(redo.layer);
      }, 100)
      // 记录到撤销
      context.addUndo(redo);
    }
  }
}

export function alignLayers (context, position) {
  if (!context) {
    return
  }
  const selectMap = context.getSelectMap();
  if (selectMap && Object.keys(selectMap).length > 1) {
    const layerMap = context.getLayerMap();
    const layers = [];
    for (const key in selectMap) {
      layers.push(layerMap[key]);
    }
    switch (position) {
      case 'top':
        verticalTopLayers(layers);
        break
      case 'middle':
        verticalMiddleLayers(layers);
        break
      case 'bottom':
        verticalBottomLayers(layers);
        break
      case 'left':
        horizontalLeftLayers(layers);
        break
      case 'center':
        horizontalCenterLayers(layers);
        break
      case 'right':
        horizontalRightLayers(layers);
        break
    }
  }
}

/*#############方向对齐相关###############*/
/**
 * 选中图层垂直方向顶端对齐
 */
function verticalTopLayers (layers = []) {
  if (layers.length > 1) {
    // 根据top值排序，离顶部最近的为最小值，离顶部最远的为最大值
    const sortTopLayers = layers.sort((a, b) => {
      return a.property.top - b.property.top;
    })
    // 最近
    const minTopLayer = sortTopLayers[0];

    for (let i = 1; i < layers.length; i++) {
      const layer = layers[i];
      if (layer) {
        layer.property.top = minTopLayer.property.top;
        layer.refreshLayer();
      }
    }
  }
}
/**
 * 选中图层垂直居中
 */
function verticalMiddleLayers (layers = []) {
  if (layers.length > 1) {
    // 根据top值排序，离顶部最近的为最小值，离顶部最远的为最大值
    const sortTopLayers = layers.sort((a, b) => {
      return a.property.top - b.property.top;
    })
    // 最近
    const minTopLayer = sortTopLayers[0];
    // 最远
    const maxTopLayer = sortTopLayers[sortTopLayers.length - 1];
    // 中线 = (最远的top + 最远的高度 - 最近的top) / 2
    const centerLine = (maxTopLayer.property.top + maxTopLayer.property.height - minTopLayer.property.top) / 2;
    // 中线到顶部的距离
    const centerLineByTop = centerLine + minTopLayer.property.top;

    for (let i = 0; i < layers.length; i++) {
      const layer = layers[i];
      if (layer) {
        layer.property.top = centerLineByTop - (layer.property.height / 2);
        layer.refreshLayer();
      }
    }
  }
}
/**
 * 选中图层垂直方向底端对齐
 */
function verticalBottomLayers (layers = []) {
  if (layers.length > 1) {
    // 根据top值排序，离顶部最近的为最小值，离顶部最远的为最大值
    const sortTopLayers = layers.sort((a, b) => {
      return (a.property.top + a.property.height) - (b.property.top + b.property.height);
    })
    // 最远
    const maxTopLayer = sortTopLayers[sortTopLayers.length - 1];
    // 底线 = 最远的top + 最远的高度
    const bottomLine = maxTopLayer.property.top + maxTopLayer.property.height;
    // 底端对齐
    for (let i = layers.length - 2; i >= 0; i--) {
      const layer = layers[i];
      if (layer) {
        // 当前图层顶部 = 底线 - 当前图层高度
        layer.property.top = bottomLine - layer.property.height;
        layer.refreshLayer();
      }
    }
  }
}
/**
 * 选中图层水平方向左对齐
 */
function horizontalLeftLayers (layers = []) {
  if (layers.length > 1) {
    // 根据left值排序，离左边最近的为最小值，离左边最远的为最大值
    const sortLeftLayers = layers.sort((a, b) => {
      return a.property.left - b.property.left;
    })
    // 最近
    const minLeftLayer = sortLeftLayers[0];

    for (let i = 1; i < layers.length; i++) {
      const layer = layers[i];
      if (layer) {
        layer.property.left = minLeftLayer.property.left;
        layer.refreshLayer();
      }
    }
  }
}
/**
 * 选中图层水平方向居中对齐
 */
function horizontalCenterLayers (layers = []) {
  if (layers.length > 1) {
    // 根据left值排序,离左边最近的为最小值，离左边最远的为最大值
    const sortLeftLayers = layers.sort((a, b) => {
      return a.property.left - b.property.left;
    })
    // 最近
    const minLeftLayer = sortLeftLayers[0];
    // 最远
    const maxLeftLayer = sortLeftLayers[sortLeftLayers.length - 1];
    // 中线 = (最远的top + 最远的高度 - 最近的top) / 2
    const centerLine = (maxLeftLayer.property.left + maxLeftLayer.property.width - minLeftLayer.property.left) / 2;
    // 中线到顶部的距离
    const centerLineByLeft = centerLine + minLeftLayer.property.left;

    for (let i = 0; i < layers.length; i++) {
      const layer = layers[i];
      if (layer) {
        layer.property.left = centerLineByLeft - (layer.property.width / 2);
        layer.refreshLayer();
      }
    }
  }
}

/**
 * 选中图层水平方向右对齐
 */
function horizontalRightLayers (layers = []) {
  if (layers.length > 1) {
    // 根据left值排序，离左边最近的为最小值，离左边最远的为最大值
    const sortLeftLayers = layers.sort((a, b) => {
      return (a.property.left + a.property.width) - (b.property.left + b.property.width);
    })
    // 最远
    const maxLeftLayer = sortLeftLayers[sortLeftLayers.length - 1];
    // 右线 = 最远的left + 最远的宽度
    const rightLine = maxLeftLayer.property.left + maxLeftLayer.property.width;
    // 底端对齐
    for (let i = layers.length - 2; i >= 0; i--) {
      const layer = layers[i];
      if (layer) {
        // 当前图层左侧 = 右线 - 当前图层宽度
        layer.property.left = rightLine - layer.property.width;
        layer.refreshLayer();
      }
    }
  }
}