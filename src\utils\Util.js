// 导入日期工具类
import DateUtil from "./DateUtil.js";

/**
 * 导出日期工具类，方便在项目中使用
 */
export { DateUtil };

/**
 * 描述:获取uuid
 * @returns {string}
 */
export function uuid() {
  return "axxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
/**
 * 获取随机数字
 * @returns {string}
 */
export function getRandom(len) {
  let chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
  let maxPos = chars.length;
  let code = "";
  for (let i = 0; i < len; i++) {
    code += chars.charAt(Math.floor(Math.random() * maxPos));
  }
  return code; //直接转换为小写
}
/**
 * 描述:深度克隆json
 * @param json
 * @returns {any}
 */
export function jsonClone(json) {
  return JSON.parse(JSON.stringify(json));
}
/**
 * 描述:格式化日期
 * @param {*} date
 * @param {*} format
 * @returns
 */
export function formatDate(date, format) {
  if (!date || date === "") {
    return "";
  }
  if (typeof date === "number") {
    date = new Date(date);
  }
  if (typeof date === "string") {
    return date;
  }
  var o = {
    "M+": date.getMonth() + 1,
    "d+": date.getDate(),
    "H+": date.getHours(),
    "m+": date.getMinutes(),
    "s+": date.getSeconds(),
  };
  if (/(y+)/.test(format))
    format = format.replace(
      RegExp.$1,
      (date.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  for (var k in o)
    if (new RegExp("(" + k + ")").test(format))
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
  return format;
}

/**
 * 在当前日期添加天数
 * @param daysToAdd 天数
 * @returns {Date} 日期实例
 */
export function addDaysToDate(daysToAdd) {
  const currentDate = new Date();
  let newDate = new Date(currentDate);
  newDate.setDate(currentDate.getDate() + daysToAdd);

  while (
    newDate.getDate() >
    new Date(newDate.getFullYear(), newDate.getMonth() + 1, 0).getDate()
  ) {
    newDate.setMonth(newDate.getMonth() + 1);
    newDate.setDate(
      newDate.getDate() -
        new Date(newDate.getFullYear(), newDate.getMonth(), 0).getDate()
    );
  }

  return newDate;
}

function countSubstring(str, sub) {
  let count = 0;
  // 使用正则表达式，并设置全局标志'g'来匹配所有出现的子串
  const regex = new RegExp(sub, "g");
  // 使用match方法获取所有匹配的子串，并计算数量
  if (regex.test(str)) {
    count = str.match(regex).length;
  }
  return count;
}
/**
 *
 * @param {*} formatStr
 * @param {*} DateStr
 * @returns
 */
export function formatDateToStr(dateStr, formatStr) {
  let formatDate;
  if (dateStr && formatStr) {
    const formatExpr = formatStr.toLowerCase();
    const data = new Date();
    const year = data.getFullYear();
    const month = data.getMonth() + 1;
    const day = data.getDate();
    const hour = data.getHours();
    const minutes = data.getMinutes();
    const seconds = data.getSeconds();

    let index = 0;
    let num = 0;
    if (formatExpr.indexOf("y") != -1) {
      index = formatStr.indexOf("y");
      num = countSubstring(formatExpr, "y");
      formatDate = dateStr.substr(index, num);
    } else {
      formatDate = year;
    }
    if (formatStr.indexOf("m") != -1) {
      index = formatStr.indexOf("m");
      num = countSubstring(formatExpr, "m");
      formatDate += "-" + dateStr.substr(index, num);
    } else {
      formatDate += "-" + month;
    }
    if (formatStr.indexOf("d") != -1) {
      index = formatStr.indexOf("d");
      num = countSubstring(formatExpr, "d");
      formatDate += "-" + dateStr.substr(index, num);
    } else {
      formatDate += "-" + day;
    }
    if (formatStr.indexOf("h") != -1) {
      index = formatStr.indexOf("h");
      num = countSubstring(formatExpr, "h");
      formatDate += " " + dateStr.substr(index, num);
    } else {
      formatDate += " " + hour;
    }
    if (formatStr.indexOf("i") != -1) {
      index = formatStr.indexOf("i");
      num = countSubstring(formatExpr, "i");
      formatDate += ":" + dateStr.substr(index, num);
    } else {
      formatDate += ":" + minutes;
    }
    if (formatStr.indexOf("s") != -1) {
      index = formatStr.indexOf("s");
      num = countSubstring(formatExpr, "s");
      formatDate += ":" + dateStr.substr(index, num);
    } else {
      formatDate += ":" + seconds;
    }
  }
  return formatDate;
}
/**
 * 描述:获取地址栏参数
 * @param {*} name
 * @returns
 */
export function getParameter(name) {
  if (name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return r[2];
  } else {
    const params = {};
    var paramsStr = window.location.search;
    var num = paramsStr.indexOf("?");
    paramsStr = paramsStr.substr(num + 1); //取得所有参数   stringvar.substr(start [, length ]
    var arr = paramsStr.split("&"); //各个参数放到数组里
    for (var i = 0; i < arr.length; i++) {
      num = arr[i].indexOf("=");
      if (num > 0) {
        const name = arr[i].substring(0, num);
        const value = arr[i].substr(num + 1);
        params[name] = value;
      }
    }
    return params;
  }
  return null;
}
export function getUrlParams(name) {
  let obj;
  //判断是否存在参数
  let url = document.URL;
  if (url.indexOf("?") < 0) {
    return obj;
  }
  //解析参数
  let arr = url.split("?");
  url = arr[1];
  let array = url.split("&");
  for (let i = 0; i < array.length; i++) {
    let arr2 = array[i];
    let arr3 = arr2.split("=");
    if (!obj) {
      obj = {};
    }
    obj[arr3[0]] = arr3[1];
  }
  //判断 根据名称获取值
  if (name && obj) {
    if (obj[name]) {
      return obj[name];
    } else {
      return;
    }
  }
  return obj;
}
/**
 * 获取http的 host 包括Ip和端口
 * 例如 http://***********:8090 https://***********:8090
 * @returns {*}
 */
export function getHttpHost() {
  if (window.origin) {
    return window.origin;
  }
}
/**
 * 描述:获取应用编码
 * @returns {*}
 */
export function getAppCode() {
  if (window.location && window.location.pathname) {
    var pathes = window.location.pathname.split("/");
    if (pathes && pathes[1]) {
      return pathes[1];
    }
  }
}
/**
 * 判断开头是
 * @param {*} str
 * @param {*} startStr
 * @returns
 */
export function startsWith(str, startStr) {
  //兼容
  if (typeof String.prototype.startsWith != "function") {
    String.prototype.startsWith = function (prefix) {
      return this.slice(0, prefix.length) === prefix;
    };
  }
  return str.startsWith(startStr);
}
/**
 * 判断结尾是
 * @param {*} str
 * @param {*} endStr
 * @returns
 */
export function endsWith(str, endStr) {
  //兼容
  if (typeof String.prototype.endsWith != "function") {
    String.prototype.endWith = function (endStr) {
      var d = this.length - endStr.length;
      return d >= 0 && this.lastIndexOf(endStr) == d;
    };
  }
  return str.endsWith(endStr);
}

//判断数字不等空
// export function isNumNotEmpty(value){
//   if(value || 0===value || "0"===value){
//       return true;
//   }else{
//       return false;
//   }
// }
//删除属性
// const obj=$(this.container).attr("style");
// if(!isEmpty(obj)){
//   $(this.container).removeAttr("style");
// }
//判断是否为空
export function isEmpty(obj) {
  if (
    (!obj && typeof obj != "boolean") ||
    (!obj && (obj != 0 || obj != "0")) ||
    typeof obj === "undefined" ||
    obj === "undefined" ||
    obj === null ||
    obj === ""
  ) {
    return true;
  } else {
    return false;
  }
}
//判断Json是否为空{}
export function isEmptyObj(jsonObj) {
  if (typeof jsonObj !== "object") {
    return true;
  }
  return Object.keys(jsonObj).length === 0;
}

export function isJson(val) {
  try {
    JSON.parse(val);
    return true;
  } catch (error) {
    return false;
  }
}
export function isObj(val) {
  if (typeof val === "object") {
    return true;
  } else {
    return false;
  }
}
export function isStr(val) {
  if (typeof val === "string") {
    return true;
  } else {
    return false;
  }
}
//isNumber
export function isNum(val) {
  // const regPos = /^[0-9]+.?[0-9]*/;
  var regPos = /^\d+(\.\d+)?$/; //非负浮点数
  var regNeg =
    /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/; //负浮点数
  if (regPos.test(val) || regNeg.test(val)) {
    return true;
  } else {
    return false;
  }
}
export function isFun(val) {
  var regExp = /^\s*\bfunction\b/; //正则判断函数
  if (regExp.test(val)) {
    return true;
  } else {
    return false;
  }
}

export function isInterval(num, min, max) {
  if (num >= min && num < max) {
    return true;
  } else {
    return false;
  }
}
function getType(value) {
  return Object.prototype.toString.call(value).slice(8, -1).toUpperCase();
}
export function isObject(value) {
  return getType(value) === "object".toUpperCase();
}
export function isArray(value) {
  return getType(value) === "array".toUpperCase();
}

export function isEmptyArray(arr) {
  return isArray(arr) && arr.length === 0;
}

export function isEmptyObject(obj) {
  return obj && isObject(obj) && Object.keys(obj).length === 0;
}
export function listToArray(list, key) {
  let array;
  if (key && list && list.length) {
    for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if (!array) {
        array = [];
      }
      array.push(item[key]);
    }
  }
  return array;
}
//转换成Map
export function toList(map) {
  var list = [];
  for (let key in map) {
    list.push(map[key]);
  }
  return list;
}
//转换成Map
export function toMap(array, key) {
  var map = {};
  for (var i = 0; i < array.length; i++) {
    map[array[i][key]] = array[i];
  }
  return map;
}
//转换成Tree
export function toTree(array, key, pkey, childKey) {
  if (!array) {
    return;
  }
  var map = toMap(array, key);
  var tree = [];
  for (var i = 0; i < array.length; i++) {
    var menu = array[i];
    if (menu[pkey] && map[menu[pkey]]) {
      var parent = map[menu[pkey]]; //获取父节点数据
      if (parent[childKey]) {
        parent[childKey].push(menu);
      } else {
        parent[childKey] = [menu];
      }
    } else {
      tree.push(menu);
    }
  }
  return { map: map, tree: tree };
}

/**
 * 删除数组对象
 * @param arr
 * @param obj
 */
export function remove(arr, obj) {
  if (arr instanceof Array) {
    const index = arr.indexOf(obj);
    if (index > -1) {
      arr.splice(index, 1);
    }
  } else {
    console.error("填入参数不是数组，不能删除!");
  }
}
/**
 * 描述:删除数组中某一个对象
 */
export function removeArr(array, _obj) {
  const length = array.length;
  for (let i = 0; i < length; i++) {
    if (array[i] == _obj) {
      if (i == 0) {
        array.shift(); //删除并返回数组的第一个元素
        return;
      } else if (i == length - 1) {
        array.pop(); //删除并返回数组的最后一个元素
        return;
      } else {
        array.splice(i, 1); //删除下标为i的元素
        return;
      }
    }
  }
}
export function openFullWindow(url) {
  var h = screen.availHeight - 65;
  var w = screen.availWidth - 15;
  var vars =
    "top=0,left=0,height=" +
    h +
    ",width=" +
    w +
    ",fullscreen,status=no,toolbar=no,menubar=no,location=no,resizable=1,scrollbars=1";

  var win = window.open(url, "", vars, true);
  return win;
}

export function getUrlParameter(name) {
  if (name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r !== null) return decodeURI(r[2]);
  } else {
    const params = {};
    var paramsStr = window.location.search;
    var num = paramsStr.indexOf("?");
    paramsStr = paramsStr.substr(num + 1); //取得所有参数   stringvar.substr(start [, length ]
    var arr = paramsStr.split("&"); //各个参数放到数组里
    for (var i = 0; i < arr.length; i++) {
      num = arr[i].indexOf("=");
      if (num > 0) {
        const name = arr[i].substring(0, num);
        const value = arr[i].substr(num + 1);
        params[name] = decodeURI(value);
      }
    }
    return params;
  }
  return null;
}
/**
 * 函数防抖
 * @param func 需要防抖的函数
 * @param wait 防抖等待时间
 * @param immediate 是否立即执行
 * @returns {(function(...[*]): void)|*}
 */
export function debounce(func, wait, immediate = false) {
  let timeout;

  return function () {
    const context = this;
    const args = arguments;

    const later = function () {
      timeout = null;
      if (!immediate) {
        func.apply(context, args);
      }
    };

    const callNow = immediate && !timeout;

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);

    if (callNow) {
      func.apply(context, args);
    }
  };
}
