<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密方法Loading测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .test-button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-button.encrypt {
            background: #28a745;
        }
        
        .test-button.encrypt:hover {
            background: #218838;
        }
        
        .test-button.decrypt {
            background: #17a2b8;
        }
        
        .test-button.decrypt:hover {
            background: #138496;
        }
        
        .log-panel {
            background: #343a40;
            color: #fff;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        /* 模拟Loading样式 */
        .mock-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .mock-loading-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .mock-loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .method-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <h1>加密方法Loading测试</h1>
    
    <div class="test-section">
        <h2>postEncrypt 加密POST请求测试</h2>
        <div class="description">
            测试加密POST请求的loading显示，默认消息为"加密提交中..."
        </div>
        <div class="method-info">
            service.postEncrypt(url, data, success, fail, dataType, options)
        </div>
        <div class="button-group">
            <button class="test-button encrypt" onclick="testPostEncrypt()">默认加密提交</button>
            <button class="test-button encrypt" onclick="testPostEncryptCustom()">自定义消息加密提交</button>
            <button class="test-button encrypt" onclick="testPostEncryptNoLoading()">无Loading加密提交</button>
        </div>
    </div>

    <div class="test-section">
        <h2>getDecrypt 解密GET请求测试</h2>
        <div class="description">
            测试解密GET请求的loading显示，默认消息为"解密加载中..."
        </div>
        <div class="method-info">
            service.getDecrypt(url, data, success, fail, dataType, options)
        </div>
        <div class="button-group">
            <button class="test-button decrypt" onclick="testGetDecrypt()">默认解密获取</button>
            <button class="test-button decrypt" onclick="testGetDecryptCustom()">自定义消息解密获取</button>
            <button class="test-button decrypt" onclick="testGetDecryptNoLoading()">无Loading解密获取</button>
        </div>
    </div>

    <div class="test-section">
        <h2>混合请求测试</h2>
        <div class="description">
            测试加密和解密方法与普通方法的混合使用
        </div>
        <div class="button-group">
            <button class="test-button" onclick="testMixedRequests()">混合请求测试</button>
            <button class="test-button" onclick="testPriorityMixed()">优先级混合测试</button>
            <button class="test-button" onclick="testSecurityWorkflow()">安全工作流测试</button>
        </div>
    </div>

    <div class="test-section">
        <h2>操作日志</h2>
        <div class="log-panel" id="log-panel">
            日志信息将在这里显示...
        </div>
        <div class="button-group">
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        // 模拟Service类的加密方法
        class MockService {
            constructor() {
                this.loadingManager = new MockGlobalLoadingManager();
            }

            postEncrypt(url, data, success, fail, dataType, options = {}) {
                const showLoading = options.showLoading !== false;
                const loadingMessage = options.loadingMessage || "加密提交中...";
                const loadingPriority = options.loadingPriority || 0;
                
                let requestId = null;
                
                if (showLoading) {
                    requestId = this.loadingManager.startRequest(loadingMessage, loadingPriority);
                }
                
                this.log(`postEncrypt: ${url}, 消息: ${loadingMessage}`);
                
                // 模拟加密和网络请求
                setTimeout(() => {
                    if (showLoading && requestId) {
                        this.loadingManager.endRequest(requestId);
                    }
                    
                    if (Math.random() > 0.2) {
                        this.log(`postEncrypt成功: ${url}`);
                        if (success) success({ status: 'success', encrypted: true });
                    } else {
                        this.log(`postEncrypt失败: ${url}`, 'error');
                        if (fail) fail({ error: 'Network error' });
                    }
                }, Math.random() * 2000 + 1000);
            }

            getDecrypt(url, data, success, fail, dataType, options = {}) {
                const showLoading = options.showLoading !== false;
                const loadingMessage = options.loadingMessage || "解密加载中...";
                const loadingPriority = options.loadingPriority || 0;
                
                let requestId = null;
                
                if (showLoading) {
                    requestId = this.loadingManager.startRequest(loadingMessage, loadingPriority);
                }
                
                this.log(`getDecrypt: ${url}, 消息: ${loadingMessage}`);
                
                // 模拟网络请求和解密
                setTimeout(() => {
                    if (showLoading && requestId) {
                        this.loadingManager.endRequest(requestId);
                    }
                    
                    if (Math.random() > 0.2) {
                        this.log(`getDecrypt成功: ${url}`);
                        if (success) success({ data: 'decrypted data', decrypted: true });
                    } else {
                        this.log(`getDecrypt失败: ${url}`, 'error');
                        if (fail) fail({ error: 'Decryption error' });
                    }
                }, Math.random() * 2000 + 1000);
            }

            getRequest(url, data, success, fail, dataType, options = {}) {
                const showLoading = options.showLoading !== false;
                const loadingMessage = options.loadingMessage || "加载中...";
                const loadingPriority = options.loadingPriority || 0;
                
                let requestId = null;
                
                if (showLoading) {
                    requestId = this.loadingManager.startRequest(loadingMessage, loadingPriority);
                }
                
                this.log(`getRequest: ${url}, 消息: ${loadingMessage}`);
                
                setTimeout(() => {
                    if (showLoading && requestId) {
                        this.loadingManager.endRequest(requestId);
                    }
                    
                    if (success) success({ data: 'normal data' });
                }, Math.random() * 1500 + 500);
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logPanel = document.getElementById('log-panel');
                const logEntry = document.createElement('div');
                logEntry.innerHTML = `[${timestamp}] ${message}`;
                
                if (type === 'error') {
                    logEntry.style.color = '#dc3545';
                } else if (type === 'success') {
                    logEntry.style.color = '#28a745';
                }
                
                logPanel.appendChild(logEntry);
                logPanel.scrollTop = logPanel.scrollHeight;
            }
        }

        // 简化的Loading管理器
        class MockGlobalLoadingManager {
            constructor() {
                this.requestCount = 0;
                this.isVisible = false;
                this.currentMessage = '';
                this.messageQueue = [];
                this.loadingElement = null;
            }

            startRequest(message, priority) {
                const requestId = 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5);
                this.requestCount++;
                
                this.addMessageToQueue(message, priority, requestId);
                this.showLoading();
                
                return requestId;
            }

            endRequest(requestId) {
                if (this.requestCount > 0) {
                    this.requestCount--;
                    this.removeMessageFromQueue(requestId);
                    
                    if (this.requestCount === 0) {
                        this.hideLoading();
                    } else {
                        this.updateMessage();
                    }
                }
            }

            showLoading() {
                if (!this.isVisible) {
                    this.loadingElement = this.createLoadingElement();
                    document.body.appendChild(this.loadingElement);
                    this.isVisible = true;
                }
                this.updateMessage();
            }

            hideLoading() {
                if (this.isVisible && this.loadingElement) {
                    document.body.removeChild(this.loadingElement);
                    this.isVisible = false;
                    this.loadingElement = null;
                }
            }

            createLoadingElement() {
                const loading = document.createElement('div');
                loading.className = 'mock-loading';
                loading.innerHTML = `
                    <div class="mock-loading-content">
                        <div class="mock-loading-spinner"></div>
                        <div class="mock-loading-message">${this.currentMessage}</div>
                    </div>
                `;
                return loading;
            }

            updateMessage() {
                if (this.messageQueue.length > 0) {
                    const highestPriorityMessage = this.messageQueue[0].message;
                    if (this.currentMessage !== highestPriorityMessage) {
                        this.currentMessage = highestPriorityMessage;
                        
                        if (this.isVisible && this.loadingElement) {
                            const messageElement = this.loadingElement.querySelector('.mock-loading-message');
                            if (messageElement) {
                                messageElement.textContent = this.currentMessage;
                            }
                        }
                    }
                }
            }

            addMessageToQueue(message, priority, requestId) {
                this.messageQueue.push({ message, priority, requestId });
                this.messageQueue.sort((a, b) => b.priority - a.priority);
            }

            removeMessageFromQueue(requestId) {
                this.messageQueue = this.messageQueue.filter(item => item.requestId !== requestId);
            }
        }

        // 创建服务实例
        const service = new MockService();

        // 测试函数
        function testPostEncrypt() {
            service.postEncrypt('/api/secure-save', { data: 'sensitive' }, 
                (result) => service.log('加密提交成功', 'success'),
                (error) => service.log('加密提交失败', 'error')
            );
        }

        function testPostEncryptCustom() {
            service.postEncrypt('/api/user-login', { username: 'admin', password: 'pass' }, 
                (result) => service.log('登录成功', 'success'),
                (error) => service.log('登录失败', 'error'),
                'json',
                {
                    loadingMessage: '正在验证用户身份...',
                    loadingPriority: 10
                }
            );
        }

        function testPostEncryptNoLoading() {
            service.postEncrypt('/api/background-save', { data: 'background' }, 
                (result) => service.log('后台加密保存成功', 'success'),
                (error) => service.log('后台加密保存失败', 'error'),
                'json',
                { showLoading: false }
            );
        }

        function testGetDecrypt() {
            service.getDecrypt('/api/secure-config', { module: 'dashboard' }, 
                (result) => service.log('解密获取成功', 'success'),
                (error) => service.log('解密获取失败', 'error')
            );
        }

        function testGetDecryptCustom() {
            service.getDecrypt('/api/user-profile', { userId: 123 }, 
                (result) => service.log('用户资料获取成功', 'success'),
                (error) => service.log('用户资料获取失败', 'error'),
                'json',
                {
                    loadingMessage: '正在获取用户资料...',
                    loadingPriority: 8
                }
            );
        }

        function testGetDecryptNoLoading() {
            service.getDecrypt('/api/background-config', {}, 
                (result) => service.log('后台解密获取成功', 'success'),
                (error) => service.log('后台解密获取失败', 'error'),
                'json',
                { showLoading: false }
            );
        }

        function testMixedRequests() {
            service.getRequest('/api/normal-data', {}, 
                (result) => service.log('普通请求成功', 'success'));
            
            service.postEncrypt('/api/secure-save', { data: 'secret' }, 
                (result) => service.log('加密请求成功', 'success'));
            
            service.getDecrypt('/api/secure-data', {}, 
                (result) => service.log('解密请求成功', 'success'));
        }

        function testPriorityMixed() {
            service.getRequest('/api/low-priority', {}, null, null, 'json', {
                loadingMessage: '低优先级请求...', loadingPriority: 1
            });
            
            service.postEncrypt('/api/high-priority', {}, null, null, 'json', {
                loadingMessage: '高优先级加密请求！', loadingPriority: 10
            });
            
            service.getDecrypt('/api/medium-priority', {}, null, null, 'json', {
                loadingMessage: '中优先级解密请求...', loadingPriority: 5
            });
        }

        function testSecurityWorkflow() {
            // 模拟安全工作流：登录 -> 获取配置 -> 保存数据
            service.postEncrypt('/api/login', { username: 'user', password: 'pass' }, 
                (result) => {
                    service.log('步骤1: 登录成功', 'success');
                    
                    // 登录成功后获取配置
                    service.getDecrypt('/api/user-config', { token: result.token }, 
                        (config) => {
                            service.log('步骤2: 配置获取成功', 'success');
                            
                            // 获取配置后保存数据
                            service.postEncrypt('/api/save-data', { config, data: 'user data' }, 
                                (saveResult) => service.log('步骤3: 数据保存成功', 'success'),
                                null, 'json', { loadingMessage: '正在保存用户数据...', loadingPriority: 7 }
                            );
                        },
                        null, 'json', { loadingMessage: '正在获取用户配置...', loadingPriority: 8 }
                    );
                },
                null, 'json', { loadingMessage: '正在登录系统...', loadingPriority: 10 }
            );
        }

        function clearLog() {
            document.getElementById('log-panel').innerHTML = '';
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            service.log('加密方法Loading测试环境准备就绪');
        });
    </script>
</body>
</html>
