<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全局Loading配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .test-button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-button.success {
            background: #28a745;
        }
        
        .test-button.success:hover {
            background: #218838;
        }
        
        .test-button.danger {
            background: #dc3545;
        }
        
        .test-button.danger:hover {
            background: #c82333;
        }
        
        .config-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .config-item {
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .config-item label {
            min-width: 120px;
            font-weight: bold;
        }
        
        .config-item input[type="checkbox"] {
            transform: scale(1.2);
        }
        
        .config-item input[type="number"] {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 100px;
        }
        
        .status-panel {
            background: #e9ecef;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-panel {
            background: #343a40;
            color: #fff;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        /* 模拟Loading样式 */
        .mock-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .mock-loading-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .mock-loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .enabled {
            color: #28a745;
            font-weight: bold;
        }
        
        .disabled {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>全局Loading配置测试</h1>
    
    <div class="test-section">
        <h2>全局Loading配置</h2>
        <div class="description">
            通过全局配置控制所有请求的loading显示行为
        </div>
        
        <div class="config-panel">
            <div class="config-item">
                <label>启用Loading:</label>
                <input type="checkbox" id="enableLoading" checked onchange="updateGlobalConfig()">
                <span id="loadingStatus" class="enabled">已启用</span>
            </div>
            
            <div class="config-item">
                <label>显示延迟(ms):</label>
                <input type="number" id="showDelay" value="100" min="0" max="5000" onchange="updateGlobalConfig()">
            </div>
            
            <div class="config-item">
                <label>隐藏延迟(ms):</label>
                <input type="number" id="hideDelay" value="200" min="0" max="5000" onchange="updateGlobalConfig()">
            </div>
            
            <div class="config-item">
                <label>超时时间(ms):</label>
                <input type="number" id="timeout" value="30000" min="1000" max="120000" onchange="updateGlobalConfig()">
            </div>
            
            <div class="config-item">
                <label>最小显示时间(ms):</label>
                <input type="number" id="minShowTime" value="500" min="0" max="5000" onchange="updateGlobalConfig()">
            </div>
        </div>
        
        <div class="button-group">
            <button class="test-button success" onclick="applyConfig()">应用配置</button>
            <button class="test-button" onclick="resetConfig()">重置默认</button>
        </div>
    </div>

    <div class="test-section">
        <h2>请求测试</h2>
        <div class="description">
            测试在不同全局配置下的请求loading行为
        </div>
        <div class="button-group">
            <button class="test-button" onclick="testGetRequest()">GET请求</button>
            <button class="test-button" onclick="testPostRequest()">POST请求</button>
            <button class="test-button" onclick="testEncryptRequest()">加密请求</button>
            <button class="test-button" onclick="testDecryptRequest()">解密请求</button>
            <button class="test-button" onclick="testConcurrentRequests()">并发请求</button>
        </div>
    </div>

    <div class="test-section">
        <h2>配置切换测试</h2>
        <div class="description">
            动态切换全局配置并测试效果
        </div>
        <div class="button-group">
            <button class="test-button success" onclick="enableGlobalLoading()">启用全局Loading</button>
            <button class="test-button danger" onclick="disableGlobalLoading()">禁用全局Loading</button>
            <button class="test-button" onclick="testWithDisabledLoading()">禁用状态下测试请求</button>
        </div>
    </div>

    <div class="test-section">
        <h2>当前状态</h2>
        <div class="status-panel" id="status-panel">
            状态信息将在这里显示...
        </div>
    </div>

    <div class="test-section">
        <h2>操作日志</h2>
        <div class="log-panel" id="log-panel">
            日志信息将在这里显示...
        </div>
        <div class="button-group">
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        // 模拟Context类
        class MockContext {
            constructor() {
                this.config = {
                    globalLoading: {
                        enabled: true,
                        showDelay: 100,
                        hideDelay: 200,
                        timeout: 30000,
                        minShowTime: 500,
                        defaultMessages: {
                            get: "加载中...",
                            post: "提交中...",
                            postEncrypt: "加密提交中...",
                            getDecrypt: "解密加载中..."
                        }
                    }
                };
            }

            getGlobalLoadingConfig() {
                return this.config.globalLoading;
            }

            setGlobalLoadingConfig(newConfig) {
                this.config.globalLoading = { ...this.config.globalLoading, ...newConfig };
            }

            isGlobalLoadingEnabled() {
                return this.config.globalLoading.enabled === true;
            }

            setGlobalLoadingEnabled(enabled) {
                this.config.globalLoading.enabled = !!enabled;
            }
        }

        // 模拟GlobalLoadingManager
        class MockGlobalLoadingManager {
            constructor() {
                this.context = null;
                this.requestCount = 0;
                this.isVisible = false;
                this.currentMessage = '';
                this.messageQueue = [];
                this.loadingElement = null;
                
                this.defaultConfig = {
                    showDelay: 100,
                    hideDelay: 200,
                    timeout: 30000,
                    minShowTime: 500
                };
            }

            setContext(context) {
                this.context = context;
            }

            getConfig() {
                if (this.context) {
                    const globalConfig = this.context.getGlobalLoadingConfig();
                    return {
                        showDelay: globalConfig.showDelay || this.defaultConfig.showDelay,
                        hideDelay: globalConfig.hideDelay || this.defaultConfig.hideDelay,
                        timeout: globalConfig.timeout || this.defaultConfig.timeout,
                        minShowTime: globalConfig.minShowTime || this.defaultConfig.minShowTime,
                    };
                }
                return this.defaultConfig;
            }

            startRequest(message, priority) {
                const requestId = 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5);
                this.requestCount++;
                
                this.addMessageToQueue(message, priority, requestId);
                this.showLoading();
                
                return requestId;
            }

            endRequest(requestId) {
                if (this.requestCount > 0) {
                    this.requestCount--;
                    this.removeMessageFromQueue(requestId);
                    
                    if (this.requestCount === 0) {
                        this.hideLoading();
                    } else {
                        this.updateMessage();
                    }
                }
            }

            showLoading() {
                if (!this.isVisible) {
                    this.loadingElement = this.createLoadingElement();
                    document.body.appendChild(this.loadingElement);
                    this.isVisible = true;
                }
                this.updateMessage();
            }

            hideLoading() {
                if (this.isVisible && this.loadingElement) {
                    document.body.removeChild(this.loadingElement);
                    this.isVisible = false;
                    this.loadingElement = null;
                }
            }

            createLoadingElement() {
                const loading = document.createElement('div');
                loading.className = 'mock-loading';
                loading.innerHTML = `
                    <div class="mock-loading-content">
                        <div class="mock-loading-spinner"></div>
                        <div class="mock-loading-message">${this.currentMessage}</div>
                    </div>
                `;
                return loading;
            }

            updateMessage() {
                if (this.messageQueue.length > 0) {
                    const highestPriorityMessage = this.messageQueue[0].message;
                    if (this.currentMessage !== highestPriorityMessage) {
                        this.currentMessage = highestPriorityMessage;
                        
                        if (this.isVisible && this.loadingElement) {
                            const messageElement = this.loadingElement.querySelector('.mock-loading-message');
                            if (messageElement) {
                                messageElement.textContent = this.currentMessage;
                            }
                        }
                    }
                }
            }

            addMessageToQueue(message, priority, requestId) {
                this.messageQueue.push({ message, priority, requestId });
                this.messageQueue.sort((a, b) => b.priority - a.priority);
            }

            removeMessageFromQueue(requestId) {
                this.messageQueue = this.messageQueue.filter(item => item.requestId !== requestId);
            }
        }

        // 模拟Service类
        class MockService {
            constructor(context) {
                this.context = context;
                this.loadingManager = globalLoadingManager;
            }

            getRequest(url, data, success, fail, dataType, options = {}) {
                const globalLoadingEnabled = this.context ? this.context.isGlobalLoadingEnabled() : true;
                const globalConfig = this.context ? this.context.getGlobalLoadingConfig() : {};
                
                const showLoading = globalLoadingEnabled && options.showLoading !== false;
                const loadingMessage = options.loadingMessage || globalConfig.defaultMessages?.get || "加载中...";
                const loadingPriority = options.loadingPriority || 0;
                
                let requestId = null;
                
                if (showLoading) {
                    requestId = this.loadingManager.startRequest(loadingMessage, loadingPriority);
                }
                
                this.log(`GET请求: ${url}, Loading: ${showLoading ? '启用' : '禁用'}, 消息: ${loadingMessage}`);
                
                setTimeout(() => {
                    if (showLoading && requestId) {
                        this.loadingManager.endRequest(requestId);
                    }
                    if (success) success({ data: 'mock data' });
                }, Math.random() * 2000 + 500);
            }

            postRequest(url, data, success, fail, dataType, options = {}) {
                const globalLoadingEnabled = this.context ? this.context.isGlobalLoadingEnabled() : true;
                const globalConfig = this.context ? this.context.getGlobalLoadingConfig() : {};
                
                const showLoading = globalLoadingEnabled && options.showLoading !== false;
                const loadingMessage = options.loadingMessage || globalConfig.defaultMessages?.post || "提交中...";
                const loadingPriority = options.loadingPriority || 0;
                
                let requestId = null;
                
                if (showLoading) {
                    requestId = this.loadingManager.startRequest(loadingMessage, loadingPriority);
                }
                
                this.log(`POST请求: ${url}, Loading: ${showLoading ? '启用' : '禁用'}, 消息: ${loadingMessage}`);
                
                setTimeout(() => {
                    if (showLoading && requestId) {
                        this.loadingManager.endRequest(requestId);
                    }
                    if (success) success({ status: 'success' });
                }, Math.random() * 2000 + 500);
            }

            postEncrypt(url, data, success, fail, dataType, options = {}) {
                const globalLoadingEnabled = this.context ? this.context.isGlobalLoadingEnabled() : true;
                const globalConfig = this.context ? this.context.getGlobalLoadingConfig() : {};
                
                const showLoading = globalLoadingEnabled && options.showLoading !== false;
                const loadingMessage = options.loadingMessage || globalConfig.defaultMessages?.postEncrypt || "加密提交中...";
                const loadingPriority = options.loadingPriority || 0;
                
                let requestId = null;
                
                if (showLoading) {
                    requestId = this.loadingManager.startRequest(loadingMessage, loadingPriority);
                }
                
                this.log(`加密POST请求: ${url}, Loading: ${showLoading ? '启用' : '禁用'}, 消息: ${loadingMessage}`);
                
                setTimeout(() => {
                    if (showLoading && requestId) {
                        this.loadingManager.endRequest(requestId);
                    }
                    if (success) success({ encrypted: true });
                }, Math.random() * 2000 + 1000);
            }

            getDecrypt(url, data, success, fail, dataType, options = {}) {
                const globalLoadingEnabled = this.context ? this.context.isGlobalLoadingEnabled() : true;
                const globalConfig = this.context ? this.context.getGlobalLoadingConfig() : {};
                
                const showLoading = globalLoadingEnabled && options.showLoading !== false;
                const loadingMessage = options.loadingMessage || globalConfig.defaultMessages?.getDecrypt || "解密加载中...";
                const loadingPriority = options.loadingPriority || 0;
                
                let requestId = null;
                
                if (showLoading) {
                    requestId = this.loadingManager.startRequest(loadingMessage, loadingPriority);
                }
                
                this.log(`解密GET请求: ${url}, Loading: ${showLoading ? '启用' : '禁用'}, 消息: ${loadingMessage}`);
                
                setTimeout(() => {
                    if (showLoading && requestId) {
                        this.loadingManager.endRequest(requestId);
                    }
                    if (success) success({ decrypted: true });
                }, Math.random() * 2000 + 1000);
            }

            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logPanel = document.getElementById('log-panel');
                const logEntry = document.createElement('div');
                logEntry.innerHTML = `[${timestamp}] ${message}`;
                logPanel.appendChild(logEntry);
                logPanel.scrollTop = logPanel.scrollHeight;
            }
        }

        // 创建实例
        const context = new MockContext();
        const globalLoadingManager = new MockGlobalLoadingManager();
        const service = new MockService(context);

        // 设置Context
        globalLoadingManager.setContext(context);

        // 更新状态显示
        function updateStatus() {
            const config = context.getGlobalLoadingConfig();
            const statusPanel = document.getElementById('status-panel');
            statusPanel.innerHTML = `
                <strong>全局Loading配置:</strong><br>
                启用状态: ${config.enabled ? '<span class="enabled">已启用</span>' : '<span class="disabled">已禁用</span>'}<br>
                显示延迟: ${config.showDelay}ms<br>
                隐藏延迟: ${config.hideDelay}ms<br>
                超时时间: ${config.timeout}ms<br>
                最小显示时间: ${config.minShowTime}ms<br>
                更新时间: ${new Date().toLocaleTimeString()}
            `;
        }

        // 更新全局配置
        function updateGlobalConfig() {
            const enabled = document.getElementById('enableLoading').checked;
            const showDelay = parseInt(document.getElementById('showDelay').value);
            const hideDelay = parseInt(document.getElementById('hideDelay').value);
            const timeout = parseInt(document.getElementById('timeout').value);
            const minShowTime = parseInt(document.getElementById('minShowTime').value);

            context.setGlobalLoadingConfig({
                enabled,
                showDelay,
                hideDelay,
                timeout,
                minShowTime
            });

            document.getElementById('loadingStatus').textContent = enabled ? '已启用' : '已禁用';
            document.getElementById('loadingStatus').className = enabled ? 'enabled' : 'disabled';

            updateStatus();
        }

        // 应用配置
        function applyConfig() {
            updateGlobalConfig();
            service.log('全局Loading配置已更新');
        }

        // 重置配置
        function resetConfig() {
            document.getElementById('enableLoading').checked = true;
            document.getElementById('showDelay').value = 100;
            document.getElementById('hideDelay').value = 200;
            document.getElementById('timeout').value = 30000;
            document.getElementById('minShowTime').value = 500;
            
            updateGlobalConfig();
            service.log('全局Loading配置已重置为默认值');
        }

        // 测试函数
        function testGetRequest() {
            service.getRequest('/api/test-data', {}, 
                (data) => service.log('GET请求成功'));
        }

        function testPostRequest() {
            service.postRequest('/api/save-data', { test: 'data' }, 
                (result) => service.log('POST请求成功'));
        }

        function testEncryptRequest() {
            service.postEncrypt('/api/secure-save', { sensitive: 'data' }, 
                (result) => service.log('加密请求成功'));
        }

        function testDecryptRequest() {
            service.getDecrypt('/api/secure-data', {}, 
                (result) => service.log('解密请求成功'));
        }

        function testConcurrentRequests() {
            service.getRequest('/api/data1', {}, (data) => service.log('并发请求1完成'));
            service.postRequest('/api/data2', {}, (data) => service.log('并发请求2完成'));
            service.postEncrypt('/api/data3', {}, (data) => service.log('并发请求3完成'));
        }

        function enableGlobalLoading() {
            context.setGlobalLoadingEnabled(true);
            document.getElementById('enableLoading').checked = true;
            updateGlobalConfig();
            service.log('全局Loading已启用');
        }

        function disableGlobalLoading() {
            context.setGlobalLoadingEnabled(false);
            document.getElementById('enableLoading').checked = false;
            updateGlobalConfig();
            service.log('全局Loading已禁用');
        }

        function testWithDisabledLoading() {
            service.log('在禁用Loading状态下测试请求...');
            service.getRequest('/api/test1', {}, (data) => service.log('禁用状态请求1完成'));
            service.postRequest('/api/test2', {}, (data) => service.log('禁用状态请求2完成'));
        }

        function clearLog() {
            document.getElementById('log-panel').innerHTML = '';
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            updateStatus();
            service.log('全局Loading配置测试环境准备就绪');
            
            // 定时更新状态
            setInterval(updateStatus, 2000);
        });
    </script>
</body>
</html>
