<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全局Loading管理器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .test-button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-button.danger {
            background: #dc3545;
        }
        
        .test-button.danger:hover {
            background: #c82333;
        }
        
        .test-button.success {
            background: #28a745;
        }
        
        .test-button.success:hover {
            background: #218838;
        }
        
        .status-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-panel {
            background: #343a40;
            color: #fff;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        /* 模拟Loading样式 */
        .mock-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .mock-loading-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .mock-loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>全局Loading管理器测试</h1>
    
    <div class="test-section">
        <h2>基础功能测试</h2>
        <div class="description">
            测试单个请求的loading显示和隐藏功能
        </div>
        <div class="button-group">
            <button class="test-button" onclick="testSingleRequest()">单个请求</button>
            <button class="test-button" onclick="testQuickRequest()">快速请求</button>
            <button class="test-button" onclick="testCustomMessage()">自定义消息</button>
        </div>
    </div>

    <div class="test-section">
        <h2>并发请求测试</h2>
        <div class="description">
            测试多个并发请求时只显示一个loading的功能
        </div>
        <div class="button-group">
            <button class="test-button" onclick="testConcurrentRequests()">3个并发请求</button>
            <button class="test-button" onclick="testManyRequests()">10个并发请求</button>
            <button class="test-button" onclick="testPriorityMessages()">优先级消息测试</button>
        </div>
    </div>

    <div class="test-section">
        <h2>错误处理测试</h2>
        <div class="description">
            测试异常情况下的loading处理
        </div>
        <div class="button-group">
            <button class="test-button danger" onclick="testTimeout()">超时测试</button>
            <button class="test-button danger" onclick="testUnmatchedEnd()">未匹配结束</button>
            <button class="test-button success" onclick="forceReset()">强制重置</button>
        </div>
    </div>

    <div class="test-section">
        <h2>状态监控</h2>
        <div class="status-panel" id="status-panel">
            状态信息将在这里显示...
        </div>
        <div class="button-group">
            <button class="test-button" onclick="startStatusMonitor()">开始监控</button>
            <button class="test-button" onclick="stopStatusMonitor()">停止监控</button>
        </div>
    </div>

    <div class="test-section">
        <h2>操作日志</h2>
        <div class="log-panel" id="log-panel">
            日志信息将在这里显示...
        </div>
        <div class="button-group">
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        // 模拟GlobalLoadingManager
        class MockGlobalLoadingManager {
            constructor() {
                this.requestCount = 0;
                this.isVisible = false;
                this.currentMessage = '加载中...';
                this.messageQueue = [];
                this.loadingElement = null;
                this.showTimer = null;
                this.hideTimer = null;
                this.timeoutTimer = null;
                this.showStartTime = null;
                
                this.config = {
                    showDelay: 100,
                    hideDelay: 200,
                    timeout: 30000,
                    minShowTime: 500
                };
            }

            startRequest(message = '加载中...', priority = 0) {
                const requestId = this.generateRequestId();
                this.requestCount++;
                
                this.addMessageToQueue(message, priority, requestId);
                this.showLoading();
                
                this.log(`开始请求 ${requestId}, 当前请求数: ${this.requestCount}, 消息: ${message}`);
                return requestId;
            }

            endRequest(requestId) {
                if (this.requestCount > 0) {
                    this.requestCount--;
                    this.removeMessageFromQueue(requestId);
                    
                    this.log(`结束请求 ${requestId}, 剩余请求数: ${this.requestCount}`);
                    
                    if (this.requestCount === 0) {
                        this.hideLoading();
                    } else {
                        this.updateMessage();
                    }
                }
            }

            showLoading() {
                if (this.hideTimer) {
                    clearTimeout(this.hideTimer);
                    this.hideTimer = null;
                }

                if (this.isVisible) {
                    this.updateMessage();
                    return;
                }

                if (!this.showTimer) {
                    this.showTimer = setTimeout(() => {
                        this.showLoadingImmediately();
                        this.showTimer = null;
                    }, this.config.showDelay);
                }
            }

            showLoadingImmediately() {
                if (!this.isVisible) {
                    this.loadingElement = this.createLoadingElement();
                    document.body.appendChild(this.loadingElement);
                    this.isVisible = true;
                    this.showStartTime = Date.now();
                    
                    this.setTimeoutProtection();
                    this.log('Loading显示');
                }
                this.updateMessage();
            }

            hideLoading() {
                if (this.showTimer) {
                    clearTimeout(this.showTimer);
                    this.showTimer = null;
                }

                if (!this.isVisible) {
                    return;
                }

                const showDuration = Date.now() - (this.showStartTime || 0);
                const remainingMinTime = Math.max(0, this.config.minShowTime - showDuration);
                const hideDelay = Math.max(this.config.hideDelay, remainingMinTime);

                this.hideTimer = setTimeout(() => {
                    this.hideLoadingImmediately();
                    this.hideTimer = null;
                }, hideDelay);
            }

            hideLoadingImmediately() {
                if (this.isVisible && this.loadingElement) {
                    document.body.removeChild(this.loadingElement);
                    this.isVisible = false;
                    this.loadingElement = null;
                    this.showStartTime = null;
                    
                    this.log('Loading隐藏');
                }
                this.clearTimeoutProtection();
            }

            createLoadingElement() {
                const loading = document.createElement('div');
                loading.className = 'mock-loading';
                loading.innerHTML = `
                    <div class="mock-loading-content">
                        <div class="mock-loading-spinner"></div>
                        <div class="mock-loading-message">${this.currentMessage}</div>
                    </div>
                `;
                return loading;
            }

            updateMessage() {
                if (this.messageQueue.length > 0) {
                    const highestPriorityMessage = this.messageQueue[0].message;
                    if (this.currentMessage !== highestPriorityMessage) {
                        this.currentMessage = highestPriorityMessage;
                        
                        if (this.isVisible && this.loadingElement) {
                            const messageElement = this.loadingElement.querySelector('.mock-loading-message');
                            if (messageElement) {
                                messageElement.textContent = this.currentMessage;
                            }
                        }
                    }
                }
            }

            addMessageToQueue(message, priority, requestId) {
                this.messageQueue.push({
                    message,
                    priority,
                    requestId,
                    timestamp: Date.now()
                });
                this.messageQueue.sort((a, b) => b.priority - a.priority);
            }

            removeMessageFromQueue(requestId) {
                this.messageQueue = this.messageQueue.filter(item => item.requestId !== requestId);
            }

            setTimeoutProtection() {
                this.clearTimeoutProtection();
                this.timeoutTimer = setTimeout(() => {
                    this.log('Loading超时，强制隐藏', 'warn');
                    this.forceReset();
                }, this.config.timeout);
            }

            clearTimeoutProtection() {
                if (this.timeoutTimer) {
                    clearTimeout(this.timeoutTimer);
                    this.timeoutTimer = null;
                }
            }

            forceReset() {
                this.log('强制重置', 'warn');
                this.requestCount = 0;
                this.messageQueue = [];
                this.clearAllTimers();
                this.hideLoadingImmediately();
            }

            clearAllTimers() {
                if (this.showTimer) {
                    clearTimeout(this.showTimer);
                    this.showTimer = null;
                }
                if (this.hideTimer) {
                    clearTimeout(this.hideTimer);
                    this.hideTimer = null;
                }
                this.clearTimeoutProtection();
            }

            generateRequestId() {
                return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }

            getStatus() {
                return {
                    requestCount: this.requestCount,
                    isVisible: this.isVisible,
                    currentMessage: this.currentMessage,
                    messageQueueLength: this.messageQueue.length
                };
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logPanel = document.getElementById('log-panel');
                const logEntry = document.createElement('div');
                logEntry.innerHTML = `[${timestamp}] ${message}`;
                
                if (type === 'warn') {
                    logEntry.style.color = '#ffc107';
                } else if (type === 'error') {
                    logEntry.style.color = '#dc3545';
                }
                
                logPanel.appendChild(logEntry);
                logPanel.scrollTop = logPanel.scrollHeight;
            }
        }

        // 创建全局实例
        const globalLoadingManager = new MockGlobalLoadingManager();
        let statusMonitorInterval = null;

        // 测试函数
        function testSingleRequest() {
            const requestId = globalLoadingManager.startRequest('加载数据中...');
            setTimeout(() => {
                globalLoadingManager.endRequest(requestId);
            }, 2000);
        }

        function testQuickRequest() {
            const requestId = globalLoadingManager.startRequest('快速请求...');
            setTimeout(() => {
                globalLoadingManager.endRequest(requestId);
            }, 200);
        }

        function testCustomMessage() {
            const requestId = globalLoadingManager.startRequest('正在处理您的请求，请稍候...');
            setTimeout(() => {
                globalLoadingManager.endRequest(requestId);
            }, 3000);
        }

        function testConcurrentRequests() {
            const req1 = globalLoadingManager.startRequest('加载图表数据...', 1);
            const req2 = globalLoadingManager.startRequest('加载用户信息...', 2);
            const req3 = globalLoadingManager.startRequest('加载配置...', 0);

            setTimeout(() => globalLoadingManager.endRequest(req1), 1000);
            setTimeout(() => globalLoadingManager.endRequest(req3), 1500);
            setTimeout(() => globalLoadingManager.endRequest(req2), 2000);
        }

        function testManyRequests() {
            const requests = [];
            for (let i = 0; i < 10; i++) {
                const requestId = globalLoadingManager.startRequest(`请求${i + 1}...`, Math.floor(Math.random() * 5));
                requests.push(requestId);
            }

            requests.forEach((requestId, index) => {
                setTimeout(() => {
                    globalLoadingManager.endRequest(requestId);
                }, (index + 1) * 300);
            });
        }

        function testPriorityMessages() {
            const lowPriority = globalLoadingManager.startRequest('低优先级消息...', 1);
            setTimeout(() => {
                const highPriority = globalLoadingManager.startRequest('高优先级消息！', 10);
                setTimeout(() => {
                    globalLoadingManager.endRequest(highPriority);
                    setTimeout(() => {
                        globalLoadingManager.endRequest(lowPriority);
                    }, 1000);
                }, 1000);
            }, 500);
        }

        function testTimeout() {
            // 修改超时时间为5秒用于测试
            globalLoadingManager.config.timeout = 5000;
            globalLoadingManager.startRequest('超时测试请求...');
            // 故意不调用endRequest
        }

        function testUnmatchedEnd() {
            globalLoadingManager.endRequest('non-existent-id');
        }

        function forceReset() {
            globalLoadingManager.forceReset();
        }

        function startStatusMonitor() {
            if (statusMonitorInterval) {
                clearInterval(statusMonitorInterval);
            }
            
            statusMonitorInterval = setInterval(() => {
                const status = globalLoadingManager.getStatus();
                const statusPanel = document.getElementById('status-panel');
                statusPanel.innerHTML = `
                    <strong>当前状态:</strong><br>
                    请求数量: ${status.requestCount}<br>
                    Loading可见: ${status.isVisible}<br>
                    当前消息: ${status.currentMessage}<br>
                    消息队列长度: ${status.messageQueueLength}<br>
                    更新时间: ${new Date().toLocaleTimeString()}
                `;
            }, 500);
        }

        function stopStatusMonitor() {
            if (statusMonitorInterval) {
                clearInterval(statusMonitorInterval);
                statusMonitorInterval = null;
            }
        }

        function clearLog() {
            document.getElementById('log-panel').innerHTML = '';
        }

        // 页面加载完成后开始状态监控
        window.addEventListener('load', () => {
            startStatusMonitor();
            globalLoadingManager.log('页面加载完成，测试环境准备就绪');
        });
    </script>
</body>
</html>
