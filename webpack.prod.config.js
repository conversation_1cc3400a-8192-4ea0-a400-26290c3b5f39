/**
 * Created by <PERSON><PERSON><PERSON>Gao on 2018-05-18.
 * Base on Webpack4
 */
//使用插件html-webpack-plugin打包合并html
//const HtmlWebpackPlugin = require("html-webpack-plugin")
//使用插件extract-text-webpack-plugin打包独立的css

const path = require("path");
//使用UglifyJsPlugin压缩代码
const uglifyjs = require('uglifyjs-webpack-plugin');

module.exports = {
  mode: "production", //开发模式 mode:'development', 生产模式
  entry: {
    lsd: "./src/Lsd.js"
  },
  /**
   开发环境推荐使用：
   1.eval ：每个模块使用eval()和//@ sourceURL执行。这是非常快。主要缺点是，它没有正确显示行号，因为它被映射到转换代码而不是原始代码(没有来自加载器的源映射)。
   2.eval-source-map：每个模块使用eval()执行，而SourceMap作为DataUrl添加到eval()中。最初它是缓慢的，但是它提供快速的重建速度和产生真实的文件。行号被正确映射，因为它被映射到原始代码。它产生了最优质的开发资源。
   3.cheap-eval-source-map：与eval-source-map类似，每个模块都使用eval()执行。它没有列映射，它只映射行号。它忽略了来自加载器的源代码，并且只显示与eval devtool相似的经过转换的代码。
   4.cheap-module-eval-source-map：类似于cheap-eval-source-map，在本例中，来自加载器的源映射被处理以获得更好的结果。然而，加载器源映射被简化为每一行的单个映射。
   生产环境推荐使用：
   1.(none) ：(省略devtool选项)-不触发SourceMap。这是一个很好的选择。
   2.source-map：一个完整的SourceMap是作为一个单独的文件。它为bundle 添加了引用注释，因此开发工具知道在哪里找到它。
   3.hidden-source-map：与source-map相同，但不向bundle 添加引用注释。如果您只希望SourceMaps从错误报告中映射错误堆栈跟踪，但不想为浏览器开发工具暴露您的SourceMap，可以使用此选项。
   4.nosources-source-map：一个SourceMap是在没有源代码的情况下创建的。它可以用于在客户机上映射堆栈跟踪，而不暴露所有源代码。您可以将源映射文件部署到webserver。
   */
  //devtool:'source-map', //生产环境默认不配置相关
  performance: { hints: false },
  output: {
    //path:path.resolve("../../lcdp-html/designer/lsd/dist/asserts"),
    path: path.resolve("./dist/asserts"),
    filename: "[name].bundle.js",
    publicPath: "/"
  },
  module: {
    rules: [
      {
        test: /\.(jsx|js)?$/,
        exclude: /node_modules/,
        loader: "babel-loader",
        options: {
          "presets": [
            ["env", {
              "targets": {
                "browsers": "ie 10" //支持ie 10
              }
            }]
          ]
        }
      },
      {
        test: /\.css$/,
        use: [{ loader: "style-loader" }, { loader: "css-loader" }]
      },
      {
        test: /\.(eot|woff|woff2|ttf|svg|png|jpg|otf)$/,
        use: [
          {
            loader: "url-loader",
            options: {
              limit: 10000000
            }
          }
        ]
      }
    ]
  },
  plugins: [
    new uglifyjs({
      // uglifyOptions: {
      //     compress: {
      //         //取消内联(临时方案)
      //         inline: false,
      //     },
      // },
      sourceMap: false
    }), //压缩js
  ]
};
